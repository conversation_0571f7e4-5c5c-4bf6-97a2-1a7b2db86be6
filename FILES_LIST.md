# قائمة الملفات المطلوبة لدمج مستخرج الصور المحسن

## 📁 الملفات الجديدة (انسخها إلى أداتك)

### 1️⃣ الملف الرئيسي 
- **`mcpedl_image_extractor_v2.py`** - الأداة المحسنة الرئيسية
- **الوظيفة:** استبدال أي ملف استخراج صور قديم
- **الاستخدام:** `from mcpedl_image_extractor_v2 import get_mod_images_only`

### 2️⃣ ملفات المتطلبات
- **`requirements.txt`** - قائمة المكتبات المطلوبة
- **`install_requirements.py`** - تثبيت تلقائي للمتطلبات
- **الاستخدام:** `python install_requirements.py`

### 3️⃣ ملفات الاختبار والأمثلة
- **`test_extractor.py`** - اختبار شامل للأداة
- **`quick_example.py`** - أمثلة متنوعة للاستخدام
- **الاستخدام:** `python test_extractor.py`

### 4️⃣ ملفات التوثيق
- **`README_INTEGRATION.md`** - دليل الدمج والاستخدام
- **`FILES_LIST.md`** - هذه القائمة

## 🔄 الملفات المطلوب استبدالها

### ❌ احذف أو استبدل هذه الملفات:
```
mcpedl_extractor_fixed.py          → استبدل بـ mcpedl_image_extractor_v2.py
enhanced_mcpedl_extractor.py       → استبدل بـ mcpedl_image_extractor_v2.py  
mcpedl_selenium_scraper.py         → استبدل بـ mcpedl_image_extractor_v2.py
أي ملف استخراج صور قديم           → استبدل بـ mcpedl_image_extractor_v2.py
```

## 📦 هيكل الملفات النهائي

```
your_tool/
├── mcpedl_image_extractor_v2.py    # ✅ الملف الرئيسي الجديد
├── requirements.txt                # ✅ المتطلبات
├── install_requirements.py         # ✅ التثبيت التلقائي
├── test_extractor.py              # ✅ الاختبارات
├── quick_example.py               # ✅ الأمثلة
├── README_INTEGRATION.md          # ✅ دليل الاستخدام
├── [ملفات أداتك الأخرى...]        # 📁 ملفاتك الحالية
└── [واجهة المستخدم...]            # 🖥️ واجهتك الحالية
```

## ⚡ خطوات الدمج السريعة

### 1. نسخ الملفات
```bash
# انسخ جميع الملفات إلى مجلد أداتك
cp integration_files/* /path/to/your/tool/
```

### 2. تثبيت المتطلبات
```bash
cd /path/to/your/tool/
python install_requirements.py
```

### 3. اختبار الأداة
```bash
python test_extractor.py
```

### 4. تعديل كودك
```python
# في ملف أداتك الرئيسي، استبدل:
# ❌ from old_extractor import extract_images
# ✅ بـ:
from mcpedl_image_extractor_v2 import get_mod_images_only

# استخدام بسيط:
images = get_mod_images_only(url)
```

## 🎯 الاستخدام في أداتك

### للواجهة الرسومية (GUI):
```python
def extract_button_clicked():
    url = self.url_entry.get()
    
    # استخراج الصور
    from mcpedl_image_extractor_v2 import get_mod_images_only
    images = get_mod_images_only(url)
    
    # تحديث الواجهة
    self.update_image_list(images)
    self.status_label.config(text=f"✅ تم استخراج {len(images)} صورة")
```

### لسطر الأوامر (CLI):
```python
import sys
from mcpedl_image_extractor_v2 import get_mod_images_only

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("الاستخدام: python script.py <url>")
        sys.exit(1)
    
    url = sys.argv[1]
    images = get_mod_images_only(url)
    
    print(f"تم استخراج {len(images)} صورة:")
    for img in images:
        print(f"  - {img}")
```

### للمعالجة الدفعية:
```python
from mcpedl_image_extractor_v2 import extract_mcpedl_images

urls = ["url1", "url2", "url3"]
results = []

for url in urls:
    result = extract_mcpedl_images(url)
    if result['success']:
        results.append({
            'url': url,
            'mod_name': result['mod_name'],
            'images': result['main_mod_images']
        })
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError"
**الحل:**
```bash
pip install selenium cloudscraper beautifulsoup4 requests
```

### مشكلة: "ChromeDriver not found"
**الحل:**
```bash
# Ubuntu/Debian
sudo apt install chromium-browser

# أو تحميل من: https://chromedriver.chromium.org/
```

### مشكلة: "لا توجد صور"
**الحل:**
```python
# جرب بدون Selenium
from mcpedl_image_extractor_v2 import extract_mcpedl_images
result = extract_mcpedl_images(url, use_selenium=False)
```

## ✅ التحقق من النجاح

بعد الدمج، يجب أن تحصل على:
- ✅ استخراج 5+ صور من `https://mcpedl.com/glow-em-all-shader/`
- ✅ استخراج 30+ صور من `https://mcpedl.com/simple-guns-fws/`
- ✅ رفض صور المستخدمين والإعلانات
- ✅ معدل نجاح 90%+ على معظم المودات

## 📞 الدعم

إذا واجهت مشاكل:
1. شغل `test_extractor.py` للتشخيص
2. تحقق من تثبيت المتطلبات
3. جرب `quick_example.py` للتعلم
4. راجع `README_INTEGRATION.md` للتفاصيل

---

**🎉 مع هذه الملفات، ستحصل على استخراج صور دقيق وموثوق لجميع مودات MCPEDL!**
