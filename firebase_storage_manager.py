# -*- coding: utf-8 -*-
"""
مدير Firebase Storage لتخزين ملفات المودات
Firebase Storage Manager for Mod Files
"""

import os
import json
import time
import hashlib
import requests
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
import tempfile

try:
    import firebase_admin
    from firebase_admin import credentials, storage
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    print("⚠️ firebase-admin غير متوفر. سيتم تعطيل رفع الملفات.")




# دالة مساعدة للاستخدام السهل
from firebase_config import firebase_manager as global_firebase_manager

def create_firebase_manager():
    """إنشاء مدير Firebase Storage مع حلول بديلة"""

    # أولاً: محاولة استخدام FirebaseManager المهيأ عالمياً من firebase_config.py
    if global_firebase_manager and global_firebase_manager.is_initialized:
        print("✅ تم استخدام FirebaseManager المهيأ عالمياً.")
        return global_firebase_manager

    # ثانياً: محاولة تهيئة Firebase مباشرة
    try:
        print("🔄 محاولة تهيئة Firebase مباشرة...")
        if global_firebase_manager.auto_initialize():
            print("✅ تم تهيئة Firebase بنجاح")
            return global_firebase_manager
    except Exception as firebase_error:
        print(f"⚠️ فشل تهيئة Firebase: {firebase_error}")

    # إذا فشل Firebase
    print("❌ فشل في تهيئة Firebase Storage")
    print("💡 نصائح:")
    print("   - تأكد من وجود ملف firebase-service-account.json")
    print("   - راجع إعدادات Firebase في FIREBASE_TROUBLESHOOTING.md")
    print("   - تحقق من اتصالك بالإنترنت")

    return None


# اختبار سريع
if __name__ == "__main__":
    print("🧪 اختبار Firebase Storage Manager...")
    
    manager = create_firebase_manager()
    
    if manager and manager.is_initialized:
        print("✅ تم تهيئة Firebase بنجاح")
        
        # فحص الاتصال
        status = manager.test_connection()
        print(f"📊 حالة الاتصال: {status}")

        # عرض قائمة الملفات
        files = manager.list_files("mods", 5)
        print(f"📁 الملفات الموجودة: {len(files)}")
        
    else:
        print("❌ فشل في تهيئة Firebase")
