# -*- coding: utf-8 -*-
"""
أداة محسنة لاستخراج الصور الرئيسية لمودات MCPEDL - النسخة النهائية المحسنة
تحل مشكلة عدم استخراج الصور الرئيسية من صفحات MCPEDL

✅ حل مشكلة المحتوى الديناميكي (Vue.js/Nuxt.js)
✅ استخراج دقيق للصور الرئيسية فقط
✅ دعم متعدد الطرق (Selenium + CloudScraper + Requests)
✅ فلترة ذكية للصور غير المرغوبة

المطور: MiniMax Agent
التاريخ: 2025-06-22
"""

import time
import json
import re
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse

# محاولة استيراد Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# محاولة استيراد cloudscraper كبديل
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False

# محاولة استيراد requests كبديل أخير
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

# BeautifulSoup مطلوب
from bs4 import BeautifulSoup


class MCPEDLImageExtractorV2:
    """
    مستخرج محسن للصور الرئيسية لمودات MCPEDL - النسخة الثانية
    يحل مشكلة المحتوى الديناميكي ويستخرج الصور بدقة عالية
    """
    
    def __init__(self, prefer_selenium=True, headless=True, timeout=15):
        """
        تهيئة المستخرج
        
        Args:
            prefer_selenium: تفضيل استخدام Selenium (أدق ولكن أبطأ)
            headless: تشغيل المتصفح في الخلفية
            timeout: مهلة انتظار تحميل المحتوى (ثواني)
        """
        self.prefer_selenium = prefer_selenium and SELENIUM_AVAILABLE
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.session = None
        
        # إعداد الطريقة المفضلة
        self._setup_extractor()
    
    def _setup_extractor(self):
        """إعداد أداة الاستخراج المناسبة"""
        if self.prefer_selenium:
            try:
                self._setup_selenium()
                print("✅ تم إعداد Selenium WebDriver")
                return
            except Exception as e:
                print(f"⚠️ فشل إعداد Selenium: {e}")
                print("🔄 التبديل إلى cloudscraper...")
        
        # إعداد cloudscraper كبديل
        if CLOUDSCRAPER_AVAILABLE:
            try:
                self._setup_cloudscraper()
                print("✅ تم إعداد cloudscraper")
                return
            except Exception as e:
                print(f"⚠️ فشل إعداد cloudscraper: {e}")
        
        # استخدام requests كبديل أخير
        if REQUESTS_AVAILABLE:
            self._setup_requests()
            print("✅ تم إعداد requests (محدود الوظائف)")
        else:
            raise RuntimeError("❌ لا توجد أدوات HTTP متاحة!")
    
    def _setup_selenium(self):
        """إعداد Selenium WebDriver"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
        
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        # تجنب اكتشاف البوت
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    def _setup_cloudscraper(self):
        """إعداد cloudscraper"""
        self.session = cloudscraper.create_scraper()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive'
        })
    
    def _setup_requests(self):
        """إعداد requests عادي"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def extract_mod_images(self, url: str) -> Dict[str, Any]:
        """
        الدالة الرئيسية لاستخراج الصور الرئيسية للمود
        
        Args:
            url: رابط صفحة المود في MCPEDL
            
        Returns:
            dict: قاموس يحتوي على الصور المستخرجة والمعلومات التفصيلية
        """
        print(f"🔍 بدء استخراج الصور من: {url}")
        
        try:
            # الحصول على محتوى الصفحة
            page_content = self._get_page_content(url)
            
            if not page_content:
                return self._create_error_result(url, "فشل في تحميل محتوى الصفحة")
            
            # تحليل HTML
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # استخراج اسم المود
            mod_name = self._extract_mod_name(soup)
            print(f"📝 اسم المود: {mod_name}")
            
            # استخراج جميع الصور
            all_images = self._extract_all_images(soup, url)
            
            # تصنيف وفلترة الصور
            classified_images = self._classify_images(all_images)
            
            # إنشاء النتيجة
            result = {
                'mod_name': mod_name,
                'source_url': url,
                'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_images_found': len(all_images),
                'main_mod_images': classified_images['mod_images'],
                'filtered_out': classified_images['filtered_images'],
                'statistics': self._generate_statistics(classified_images['mod_images']),
                'extraction_method': self._get_extraction_method(),
                'success': True
            }
            
            print(f"✅ تم الانتهاء - استخراج {len(classified_images['mod_images'])} صورة رئيسية")
            return result
            
        except Exception as e:
            print(f"❌ خطأ في استخراج الصور: {str(e)}")
            return self._create_error_result(url, str(e))
    
    def _get_page_content(self, url: str) -> Optional[str]:
        """الحصول على محتوى الصفحة باستخدام الطريقة المناسبة"""
        if self.driver:
            return self._get_content_with_selenium(url)
        elif self.session:
            return self._get_content_with_session(url)
        else:
            raise RuntimeError("لا توجد أداة متاحة لتحميل الصفحة")
    
    def _get_content_with_selenium(self, url: str) -> str:
        """تحميل المحتوى باستخدام Selenium"""
        print("⏳ تحميل الصفحة باستخدام Selenium...")
        
        self.driver.get(url)
        
        # انتظار تحميل المحتوى الديناميكي
        print("⏳ انتظار تحميل المحتوى الديناميكي...")
        time.sleep(3)  # انتظار أساسي
        
        # انتظار ظهور صور forgecdn
        wait = WebDriverWait(self.driver, self.timeout)
        try:
            wait.until(EC.presence_of_element_located((By.XPATH, "//img[contains(@src, 'forgecdn.net')]")))
            print("✅ تم اكتشاف صور forgecdn")
        except TimeoutException:
            print("⚠️ لم يتم اكتشاف صور forgecdn، الاستمرار بالمحتوى المتاح")
        
        time.sleep(2)  # انتظار إضافي
        return self.driver.page_source
    
    def _get_content_with_session(self, url: str) -> Optional[str]:
        """تحميل المحتوى باستخدام HTTP session"""
        print("⏳ تحميل الصفحة باستخدام HTTP...")
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            print(f"✅ تم تحميل الصفحة - {response.status_code}")
            return response.text
        except Exception as e:
            print(f"❌ خطأ في تحميل الصفحة: {e}")
            return None
    
    def _extract_mod_name(self, soup: BeautifulSoup) -> str:
        """استخراج اسم المود"""
        selectors = [
            'h1.post-page__title',
            'h1.entry-title',
            'h1.post-title',
            'h1',
            'title'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                name = element.get_text().strip()
                # تنظيف الاسم
                name = re.sub(r'\s*\|\s*.*$', '', name)
                name = re.sub(r'\s*-\s*MCPEDL.*$', '', name)
                if name and len(name) > 3:
                    return name.strip()
        
        return "مود غير معروف"
    
    def _extract_all_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج جميع الصور من الصفحة"""
        print("🔍 استخراج جميع الصور من الصفحة...")
        
        images = set()  # استخدام set لتجنب المكررات
        
        # البحث عن جميع وسوم img
        img_tags = soup.find_all('img')
        print(f"📊 تم العثور على {len(img_tags)} وسم img")
        
        for img in img_tags:
            src = self._get_image_src(img)
            if src:
                # تحويل الروابط النسبية لمطلقة
                full_url = urljoin(base_url, src)
                images.add(full_url)
        
        print(f"📊 إجمالي الصور الفريدة: {len(images)}")
        return list(images)
    
    def _get_image_src(self, img_tag) -> Optional[str]:
        """استخراج رابط الصورة من وسم img"""
        # ترتيب أولوية المصادر
        sources = ['src', 'data-src', 'data-original', 'data-lazy', 'data-srcset']
        
        for attr in sources:
            src = img_tag.get(attr)
            if src and src.strip() and not src.startswith('data:image'):
                # إذا كان srcset، أخذ أول رابط
                if 'srcset' in attr and ',' in src:
                    src = src.split(',')[0].strip().split(' ')[0]
                return src.strip()
        
        return None
    
    def _classify_images(self, images: List[str]) -> Dict:
        """تصنيف وفلترة الصور للحصول على الصور الرئيسية فقط"""
        print("🔍 تصنيف وفلترة الصور...")
        
        mod_images = []
        filtered_images = []
        
        for img_url in images:
            classification = self._classify_single_image(img_url)
            
            if classification['is_mod_image']:
                mod_images.append(img_url)
            else:
                filtered_images.append({
                    'url': img_url,
                    'reason': classification['reason'],
                    'category': classification['category']
                })
        
        return {
            'mod_images': mod_images,
            'filtered_images': filtered_images
        }
    
    def _classify_single_image(self, url: str) -> Dict:
        """تصنيف صورة واحدة - خوارزمية محسنة ودقيقة"""
        if not url or not isinstance(url, str):
            return {'is_mod_image': False, 'reason': 'رابط غير صالح', 'category': 'invalid'}
        
        url_lower = url.lower()
        
        # 1. فحص صور المستخدمين أولاً (رفض فوري)
        user_patterns = [
            '/users/', '/user/', '/profile/', '/avatar',
            'gravatar.com', 'secure.gravatar', 'profile_pic'
        ]
        if any(pattern in url_lower for pattern in user_patterns):
            return {'is_mod_image': False, 'reason': 'صورة مستخدم', 'category': 'user'}
        
        # 2. فحص صور النظام والواجهة (رفض فوري)
        system_patterns = [
            '/img/', '/_nuxt/', '/assets/', '/static/',
            'logo.', 'favicon', 'icon.', 'empty.png',
            'placeholder', 'loading.', 'spinner.', 'shield.'
        ]
        if any(pattern in url_lower for pattern in system_patterns):
            return {'is_mod_image': False, 'reason': 'صورة نظام/واجهة', 'category': 'system'}
        
        # 3. فحص الإعلانات (رفض فوري)
        ads_patterns = [
            'advertisement', 'banner', 'ads', 'sponsored',
            'promo', 'affiliate', 'monetize', 'doubleclick',
            'adsrvr', 'pubmatic', 'scorecardresearch'
        ]
        if any(pattern in url_lower for pattern in ads_patterns):
            return {'is_mod_image': False, 'reason': 'إعلان', 'category': 'ads'}
        
        # 4. فحص المودات المقترحة (رفض فوري)
        suggested_patterns = [
            'related-post', 'suggested', 'recommended',
            'you-may-also-like', 'similar-mods', 'more-mods'
        ]
        if any(pattern in url_lower for pattern in suggested_patterns):
            return {'is_mod_image': False, 'reason': 'مود مقترح', 'category': 'suggested'}
        
        # 5. قبول المصادر الموثوقة (قبول فوري) - هذا هو الأهم!
        trusted_sources = [
            'media.forgecdn.net/attachments/description',  # المصدر الرئيسي
            'mcpedl.com/wp-content/uploads',               # رفع محلي
            'api.mcpedl.com/storage',                      # تخزين API
            'r2.mcpedl.com/content'                        # محتوى R2
        ]
        
        if any(source in url_lower for source in trusted_sources):
            return {'is_mod_image': True, 'reason': 'مصدر موثوق', 'category': 'trusted'}
        
        # 6. فحص مؤشرات المحتوى (قبول مشروط)
        content_indicators = [
            'screenshot', 'preview', 'mod', 'addon',
            'texture', 'resource', 'behavior', 'showcase'
        ]
        
        if any(indicator in url_lower for indicator in content_indicators):
            return {'is_mod_image': True, 'reason': 'مؤشرات محتوى', 'category': 'content'}
        
        # 7. رفض البقية
        return {'is_mod_image': False, 'reason': 'غير مطابق للمعايير', 'category': 'other'}
    
    def _generate_statistics(self, mod_images: List[str]) -> Dict:
        """إنشاء إحصائيات الصور"""
        stats = {
            'total': len(mod_images),
            'forgecdn_images': 0,
            'mcpedl_images': 0,
            'other_images': 0
        }
        
        for img in mod_images:
            if 'forgecdn.net' in img:
                stats['forgecdn_images'] += 1
            elif 'mcpedl.com' in img:
                stats['mcpedl_images'] += 1
            else:
                stats['other_images'] += 1
        
        return stats
    
    def _get_extraction_method(self) -> str:
        """الحصول على طريقة الاستخراج المستخدمة"""
        if self.driver:
            return "selenium"
        elif CLOUDSCRAPER_AVAILABLE and hasattr(self.session, 'get'):
            return "cloudscraper"
        else:
            return "requests"
    
    def _create_error_result(self, url: str, error: str) -> Dict:
        """إنشاء نتيجة خطأ"""
        return {
            'error': error,
            'mod_name': '',
            'main_mod_images': [],
            'source_url': url,
            'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'success': False
        }
    
    def close(self):
        """إغلاق الموارد"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            print("🔒 تم إغلاق متصفح Selenium")
        
        if self.session:
            self.session.close()
            self.session = None
            print("🔒 تم إغلاق جلسة HTTP")


# =================== دوال مساعدة للاستخدام السريع ===================

def extract_mcpedl_images(url: str, use_selenium: bool = True, headless: bool = True) -> Dict[str, Any]:
    """
    دالة سريعة لاستخراج صور المود
    
    Args:
        url: رابط صفحة المود
        use_selenium: استخدام Selenium للدقة (موصى به)
        headless: تشغيل المتصفح في الخلفية
        
    Returns:
        dict: نتيجة الاستخراج مع الصور والمعلومات
    """
    extractor = MCPEDLImageExtractorV2(prefer_selenium=use_selenium, headless=headless)
    
    try:
        result = extractor.extract_mod_images(url)
        return result
    finally:
        extractor.close()


def get_mod_images_only(url: str) -> List[str]:
    """
    دالة مبسطة للحصول على الصورة الرئيسية للمود فقط
    
    Args:
        url: رابط صفحة المود
        
    Returns:
        list: قائمة تحتوي على رابط الصورة الرئيسية فقط
    """
    result = extract_mcpedl_images(url)
    
    if result.get('success', False):
        images = result.get('main_mod_images', [])
        # فقط إرجاع الصورة الأولى (الرئيسية) إذا وجدت
        if images and len(images) > 0:
            print(f"✅ تم استخراج الصورة الرئيسية للمود فقط: {images[0]}")
            return [images[0]]
        else:
            print("⚠️ لم يتم العثور على أي صورة رئيسية")
            return []
    else:
        print(f"خطأ: {result.get('error', 'فشل غير معروف')}")
        return []


# =================== كود الاختبار ===================

if __name__ == "__main__":
    print("🚀 تشغيل اختبار مستخرج الصور المحسن...")
    
    # فحص التبعيات
    print("\n🔍 فحص التبعيات:")
    print(f"   Selenium: {'✅ متاح' if SELENIUM_AVAILABLE else '❌ غير متاح'}")
    print(f"   Cloudscraper: {'✅ متاح' if CLOUDSCRAPER_AVAILABLE else '❌ غير متاح'}")
    print(f"   Requests: {'✅ متاح' if REQUESTS_AVAILABLE else '❌ غير متاح'}")
    
    if not any([SELENIUM_AVAILABLE, CLOUDSCRAPER_AVAILABLE, REQUESTS_AVAILABLE]):
        print("❌ لا توجد أدوات HTTP متاحة! يرجى تثبيت إحدى المكتبات المطلوبة.")
        exit(1)
    
    # اختبار سريع
    test_url = "https://mcpedl.com/glow-em-all-shader/"
    print(f"\n🧪 اختبار سريع على: {test_url}")
    
    try:
        images = get_mod_images_only(test_url)
        print(f"✅ تم استخراج {len(images)} صورة")
        
        if images:
            print("🖼️ أول 3 صور:")
            for i, img in enumerate(images[:3], 1):
                print(f"   {i}. {img}")
    
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
