#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لميزة استخراج MCPEDL
"""

def quick_test():
    """اختبار سريع للميزة الجديدة"""
    print("🚀 اختبار سريع لميزة استخراج MCPEDL")
    print("=" * 50)
    
    try:
        # اختبار الاستيراد
        print("1. اختبار الاستيراد...")
        from mcpedl_scraper_module import MCPEDLScraper, scrape_mcpedl_mod
        print("✅ تم استيراد الوحدة بنجاح")
        
        # اختبار إنشاء المستخرج
        print("\n2. اختبار إنشاء المستخرج...")
        scraper = MCPEDLScraper()
        print("✅ تم إنشاء المستخرج بنجاح")
        
        # اختبار التحقق من الروابط
        print("\n3. اختبار التحقق من الروابط...")
        test_urls = [
            "https://mcpedl.com/dragon-mounts-v1-3-25/",
            "https://google.com",
            ""
        ]
        
        for url in test_urls:
            is_valid = scraper.is_valid_mcpedl_url(url)
            status = "✅" if (is_valid and "mcpedl" in url) or (not is_valid and "mcpedl" not in url) else "❌"
            print(f"{status} {url}: {'صحيح' if is_valid else 'خاطئ'}")
        
        # اختبار دوال التنظيف
        print("\n4. اختبار دوال التنظيف...")
        test_text = "  نص مع مسافات زائدة  "
        cleaned = scraper.clean_text(test_text)
        print(f"✅ تنظيف النص: '{test_text}' → '{cleaned}'")
        
        # اختبار استخراج الإصدارات
        print("\n5. اختبار استخراج الإصدارات...")
        version_text = "For Minecraft 1.20.1+"
        versions = scraper.extract_version_numbers(version_text)
        print(f"✅ استخراج الإصدارات: '{version_text}' → {versions}")
        
        # إغلاق المستخرج
        scraper.close()
        print("\n✅ تم إغلاق المستخرج")
        
        print("\n" + "=" * 50)
        print("🎉 جميع الاختبارات نجحت! الميزة جاهزة للاستخدام")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملف mcpedl_scraper_module.py")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_integration():
    """اختبار التكامل مع الأداة الرئيسية"""
    print("\n🔗 اختبار التكامل مع الأداة الرئيسية")
    print("=" * 50)
    
    try:
        # محاولة استيراد الدوال من الأداة الرئيسية
        import sys
        import os
        
        # التحقق من وجود الملف الرئيسي
        if os.path.exists('mod_processor.py'):
            print("✅ ملف mod_processor.py موجود")
            
            # قراءة الملف للتحقق من وجود الدوال الجديدة
            with open('mod_processor.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_functions = [
                'handle_mcpedl_extraction',
                'mcpedl_extraction_task',
                'populate_fields_from_mcpedl_data',
                'MCPEDL_SCRAPER_AVAILABLE'
            ]
            
            missing_functions = []
            for func in required_functions:
                if func not in content:
                    missing_functions.append(func)
            
            if missing_functions:
                print(f"❌ دوال مفقودة: {missing_functions}")
                return False
            else:
                print("✅ جميع الدوال المطلوبة موجودة")
            
            # التحقق من وجود واجهة MCPEDL
            if 'استخراج من MCPEDL' in content:
                print("✅ واجهة MCPEDL موجودة")
            else:
                print("❌ واجهة MCPEDL مفقودة")
                return False
            
            print("✅ التكامل مع الأداة الرئيسية ناجح")
            return True
            
        else:
            print("❌ ملف mod_processor.py غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل سريع لميزة استخراج MCPEDL")
    print("=" * 60)
    
    # اختبار الوحدة الأساسية
    if not quick_test():
        print("\n❌ فشل الاختبار الأساسي")
        return False
    
    # اختبار التكامل
    if not test_integration():
        print("\n❌ فشل اختبار التكامل")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 جميع الاختبارات نجحت!")
    print("💡 يمكنك الآن تشغيل الأداة الرئيسية:")
    print("   python mod_processor.py")
    print("\n📖 للمزيد من المعلومات، راجع:")
    print("   - MCPEDL_FEATURE_README.md")
    print("   - MCPEDL_INSTALLATION.md")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 نصائح لحل المشاكل:")
        print("1. تأكد من وجود جميع الملفات المطلوبة")
        print("2. تأكد من تثبيت المكتبات: pip install requests beautifulsoup4 lxml")
        print("3. راجع ملف MCPEDL_INSTALLATION.md للتفاصيل")
        input("\nاضغط Enter للخروج...")
    else:
        input("\nاضغط Enter للخروج...")
