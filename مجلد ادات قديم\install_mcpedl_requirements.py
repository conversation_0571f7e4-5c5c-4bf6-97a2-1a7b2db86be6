#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت المكتبات المطلوبة لميزة استخراج MCPEDL
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"تثبيت {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package}: {e}")
        return False

def main():
    """تثبيت جميع المكتبات المطلوبة"""
    print("🚀 بدء تثبيت المكتبات المطلوبة لميزة استخراج MCPEDL")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.2", 
        "lxml>=4.9.3",
        "cloudscraper>=1.2.71"
    ]
    
    # المكتبات الاختيارية
    optional_packages = [
        "python-dotenv>=1.0.0",
        "Pillow>=10.0.0"
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    # تثبيت المكتبات المطلوبة
    print("📦 تثبيت المكتبات المطلوبة:")
    for package in required_packages:
        if install_package(package):
            success_count += 1
        print()
    
    # تثبيت المكتبات الاختيارية
    print("📦 تثبيت المكتبات الاختيارية:")
    for package in optional_packages:
        install_package(package)
        print()
    
    print("=" * 60)
    
    if success_count == total_count:
        print("🎉 تم تثبيت جميع المكتبات المطلوبة بنجاح!")
        print("✅ ميزة استخراج MCPEDL جاهزة للاستخدام")
        
        # اختبار الاستيراد
        print("\n🧪 اختبار الاستيراد...")
        try:
            import requests
            import bs4
            import cloudscraper
            print("✅ جميع المكتبات تعمل بشكل صحيح")
            
            # اختبار cloudscraper
            scraper = cloudscraper.create_scraper()
            print("✅ cloudscraper يعمل بشكل صحيح")
            
        except ImportError as e:
            print(f"❌ خطأ في الاستيراد: {e}")
            return False
            
    else:
        print(f"⚠️ تم تثبيت {success_count} من أصل {total_count} مكتبات مطلوبة")
        print("❌ قد لا تعمل ميزة استخراج MCPEDL بشكل صحيح")
        return False
    
    print("\n💡 الخطوات التالية:")
    print("1. شغل الأداة الرئيسية: python mod_processor.py")
    print("2. ابحث عن قسم 'استخراج من MCPEDL'")
    print("3. الصق رابط مود من mcpedl.com")
    print("4. اضغط 'استخراج البيانات من MCPEDL'")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 التثبيت مكتمل!")
        else:
            print("\n❌ فشل في التثبيت")
            
        input("\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التثبيت")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
