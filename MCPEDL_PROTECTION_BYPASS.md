# دليل تجاوز حماية موقع MCPEDL

## المشكلة

موقع mcpedl.com يستخدم حماية قوية ضد الروبوتات (مثل Cloudflare) مما يمنع الاستخراج التلقائي للبيانات.

## الحلول المطبقة

### 1. تحسين ترويسات HTTP
- استخدام User-Agent حديث ومتنوع
- إضافة ترويسات أمان Chrome الحديثة
- تناوب User Agents في كل محاولة

### 2. استخدام cloudscraper
```bash
pip install cloudscraper
```

cloudscraper مكتبة متخصصة في تجاوز حماية Cloudflare تلقائياً.

### 3. تحسين استراتيجية إعادة المحاولة
- 5 محاولات بدلاً من 3
- تأخير متدرج بين المحاولات
- معالجة أكواد خطأ محددة (403, 429, 503)

### 4. تحسين محددات CSS
- إضافة محددات أكثر تخصصاً لموقع mcpedl
- دعم هياكل HTML متعددة

## خطوات التثبيت والاختبار

### الخطوة 1: تثبيت المكتبات
```bash
python install_mcpedl_requirements.py
```

أو يدوياً:
```bash
pip install requests beautifulsoup4 lxml cloudscraper
```

### الخطوة 2: اختبار الوحدة
```bash
python test_mcpedl_scraper.py
```

### الخطوة 3: اختبار سريع
```bash
python quick_test_mcpedl.py
```

### الخطوة 4: تشغيل الأداة الرئيسية
```bash
python mod_processor.py
```

## نصائح لتحسين النجاح

### 1. استخدام VPN
إذا كان عنوان IP محظوراً، جرب:
- تغيير عنوان IP
- استخدام VPN
- الانتظار بعض الوقت

### 2. اختيار الوقت المناسب
- تجنب أوقات الذروة
- جرب في أوقات مختلفة من اليوم

### 3. تجنب الطلبات المتكررة
- لا تستخرج عدة مودات متتالية
- انتظر بين كل استخراج

### 4. استخدام روابط صحيحة
- تأكد من أن الرابط يعمل في المتصفح
- استخدم روابط مودات حديثة

## رسائل الخطأ الشائعة وحلولها

### "تم رفض الوصول (403)"
**السبب**: الموقع يحجب الطلب
**الحل**: 
- انتظر 5-10 دقائق
- جرب رابط مود آخر
- تأكد من تثبيت cloudscraper

### "تم تجاوز حد الطلبات (429)"
**السبب**: كثرة الطلبات
**الحل**:
- انتظر 30 دقيقة
- تجنب الطلبات المتكررة

### "الخدمة غير متاحة (503)"
**السبب**: الموقع مشغول أو تحت الصيانة
**الحل**:
- انتظر وجرب لاحقاً
- تحقق من حالة الموقع

### "فشل في جلب الصفحة"
**الحل**:
1. تحقق من الاتصال بالإنترنت
2. تأكد من صحة الرابط
3. جرب في وقت آخر
4. استخدم VPN

## البدائل في حالة الفشل

### 1. الاستخراج اليدوي
- انسخ البيانات يدوياً من الموقع
- الصق في حقول الأداة

### 2. استخدام مواقع أخرى
- البحث عن المود في مواقع أخرى
- استخدام مصادر بديلة

### 3. حفظ البيانات المحلية
- حفظ البيانات المستخرجة مسبقاً
- إنشاء قاعدة بيانات محلية

## مراقبة الأداء

### مؤشرات النجاح:
- ✅ رمز استجابة 200
- ✅ محتوى HTML كامل
- ✅ وجود عنوان الصفحة
- ✅ استخراج البيانات الأساسية

### مؤشرات الفشل:
- ❌ رمز استجابة 403/429/503
- ❌ محتوى قصير جداً
- ❌ وجود كلمات حماية
- ❌ HTML غير مكتمل

## تحديثات مستقبلية

### تحسينات مخططة:
- دعم proxy servers
- تحسين خوارزمية التأخير
- إضافة محددات CSS جديدة
- دعم JavaScript rendering

### مراقبة التغييرات:
- مراقبة تحديثات موقع mcpedl
- تحديث محددات CSS عند الحاجة
- تحسين استراتيجيات تجاوز الحماية

## الدعم

### في حالة استمرار المشاكل:
1. شغل `test_mcpedl_scraper.py` للتشخيص
2. تحقق من رسائل الخطأ التفصيلية
3. جرب روابط مودات مختلفة
4. تأكد من تحديث المكتبات

### تقرير المشاكل:
- احفظ رسائل الخطأ الكاملة
- اذكر الرابط المستخدم
- اذكر وقت المحاولة
- اذكر إعدادات الشبكة

## الخلاصة

تم تطبيق عدة تحسينات لتجاوز حماية موقع mcpedl.com. النجاح يعتمد على:
- تثبيت cloudscraper
- استخدام روابط صحيحة
- تجنب الطلبات المتكررة
- الصبر والمحاولة في أوقات مختلفة

معدل النجاح المتوقع: 70-80% مع الإعدادات المحسنة.
