# -*- coding: utf-8 -*-
"""
إصلاح المشاكل المتبقية في الأداة
"""

import os
import sys
import json

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_gemini_api_keys():
    """إصلاح مفاتيح Gemini API"""
    try:
        config_file = "api_keys.json"
        
        # إنشاء ملف مفاتيح API إذا لم يكن موجوداً
        if not os.path.exists(config_file):
            default_config = {
                "GEMINI_API_KEY": "",
                "gemini_api_keys": [
                    "AIzaSyD7E2B9iYXV9_2houLLHDWXUA-K53kUGq0",
                    "AIzaSyBY58cZFvFzRQpzzYJ7m1VjwvS7af-vHhM",
                    "AIzaSyBSbEdSARy5ims96kxF1om2725VZxwl6nU"
                ],
                "note": "أضف مفاتيح Gemini API الخاصة بك هنا"
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            print(f"[OK] تم إنشاء ملف مفاتيح API: {config_file}")
            return True
        else:
            print(f"[OK] ملف مفاتيح API موجود: {config_file}")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح مفاتيح Gemini API: {e}")
        return False

def fix_firebase_config():
    """إصلاح إعدادات Firebase"""
    try:
        firebase_config_file = "firebase_config.json"
        
        # التحقق من وجود ملف إعدادات Firebase
        if not os.path.exists(firebase_config_file):
            default_firebase_config = {
                "service_account_path": "firebase-service-account.json",
                "storage_bucket_name": "download-e33a2.firebasestorage.app",
                "initialized": False,
                "note": "تأكد من وجود ملف firebase-service-account.json"
            }
            
            with open(firebase_config_file, 'w', encoding='utf-8') as f:
                json.dump(default_firebase_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ تم إنشاء ملف إعدادات Firebase: {firebase_config_file}")
        
        # التحقق من وجود ملف service account
        service_account_file = "firebase-service-account.json"
        if not os.path.exists(service_account_file):
            # إنشاء ملف template
            template_file = "firebase-service-account-template.json"
            if os.path.exists(template_file):
                print(f"⚠️ استخدم {template_file} كمرجع لإنشاء {service_account_file}")
            else:
                template_content = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_content, f, indent=2, ensure_ascii=False)
                
                print(f"✅ تم إنشاء ملف template: {template_file}")
                print(f"⚠️ يرجى تحديث {template_file} بمعلومات مشروعك الحقيقية وإعادة تسميته إلى {service_account_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح إعدادات Firebase: {e}")
        return False

def fix_database_config():
    """إصلاح إعدادات قاعدة البيانات"""
    try:
        db_config_file = "database_config.json"
        
        if not os.path.exists(db_config_file):
            default_db_config = {
                "database_provider": "firebase",
                "firebase_web_api_enabled": True,
                "note": "استخدام Firebase كقاعدة بيانات أساسية"
            }
            
            with open(db_config_file, 'w', encoding='utf-8') as f:
                json.dump(default_db_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ تم إنشاء ملف إعدادات قاعدة البيانات: {db_config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح إعدادات قاعدة البيانات: {e}")
        return False

def fix_enhanced_integration_config():
    """إصلاح إعدادات التكامل المحسن"""
    try:
        config_file = "enhanced_integration_config.json"
        
        if not os.path.exists(config_file):
            default_config = {
                "enabled": True,
                "components": {},
                "settings": {
                    "auto_enhance_images": True,
                    "auto_extract_social": False,  # معطل كما طلب المستخدم
                    "auto_backup": True,
                    "image_processing_enabled": True,
                    "firebase_upload_enabled": True
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ تم إنشاء ملف إعدادات التكامل المحسن: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح إعدادات التكامل المحسن: {e}")
        return False

def create_requirements_file():
    """إنشاء ملف المتطلبات"""
    try:
        requirements_content = """# متطلبات الأداة
requests>=2.28.0
beautifulsoup4>=4.11.0
Pillow>=9.0.0
google-generativeai>=0.3.0
google-cloud-storage>=2.10.0
google-cloud-firestore>=2.11.0
firebase-admin>=6.2.0
pyperclip>=1.8.2
"""
        
        with open("requirements_fixed.txt", 'w', encoding='utf-8') as f:
            f.write(requirements_content)
        
        print("✅ تم إنشاء ملف المتطلبات: requirements_fixed.txt")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف المتطلبات: {e}")
        return False

def create_installation_guide():
    """إنشاء دليل التثبيت"""
    try:
        guide_content = """# دليل التثبيت والإعداد

## 1. تثبيت المتطلبات
```bash
pip install -r requirements_fixed.txt
```

## 2. إعداد Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. فعّل Firebase Storage و Firestore
4. أنشئ Service Account وحمّل ملف JSON
5. ضع الملف في المجلد وأعد تسميته إلى `firebase-service-account.json`

## 3. إعداد Gemini API
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. أضف المفتاح إلى ملف `api_keys.json`

## 4. تشغيل الأداة
```bash
python mod_processor_broken_final.py
```

## 5. اختبار الإصلاحات
```bash
python test_all_fixes.py
```

## المشاكل التي تم حلها:
- ✅ ERR_MODULE_IMPORT_FAILED
- ✅ ERR_JSON_PARSE_FAILED  
- ✅ ERR_GEMINI_API_UNAVAILABLE
- ✅ ERR_DESC_GENERATOR_DISABLED
- ✅ ERR_DUPLICATE_IMAGES
- ❌ ERR_SOCIAL_EXTRACTION_DISABLED (معطل كما هو مطلوب)
- ✅ ERR_YOUTUBE_VIDEOS_NOT_FOUND
- ✅ معالجة الصور ورفعها على Firebase
"""
        
        with open("INSTALLATION_GUIDE_FIXED.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print("✅ تم إنشاء دليل التثبيت: INSTALLATION_GUIDE_FIXED.md")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دليل التثبيت: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("=" * 60)
    print("إصلاح المشاكل المتبقية في الأداة")
    print("=" * 60)
    
    fixes = [
        ("مفاتيح Gemini API", fix_gemini_api_keys),
        ("إعدادات Firebase", fix_firebase_config),
        ("إعدادات قاعدة البيانات", fix_database_config),
        ("إعدادات التكامل المحسن", fix_enhanced_integration_config),
        ("ملف المتطلبات", create_requirements_file),
        ("دليل التثبيت", create_installation_guide),
    ]
    
    results = {}
    
    for fix_name, fix_func in fixes:
        print(f"\n{'='*20} {fix_name} {'='*20}")
        try:
            results[fix_name] = fix_func()
        except Exception as e:
            print(f"❌ خطأ في إصلاح {fix_name}: {e}")
            results[fix_name] = False
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الإصلاح:")
    print("=" * 60)
    
    total_fixes = len(results)
    successful_fixes = sum(results.values())
    
    for fix_name, status in results.items():
        status_text = "✅ نجح" if status else "❌ فشل"
        print(f"{fix_name}: {status_text}")
    
    print(f"\n📈 الإحصائيات:")
    print(f"إجمالي الإصلاحات: {total_fixes}")
    print(f"الإصلاحات الناجحة: {successful_fixes}")
    print(f"الإصلاحات الفاشلة: {total_fixes - successful_fixes}")
    print(f"معدل النجاح: {(successful_fixes/total_fixes)*100:.1f}%")
    
    if successful_fixes == total_fixes:
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. راجع ملف INSTALLATION_GUIDE_FIXED.md")
        print("2. قم بتشغيل test_all_fixes.py للتحقق من الإصلاحات")
        print("3. ابدأ استخدام الأداة")
    else:
        print("\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    return successful_fixes == total_fixes

if __name__ == "__main__":
    main()
