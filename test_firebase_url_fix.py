# -*- coding: utf-8 -*-
"""
اختبار إصلاح روابط Firebase
Test Firebase URL fix
"""

import os
import sys
import requests
import zipfile
import io
import json
from firebase_config import firebase_manager

def create_test_mod():
    """إنشاء ملف مود تجريبي"""
    print("🔧 إنشاء ملف مود تجريبي...")
    
    zip_buffer = io.BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # إضافة ملف manifest.json
        manifest = {
            "format_version": 2,
            "header": {
                "description": "Test Mod for URL Fix",
                "name": "URL Fix Test Mod",
                "uuid": "12345678-1234-1234-1234-123456789012",
                "version": [1, 0, 0],
                "min_engine_version": [1, 16, 0]
            },
            "modules": [
                {
                    "description": "Test Module",
                    "type": "resources",
                    "uuid": "*************-4321-4321-************",
                    "version": [1, 0, 0]
                }
            ]
        }
        
        zip_file.writestr("manifest.json", json.dumps(manifest, indent=2))
        zip_file.writestr("pack_icon.png", b"PNG fake content for testing")
        zip_file.writestr("README.txt", "This is a test mod file for URL fix testing.")
    
    zip_buffer.seek(0)
    content = zip_buffer.getvalue()
    
    print(f"✅ تم إنشاء ملف مود تجريبي - الحجم: {len(content)} بايت")
    return content, "url_fix_mod.mcpack"

def test_firebase_url():
    """اختبار رفع ملف والتأكد من صحة الرابط"""
    print("🚀 بدء اختبار إصلاح روابط Firebase...")
    print("=" * 60)
    
    # تهيئة Firebase
    print("🔄 تهيئة Firebase...")
    if not firebase_manager.auto_initialize():
        print("❌ فشل في تهيئة Firebase")
        return False
    
    print("✅ تم تهيئة Firebase بنجاح")
    
    # إنشاء ملف تجريبي
    mod_content, mod_filename = create_test_mod()
    
    # رفع الملف
    print(f"\n📤 رفع الملف: {mod_filename}")
    try:
        public_url = firebase_manager.upload_mod_to_storage(mod_content, mod_filename)
        
        if not public_url:
            print("❌ فشل في رفع الملف")
            return False
        
        print(f"✅ تم رفع الملف بنجاح")
        print(f"🔗 الرابط المُعاد: {public_url}")
        
        # فحص تنسيق الرابط
        print(f"\n🔍 فحص تنسيق الرابط...")
        
        if public_url.startswith('https://storage.googleapis.com/'):
            print("✅ الرابط بتنسيق Google Cloud Storage الصحيح")
            
            # تحويل إلى تنسيق Firebase API
            parts = public_url.replace('https://storage.googleapis.com/', '').split('/', 1)
            if len(parts) == 2:
                bucket_name = parts[0]
                file_path = parts[1]
                
                from urllib.parse import quote
                encoded_path = quote(file_path, safe='')
                firebase_api_url = f"https://firebasestorage.googleapis.com/v0/b/{bucket_name}/o/{encoded_path}?alt=media"
                
                print(f"🔄 رابط Firebase API: {firebase_api_url}")
                
                # اختبار الوصول للرابط
                print(f"\n🌐 اختبار الوصول للرابط...")
                try:
                    response = requests.head(firebase_api_url, timeout=10)
                    if response.status_code == 200:
                        print("✅ الرابط يعمل بشكل صحيح!")
                        print(f"📊 حجم الملف: {response.headers.get('content-length', 'غير معروف')} بايت")
                        print(f"📄 نوع المحتوى: {response.headers.get('content-type', 'غير معروف')}")
                        
                        # اختبار تحميل جزء من الملف
                        print(f"\n📥 اختبار تحميل جزء من الملف...")
                        download_response = requests.get(firebase_api_url, timeout=10, stream=True)
                        if download_response.status_code == 200:
                            downloaded_size = 0
                            for chunk in download_response.iter_content(chunk_size=1024):
                                downloaded_size += len(chunk)
                                if downloaded_size >= 1024:  # تحميل 1KB فقط للاختبار
                                    break
                            
                            print(f"✅ تم تحميل {downloaded_size} بايت بنجاح")
                            return True
                        else:
                            print(f"❌ فشل في تحميل الملف - كود الاستجابة: {download_response.status_code}")
                            return False
                    else:
                        print(f"❌ الرابط لا يعمل - كود الاستجابة: {response.status_code}")
                        print(f"📄 رسالة الخطأ: {response.text}")
                        return False
                        
                except Exception as e:
                    print(f"❌ خطأ في اختبار الرابط: {e}")
                    return False
            else:
                print("❌ تنسيق الرابط غير صحيح")
                return False
                
        elif public_url.startswith('https://firebasestorage.googleapis.com/'):
            print("✅ الرابط بتنسيق Firebase API")
            
            # اختبار الرابط مباشرة
            print(f"\n🌐 اختبار الوصول للرابط...")
            try:
                response = requests.head(public_url, timeout=10)
                if response.status_code == 200:
                    print("✅ الرابط يعمل بشكل صحيح!")
                    return True
                else:
                    print(f"❌ الرابط لا يعمل - كود الاستجابة: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ خطأ في اختبار الرابط: {e}")
                return False
        else:
            print(f"❌ تنسيق رابط غير معروف: {public_url}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في رفع الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    success = test_firebase_url()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 نجح اختبار إصلاح روابط Firebase!")
        print("✅ الروابط تعمل بشكل صحيح")
    else:
        print("\n" + "=" * 60)
        print("❌ فشل اختبار إصلاح روابط Firebase")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
