# -*- coding: utf-8 -*-
"""
Firebase Storage Web API - نسخة مبسطة تعمل بدون Admin SDK
Firebase Storage Web API - Simplified version without Admin SDK
"""

import os
import json
import time
import requests
import base64
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse

class FirebaseWebStorage:
    """مدير Firebase Storage باستخدام Web API"""
    
    def __init__(self, config_path: str = "firebase_config.json"):
        """تهيئة Firebase Web Storage"""
        self.config = self._load_config(config_path)
        self.is_initialized = False
        
        if self.config:
            self._initialize()

    def _load_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """تحميل إعدادات Firebase"""
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ تم تحميل إعدادات Firebase من {config_path}")
                return config
            else:
                print(f"⚠️ ملف الإعدادات غير موجود: {config_path}")
                return None
        except Exception as e:
            print(f"❌ خطأ في تحميل إعدادات Firebase: {e}")
            return None

    def _initialize(self):
        """تهيئة Firebase Web Storage"""
        try:
            required_fields = ['project_id', 'storage_bucket', 'apiKey']
            missing_fields = [field for field in required_fields if not self.config.get(field)]
            
            if missing_fields:
                print(f"❌ حقول مفقودة في الإعدادات: {missing_fields}")
                return
            
            self.project_id = self.config['project_id']
            self.storage_bucket = self.config['storage_bucket']
            self.api_key = self.config['apiKey']
            
            # روابط API
            self.storage_api_base = f"https://firebasestorage.googleapis.com/v0/b/{self.storage_bucket}/o"
            self.upload_api_base = f"https://firebasestorage.googleapis.com/v0/b/{self.storage_bucket}/o"
            
            self.is_initialized = True
            print(f"✅ تم تهيئة Firebase Web Storage: {self.storage_bucket}")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة Firebase Web Storage: {e}")
            self.is_initialized = False

    def check_connection(self) -> Dict[str, Any]:
        """فحص الاتصال مع Firebase Storage"""
        status = {
            'connected': False,
            'bucket_accessible': False,
            'can_upload': False,
            'can_download': False,
            'error': None,
            'bucket_name': self.storage_bucket if self.is_initialized else None
        }
        
        if not self.is_initialized:
            status['error'] = "Firebase غير مهيأ"
            return status
        
        try:
            # اختبار الوصول للـ bucket
            test_url = f"{self.storage_api_base}?alt=media"
            response = requests.get(test_url, timeout=10)
            
            if response.status_code in [200, 404]:  # 404 طبيعي إذا لم توجد ملفات
                status['connected'] = True
                status['bucket_accessible'] = True
                
                # اختبار رفع ملف صغير
                test_upload = self._test_upload()
                status['can_upload'] = test_upload['success']
                status['can_download'] = test_upload['success']
                
                if test_upload['success']:
                    # حذف الملف التجريبي
                    self._delete_file(test_upload['file_path'])
            else:
                status['error'] = f"HTTP {response.status_code}: {response.text}"
                
        except Exception as e:
            status['error'] = f"خطأ في الاتصال: {e}"
        
        return status

    def _test_upload(self) -> Dict[str, Any]:
        """اختبار رفع ملف صغير"""
        try:
            test_content = f"Firebase test - {int(time.time())}"
            test_file_path = f"test/connection_test_{int(time.time())}.txt"
            
            # رفع الملف
            upload_url = f"{self.upload_api_base}/{test_file_path.replace('/', '%2F')}?uploadType=media"
            
            headers = {
                'Content-Type': 'text/plain',
                'Authorization': f'Bearer {self.api_key}' if self.api_key else ''
            }
            
            response = requests.post(upload_url, data=test_content.encode(), headers=headers, timeout=30)
            
            if response.status_code in [200, 201]:
                return {
                    'success': True,
                    'file_path': test_file_path,
                    'response': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"خطأ في اختبار الرفع: {e}"
            }

    def upload_mod_file(self, file_url: str, mod_name: str, file_type: str = "addon") -> Optional[Dict[str, Any]]:
        """رفع ملف المود إلى Firebase Storage"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
        
        try:
            print(f"📤 بدء رفع ملف المود: {mod_name}")
            
            # تحميل الملف من الرابط
            print(f"⬇️ تحميل الملف من: {file_url}")
            response = requests.get(file_url, timeout=60, stream=True)
            response.raise_for_status()
            
            # معلومات الملف
            content_type = response.headers.get('content-type', 'application/octet-stream')
            file_size = int(response.headers.get('content-length', 0))
            
            print(f"📊 حجم الملف: {file_size / (1024*1024):.2f} MB")
            
            # إنشاء مسار الملف
            file_extension = self._get_file_extension(file_url, content_type)
            timestamp = int(time.time())
            safe_mod_name = self._sanitize_filename(mod_name)
            file_path = f"mods/{file_type}/{safe_mod_name}_{timestamp}{file_extension}"
            
            # رفع الملف
            upload_url = f"{self.upload_api_base}/{file_path.replace('/', '%2F')}?uploadType=media"
            
            headers = {
                'Content-Type': content_type,
            }
            
            print("⬆️ رفع الملف إلى Firebase Storage...")
            upload_response = requests.post(upload_url, data=response.content, headers=headers, timeout=120)
            
            if upload_response.status_code in [200, 201]:
                upload_data = upload_response.json()
                
                # إنشاء رابط التحميل العام
                download_url = f"{self.storage_api_base}/{file_path.replace('/', '%2F')}?alt=media"
                
                upload_info = {
                    'firebase_url': download_url,
                    'file_path': file_path,
                    'file_size': file_size,
                    'content_type': content_type,
                    'upload_time': timestamp,
                    'mod_name': mod_name,
                    'file_type': file_type,
                    'original_url': file_url,
                    'firebase_data': upload_data
                }
                
                print(f"✅ تم رفع الملف بنجاح!")
                print(f"🔗 رابط التحميل: {download_url}")
                
                return upload_info
            else:
                print(f"❌ فشل رفع الملف: HTTP {upload_response.status_code}")
                print(f"   الاستجابة: {upload_response.text}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في رفع الملف: {e}")
            return None

    def _get_file_extension(self, url: str, content_type: str) -> str:
        """استخراج امتداد الملف"""
        # محاولة استخراج من الرابط
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        if '.' in path:
            extension = os.path.splitext(path)[1]
            if extension:
                return extension
        
        # استخراج من نوع المحتوى
        content_type_map = {
            'application/zip': '.zip',
            'application/x-zip-compressed': '.zip',
            'application/octet-stream': '.mcpack',
            'application/vnd.android.package-archive': '.apk'
        }
        
        return content_type_map.get(content_type, '.mcpack')

    def _sanitize_filename(self, filename: str) -> str:
        """تنظيف اسم الملف"""
        import re
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '-', sanitized)
        return sanitized.strip('-')[:50]
        
    def upload_mod_to_storage(self, file_content: bytes, file_name: str) -> Optional[str]:
        """رفع ملف مود إلى Firebase Storage وإرجاع رابط التحميل"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
            
        try:
            print(f"📤 بدء رفع ملف المود: {file_name}")
            
            # تحديد نوع المحتوى
            content_type = 'application/octet-stream'
            file_size = len(file_content)
            
            print(f"📊 حجم الملف: {file_size / (1024*1024):.2f} MB")
            
            # إنشاء مسار الملف
            file_extension = os.path.splitext(file_name)[1] or '.mcpack'
            timestamp = int(time.time())
            safe_mod_name = self._sanitize_filename(os.path.splitext(file_name)[0])
            file_path = f"mods/addon/{safe_mod_name}_{timestamp}{file_extension}"
            
            # رفع الملف
            upload_url = f"{self.upload_api_base}/{file_path.replace('/', '%2F')}?uploadType=media"
            
            headers = {
                'Content-Type': content_type,
            }
            
            print("⬆️ رفع الملف إلى Firebase Storage...")
            upload_response = requests.post(upload_url, data=file_content, headers=headers, timeout=120)
            
            if upload_response.status_code in [200, 201]:
                # إنشاء رابط التحميل العام
                download_url = f"{self.storage_api_base}/{file_path.replace('/', '%2F')}?alt=media"
                
                print(f"✅ تم رفع الملف بنجاح!")
                print(f"🔗 رابط التحميل: {download_url}")
                
                return download_url
            else:
                print(f"❌ فشل رفع الملف: HTTP {upload_response.status_code}")
                print(f"   الاستجابة: {upload_response.text}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في رفع الملف: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    def upload_image_to_storage(self, file_content: bytes, file_name: str) -> Optional[str]:
        """رفع صورة إلى Firebase Storage وإرجاع رابط التحميل"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
            
        try:
            print(f"📤 بدء رفع الصورة: {file_name}")
            
            # تحديد نوع المحتوى
            content_type = 'image/jpeg'  # افتراضي
            if file_name.lower().endswith('.png'):
                content_type = 'image/png'
            elif file_name.lower().endswith('.gif'):
                content_type = 'image/gif'
                
            file_size = len(file_content)
            
            print(f"📊 حجم الصورة: {file_size / (1024*1024):.2f} MB")
            
            # إنشاء مسار الملف
            file_extension = os.path.splitext(file_name)[1] or '.jpg'
            timestamp = int(time.time())
            safe_image_name = self._sanitize_filename(os.path.splitext(file_name)[0])
            file_path = f"images/{safe_image_name}_{timestamp}{file_extension}"
            
            # رفع الملف
            upload_url = f"{self.upload_api_base}/{file_path.replace('/', '%2F')}?uploadType=media"
            
            headers = {
                'Content-Type': content_type,
            }
            
            print("⬆️ رفع الصورة إلى Firebase Storage...")
            upload_response = requests.post(upload_url, data=file_content, headers=headers, timeout=120)
            
            if upload_response.status_code in [200, 201]:
                # إنشاء رابط التحميل العام
                download_url = f"{self.storage_api_base}/{file_path.replace('/', '%2F')}?alt=media"
                
                print(f"✅ تم رفع الصورة بنجاح!")
                print(f"🔗 رابط التحميل: {download_url}")
                
                return download_url
            else:
                print(f"❌ فشل رفع الصورة: HTTP {upload_response.status_code}")
                print(f"   الاستجابة: {upload_response.text}")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في رفع الصورة: {e}")
            import traceback
            traceback.print_exc()
            return None

    def list_uploaded_mods(self, limit: int = 50) -> List[Dict[str, Any]]:
        """قائمة المودات المرفوعة"""
        if not self.is_initialized:
            return []
        
        try:
            # استخدام Firebase REST API لجلب قائمة الملفات
            list_url = f"{self.storage_api_base}?prefix=mods/&maxResults={limit}"
            response = requests.get(list_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])
                
                mods = []
                for item in items:
                    mod_info = {
                        'name': item.get('name', ''),
                        'size': int(item.get('size', 0)),
                        'created': item.get('timeCreated'),
                        'updated': item.get('updated'),
                        'download_url': f"{self.storage_api_base}/{item.get('name', '').replace('/', '%2F')}?alt=media",
                        'metadata': item.get('metadata', {})
                    }
                    mods.append(mod_info)
                
                return mods
            else:
                print(f"❌ خطأ في جلب قائمة الملفات: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ خطأ في جلب قائمة المودات: {e}")
            return []

    def _delete_file(self, file_path: str) -> bool:
        """حذف ملف"""
        try:
            delete_url = f"{self.storage_api_base}/{file_path.replace('/', '%2F')}"
            response = requests.delete(delete_url, timeout=30)
            
            return response.status_code in [200, 204, 404]  # 404 يعني الملف غير موجود أصلاً
            
        except Exception as e:
            print(f"❌ خطأ في حذف الملف: {e}")
            return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """إحصائيات التخزين"""
        if not self.is_initialized:
            return {}
        
        try:
            mods = self.list_uploaded_mods(1000)  # جلب حتى 1000 ملف للإحصائيات
            
            stats = {
                'total_files': len(mods),
                'total_size': sum(mod.get('size', 0) for mod in mods),
                'mod_files': len([mod for mod in mods if mod['name'].startswith('mods/')]),
                'mod_size': sum(mod.get('size', 0) for mod in mods if mod['name'].startswith('mods/')),
                'bucket_name': self.storage_bucket
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ خطأ في جلب الإحصائيات: {e}")
            return {}


# دالة مساعدة للاستخدام السهل
def create_firebase_web_storage() -> Optional[FirebaseWebStorage]:
    """إنشاء مدير Firebase Web Storage"""
    return FirebaseWebStorage()


# اختبار سريع
if __name__ == "__main__":
    print("🧪 اختبار Firebase Web Storage...")
    
    storage = create_firebase_web_storage()
    
    if storage and storage.is_initialized:
        print("✅ تم تهيئة Firebase Web Storage بنجاح")
        
        # فحص الاتصال
        status = storage.check_connection()
        print(f"📊 حالة الاتصال: {status}")
        
        # إحصائيات التخزين
        stats = storage.get_storage_stats()
        print(f"📈 إحصائيات التخزين: {stats}")
        
    else:
        print("❌ فشل في تهيئة Firebase Web Storage")
