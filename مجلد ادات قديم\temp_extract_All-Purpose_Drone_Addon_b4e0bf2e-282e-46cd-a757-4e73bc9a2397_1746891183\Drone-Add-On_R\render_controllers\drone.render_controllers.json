{"format_version": "1.8.0", "render_controllers": {"controller.render.drone": {"arrays": {"textures": {"Array.states": ["Texture.state_4", "Texture.state_3", "Texture.state_2", "Texture.state_1"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": true}, {"offScreen": "q.variant == 2"}, {"panel": "q.is_chested"}, {"part_1": "q.is_chested"}, {"part_2": "q.is_chested"}, {"part_3": "q.is_chested"}, {"part_4": "q.is_chested"}, {"part_5": "q.is_chested"}, {"part_6": "q.is_chested"}], "materials": [{"*": "Material.default"}], "textures": ["Array.states[math.clamp(query.health / 15, 0, 3)]"]}, "controller.render.drone.eye": {"arrays": {"textures": {"Array.states": ["Texture.state_4", "Texture.state_3", "Texture.state_2", "Texture.state_1"]}}, "geometry": "Geometry.eye", "part_visibility": [{"*": true}, {"blueScreen": "!q.has_target && q.variant != 2"}, {"pupilBlue": "!q.has_target && q.variant != 2"}, {"redScreen": "q.has_target && q.variant != 2"}, {"pupilRed": "q.has_target && q.variant != 2"}], "materials": [{"*": "Material.default"}], "textures": ["Array.states[math.clamp(query.health / 15, 0, 3)]"], "ignore_lighting": true}}}