{"namespace": "drone_inventory", "drone_inventory_panel": {"type": "panel", "controls": [{"container_gamepad_helpers@common.container_gamepad_helpers": {}}, {"selected_item_details_factory@common.selected_item_details_factory": {}}, {"item_lock_notification_factory@common.item_lock_notification_factory": {}}, {"root_panel@common.root_panel": {"layer": 1, "controls": [{"common_panel@common.common_panel": {}}, {"chest_panel": {"type": "panel", "layer": 5, "controls": [{"drone_render": {"type": "image", "size": [54, 54], "layer": 50, "texture": "textures/icons/drone_inventory", "controls": [{"drone_light": {"type": "image", "size": [54, 54], "layer": 50, "texture": "textures/icons/drone_light", "anims": ["@drone_inventory.light_anim_start"]}}], "anims": ["@drone_inventory.offset_anim_start"]}}, {"hopper_label@drone_inventory.drone_chest_panel": {}}, {"inventory_panel_player_label@common.inventory_panel_player_label": {}}, {"hotbar_grid@common.hotbar_grid_template": {}}, {"inventory_take_progress_icon_button@common.inventory_take_progress_icon_button": {}}, {"flying_item_renderer@common.flying_item_renderer": {"layer": 15}}, {"bundle_touch_tooltip@gameplay.bundle_touch_tooltip": {}}, {"bundle_cursor_tooltip@gameplay.bundle_cursor_tooltip": {}}]}}, {"inventory_selected_icon_button@common.inventory_selected_icon_button": {}}, {"gamepad_cursor@common.gamepad_cursor_button": {}}]}}]}, "light_anim_start": {"anim_type": "alpha", "easing": "in_quart", "duration": 0.8, "from": 1, "to": 0, "next": "@drone_inventory.light_anim_start"}, "offset_anim_start": {"anim_type": "offset", "from": [-42, "-65%y"], "to": [-42, "-55%y"], "duration": 1, "next": "@drone_inventory.offset_anim_end"}, "offset_anim_end": {"anim_type": "offset", "from": [-42, "-55%y"], "to": [-42, "-65%y"], "duration": 1, "next": "@drone_inventory.offset_anim_start"}, "drone_chest_panel": {"type": "panel", "size": ["100%", "50%"], "offset": [0, 12], "anchor_to": "top_left", "anchor_from": "top_left", "controls": [{"drone_inventory@drone_inventory.drone_slots": {"offset": [38, 9]}}]}, "drone_slots": {"type": "panel", "size": ["100% + 2px", "100%c + 3px"], "controls": [{"slotsX3": {"type": "stack_panel", "orientation": "vertical", "offset": [73, 0], "collection_name": "container_items", "controls": [{"slot1@drone_slot_item": {"$offset_cell": [0, 0], "collection_index": 1}}, {"slot4@drone_slot_item": {"$offset_cell": [0, 5], "collection_index": 4}}, {"slot3@drone_slot_item": {"$offset_cell": [0, 10], "collection_index": 3}}]}}, {"slotsX2": {"type": "stack_panel", "orientation": "horizontal", "collection_name": "container_items", "offset": [-7, 0], "controls": [{"slot2@drone_slot_item": {"$offset_cell": [32, 23], "collection_index": 2}}, {"slot0@drone_slot_item": {"$offset_cell": [-32, 23], "collection_index": 0}}]}}]}, "drone_slot_item@drone_inventory.drone_container_item": {"$item_collection_name": "container_items"}, "drone_container_item": {"type": "input_panel", "size": [18, 18], "layer": 1, "$offset_cell|default": [0, 0], "$cell_image_size|default": [18, 18], "$cell_overlay_ref|default": "common.cell_overlay", "$button_ref|default": "common.container_slot_button_prototype", "$stack_count_required|default": true, "$durability_bar_required|default": true, "$storage_bar_required|default": true, "$item_renderer|default": "common.item_renderer", "$item_renderer_panel_size|default": [18, 18], "$item_renderer_size|default": [16, 16], "$item_renderer_offset|default": [0, 0], "$background_images|default": "common.cell_image_panel", "$background_image_control_name|default": "bg", "$focus_id|default": "", "$focus_override_down|default": "", "$focus_override_up|default": "", "$focus_override_left|default": "", "$focus_override_right|default": "", "focus_identifier": "$focus_id", "focus_change_down": "$focus_override_down", "focus_change_up": "$focus_override_up", "focus_change_left": "$focus_override_left", "focus_change_right": "$focus_override_right", "focus_enabled": true, "focus_wrap_enabled": false, "focus_magnet_enabled": true, "controls": [{"item_cell": {"type": "panel", "size": "$cell_image_size", "offset": "$offset_cell", "layer": 0, "controls": [{"$background_image_control_name@$background_images": {"layer": 1}}, {"item": {"type": "panel", "size": "$item_renderer_panel_size", "layer": 0, "controls": [{"stack_count_label@common.stack_count_label": {"layer": 27}}, {"$item_renderer@$item_renderer": {"size": "$item_renderer_size", "offset": "$item_renderer_offset", "anchor_to": "center", "anchor_from": "center", "layer": 7}}]}}, {"durability_bar@common.durability_bar": {"layer": 20}}, {"storage_bar@common.storage_bar": {"layer": 20}}]}}, {"item_cell_overlay_ref@$cell_overlay_ref": {"offset": "$offset_cell", "layer": 3}}, {"item_selected_image@common.slot_selected": {"offset": "$offset_cell", "layer": 4}}, {"item_button_ref@$button_ref": {"offset": "$offset_cell", "tts_ignore_count": true, "tts_skip_message": true, "tts_inherit_siblings": true, "layer": 5}}, {"container_item_lock_overlay@common.container_item_lock_overlay": {"size": "$item_renderer_size", "offset": "$offset_cell", "anchor_to": "top_left", "anchor_from": "top_left", "layer": 6}}, {"item_lock_cell_image@common.item_lock_cell_image": {"offset": "$offset_cell", "layer": 2}}, {"bundle_slot_panel@gameplay.bundle_slot_panel": {"offset": "$offset_cell", "layer": 10}}]}}