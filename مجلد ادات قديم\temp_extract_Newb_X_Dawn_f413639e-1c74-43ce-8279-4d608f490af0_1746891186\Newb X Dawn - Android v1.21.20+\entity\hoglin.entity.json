{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:hoglin", "materials": {"default": "phantom"}, "textures": {"default": "textures/entity/hoglin/hoglin"}, "geometry": {"default": "geometry.hoglin"}, "spawn_egg": {"base_color": "#C66E55", "overlay_color": "#5f6464"}, "scripts": {"pre_animation": ["variable.tcos_right_side = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;", "variable.tcos_left_side = -variable.tcos_right_side;", "variable.attack_head_rot =  Math.sin(variable.attack_time * 180.0) * -37.3;"]}, "animations": {"walk": "animation.hoglin.walk", "look_at_target": "animation.hoglin.look_at_target", "attack": "animation.hoglin.attack", "hoglin_baby_scaling": "animation.hoglin.baby_scaling"}, "animation_controllers": [{"look_at_target": "controller.animation.hoglin.look_at_target"}, {"walk": "controller.animation.hoglin.walk"}, {"attack": "controller.animation.hoglin.attack"}, {"hoglin_baby_scaling": "controller.animation.hoglin.baby_scaling"}], "render_controllers": ["controller.render.hoglin"]}}}