# 🔥 دليل Firebase Storage لتخزين ملفات المودات

## 📋 نظرة عامة

تم دمج Firebase Storage في أداة نشر المودات لتوفير تخزين موثوق وسريع لملفات المودات مع روابط تحميل مباشرة.

## ✨ الميزات

### 🔄 رفع تلقائي للملفات
- **رفع تلقائي** عند نشر المود
- **نسخ احتياطي** للرابط الأصلي
- **روابط تحميل مباشرة** من Firebase
- **تصنيف الملفات** حسب النوع (addon, shader)

### 📊 إدارة الملفات
- **قائمة الملفات المرفوعة** مع التفاصيل
- **إحصائيات التخزين** (عدد الملفات، الحجم الإجمالي)
- **نسخ روابط التحميل** بسهولة
- **حذف الملفات** غير المرغوبة

### 🔒 الأمان والموثوقية
- **تشفير البيانات** أثناء النقل والتخزين
- **نسخ احتياطية متعددة** في مراكز بيانات Google
- **روابط عامة آمنة** للتحميل
- **مراقبة الاستخدام** والحصص

## 🛠️ الإعداد والتكوين

### 1. إنشاء مشروع Firebase

#### أ. إنشاء المشروع:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر "Create a project" أو "إنشاء مشروع"
3. أدخل اسم المشروع: `download-e33a2`
4. اختر الإعدادات المناسبة
5. انقر "Create project"

#### ب. تفعيل Storage:
1. في لوحة تحكم المشروع، اذهب إلى "Storage"
2. انقر "Get started"
3. اختر قواعد الأمان (ابدأ بـ test mode)
4. اختر موقع التخزين (أقرب منطقة جغرافية)

### 2. إعداد مفاتيح الخدمة

#### أ. إنشاء Service Account:
1. اذهب إلى "Project Settings" > "Service accounts"
2. انقر "Generate new private key"
3. احفظ الملف JSON بأمان

#### ب. تحديث ملف الإعدادات:
```json
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### 3. تثبيت المتطلبات

```bash
pip install firebase-admin
```

## 📖 كيفية الاستخدام

### 🔄 الاستخدام التلقائي

عند نشر مود جديد:
1. املأ جميع بيانات المود
2. أدخل رابط تحميل المود
3. انقر "نشر المود"
4. سيتم رفع الملف تلقائياً إلى Firebase
5. سيتم استخدام رابط Firebase في قاعدة البيانات

### 🔧 الإدارة اليدوية

#### فحص الحالة:
- انقر "حالة قاعدة البيانات والخدمات"
- راجع قسم "فحص Firebase Storage"
- تأكد من أن جميع الخدمات تعمل (✅)

#### إدارة الملفات:
- انقر "إدارة Firebase Storage"
- راجع قائمة الملفات المرفوعة
- انسخ روابط التحميل حسب الحاجة
- احذف الملفات غير المرغوبة

## 📊 مراقبة الاستخدام

### إحصائيات التخزين
- **إجمالي الملفات**: عدد جميع الملفات
- **ملفات المودات**: الملفات في مجلد `mods/`
- **الحجم الإجمالي**: مساحة التخزين المستخدمة
- **نوع الملفات**: تصنيف حسب addon/shader

### حدود Firebase المجانية
- **5 GB** مساحة تخزين مجانية
- **1 GB/يوم** نقل بيانات مجاني
- **20,000** عملية قراءة يومياً
- **20,000** عملية كتابة يومياً

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في التهيئة
```
❌ خطأ في تهيئة Firebase: [Errno 2] No such file or directory
```
**الحل**:
- تأكد من وجود ملف `firebase_config.json`
- تحقق من صحة مفاتيح Service Account
- تأكد من تفعيل Firebase Storage في المشروع

#### 2. خطأ في الصلاحيات
```
❌ خطأ في رفع الملف: 403 Forbidden
```
**الحل**:
- تحقق من قواعد الأمان في Firebase Console
- تأكد من صلاحيات Service Account
- راجع إعدادات CORS إذا لزم الأمر

#### 3. فشل رفع الملفات الكبيرة
```
❌ خطأ في رفع الملف: Request timeout
```
**الحل**:
- تحقق من سرعة الإنترنت
- جرب رفع ملفات أصغر أولاً
- تأكد من عدم تجاوز حدود الحجم (5GB للملف الواحد)

#### 4. روابط التحميل لا تعمل
```
⚠️ رابط التحميل غير متاح
```
**الحل**:
- تأكد من أن الملف تم رفعه بنجاح
- تحقق من إعدادات الخصوصية للملف
- جرب إعادة إنشاء الرابط العام

## 🔒 الأمان والخصوصية

### قواعد الأمان الموصى بها

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // السماح بالقراءة للجميع
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // السماح بالكتابة للمستخدمين المصرح لهم فقط
    match /mods/{allPaths=**} {
      allow write: if request.auth != null;
    }
  }
}
```

### نصائح الأمان
- **لا تشارك** مفاتيح Service Account
- **استخدم متغيرات البيئة** للمفاتيح الحساسة
- **راجع السجلات** بانتظام للأنشطة المشبوهة
- **حدث القواعد** حسب احتياجاتك

## 📈 التحسين والأداء

### نصائح لتحسين الأداء
- **ضغط الملفات** قبل الرفع
- **استخدام CDN** للملفات الكبيرة
- **تنظيم الملفات** في مجلدات منطقية
- **حذف الملفات القديمة** بانتظام

### مراقبة الاستخدام
- راجع **Firebase Console** للإحصائيات
- راقب **استهلاك الحصة** الشهرية
- استخدم **تنبيهات الفوترة** لتجنب التكاليف غير المتوقعة

## 🚀 التطوير المستقبلي

### ميزات مخططة
- [ ] ضغط الملفات التلقائي
- [ ] نسخ احتياطية متعددة
- [ ] تحسين سرعة الرفع
- [ ] دعم الرفع المتوازي
- [ ] إدارة أذونات متقدمة

### تحسينات تقنية
- [ ] تحسين معالجة الأخطاء
- [ ] دعم استئناف الرفع
- [ ] تشفير إضافي للملفات الحساسة
- [ ] تكامل مع خدمات CDN

## 📞 الدعم

### للحصول على المساعدة
- 📖 **الوثائق الرسمية**: [Firebase Storage Docs](https://firebase.google.com/docs/storage)
- 💬 **المجتمع**: [Firebase Community](https://firebase.google.com/community)
- 🐛 **تقرير الأخطاء**: [GitHub Issues]
- 📧 **الدعم التقني**: <EMAIL>

---

## 📄 الترخيص

هذا المشروع يستخدم Firebase Storage وفقاً لشروط خدمة Google Firebase. الرجاء مراجعة [شروط الخدمة](https://firebase.google.com/terms) قبل الاستخدام.

## 🙏 شكر وتقدير

- **Google Firebase** لخدمات التخزين السحابي المتقدمة
- **مجتمع المطورين** للدعم والتطوير المستمر
