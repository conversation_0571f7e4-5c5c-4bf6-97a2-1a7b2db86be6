# -*- coding: utf-8 -*-
"""
اختبار نهائي للتطبيق الرئيسي
Final test for the main application
"""

import os
import sys
import tempfile
import zipfile
import io
import json

def create_real_mod_file():
    """إنشاء ملف مود حقيقي للاختبار"""
    print("🔧 إنشاء ملف مود حقيقي للاختبار...")
    
    zip_buffer = io.BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # إضافة ملف manifest.json صحيح
        manifest = {
            "format_version": 2,
            "header": {
                "description": "Final Test Mod - Complete functionality test",
                "name": "Final Test Mod",
                "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                "version": [1, 0, 0],
                "min_engine_version": [1, 16, 0]
            },
            "modules": [
                {
                    "description": "Resource Pack Module",
                    "type": "resources",
                    "uuid": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
                    "version": [1, 0, 0]
                }
            ]
        }
        
        zip_file.writestr("manifest.json", json.dumps(manifest, indent=2))
        
        # إضافة ملف pack_icon.png وهمي
        fake_png = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x10\x00\x00\x00\x10\x08\x02\x00\x00\x00\x90\x91h6\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\x00\x02\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
        zip_file.writestr("pack_icon.png", fake_png)
        
        # إضافة ملف README
        readme_content = """# Final Test Mod

This is a test mod created for final testing of the Firebase upload functionality.

## Features:
- Complete manifest.json
- Valid pack icon
- Proper file structure

## Version: 1.0.0
## Compatible with: Minecraft Bedrock Edition 1.16+
"""
        zip_file.writestr("README.md", readme_content)
        
        # إضافة مجلد textures وهمي
        zip_file.writestr("textures/blocks/stone.png", fake_png)
        zip_file.writestr("textures/items/diamond.png", fake_png)
    
    zip_buffer.seek(0)
    content = zip_buffer.getvalue()
    
    print(f"✅ تم إنشاء ملف مود حقيقي - الحجم: {len(content)} بايت")
    return content

def test_mod_upload_integration():
    """اختبار تكامل رفع المود في التطبيق الرئيسي"""
    print("🚀 بدء اختبار تكامل رفع المود...")
    print("=" * 60)
    
    try:
        # استيراد الدوال المطلوبة من التطبيق الرئيسي
        from mod_processor_broken_final import upload_mod_to_firebase_and_get_download_link
        
        # إنشاء ملف مود للاختبار
        mod_content = create_real_mod_file()
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.mcpack', delete=False) as temp_file:
            temp_file.write(mod_content)
            temp_file_path = temp_file.name
        
        print(f"📁 ملف مؤقت: {temp_file_path}")
        
        try:
            # اختبار رفع المود
            print(f"\n📤 اختبار رفع المود باستخدام التطبيق الرئيسي...")
            
            firebase_url = upload_mod_to_firebase_and_get_download_link(
                mod_content,
                "final_integration_mod.mcpack"
            )
            
            if firebase_url:
                print(f"✅ تم رفع المود بنجاح!")
                print(f"🔗 رابط Firebase: {firebase_url}")
                
                # اختبار الرابط
                import requests
                print(f"\n🌐 اختبار الرابط...")
                try:
                    response = requests.head(firebase_url, timeout=10)
                    if response.status_code == 200:
                        print("✅ الرابط يعمل بشكل صحيح!")
                        print(f"📊 حجم الملف: {response.headers.get('content-length', 'غير معروف')} بايت")
                        return True
                    else:
                        print(f"❌ مشكلة في الرابط - كود الاستجابة: {response.status_code}")
                        if response.status_code == 400:
                            print("🔍 تفاصيل الخطأ:")
                            print(response.text)
                        return False
                except Exception as e:
                    print(f"❌ خطأ في اختبار الرابط: {e}")
                    return False
            else:
                print("❌ فشل في رفع المود")
                return False
                
        finally:
            # حذف الملف المؤقت
            try:
                os.unlink(temp_file_path)
                print(f"🗑️ تم حذف الملف المؤقت")
            except:
                pass
                
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def test_application_components():
    """اختبار مكونات التطبيق الأساسية"""
    print("\n📦 اختبار مكونات التطبيق...")
    
    try:
        # اختبار استيراد المكونات الأساسية
        from mod_processor_broken_final import (
            FIREBASE_STORAGE_AVAILABLE,
            DATABASE_PROVIDER,
            firebase_manager
        )
        
        print(f"✅ Firebase Storage: {'متاح' if FIREBASE_STORAGE_AVAILABLE else 'غير متاح'}")
        print(f"✅ قاعدة البيانات: {DATABASE_PROVIDER}")
        
        # اختبار Firebase
        if firebase_manager and firebase_manager.is_initialized:
            print("✅ Firebase مهيأ ومتصل")
        else:
            print("⚠️ Firebase غير مهيأ")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المكونات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🧪 بدء الاختبار النهائي الشامل للتطبيق...")
    print("=" * 70)
    
    tests = [
        ("مكونات التطبيق", test_application_components),
        ("تكامل رفع المود", test_mod_upload_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار النهائي:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للاستخدام")
        print("✅ Firebase Storage يعمل بشكل مثالي")
        print("✅ روابط التحميل تعمل بشكل صحيح")
        return True
    else:
        print(f"\n⚠️ فشل {total - passed} اختبار من أصل {total}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
