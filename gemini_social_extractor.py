# -*- coding: utf-8 -*-
"""
Gemini Social Extractor Module
This module uses Google's Gemini AI to extract social media links and information from mod descriptions.
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple

class GeminiSocialExtractor:
    """
    A class that uses Gemini AI to extract social media information from text.
    """
    
    def __init__(self, gemini_model=None):
        """
        Initialize the GeminiSocialExtractor with a Gemini model.
        
        Args:
            gemini_model: The Gemini model to use for extraction
        """
        self.gemini_model = gemini_model
        self.social_media_patterns = {
            'twitter': r'https?://(?:www\.)?(?:twitter\.com|x\.com)/([a-zA-Z0-9_]+)',
            'discord': r'https?://(?:www\.)?discord\.(?:gg|com)/([a-zA-Z0-9_-]+)',
            'youtube': r'https?://(?:www\.)?youtube\.com/(?:channel|c|user)/([a-zA-Z0-9_-]+)',
            'instagram': r'https?://(?:www\.)?instagram\.com/([a-zA-Z0-9_\.]+)',
            'facebook': r'https?://(?:www\.)?facebook\.com/([a-zA-Z0-9_\.]+)',
            'github': r'https?://(?:www\.)?github\.com/([a-zA-Z0-9_-]+)',
            'patreon': r'https?://(?:www\.)?patreon\.com/([a-zA-Z0-9_-]+)',
            'twitch': r'https?://(?:www\.)?twitch\.tv/([a-zA-Z0-9_]+)',
            'tiktok': r'https?://(?:www\.)?tiktok\.com/@([a-zA-Z0-9_\.]+)'
        }
    
    def extract_social_media_links(self, text: str) -> Dict[str, List[str]]:
        """
        Extract social media links from text using regex patterns.
        
        Args:
            text: The text to extract links from
            
        Returns:
            A dictionary mapping social media platforms to lists of links
        """
        result = {}
        
        for platform, pattern in self.social_media_patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                links = []
                for match in matches:
                    if platform == 'twitter':
                        links.append(f"https://twitter.com/{match}")
                    elif platform == 'discord':
                        links.append(f"https://discord.gg/{match}")
                    elif platform == 'youtube':
                        links.append(f"https://youtube.com/channel/{match}")
                    elif platform == 'instagram':
                        links.append(f"https://instagram.com/{match}")
                    elif platform == 'facebook':
                        links.append(f"https://facebook.com/{match}")
                    elif platform == 'github':
                        links.append(f"https://github.com/{match}")
                    elif platform == 'patreon':
                        links.append(f"https://patreon.com/{match}")
                    elif platform == 'twitch':
                        links.append(f"https://twitch.tv/{match}")
                    elif platform == 'tiktok':
                        links.append(f"https://tiktok.com/@{match}")
                
                result[platform] = links
        
        return result
    
    def extract_with_gemini(self, text: str) -> Dict[str, Any]:
        """
        Use Gemini AI to extract social media information from text.
        
        Args:
            text: The text to extract information from
            
        Returns:
            A dictionary containing extracted social media information
        """
        if not self.gemini_model:
            print("⚠️ Gemini model not available for social extraction")
            # Fall back to regex extraction
            return {"links": self.extract_social_media_links(text), "method": "regex"}
        
        try:
            prompt = f"""
            Extract all social media links and handles from the following text. 
            Return the result as a JSON object with the following structure:
            {{
                "twitter": [{"username": "username", "url": "full_url"}],
                "discord": [{"server": "server_name", "url": "full_url"}],
                "youtube": [{"channel": "channel_name", "url": "full_url"}],
                "instagram": [{"username": "username", "url": "full_url"}],
                "facebook": [{"page": "page_name", "url": "full_url"}],
                "github": [{"username": "username", "url": "full_url"}],
                "patreon": [{"username": "username", "url": "full_url"}],
                "other": [{"platform": "platform_name", "url": "full_url"}]
            }}
            
            Only include platforms that are actually mentioned in the text.
            
            TEXT:
            {text}
            """
            
            response = self.gemini_model.generate_content(prompt)
            
            # Extract JSON from response
            response_text = response.text
            
            # Try to find JSON in the response
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # If no JSON code block, try to extract JSON directly
                json_str = response_text
            
            # Parse the JSON
            try:
                result = json.loads(json_str)
                return {"data": result, "method": "gemini"}
            except json.JSONDecodeError as e:
                print(f"⚠️ Error parsing Gemini social extraction response: {e}")
                # Fall back to regex extraction
                return {"links": self.extract_social_media_links(text), "method": "regex"}
                
        except Exception as e:
            print(f"⚠️ Error in Gemini social extraction: {e}")
            # Fall back to regex extraction
            return {"links": self.extract_social_media_links(text), "method": "regex"}
    
    def extract(self, text: str) -> Dict[str, Any]:
        """
        Extract social media information from text, using Gemini if available.
        
        Args:
            text: The text to extract information from
            
        Returns:
            A dictionary containing extracted social media information
        """
        if self.gemini_model:
            return self.extract_with_gemini(text)
        else:
            return {"links": self.extract_social_media_links(text), "method": "regex"}