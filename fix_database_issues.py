# -*- coding: utf-8 -*-
"""
ملف إصلاح مشاكل قاعدة البيانات
"""

import os
import json
import sys

def fix_supabase_bucket_creation():
    """إصلاح مشكلة إنشاء buckets في Supabase"""
    print("🔧 إصلاح مشكلة إنشاء buckets في Supabase...")
    
    try:
        from supabase import create_client, Client
        
        # تحميل الإعدادات
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        supabase_url = config.get('supabase_url')
        # استخدام service key بدلاً من anon key لإنشاء buckets
        service_key = config.get('SUPABASE_SERVICE_KEY')
        
        if not service_key:
            print("❌ مفتاح service_role غير موجود")
            return False
        
        print(f"🔑 استخدام service key: {service_key[:20]}...")
        
        # إنشاء عميل بصلاحيات admin
        supabase: Client = create_client(supabase_url, service_key)
        
        # محاولة إنشاء buckets بالطريقة الصحيحة
        buckets_to_create = [
            {'id': 'image', 'name': 'image', 'public': True},
            {'id': 'my_new_mods_bucket', 'name': 'my_new_mods_bucket', 'public': True}
        ]
        
        existing_buckets = supabase.storage.list_buckets()
        existing_names = [bucket['name'] for bucket in existing_buckets]
        
        for bucket_config in buckets_to_create:
            bucket_name = bucket_config['name']
            
            if bucket_name not in existing_names:
                print(f"🔄 إنشاء bucket: {bucket_name}")
                try:
                    # استخدام الطريقة الصحيحة لإنشاء bucket
                    result = supabase.storage.create_bucket(bucket_config)
                    print(f"✅ تم إنشاء bucket: {bucket_name}")
                except Exception as e:
                    print(f"❌ فشل إنشاء bucket {bucket_name}: {e}")
                    
                    # محاولة بديلة
                    try:
                        result = supabase.storage.create_bucket(bucket_name)
                        print(f"✅ تم إنشاء bucket بالطريقة البديلة: {bucket_name}")
                    except Exception as e2:
                        print(f"❌ فشل أيضاً بالطريقة البديلة: {e2}")
            else:
                print(f"✅ bucket موجود مسبقاً: {bucket_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح Supabase: {e}")
        return False

def fix_gemini_model_issue():
    """إصلاح مشكلة نموذج Gemini"""
    print("🔧 إصلاح مشكلة نموذج Gemini...")
    
    try:
        import google.generativeai as genai
        
        # تحميل الإعدادات
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        gemini_keys = config.get('gemini_api_keys', [])
        if not gemini_keys:
            print("❌ لا توجد مفاتيح Gemini")
            return False
        
        test_key = gemini_keys[0]
        print(f"🔑 اختبار المفتاح: {test_key[:20]}...")
        
        genai.configure(api_key=test_key)
        
        # جلب قائمة النماذج المتاحة
        print("🔄 جلب قائمة النماذج المتاحة...")
        models = genai.list_models()
        
        available_models = []
        for model in models:
            if 'generateContent' in model.supported_generation_methods:
                available_models.append(model.name)
                print(f"  ✅ {model.name}")
        
        if not available_models:
            print("❌ لا توجد نماذج متاحة")
            return False
        
        # اختبار أول نموذج متاح
        test_model_name = available_models[0]
        print(f"🔄 اختبار النموذج: {test_model_name}")
        
        model = genai.GenerativeModel(test_model_name)
        response = model.generate_content("Hello")
        
        print(f"✅ نجح الاتصال بـ Gemini باستخدام: {test_model_name}")
        print(f"📝 رد الاختبار: {response.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح Gemini: {e}")
        return False

def enable_firebase_firestore():
    """إرشادات تفعيل Firestore"""
    print("🔧 إرشادات تفعيل Firebase Firestore...")
    
    print("""
📋 لتفعيل Firestore في مشروع Firebase:

1. اذهب إلى: https://console.firebase.google.com/
2. اختر مشروع: download-e33a2
3. من القائمة الجانبية، اختر "Firestore Database"
4. انقر على "Create database"
5. اختر "Start in test mode" للبداية
6. اختر موقع قاعدة البيانات (مثل: us-central)
7. انقر "Done"

أو يمكنك الذهاب مباشرة إلى:
https://console.developers.google.com/apis/api/firestore.googleapis.com/overview?project=download-e33a2

💡 بعد التفعيل، انتظر بضع دقائق ثم أعد تشغيل الاختبار.
""")

def test_simple_connections():
    """اختبار بسيط للاتصالات"""
    print("🔄 اختبار بسيط للاتصالات...")
    
    results = {}
    
    # اختبار Supabase
    try:
        from supabase import create_client
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        supabase_url = config.get('supabase_url')
        supabase_key = config.get('SUPABASE_KEY')
        
        supabase = create_client(supabase_url, supabase_key)
        response = supabase.table('mods').select("id").limit(1).execute()
        
        print("✅ Supabase: متصل")
        results['supabase'] = True
        
    except Exception as e:
        print(f"❌ Supabase: فشل - {e}")
        results['supabase'] = False
    
    # اختبار Firebase Storage
    try:
        import firebase_admin
        from firebase_admin import credentials, storage
        
        # التحقق من وجود تطبيق مُهيأ
        try:
            app = firebase_admin.get_app()
        except ValueError:
            cred = credentials.Certificate("firebase-service-account.json")
            app = firebase_admin.initialize_app(cred, {
                'storageBucket': 'download-e33a2.firebasestorage.app'
            })
        
        bucket = storage.bucket()
        print("✅ Firebase Storage: متصل")
        results['firebase_storage'] = True
        
    except Exception as e:
        print(f"❌ Firebase Storage: فشل - {e}")
        results['firebase_storage'] = False
    
    # اختبار Gemini
    try:
        import google.generativeai as genai
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        gemini_keys = config.get('gemini_api_keys', [])
        genai.configure(api_key=gemini_keys[0])
        
        # استخدام نموذج أحدث
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content("Hi")
        
        print("✅ Gemini: متصل")
        results['gemini'] = True
        
    except Exception as e:
        print(f"❌ Gemini: فشل - {e}")
        results['gemini'] = False
    
    return results

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔧 إصلاح مشاكل قاعدة البيانات")
    print("=" * 50)
    
    # اختبار بسيط أولاً
    print("\n1️⃣ اختبار الاتصالات الحالية:")
    results = test_simple_connections()
    
    # إصلاح المشاكل
    print("\n2️⃣ إصلاح المشاكل:")
    
    if not results.get('supabase', True):
        fix_supabase_bucket_creation()
    
    if not results.get('gemini', True):
        fix_gemini_model_issue()
    
    if not results.get('firebase_storage', True):
        enable_firebase_firestore()
    
    # اختبار نهائي
    print("\n3️⃣ اختبار نهائي:")
    final_results = test_simple_connections()
    
    print("\n" + "=" * 50)
    print("📊 النتائج النهائية:")
    for service, status in final_results.items():
        status_text = "✅ يعمل" if status else "❌ لا يعمل"
        print(f"  {service}: {status_text}")
    print("=" * 50)

if __name__ == "__main__":
    main()
