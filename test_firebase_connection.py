# -*- coding: utf-8 -*-
"""
ملف اختبار الاتصال بـ Firebase
يختبر الاتصال بـ Firestore و Firebase Storage
"""

import os
import json
import sys
import traceback

def test_firebase_imports():
    """اختبار استيراد مكتبات Firebase"""
    print("🔄 اختبار استيراد مكتبات Firebase...")
    
    try:
        import firebase_admin
        from firebase_admin import credentials, firestore, storage
        print("✅ تم استيراد مكتبات Firebase بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل استيراد مكتبات Firebase: {e}")
        print("💡 قم بتثبيت المكتبات باستخدام:")
        print("pip install firebase-admin")
        return False

def test_service_account_file():
    """اختبار وجود ملف service account"""
    print("🔄 اختبار ملف service account...")
    
    service_account_path = "firebase-service-account.json"
    
    if not os.path.exists(service_account_path):
        print(f"❌ ملف service account غير موجود: {service_account_path}")
        return False, None
    
    try:
        with open(service_account_path, 'r', encoding='utf-8') as f:
            service_account_data = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in service_account_data]
        
        if missing_fields:
            print(f"❌ حقول مفقودة في ملف service account: {missing_fields}")
            return False, None
        
        print("✅ ملف service account صحيح")
        print(f"📍 Project ID: {service_account_data.get('project_id')}")
        print(f"📧 Client Email: {service_account_data.get('client_email')}")
        
        return True, service_account_data
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف service account: {e}")
        return False, None

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    print("🔄 بدء اختبار الاتصال بـ Firebase...")
    
    # اختبار الاستيراد
    if not test_firebase_imports():
        return False
    
    # اختبار ملف service account
    service_account_ok, service_account_data = test_service_account_file()
    if not service_account_ok:
        return False
    
    try:
        import firebase_admin
        from firebase_admin import credentials, firestore, storage
        
        # تهيئة Firebase
        print("🔄 تهيئة Firebase...")
        
        # التحقق من وجود تطبيق Firebase مُهيأ مسبقاً
        try:
            app = firebase_admin.get_app()
            print("✅ تطبيق Firebase موجود مسبقاً")
        except ValueError:
            # إنشاء تطبيق جديد
            cred = credentials.Certificate("firebase-service-account.json")
            app = firebase_admin.initialize_app(cred, {
                'storageBucket': 'download-e33a2.firebasestorage.app'
            })
            print("✅ تم تهيئة تطبيق Firebase جديد")
        
        # اختبار Firestore
        print("🔄 اختبار الاتصال بـ Firestore...")
        try:
            db = firestore.client()
            
            # اختبار كتابة وقراءة بسيطة
            test_doc_ref = db.collection('test').document('connection_test')
            test_doc_ref.set({
                'message': 'اختبار الاتصال',
                'timestamp': firestore.SERVER_TIMESTAMP
            })
            
            # قراءة البيانات
            doc = test_doc_ref.get()
            if doc.exists:
                print("✅ نجح الاتصال بـ Firestore")
                print(f"📄 بيانات الاختبار: {doc.to_dict()}")
                
                # حذف بيانات الاختبار
                test_doc_ref.delete()
                print("🗑️ تم حذف بيانات الاختبار")
            else:
                print("⚠️ لم يتم العثور على بيانات الاختبار")
                
        except Exception as firestore_e:
            print(f"❌ فشل الاتصال بـ Firestore: {firestore_e}")
        
        # اختبار Firebase Storage
        print("🔄 اختبار الاتصال بـ Firebase Storage...")
        try:
            bucket = storage.bucket()
            print(f"✅ نجح الاتصال بـ Firebase Storage")
            print(f"📦 اسم Bucket: {bucket.name}")
            
            # اختبار رفع ملف بسيط
            test_content = "اختبار رفع ملف إلى Firebase Storage"
            blob = bucket.blob('test/connection_test.txt')
            blob.upload_from_string(test_content, content_type='text/plain')
            print("✅ نجح رفع ملف الاختبار")
            
            # اختبار تحميل الملف
            downloaded_content = blob.download_as_text()
            if downloaded_content == test_content:
                print("✅ نجح تحميل ملف الاختبار")
            else:
                print("⚠️ محتوى الملف المحمل لا يطابق المحتوى الأصلي")
            
            # حذف ملف الاختبار
            blob.delete()
            print("🗑️ تم حذف ملف الاختبار")
            
        except Exception as storage_e:
            print(f"❌ فشل الاتصال بـ Firebase Storage: {storage_e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الاتصال بـ Firebase: {e}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_firebase_permissions():
    """اختبار صلاحيات Firebase"""
    print("\n🔄 اختبار صلاحيات Firebase...")
    
    try:
        import firebase_admin
        from firebase_admin import firestore, storage
        
        db = firestore.client()
        bucket = storage.bucket()
        
        # اختبار صلاحيات مختلفة
        tests = [
            ("قراءة Firestore", lambda: db.collection('mods').limit(1).get()),
            ("كتابة Firestore", lambda: db.collection('test').add({'test': True})),
            ("قراءة Storage", lambda: list(bucket.list_blobs(max_results=1))),
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                print(f"✅ {test_name}: نجح")
            except Exception as e:
                print(f"❌ {test_name}: فشل - {e}")
                
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔥 اختبار الاتصال بـ Firebase")
    print("=" * 50)
    
    # اختبار الاتصال الأساسي
    connection_success = test_firebase_connection()
    
    # اختبار الصلاحيات
    if connection_success:
        test_firebase_permissions()
    
    print("\n" + "=" * 50)
    if connection_success:
        print("✅ اكتمل اختبار Firebase بنجاح")
    else:
        print("❌ فشل اختبار Firebase")
    print("=" * 50)

if __name__ == "__main__":
    main()
