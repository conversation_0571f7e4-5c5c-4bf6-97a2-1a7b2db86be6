"""
اختبار تنظيف الوصف
"""

import re

def improved_clean_basic_description(description: str) -> str:
    """
    تنظيف الوصف الأساسي من المعلومات غير المرغوبة وتنسيق الفقرات
    
    التحسينات:
    - إزالة العناوين والفقرات المنسقة
    - تحويل النص إلى فقرة واحدة متصلة
    - إزالة علامات [ENGLISH_DESCRIPTION] و [/ENGLISH_DESCRIPTION]
    """
    if not description:
        return ""

    # إزالة علامات [ENGLISH_DESCRIPTION] و [/ENGLISH_DESCRIPTION]
    description = re.sub(r'\[ENGLISH_DESCRIPTION\]|\[\/ENGLISH_DESCRIPTION\]', '', description)
    
    # تقسيم النص إلى أسطر
    lines = description.split('\n')
    cleaned_lines = []

    # أنماط الأسطر التي يجب تجنبها
    avoid_patterns = [
        r'minecraft pe texture packs',
        r'published on',
        r'updated on',
        r'skip to downloads',
        r'select version for changelog',
        r'changelog',
        r'installation',
        r'downloads',
        r'join discord',
        r'download packs',
        r'supported minecraft versions',
        r'resolutions',
        r'you may also like',
        r'installation guides',
        r'android',
        r'ios',
        r'windows 10',
        r'by\w+',
        r'\d{1,2}\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)',
        r'\d+\.\d+',
        r'mb\)',
        r'manifest\.json',
        r'pack uuid',
        r'mcc\)',
    ]

    for line in lines:
        line = line.strip()
        if not line:
            continue

        line_lower = line.lower()

        # تجنب الأسطر غير المرغوبة
        should_skip = False
        for pattern in avoid_patterns:
            if re.search(pattern, line_lower):
                should_skip = True
                break

        if should_skip:
            continue

        # تجنب الأسطر القصيرة جداً أو التي تحتوي على أرقام فقط
        if len(line) < 10 or line.isdigit():
            continue

        # تجنب الأسطر التي تحتوي على معلومات تقنية
        if any(tech in line_lower for tech in ['1.21', '16x', 'mb', 'uuid', 'manifest']):
            continue

        # تجنب الأسطر التي تبدأ بعلامات العناوين
        if re.match(r'^#+\s+', line) or re.match(r'^[A-Z\s]{10,}$', line):
            continue

        cleaned_lines.append(line)

    # دمج الأسطر المنظفة في فقرة واحدة
    cleaned_text = ' '.join(cleaned_lines)
    
    # تنظيف إضافي
    cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)  # تقليل المسافات المتعددة
    cleaned_text = re.sub(r'^\s*[\*\-\•]\s*', '', cleaned_text, flags=re.MULTILINE)  # إزالة النقاط
    
    return cleaned_text.strip()

# وصف مع علامات [ENGLISH_DESCRIPTION]
test_description = """
[ENGLISH_DESCRIPTION]
Elevate your Minecraft journey with Easy Waypoints, the essential addon designed to fundamentally enhance your entire gameplay experience. 

This remarkable addition injects your world with a suite of exciting new features and compelling content, ensuring every adventure feels fresh and deeply engaging from the moment you begin.

# Key Features

* Easy navigation
* Mark important locations
* Never get lost again

Easy Waypoints introduces innovative gameplay mechanics and content that completely transform the way you interact with your vast, blocky universe.
[/ENGLISH_DESCRIPTION]
"""

cleaned = improved_clean_basic_description(test_description)

print("الوصف الأصلي:")
print(test_description)
print("\nالوصف بعد التنظيف:")
print(cleaned)

# التحقق من النتائج
issues = []
if "[ENGLISH_DESCRIPTION]" in cleaned:
    issues.append("لا يزال يحتوي على [ENGLISH_DESCRIPTION]")
if "[/ENGLISH_DESCRIPTION]" in cleaned:
    issues.append("لا يزال يحتوي على [/ENGLISH_DESCRIPTION]")
if "# Key Features" in cleaned:
    issues.append("لا يزال يحتوي على عناوين")
if "\n\n" in cleaned:
    issues.append("لا يزال يحتوي على فقرات متعددة")

if issues:
    print("\n❌ مشاكل في تنظيف الوصف:")
    for issue in issues:
        print(f"  - {issue}")
else:
    print("\n✅ تم تنظيف الوصف بنجاح!")