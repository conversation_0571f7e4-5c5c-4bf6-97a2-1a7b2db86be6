# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL محسن مع معالجة أفضل للأخطاء
"""

import requests
import time
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any
import traceback

# محاولة استيراد cloudscraper
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
    print("cloudscraper متوفر - سيتم استخدامه لتجاوز الحماية")
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("cloudscraper غير متوفر - سيتم استخدام requests العادي")

class EnhancedMCPEDLScraper:
    """مستخرج MCPEDL محسن مع معالجة أفضل للأخطاء"""

    def __init__(self):
        self.session = None
        self.setup_session()
        
        # User agents متنوعة
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

    def setup_session(self):
        """إعداد الجلسة"""
        if CLOUDSCRAPER_AVAILABLE:
            try:
                self.session = cloudscraper.create_scraper(
                    browser={
                        'browser': 'chrome',
                        'platform': 'windows',
                        'mobile': False
                    },
                    delay=2,
                    captcha={
                        'provider': 'anticaptcha'
                    }
                )
                print("تم إنشاء جلسة cloudscraper محسنة")
            except Exception as e:
                print(f"فشل في إنشاء cloudscraper: {e}")
                self.session = requests.Session()
        else:
            self.session = requests.Session()
            
        # إعداد headers أساسية
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })

    def is_valid_mcpedl_url(self, url: str) -> bool:
        """التحقق من صحة رابط mcpedl.com"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower() in ['mcpedl.com', 'www.mcpedl.com']
        except:
            return False

    def fetch_page_enhanced(self, url: str, max_retries: int = 3) -> Optional[BeautifulSoup]:
        """جلب الصفحة مع معالجة محسنة للأخطاء"""
        if not self.is_valid_mcpedl_url(url):
            print("رابط غير صحيح")
            return None

        for attempt in range(max_retries):
            try:
                print(f"محاولة {attempt + 1}/{max_retries}: جلب الصفحة...")
                
                # تناوب User Agent
                if attempt > 0:
                    import random
                    user_agent = random.choice(self.user_agents)
                    self.session.headers['User-Agent'] = user_agent
                    print(f"تغيير User Agent: {user_agent[:50]}...")

                # إضافة Referer للمحاولات بعد الأولى
                if attempt > 0:
                    self.session.headers['Referer'] = 'https://mcpedl.com/'

                # تأخير متدرج
                if attempt > 0:
                    delay = min(3 * (attempt + 1), 15)
                    print(f"انتظار {delay} ثانية...")
                    time.sleep(delay)

                # طلب الصفحة
                response = self.session.get(
                    url,
                    timeout=45,
                    allow_redirects=True,
                    verify=True,
                    stream=False
                )

                print(f"رمز الاستجابة: {response.status_code}")
                print(f"Content-Type: {response.headers.get('content-type', 'غير محدد')}")
                print(f"Content-Length: {response.headers.get('content-length', 'غير محدد')}")

                # معالجة رموز الاستجابة المختلفة
                if response.status_code == 403:
                    print("تم رفض الوصول (403) - محاولة تجاوز الحماية...")
                    continue
                elif response.status_code == 429:
                    print("تم تجاوز حد الطلبات (429) - انتظار أطول...")
                    time.sleep(60)
                    continue
                elif response.status_code == 503:
                    print("الخدمة غير متاحة (503) - انتظار...")
                    time.sleep(30)
                    continue
                elif response.status_code != 200:
                    print(f"رمز استجابة غير متوقع: {response.status_code}")
                    continue

                # التحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' not in content_type:
                    print(f"نوع محتوى غير متوقع: {content_type}")
                    continue

                # معالجة التشفير
                if response.encoding:
                    response.encoding = response.apparent_encoding or 'utf-8'

                content = response.text
                print(f"حجم المحتوى: {len(content)} حرف")

                # التحقق من وجود محتوى كافي
                if len(content) < 2000:
                    print(f"محتوى قليل جداً ({len(content)} حرف)")
                    continue

                # التحقق من علامات الحماية
                protection_keywords = [
                    'cloudflare', 'ddos protection', 'access denied', 
                    'blocked', 'security check', 'please wait',
                    'checking your browser', 'ray id'
                ]
                
                content_lower = content.lower()
                if any(keyword in content_lower for keyword in protection_keywords):
                    print("تم اكتشاف صفحة حماية - محاولة أخرى...")
                    continue

                # تحليل HTML
                soup = self.parse_html_safely(content)
                if not soup:
                    print("فشل في تحليل HTML")
                    continue

                # التحقق من جودة الصفحة
                if not self.validate_page_quality(soup):
                    print("جودة الصفحة غير مقبولة")
                    continue

                # حفظ للتشخيص
                if attempt == 0:
                    try:
                        with open('debug_enhanced_page.html', 'w', encoding='utf-8') as f:
                            f.write(content)
                        print("تم حفظ HTML للتشخيص")
                    except:
                        pass

                print("✅ تم جلب الصفحة بنجاح!")
                return soup

            except requests.exceptions.Timeout:
                print(f"انتهت المهلة الزمنية في المحاولة {attempt + 1}")
            except requests.exceptions.ConnectionError:
                print(f"خطأ في الاتصال في المحاولة {attempt + 1}")
            except requests.exceptions.RequestException as e:
                print(f"خطأ في الطلب في المحاولة {attempt + 1}: {e}")
            except Exception as e:
                print(f"خطأ عام في المحاولة {attempt + 1}: {e}")
                print(f"تفاصيل الخطأ: {traceback.format_exc()}")

        print(f"❌ فشل في جلب الصفحة بعد {max_retries} محاولات")
        return None

    def parse_html_safely(self, content: str) -> Optional[BeautifulSoup]:
        """تحليل HTML بأمان مع عدة parsers"""
        parsers = ['html.parser', 'lxml', 'html5lib']
        
        for parser in parsers:
            try:
                soup = BeautifulSoup(content, parser)
                print(f"نجح parser: {parser}")
                return soup
            except Exception as e:
                print(f"فشل parser {parser}: {str(e)[:100]}")
                continue
        
        print("فشل جميع parsers")
        return None

    def validate_page_quality(self, soup: BeautifulSoup) -> bool:
        """التحقق من جودة الصفحة"""
        # التحقق من وجود العناصر الأساسية
        title_element = soup.find('title')
        if not title_element:
            print("لا يوجد عنصر title")
            return False

        body_element = soup.find('body')
        if not body_element:
            print("لا يوجد عنصر body")
            return False

        # التحقق من المحتوى النصي
        page_text = soup.get_text()
        if len(page_text.strip()) < 1000:
            print(f"محتوى نصي قليل ({len(page_text.strip())} حرف)")
            return False

        # التحقق من العنوان
        title_text = title_element.get_text().lower()
        if 'mcpedl' not in title_text and 'minecraft' not in title_text:
            print(f"العنوان لا يحتوي على كلمات متوقعة: {title_text[:100]}")
            # لا نرفض الصفحة بسبب هذا فقط

        return True

    def close(self):
        """إغلاق الجلسة"""
        if self.session:
            self.session.close()

def test_enhanced_scraper():
    """اختبار المستخرج المحسن"""
    print("🧪 اختبار المستخرج المحسن...")
    
    scraper = EnhancedMCPEDLScraper()
    
    try:
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        soup = scraper.fetch_page_enhanced(test_url)
        
        if soup:
            print("✅ نجح الاختبار!")
            
            # استخراج بعض البيانات الأساسية
            title = soup.find('title')
            if title:
                print(f"العنوان: {title.get_text()[:100]}...")
            
            h1_tags = soup.find_all('h1')
            if h1_tags:
                print(f"عناوين H1: {[h1.get_text()[:50] for h1 in h1_tags[:3]]}")
            
            return True
        else:
            print("❌ فشل الاختبار")
            return False
            
    finally:
        scraper.close()

if __name__ == "__main__":
    test_enhanced_scraper()
