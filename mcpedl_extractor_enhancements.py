# -*- coding: utf-8 -*-
"""
تحسينات إضافية لمستخرج MCPEDL
Additional Enhancements for MCPEDL Extractor
"""

import re
import json
import time
from typing import List, Dict, Optional, Any
from urllib.parse import urlparse, urljoin

class MCPEDLExtractorEnhancements:
    """تحسينات إضافية لمستخرج MCPEDL"""
    
    def __init__(self):
        """تهيئة التحسينات"""
        
        # أنماط متقدمة لتجنب الصور غير المرغوبة
        self.advanced_avoid_patterns = {
            'user_content': [
                'gravatar.com',
                'secure.gravatar',
                '/avatar',
                'profile_pic',
                'user_avatar',
                'author-photo',
                'comment-avatar',
                'user-image'
            ],
            'suggested_content': [
                'related-posts',
                'suggested-mods',
                'other-mods',
                'recommended',
                'similar-addons',
                'you-might-like',
                'more-from-author',
                'popular-mods',
                'trending-addons'
            ],
            'advertisement': [
                'ads',
                'advertisement',
                'banner',
                'sponsor',
                'promo',
                'affiliate'
            ],
            'ui_elements': [
                'button',
                'icon',
                'logo',
                'header',
                'footer',
                'navigation',
                'menu'
            ]
        }
        
        # أنماط متقدمة لمواقع التواصل الاجتماعي
        self.social_patterns = {
            'youtube': {
                'domains': ['youtube.com', 'youtu.be'],
                'patterns': [
                    r'youtube\.com/c/[\w-]+',
                    r'youtube\.com/channel/[\w-]+',
                    r'youtube\.com/@[\w-]+',
                    r'youtu\.be/[\w-]+'
                ]
            },
            'discord': {
                'domains': ['discord.gg', 'discord.com'],
                'patterns': [
                    r'discord\.gg/[\w-]+',
                    r'discord\.com/invite/[\w-]+'
                ]
            },
            'twitter': {
                'domains': ['twitter.com', 'x.com'],
                'patterns': [
                    r'twitter\.com/[\w-]+',
                    r'x\.com/[\w-]+'
                ]
            },
            'github': {
                'domains': ['github.com'],
                'patterns': [
                    r'github\.com/[\w-]+/[\w-]+',
                    r'github\.com/[\w-]+'
                ]
            }
        }
        
        # أنماط روابط المشاركة لتجنبها
        self.share_link_patterns = [
            'facebook.com/sharer',
            'twitter.com/intent/tweet',
            'pinterest.com/pin/create',
            'reddit.com/submit',
            'tumblr.com/share',
            'vk.com/share',
            'telegram.me/share',
            'whatsapp.com/send',
            'mcpedl.com/share',
            'sharer.php',
            'share.php',
            'share?url=',
            'intent/tweet',
            'pin/create'
        ]

    def advanced_image_filter(self, image_urls: List[str], mod_name: str = "") -> List[str]:
        """فلترة متقدمة للصور"""
        filtered_images = []
        
        for url in image_urls:
            if self._is_valid_mod_image_advanced(url, mod_name):
                filtered_images.append(url)
        
        # ترتيب الصور حسب الأولوية
        return self._prioritize_images(filtered_images)

    def _is_valid_mod_image_advanced(self, url: str, mod_name: str = "") -> bool:
        """فحص متقدم لصحة صورة المود"""
        url_lower = url.lower()
        
        # فحص جميع أنماط التجنب
        for category, patterns in self.advanced_avoid_patterns.items():
            for pattern in patterns:
                if pattern in url_lower:
                    print(f"❌ تم رفض صورة ({category}): {url[:50]}...")
                    return False
        
        # فحص خاص لصور المودات المقترحة
        if self._is_suggested_mod_image(url, mod_name):
            print(f"❌ تم رفض صورة مود مقترح: {url[:50]}...")
            return False
        
        # فحص الامتداد
        if not self._has_valid_image_extension(url):
            return False
        
        return True

    def _is_suggested_mod_image(self, url: str, mod_name: str) -> bool:
        """فحص ما إذا كانت الصورة لمود مقترح"""
        url_lower = url.lower()

        # أنماط صور المودات المقترحة (أكثر تحديداً)
        suggested_patterns = [
            'related-posts',
            'suggested-mods',
            'recommended-addons',
            'similar-mods',
            'other-addons',
            'more-from-author'
        ]

        # فحص الأنماط في الرابط
        for pattern in suggested_patterns:
            if pattern in url_lower:
                return True

        # فحص خاص للصور الفارغة أو العامة
        if any(pattern in url_lower for pattern in ['empty.png', 'placeholder', 'default.jpg', 'arrow.svg']):
            return True

        # تخفيف فحص اسم المود - فقط للصور الواضحة غير المرتبطة
        if mod_name and len(mod_name) > 10:  # فقط للأسماء الطويلة
            mod_name_clean = re.sub(r'[^\w\s]', '', mod_name.lower())
            mod_words = [word for word in mod_name_clean.split() if len(word) > 3]  # كلمات أطول من 3 أحرف

            # إذا لم يحتوي الرابط على أي كلمة مهمة من اسم المود
            if len(mod_words) > 2:  # فقط إذا كان هناك أكثر من كلمتين مهمتين
                matches = sum(1 for word in mod_words if word in url_lower)
                if matches == 0 and 'forgecdn' not in url_lower:  # استثناء صور ForgeCD
                    return True

        return False

    def _has_valid_image_extension(self, url: str) -> bool:
        """فحص امتداد الصورة"""
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()
        
        # فحص الامتداد في الرابط
        for ext in valid_extensions:
            if ext in url_lower:
                return True
        
        # فحص المصادر الموثوقة (حتى بدون امتداد واضح)
        trusted_domains = ['forgecdn.net', 'media.forgecdn.net', 'r2.mcpedl.com']
        for domain in trusted_domains:
            if domain in url_lower:
                return True
        
        return False

    def _prioritize_images(self, image_urls: List[str]) -> List[str]:
        """ترتيب الصور حسب الأولوية"""
        prioritized = []
        
        # أولوية عالية: صور ForgeCD
        forgecd_images = [url for url in image_urls if 'forgecdn.net' in url.lower()]
        prioritized.extend(forgecd_images)
        
        # أولوية متوسطة: صور MCPEDL الرسمية
        mcpedl_images = [url for url in image_urls if 'r2.mcpedl.com' in url.lower() and url not in prioritized]
        prioritized.extend(mcpedl_images)
        
        # باقي الصور
        other_images = [url for url in image_urls if url not in prioritized]
        prioritized.extend(other_images)
        
        return prioritized

    def advanced_social_filter(self, social_links: List[Dict[str, str]], mod_name: str = "", creator_name: str = "") -> List[str]:
        """فلترة متقدمة لروابط التواصل الاجتماعي"""
        filtered_links = []
        
        for link_data in social_links:
            url = link_data.get('url', '')
            
            if self._is_valid_creator_social_link(url, link_data, mod_name, creator_name):
                filtered_links.append(url)
        
        # إزالة التكرارات وترتيب حسب الأولوية
        return self._prioritize_social_links(list(set(filtered_links)))

    def _is_valid_creator_social_link(self, url: str, link_data: Dict[str, str], mod_name: str, creator_name: str) -> bool:
        """فحص صحة رابط التواصل الاجتماعي للمطور"""
        url_lower = url.lower()
        
        # رفض روابط المشاركة
        for pattern in self.share_link_patterns:
            if pattern in url_lower:
                print(f"❌ تم رفض رابط مشاركة: {url[:50]}...")
                return False
        
        # فحص السياق
        context = link_data.get('context', '').lower()
        link_text = link_data.get('link_text', '').lower()
        
        # مؤشرات إيجابية
        positive_indicators = [
            'author', 'creator', 'developer', 'made by', 'created by',
            'follow', 'subscribe', 'contact', 'social', 'find me',
            'مطور', 'صانع', 'منشئ', 'تابعني', 'تواصل'
        ]
        
        # مؤشرات سلبية
        negative_indicators = [
            'share', 'like this', 'tweet this', 'pin this',
            'شارك', 'اعجب', 'غرد'
        ]
        
        # فحص المؤشرات في السياق والنص
        full_text = f"{context} {link_text}"
        
        has_positive = any(indicator in full_text for indicator in positive_indicators)
        has_negative = any(indicator in full_text for indicator in negative_indicators)
        
        if has_negative:
            return False
        
        # إذا كان هناك مؤشر إيجابي، قبول الرابط
        if has_positive:
            return True
        
        # فحص إضافي لاسم المطور
        if creator_name and len(creator_name) > 3:
            creator_words = creator_name.lower().split()
            if any(word in url_lower for word in creator_words if len(word) > 3):
                return True
        
        # قبول الروابط من المنصات المعروفة إذا لم تكن روابط مشاركة
        known_platforms = ['youtube.com', 'discord.gg', 'github.com', 'twitter.com', 'x.com']
        if any(platform in url_lower for platform in known_platforms):
            return True
        
        return False

    def _prioritize_social_links(self, social_links: List[str]) -> List[str]:
        """ترتيب روابط التواصل حسب الأولوية"""
        prioritized = []
        
        # ترتيب الأولوية
        priority_order = ['youtube.com', 'discord.gg', 'github.com', 'twitter.com', 'x.com']
        
        for platform in priority_order:
            platform_links = [link for link in social_links if platform in link.lower()]
            prioritized.extend(platform_links)
        
        # إضافة باقي الروابط
        other_links = [link for link in social_links if link not in prioritized]
        prioritized.extend(other_links)
        
        return prioritized[:5]  # حد أقصى 5 روابط

    def extract_social_mentions_advanced(self, html_content: str) -> List[Dict[str, str]]:
        """استخراج متقدم لإشارات التواصل الاجتماعي من النص"""
        mentions = []
        
        # أنماط متقدمة للبحث
        advanced_patterns = {
            'YouTube': [
                r'youtube\.com/c/[\w-]+',
                r'youtube\.com/channel/[\w-]+',
                r'youtube\.com/@[\w-]+',
                r'youtu\.be/[\w-]+'
            ],
            'Discord': [
                r'discord\.gg/[\w-]+',
                r'discord\.com/invite/[\w-]+',
                r'discord server:?\s*[\w-]+'
            ],
            'Twitter': [
                r'twitter\.com/[\w-]+',
                r'x\.com/[\w-]+',
                r'@[\w-]{1,15}(?=\s|$|[^\w])'
            ],
            'GitHub': [
                r'github\.com/[\w-]+/[\w-]+',
                r'github\.com/[\w-]+'
            ],
            'Telegram': [
                r't\.me/[\w-]+',
                r'telegram\.me/[\w-]+'
            ]
        }
        
        for platform, patterns in advanced_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    mention_text = match.group(0)
                    
                    # تحويل إلى رابط كامل
                    if mention_text.startswith('@'):
                        url = f"https://twitter.com/{mention_text[1:]}"
                    elif not mention_text.startswith('http'):
                        url = f"https://{mention_text}"
                    else:
                        url = mention_text
                    
                    mentions.append({
                        'url': url,
                        'platform': platform,
                        'domain': urlparse(url).netloc,
                        'link_text': mention_text,
                        'context': 'extracted from text pattern',
                        'parent_element': ''
                    })
        
        return mentions

    def validate_extraction_quality(self, mod_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم جودة الاستخراج"""
        quality_report = {
            'overall_score': 0,
            'images_quality': 0,
            'social_quality': 0,
            'issues': [],
            'recommendations': []
        }
        
        # تقييم جودة الصور
        images = mod_data.get('image_urls', [])
        if images:
            forgecd_count = sum(1 for img in images if 'forgecdn.net' in img.lower())
            suspicious_count = sum(1 for img in images if any(pattern in img.lower() 
                                 for pattern in ['gravatar', 'avatar', 'profile']))
            
            images_score = min(100, (len(images) * 20) + (forgecd_count * 10) - (suspicious_count * 20))
            quality_report['images_quality'] = max(0, images_score)
            
            if suspicious_count > 0:
                quality_report['issues'].append(f"تم اكتشاف {suspicious_count} صور مشبوهة")
            
            if len(images) < 3:
                quality_report['recommendations'].append("يُنصح بالحصول على المزيد من الصور")
        
        # تقييم جودة روابط التواصل
        social_links = mod_data.get('creator_social_channels', [])
        if social_links:
            share_count = sum(1 for link in social_links if any(pattern in link.lower() 
                            for pattern in self.share_link_patterns))
            
            social_score = min(100, (len(social_links) * 25) - (share_count * 30))
            quality_report['social_quality'] = max(0, social_score)
            
            if share_count > 0:
                quality_report['issues'].append(f"تم اكتشاف {share_count} رابط مشاركة مشبوه")
        
        # النتيجة الإجمالية
        quality_report['overall_score'] = (quality_report['images_quality'] + quality_report['social_quality']) / 2
        
        return quality_report
