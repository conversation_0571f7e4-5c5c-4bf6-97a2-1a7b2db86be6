# -*- coding: utf-8 -*-
"""
وحدة استخراج بيانات المودات من موقع mcpedl.com - إصدار محسن
تم إصلاح مشاكل استخراج الصور وتجنب الصور المقترحة
"""

import requests
import time
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any

# محاولة استيراد cloudscraper لتجاوز حماية Cloudflare
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
    print("cloudscraper متوفر - سيتم استخدامه لتجاوز الحماية")
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("cloudscraper غير متوفر - سيتم استخدام requests العادي")

# محاولة استيراد فلتر Gemini الذكي
try:
    from gemini_image_filter import GeminiImageFilter
    GEMINI_IMAGE_FILTER_AVAILABLE = True
    print("Gemini Image Filter متوفر للفلترة الذكية")
except ImportError:
    GEMINI_IMAGE_FILTER_AVAILABLE = False
    print("Gemini Image Filter غير متوفر - سيتم استخدام الفلترة التقليدية")

class MCPEDLScraperFixed:
    """كلاس استخراج بيانات المودات من mcpedl.com - إصدار محسن"""

    def __init__(self):
        # استخدام cloudscraper إذا كان متوفراً، وإلا استخدام requests العادي
        if CLOUDSCRAPER_AVAILABLE:
            self.session = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
            print("تم إنشاء جلسة cloudscraper")
        else:
            self.session = requests.Session()
            print("تم إنشاء جلسة requests عادية")

        # ترويسات محسنة لتجاوز الحماية
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # إعدادات إضافية للجلسة
        self.session.max_redirects = 10

        # قائمة User Agents للتناوب
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        # محددات CSS محسنة لموقع mcpedl.com
        self.selectors = {
            'title': 'h1.entry-title, h1.post-title, .post-header h1, h1, .entry-header h1, .page-title',
            'description': '.entry-content p, .post-content p, .content p, article p, .post-body p, .entry-summary',
            'category': '.breadcrumbs a:last-of-type, .category-link, .post-categories a, .breadcrumb-item a, nav.breadcrumb a',
            # تحديث محدد الصورة الرئيسية لاستهداف الصور في المحتوى وليس المقترحات
            'main_content_images': '.entry-content img, .post-content img, article img, .content img',
            'version': '.supported-versions, .minecraft-version, .version-info, .compatibility, .mcpe-version, .version-support',
            'download_link': '.download-link, .btn-download, a[href*="download"], .download-button, .dl-link, a[href*="mediafire"], a[href*="drive.google"]',
            'file_size': '.file-size, .download-size, .size-info, .filesize',
        }

    def extract_main_mod_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """استخراج الصورة الرئيسية للمود مع تجنب الصور المقترحة"""
        
        # البحث عن الصورة الرئيسية في المحتوى
        content_images = soup.select(self.selectors['main_content_images'])
        
        for img in content_images:
            # تجنب الصور الموجودة في روابط (مقترحات)
            parent_link = img.find_parent('a')
            if parent_link:
                # فحص إذا كان الرابط يؤدي لصفحة مود آخر
                href = parent_link.get('href', '')
                if href and ('mcpedl.com' in href or href.startswith('/')):
                    continue  # تجاهل هذه الصورة لأنها مقترحة
            
            # تجنب الصور في قسم "You may also like"
            parent_text = img.find_parent(string=re.compile(r'you may also like|related|similar', re.I))
            if parent_text:
                continue
            
            # فحص النص المحيط للتأكد من أنها ليست مقترحة
            parent_div = img.find_parent(['div', 'section', 'article'])
            if parent_div:
                div_text = parent_div.get_text().lower()
                if any(keyword in div_text for keyword in ['you may also like', 'related posts', 'similar mods', 'recommendations']):
                    continue
            
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                url = urljoin(base_url, src)
                if self.validate_image_url(url):
                    print(f"✅ تم العثور على الصورة الرئيسية: {url}")
                    return url
        
        # البحث عن الصورة المخفية في meta tags
        meta_image = soup.find('meta', property='og:image')
        if meta_image and meta_image.get('content'):
            url = urljoin(base_url, meta_image['content'])
            if self.validate_image_url(url):
                print(f"✅ تم العثور على الصورة من meta tag: {url}")
                return url
        
        # البحث في script tags للصور المخفية
        script_tags = soup.find_all('script')
        for script in script_tags:
            if script.string:
                # البحث عن عناوين URL للصور في JavaScript
                image_urls = re.findall(r'https?://[^\s<>"]+\.(?:jpg|jpeg|png|gif|webp)', script.string)
                for url in image_urls:
                    if self.validate_image_url(url) and 'mcpedl' in url:
                        print(f"✅ تم العثور على صورة مخفية في JavaScript: {url}")
                        return url
        
        print("⚠️ لم يتم العثور على الصورة الرئيسية")
        return None

    def extract_images_safely(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج الصور مع تجنب الصور المقترحة"""
        image_urls = []

        # الحصول على الصورة الرئيسية
        main_image = self.extract_main_mod_image(soup, base_url)
        if main_image:
            image_urls.append(main_image)

        # البحث عن صور إضافية في المحتوى (غير مقترحة)
        content_images = soup.select(self.selectors['main_content_images'])
        
        for img in content_images:
            # تجنب الصور في الروابط
            if img.find_parent('a'):
                continue
                
            # تجنب الصور الصغيرة (أيقونات)
            width = img.get('width')
            height = img.get('height')
            if width and height:
                try:
                    if int(width) < 100 or int(height) < 100:
                        continue
                except ValueError:
                    pass
            
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                url = urljoin(base_url, src)
                if self.validate_image_url(url) and url not in image_urls:
                    # فحص إضافي للتأكد من أنها ليست صورة مقترحة
                    img_alt = img.get('alt', '').lower()
                    img_title = img.get('title', '').lower()
                    
                    # تجنب الصور التي تحتوي على كلمات مؤشرة للمقترحات
                    skip_keywords = ['related', 'similar', 'recommend', 'also like', 'other mods']
                    if any(keyword in img_alt or keyword in img_title for keyword in skip_keywords):
                        continue
                    
                    image_urls.append(url)
                    if len(image_urls) >= 5:  # حد أقصى 5 صور
                        break

        return image_urls

    def validate_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()

        # التحقق من امتداد الصورة
        has_extension = any(ext in url_lower for ext in image_extensions)
        
        # التحقق من أن الرابط لا يحتوي على كلمات مؤشرة للمقترحات
        avoid_keywords = ['thumbnail', 'thumb', 'icon', 'avatar', 'logo', 'banner']
        has_avoid_keyword = any(keyword in url_lower for keyword in avoid_keywords)
        
        return has_extension and not has_avoid_keyword

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        title_selectors = self.selectors['title'].split(', ')
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text().strip()
                # تنظيف العنوان من النصوص الإضافية
                title = re.sub(r'\s*-\s*MCPEDL.*$', '', title, flags=re.IGNORECASE)
                title = re.sub(r'\s*\|\s*.*$', '', title)
                return title
        
        return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        desc_selectors = self.selectors['description'].split(', ')
        description_parts = []
        
        for selector in desc_selectors:
            elements = soup.select(selector)
            for element in elements[:3]:  # أول 3 فقرات فقط
                text = element.get_text().strip()
                if text and len(text) > 20:  # تجاهل النصوص القصيرة
                    description_parts.append(text)
        
        description = ' '.join(description_parts)
        
        # تنظيف الوصف من المعلومات غير المرغوبة
        description = self.clean_description(description)
        
        return description

    def clean_description(self, text: str) -> str:
        """تنظيف الوصف من المعلومات غير المرغوبة"""
        if not text:
            return ""
        
        # أنماط النصوص التي يجب إزالتها
        patterns_to_remove = [
            r'Published on.*?\d{4}',
            r'Updated on.*?\d{4}',
            r'Downloads?:\s*\d+',
            r'File size:\s*[\d\.]+\s*MB',
            r'Supported Minecraft versions:.*',
            r'Installation.*',
            r'Download.*',
            r'Join our Discord.*',
            r'You may also like.*',
            r'Related posts.*',
            r'Similar mods.*'
        ]
        
        for pattern in patterns_to_remove:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # تنظيف المسافات الزائدة
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        category_selectors = self.selectors['category'].split(', ')
        
        for selector in category_selectors:
            element = soup.select_one(selector)
            if element:
                category = element.get_text().strip()
                # تطبيع أسماء الفئات
                if 'addon' in category.lower():
                    return 'Addons'
                elif 'texture' in category.lower() or 'pack' in category.lower():
                    return 'Texture Pack'
                elif 'shader' in category.lower():
                    return 'Shaders'
                return category
        
        return "Addons"  # افتراضي

    def scrape_mod_data(self, url: str) -> Dict[str, Any]:
        """استخراج بيانات المود من الرابط"""
        try:
            print(f"🔄 بدء استخراج البيانات من: {url}")
            
            # طلب الصفحة
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # تحليل HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # استخراج البيانات
            title = self.extract_title(soup)
            description = self.extract_description(soup)
            category = self.extract_category(soup)
            images = self.extract_images_safely(soup, url)
            
            mod_data = {
                'name': title,
                'description': description,
                'category': category,
                'image_urls': images,
                'source_url': url,
                'extracted_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            print(f"✅ تم استخراج البيانات بنجاح:")
            print(f"   العنوان: {title}")
            print(f"   الفئة: {category}")
            print(f"   عدد الصور: {len(images)}")
            
            return mod_data
            
        except Exception as e:
            print(f"❌ خطأ في استخراج البيانات: {str(e)}")
            return {}

def scrape_mcpedl_mod(url: str) -> Dict[str, Any]:
    """دالة مساعدة لاستخراج بيانات المود"""
    scraper = MCPEDLScraperFixed()
    return scraper.scrape_mod_data(url)

# مثال للاستخدام
if __name__ == "__main__":
    test_url = "https://mcpedl.com/take-a-seat/"
    result = scrape_mcpedl_mod(test_url)
    print(json.dumps(result, indent=2, ensure_ascii=False))
