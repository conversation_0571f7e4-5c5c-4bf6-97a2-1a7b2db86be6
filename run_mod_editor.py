# -*- coding: utf-8 -*-
"""
مشغل أداة تخصيص وإصلاح المودات
Mod Editor and Repair Tool Launcher
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import json
from datetime import datetime

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    required_files = [
        "enhanced_mod_editor.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
            print(f"   ❌ {file}")
        else:
            print(f"   ✅ {file}")
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   • {file}")
        return False
    
    # فحص المكتبات المطلوبة
    print("\n📚 فحص المكتبات المطلوبة...")
    
    required_modules = [
        "tkinter",
        "PIL",
        "requests"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"   ❌ {module}")
    
    if missing_modules:
        print("❌ مكتبات مفقودة:")
        for module in missing_modules:
            print(f"   • {module}")
        
        print("\n💡 لتثبيت المكتبات المفقودة:")
        if "PIL" in missing_modules:
            print("   pip install Pillow")
        if "requests" in missing_modules:
            print("   pip install requests")
        
        return False
    
    return True

def check_data_files():
    """فحص ملفات البيانات"""
    print("\n📁 فحص ملفات البيانات...")
    
    data_files = [
        "extracted_mods.json",
        "batch_extracted_mods.json", 
        "published_mods.json",
        "problematic_mods.json"
    ]
    
    available_files = []
    for file in data_files:
        if os.path.exists(file):
            available_files.append(file)
            print(f"   ✅ {file}")
        else:
            print(f"   ⚠️ {file} (غير موجود)")
    
    if not available_files:
        print("⚠️ لا توجد ملفات بيانات. يمكنك إنشاؤها لاحقاً.")
    else:
        print(f"✅ تم العثور على {len(available_files)} ملف بيانات")
    
    return len(available_files) > 0

def check_gemini_config():
    """فحص إعدادات Gemini"""
    print("\n🤖 فحص إعدادات Gemini...")
    
    config_files = ["enhanced_batch_config.json", "config.json"]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                keys = config.get("gemini_api_keys", [])
                valid_keys = [key for key in keys if key and not key.startswith("أدخل")]
                
                if valid_keys:
                    print(f"   ✅ تم العثور على {len(valid_keys)} مفتاح Gemini في {config_file}")
                    return True
                else:
                    print(f"   ⚠️ {config_file} موجود لكن لا يحتوي على مفاتيح صالحة")
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {config_file}: {e}")
    
    print("⚠️ لم يتم العثور على مفاتيح Gemini صالحة")
    print("💡 يمكنك إضافة المفاتيح من داخل الأداة")
    return False

def create_sample_data():
    """إنشاء بيانات نموذجية للاختبار"""
    print("\n📝 إنشاء بيانات نموذجية...")
    
    sample_mod = {
        "name": "مود تجريبي",
        "version": "1.0.0",
        "category": "Addons",
        "creator_name": "منشئ تجريبي",
        "description_ar": "هذا وصف تجريبي للمود باللغة العربية. يمكنك تعديله واختبار الأداة.",
        "description_en": "This is a sample mod description in English. You can edit it and test the tool.",
        "telegram_description_ar": "وصف تليجرام قصير باللغة العربية",
        "telegram_description_en": "Short Telegram description in English",
        "features": [
            "ميزة تجريبية 1",
            "ميزة تجريبية 2", 
            "ميزة تجريبية 3"
        ],
        "download_url": "https://example.com/download",
        "source_url": "https://example.com/source",
        "primary_image_url": "https://example.com/image1.jpg",
        "image_urls": [
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        ],
        "extraction_timestamp": datetime.now().isoformat(),
        "extraction_method": "sample"
    }
    
    sample_data = {
        "extraction_date": datetime.now().isoformat(),
        "total_mods": 1,
        "mods": [sample_mod]
    }
    
    try:
        with open("sample_mods.json", 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False)
        
        print("   ✅ تم إنشاء ملف sample_mods.json")
        print("   💡 يمكنك فتح هذا الملف في الأداة للاختبار")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء البيانات النموذجية: {e}")
        return False

def show_startup_info():
    """عرض معلومات البدء"""
    print("\n" + "="*60)
    print("🛠️ أداة تخصيص وإصلاح المودات")
    print("Enhanced Mod Editor and Repair Tool")
    print("="*60)
    print("\n📋 الميزات المتاحة:")
    print("   • تحرير معلومات المودات الأساسية")
    print("   • إصلاح الأوصاف باستخدام Gemini AI")
    print("   • إدارة صور المودات")
    print("   • توليد أوصاف التليجرام")
    print("   • معاينة ونشر المودات")
    print("   • ترجمة الأوصاف")
    print("   • إصلاح شامل للمودات")
    print("\n🚀 بدء تشغيل الأداة...")

def main():
    """الدالة الرئيسية"""
    try:
        show_startup_info()
        
        # فحص المتطلبات
        if not check_requirements():
            print("\n❌ فشل في فحص المتطلبات. لا يمكن تشغيل الأداة.")
            input("اضغط Enter للخروج...")
            return False
        
        # فحص ملفات البيانات
        has_data = check_data_files()
        
        # فحص إعدادات Gemini
        has_gemini = check_gemini_config()
        
        # إنشاء بيانات نموذجية إذا لم توجد
        if not has_data:
            create_sample_data()
        
        print("\n✅ جميع الفحوصات اكتملت بنجاح!")
        
        if not has_gemini:
            print("\n⚠️ تحذير: لم يتم العثور على مفاتيح Gemini")
            print("   بعض الميزات المتقدمة قد لا تعمل")
            print("   يمكنك إضافة المفاتيح من إعدادات الأداة")
        
        print("\n🎮 تشغيل أداة التخصيص...")
        
        # تشغيل الأداة
        from enhanced_mod_editor import EnhancedModEditor
        
        app = EnhancedModEditor()
        
        # تحميل ملف نموذجي إذا لم توجد بيانات
        if not has_data and os.path.exists("sample_mods.json"):
            def load_sample():
                try:
                    with open("sample_mods.json", 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    from enhanced_mod_editor import ModData
                    app.mods_data = [ModData(mod) for mod in data["mods"]]
                    app.current_mod_index = 0
                    
                    if app.mods_data:
                        app.load_current_mod()
                        app.update_navigation()
                        app.update_status("تم تحميل البيانات النموذجية")
                        
                except Exception as e:
                    app.update_status(f"خطأ في تحميل البيانات النموذجية: {e}")
            
            # تحميل البيانات النموذجية بعد تهيئة الواجهة
            app.window.after(1000, load_sample)
        
        app.run()
        
        print("\n👋 تم إغلاق الأداة")
        return True
        
    except ImportError as e:
        error_msg = f"خطأ في الاستيراد: {e}"
        print(f"\n❌ {error_msg}")
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", 
                           f"{error_msg}\n\nتأكد من وجود جميع الملفات المطلوبة")
        return False
        
    except Exception as e:
        error_msg = f"خطأ عام: {e}"
        print(f"\n❌ {error_msg}")
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n💡 نصائح لحل المشاكل:")
        print("   1. تأكد من تثبيت Python 3.8 أو أحدث")
        print("   2. تأكد من تثبيت المكتبات المطلوبة:")
        print("      pip install Pillow requests")
        print("   3. تأكد من وجود ملف enhanced_mod_editor.py")
        print("   4. تأكد من وجود ملفات البيانات أو استخدم البيانات النموذجية")
        
        input("\nاضغط Enter للخروج...")
