# -*- coding: utf-8 -*-
"""
أداة تخصيص وإصلاح المودات المستخرجة
Enhanced Mod Editor and Repair Tool
"""

import os
import json
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
from PIL import Image, ImageTk
import requests
from io import BytesIO
import threading
from datetime import datetime
import re
from urllib.parse import urlparse

# استيراد الوحدات المطلوبة
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

try:
    from enhanced_batch_processor import GeminiKeyManager
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSOR_AVAILABLE = False

class ModData:
    """كلاس لتمثيل بيانات المود"""

    def __init__(self, data_dict=None):
        self.data = data_dict or {}
        self.original_data = self.data.copy()
        self.modified = False

    def get(self, key, default=None):
        return self.data.get(key, default)

    def set(self, key, value):
        if self.data.get(key) != value:
            self.data[key] = value
            self.modified = True

    def get_images(self):
        """الحصول على قائمة الصور"""
        images = []

        # الصورة الرئيسية
        primary_image = self.get('primary_image_url')
        if primary_image:
            images.append({"type": "primary", "url": primary_image})

        # الصور الإضافية
        other_images = self.get('image_urls', [])
        if isinstance(other_images, list):
            for img in other_images:
                if img and img != primary_image:
                    images.append({"type": "additional", "url": img})

        return images

    def add_image(self, image_url, image_type="additional"):
        """إضافة صورة جديدة"""
        if image_type == "primary":
            self.set('primary_image_url', image_url)
        else:
            other_images = self.get('image_urls', [])
            if not isinstance(other_images, list):
                other_images = []

            if image_url not in other_images:
                other_images.append(image_url)
                self.set('image_urls', other_images)

    def remove_image(self, image_url):
        """إزالة صورة"""
        # إزالة من الصورة الرئيسية
        if self.get('primary_image_url') == image_url:
            self.set('primary_image_url', '')

        # إزالة من الصور الإضافية
        other_images = self.get('image_urls', [])
        if isinstance(other_images, list) and image_url in other_images:
            other_images.remove(image_url)
            self.set('image_urls', other_images)

    def to_dict(self):
        """تحويل إلى قاموس"""
        return self.data.copy()

class ImageManager:
    """مدير الصور للمود"""

    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.image_cache = {}
        self.thumbnail_size = (150, 150)

    def load_image_from_url(self, url, callback=None):
        """تحميل صورة من رابط"""
        def load():
            try:
                if url in self.image_cache:
                    if callback:
                        callback(self.image_cache[url], None)
                    return

                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                image = Image.open(BytesIO(response.content))
                image.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)

                photo = ImageTk.PhotoImage(image)
                self.image_cache[url] = photo

                if callback:
                    self.parent.after(0, lambda: callback(photo, None))

            except Exception as e:
                if callback:
                    self.parent.after(0, lambda: callback(None, str(e)))

        threading.Thread(target=load, daemon=True).start()

    def load_image_from_file(self, file_path):
        """تحميل صورة من ملف محلي"""
        try:
            image = Image.open(file_path)
            image.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
            return ImageTk.PhotoImage(image)
        except Exception as e:
            return None

class GeminiDescriptionFixer:
    """مصحح الأوصاف باستخدام Gemini"""

    def __init__(self, gemini_keys):
        self.gemini_manager = None
        if ENHANCED_PROCESSOR_AVAILABLE and gemini_keys:
            self.gemini_manager = GeminiKeyManager(gemini_keys)

    def fix_description(self, description, mod_name="", language="ar"):
        """إصلاح الوصف باستخدام Gemini"""
        if not self.gemini_manager:
            return None

        if language == "ar":
            prompt = f"""
قم بإصلاح وتحسين هذا الوصف العربي لمود ماين كرافت:

اسم المود: {mod_name}
الوصف الحالي: {description}

المطلوب:
1. إصلاح الأخطاء النحوية والإملائية
2. تحسين التنسيق والوضوح
3. إضافة معلومات مفيدة إذا كانت ناقصة
4. جعل الوصف جذاب ومفهوم
5. الحفاظ على المعنى الأصلي

أرجع الوصف المحسن فقط بدون أي تعليقات إضافية.
"""
        else:
            prompt = f"""
Fix and improve this English description for a Minecraft mod:

Mod Name: {mod_name}
Current Description: {description}

Requirements:
1. Fix grammar and spelling errors
2. Improve formatting and clarity
3. Add useful information if missing
4. Make the description attractive and understandable
5. Maintain the original meaning

Return only the improved description without any additional comments.
"""

        try:
            result = self.gemini_manager.generate_content(prompt)
            return result.strip() if result else None
        except Exception as e:
            print(f"خطأ في إصلاح الوصف: {e}")
            return None

class EnhancedModEditor:
    """أداة تخصيص وإصلاح المودات المحسنة"""

    def __init__(self, parent_window=None):
        self.parent = parent_window
        self.window = None
        self.current_mod = None
        self.mods_data = []
        self.current_mod_index = 0

        # مديرو النظام
        self.image_manager = None
        self.gemini_fixer = None

        # متغيرات الواجهة (سيتم تهيئتها في create_gui)
        self.mod_name_var = None
        self.mod_version_var = None
        self.mod_category_var = None
        self.mod_creator_var = None
        self.mod_download_url_var = None
        self.mod_source_url_var = None

        # عناصر الواجهة
        self.description_ar_text = None
        self.description_en_text = None
        self.telegram_ar_text = None
        self.telegram_en_text = None
        self.features_text = None
        self.images_frame = None
        self.navigation_label = None

        self.create_gui()
        self.load_gemini_keys()

    def create_gui(self):
        """إنشاء واجهة المستخدم"""
        if self.parent:
            self.window = tk.Toplevel(self.parent)
        else:
            self.window = tk.Tk()

        self.window.title("🛠️ أداة تخصيص وإصلاح المودات - Enhanced Mod Editor")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # تهيئة متغيرات الواجهة بعد إنشاء النافذة
        self.mod_name_var = tk.StringVar(master=self.window)
        self.mod_version_var = tk.StringVar(master=self.window)
        self.mod_category_var = tk.StringVar(master=self.window)
        self.mod_creator_var = tk.StringVar(master=self.window)
        self.mod_download_url_var = tk.StringVar(master=self.window)
        self.mod_source_url_var = tk.StringVar(master=self.window)

        # إنشاء مدير الصور
        self.image_manager = ImageManager(self.window)

        # إطار رئيسي مع تمرير
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # منطقة المحتوى الرئيسية
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب المعلومات الأساسية
        self.create_basic_info_tab()

        # تبويب الأوصاف
        self.create_descriptions_tab()

        # تبويب الصور
        self.create_images_tab()

        # تبويب الروابط والملفات
        self.create_links_tab()

        # تبويب المعاينة والنشر
        self.create_preview_tab()

        # شريط الحالة
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # أزرار التحكم في الملفات
        file_frame = ttk.LabelFrame(toolbar_frame, text="📁 إدارة الملفات")
        file_frame.pack(side=tk.LEFT, padx=(0, 10), pady=5, fill=tk.Y)

        ttk.Button(file_frame, text="📂 فتح ملف المودات",
                  command=self.load_mods_file).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(file_frame, text="💾 حفظ التغييرات",
                  command=self.save_changes).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(file_frame, text="📤 تصدير المود",
                  command=self.export_current_mod).pack(side=tk.LEFT, padx=5, pady=5)

        # أزرار التنقل
        nav_frame = ttk.LabelFrame(toolbar_frame, text="🧭 التنقل")
        nav_frame.pack(side=tk.LEFT, padx=(0, 10), pady=5, fill=tk.Y)

        ttk.Button(nav_frame, text="⬅️ السابق",
                  command=self.previous_mod).pack(side=tk.LEFT, padx=5, pady=5)

        self.navigation_label = ttk.Label(nav_frame, text="0 / 0")
        self.navigation_label.pack(side=tk.LEFT, padx=10, pady=5)

        ttk.Button(nav_frame, text="➡️ التالي",
                  command=self.next_mod).pack(side=tk.LEFT, padx=5, pady=5)

        # أزرار الأدوات
        tools_frame = ttk.LabelFrame(toolbar_frame, text="🔧 أدوات")
        tools_frame.pack(side=tk.LEFT, padx=(0, 10), pady=5, fill=tk.Y)

        ttk.Button(tools_frame, text="🤖 إصلاح بـ Gemini",
                  command=self.fix_with_gemini).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(tools_frame, text="🔄 إعادة تعيين",
                  command=self.reset_current_mod).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(tools_frame, text="⚙️ إعدادات",
                  command=self.open_settings).pack(side=tk.LEFT, padx=5, pady=5)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(basic_frame, text="📋 معلومات أساسية")

        # إطار التمرير
        canvas = tk.Canvas(basic_frame)
        scrollbar = ttk.Scrollbar(basic_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # معلومات المود الأساسية
        info_frame = ttk.LabelFrame(scrollable_frame, text="معلومات المود", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        # اسم المود
        ttk.Label(info_frame, text="اسم المود:").grid(row=0, column=0, sticky=tk.W, pady=5)
        name_entry = ttk.Entry(info_frame, textvariable=self.mod_name_var, width=50)
        name_entry.grid(row=0, column=1, columnspan=2, sticky=tk.EW, padx=(10, 0), pady=5)

        # إصدار المود
        ttk.Label(info_frame, text="الإصدار:").grid(row=1, column=0, sticky=tk.W, pady=5)
        version_entry = ttk.Entry(info_frame, textvariable=self.mod_version_var, width=20)
        version_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # فئة المود
        ttk.Label(info_frame, text="الفئة:").grid(row=1, column=2, sticky=tk.W, padx=(20, 0), pady=5)
        category_combo = ttk.Combobox(info_frame, textvariable=self.mod_category_var,
                                     values=["Addons", "Shaders", "Texture Pack", "Maps", "Skins"])
        category_combo.grid(row=1, column=3, sticky=tk.W, padx=(10, 0), pady=5)

        # منشئ المود
        ttk.Label(info_frame, text="المنشئ:").grid(row=2, column=0, sticky=tk.W, pady=5)
        creator_entry = ttk.Entry(info_frame, textvariable=self.mod_creator_var, width=30)
        creator_entry.grid(row=2, column=1, columnspan=2, sticky=tk.EW, padx=(10, 0), pady=5)

        # تكوين الأعمدة
        info_frame.grid_columnconfigure(1, weight=1)
        info_frame.grid_columnconfigure(3, weight=1)

        # إطار الميزات
        features_frame = ttk.LabelFrame(scrollable_frame, text="ميزات المود", padding=10)
        features_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(features_frame, text="قائمة الميزات (ميزة واحدة في كل سطر):").pack(anchor=tk.W, pady=(0, 5))

        self.features_text = scrolledtext.ScrolledText(features_frame, height=8, width=80)
        self.features_text.pack(fill=tk.BOTH, expand=True)

        # أزرار إدارة الميزات
        features_buttons_frame = ttk.Frame(features_frame)
        features_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(features_buttons_frame, text="🤖 توليد ميزات بـ Gemini",
                  command=self.generate_features_with_gemini).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(features_buttons_frame, text="🔄 إعادة تنسيق",
                  command=self.format_features).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(features_buttons_frame, text="📋 نسخ",
                  command=lambda: self.copy_text(self.features_text)).pack(side=tk.LEFT)

        # تعبئة الإطار
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_descriptions_tab(self):
        """إنشاء تبويب الأوصاف"""
        desc_frame = ttk.Frame(self.notebook)
        self.notebook.add(desc_frame, text="📝 الأوصاف")

        # إطار الأوصاف الرئيسية
        main_desc_frame = ttk.LabelFrame(desc_frame, text="الأوصاف الرئيسية", padding=10)
        main_desc_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار الوصف العربي
        ar_frame = ttk.LabelFrame(main_desc_frame, text="الوصف العربي", padding=5)
        ar_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.description_ar_text = scrolledtext.ScrolledText(ar_frame, height=8, width=80)
        self.description_ar_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # أزرار الوصف العربي
        ar_buttons_frame = ttk.Frame(ar_frame)
        ar_buttons_frame.pack(fill=tk.X)

        ttk.Button(ar_buttons_frame, text="🤖 إصلاح بـ Gemini",
                  command=lambda: self.fix_description_with_gemini("ar")).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(ar_buttons_frame, text="🔄 ترجمة من الإنجليزي",
                  command=self.translate_en_to_ar).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(ar_buttons_frame, text="📋 نسخ",
                  command=lambda: self.copy_text(self.description_ar_text)).pack(side=tk.LEFT)

        # إطار الوصف الإنجليزي
        en_frame = ttk.LabelFrame(main_desc_frame, text="الوصف الإنجليزي", padding=5)
        en_frame.pack(fill=tk.BOTH, expand=True)

        self.description_en_text = scrolledtext.ScrolledText(en_frame, height=8, width=80)
        self.description_en_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # أزرار الوصف الإنجليزي
        en_buttons_frame = ttk.Frame(en_frame)
        en_buttons_frame.pack(fill=tk.X)

        ttk.Button(en_buttons_frame, text="🤖 إصلاح بـ Gemini",
                  command=lambda: self.fix_description_with_gemini("en")).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(en_buttons_frame, text="🔄 ترجمة من العربي",
                  command=self.translate_ar_to_en).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(en_buttons_frame, text="📋 نسخ",
                  command=lambda: self.copy_text(self.description_en_text)).pack(side=tk.LEFT)

        # إطار أوصاف التليجرام
        telegram_frame = ttk.LabelFrame(desc_frame, text="أوصاف التليجرام", padding=10)
        telegram_frame.pack(fill=tk.X, padx=10, pady=10)

        # إطار التليجرام العربي
        telegram_ar_frame = ttk.Frame(telegram_frame)
        telegram_ar_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(telegram_ar_frame, text="تليجرام عربي (300-400 حرف):").pack(anchor=tk.W)
        self.telegram_ar_text = scrolledtext.ScrolledText(telegram_ar_frame, height=4, width=80)
        self.telegram_ar_text.pack(fill=tk.X, pady=(5, 5))

        # أزرار التليجرام العربي
        telegram_ar_buttons = ttk.Frame(telegram_ar_frame)
        telegram_ar_buttons.pack(fill=tk.X)

        ttk.Button(telegram_ar_buttons, text="🤖 توليد بـ Gemini",
                  command=lambda: self.generate_telegram_description("ar")).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(telegram_ar_buttons, text="📊 عدد الأحرف",
                  command=lambda: self.show_character_count(self.telegram_ar_text)).pack(side=tk.LEFT)

        # إطار التليجرام الإنجليزي
        telegram_en_frame = ttk.Frame(telegram_frame)
        telegram_en_frame.pack(fill=tk.X)

        ttk.Label(telegram_en_frame, text="تليجرام إنجليزي (300-400 حرف):").pack(anchor=tk.W)
        self.telegram_en_text = scrolledtext.ScrolledText(telegram_en_frame, height=4, width=80)
        self.telegram_en_text.pack(fill=tk.X, pady=(5, 5))

        # أزرار التليجرام الإنجليزي
        telegram_en_buttons = ttk.Frame(telegram_en_frame)
        telegram_en_buttons.pack(fill=tk.X)

        ttk.Button(telegram_en_buttons, text="🤖 توليد بـ Gemini",
                  command=lambda: self.generate_telegram_description("en")).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(telegram_en_buttons, text="📊 عدد الأحرف",
                  command=lambda: self.show_character_count(self.telegram_en_text)).pack(side=tk.LEFT)

    def create_images_tab(self):
        """إنشاء تبويب الصور"""
        images_frame = ttk.Frame(self.notebook)
        self.notebook.add(images_frame, text="🖼️ الصور")

        # شريط أدوات الصور
        images_toolbar = ttk.Frame(images_frame)
        images_toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(images_toolbar, text="📁 إضافة من ملف",
                  command=self.add_image_from_file).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(images_toolbar, text="🔗 إضافة من رابط",
                  command=self.add_image_from_url).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(images_toolbar, text="🔄 تحديث الصور",
                  command=self.refresh_images).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(images_toolbar, text="🗑️ حذف المحدد",
                  command=self.delete_selected_image).pack(side=tk.LEFT)

        # منطقة عرض الصور
        images_container = ttk.Frame(images_frame)
        images_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # إطار التمرير للصور
        images_canvas = tk.Canvas(images_container)
        images_scrollbar = ttk.Scrollbar(images_container, orient="vertical", command=images_canvas.yview)
        self.images_frame = ttk.Frame(images_canvas)

        self.images_frame.bind(
            "<Configure>",
            lambda e: images_canvas.configure(scrollregion=images_canvas.bbox("all"))
        )

        images_canvas.create_window((0, 0), window=self.images_frame, anchor="nw")
        images_canvas.configure(yscrollcommand=images_scrollbar.set)

        images_canvas.pack(side="left", fill="both", expand=True)
        images_scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        def on_mousewheel(event):
            images_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        images_canvas.bind("<MouseWheel>", on_mousewheel)

    def create_links_tab(self):
        """إنشاء تبويب الروابط والملفات"""
        links_frame = ttk.Frame(self.notebook)
        self.notebook.add(links_frame, text="🔗 روابط وملفات")

        # إطار الروابط
        links_container = ttk.LabelFrame(links_frame, text="روابط المود", padding=10)
        links_container.pack(fill=tk.X, padx=10, pady=10)

        # رابط التحميل
        ttk.Label(links_container, text="رابط تحميل المود:").grid(row=0, column=0, sticky=tk.W, pady=5)
        download_entry = ttk.Entry(links_container, textvariable=self.mod_download_url_var, width=60)
        download_entry.grid(row=0, column=1, sticky=tk.EW, padx=(10, 0), pady=5)

        ttk.Button(links_container, text="🔗 اختبار الرابط",
                  command=self.test_download_link).grid(row=0, column=2, padx=(10, 0), pady=5)

        # رابط المصدر
        ttk.Label(links_container, text="رابط الصفحة المصدر:").grid(row=1, column=0, sticky=tk.W, pady=5)
        source_entry = ttk.Entry(links_container, textvariable=self.mod_source_url_var, width=60)
        source_entry.grid(row=1, column=1, sticky=tk.EW, padx=(10, 0), pady=5)

        ttk.Button(links_container, text="🌐 فتح في المتصفح",
                  command=self.open_source_in_browser).grid(row=1, column=2, padx=(10, 0), pady=5)

        # تكوين الأعمدة
        links_container.grid_columnconfigure(1, weight=1)

        # إطار رفع ملف جديد
        upload_frame = ttk.LabelFrame(links_frame, text="رفع ملف مود جديد", padding=10)
        upload_frame.pack(fill=tk.X, padx=10, pady=10)

        upload_info_label = ttk.Label(upload_frame,
                                     text="يمكنك رفع ملف مود جديد ليحل محل الرابط الحالي")
        upload_info_label.pack(anchor=tk.W, pady=(0, 10))

        upload_buttons_frame = ttk.Frame(upload_frame)
        upload_buttons_frame.pack(fill=tk.X)

        ttk.Button(upload_buttons_frame, text="📁 اختيار ملف محلي",
                  command=self.upload_local_mod_file).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(upload_buttons_frame, text="🔗 رفع من رابط",
                  command=self.upload_mod_from_url).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(upload_buttons_frame, text="📊 فحص الملف الحالي",
                  command=self.analyze_current_mod_file).pack(side=tk.LEFT)

        # إطار معلومات الملف
        file_info_frame = ttk.LabelFrame(links_frame, text="معلومات الملف", padding=10)
        file_info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.file_info_text = scrolledtext.ScrolledText(file_info_frame, height=10, width=80)
        self.file_info_text.pack(fill=tk.BOTH, expand=True)

    def create_preview_tab(self):
        """إنشاء تبويب المعاينة والنشر"""
        preview_frame = ttk.Frame(self.notebook)
        self.notebook.add(preview_frame, text="👁️ معاينة ونشر")

        # إطار المعاينة
        preview_container = ttk.LabelFrame(preview_frame, text="معاينة المود", padding=10)
        preview_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # منطقة المعاينة
        self.preview_text = scrolledtext.ScrolledText(preview_container, height=20, width=80)
        self.preview_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # أزرار المعاينة
        preview_buttons_frame = ttk.Frame(preview_container)
        preview_buttons_frame.pack(fill=tk.X)

        ttk.Button(preview_buttons_frame, text="🔄 تحديث المعاينة",
                  command=self.update_preview).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(preview_buttons_frame, text="📋 نسخ المعاينة",
                  command=lambda: self.copy_text(self.preview_text)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(preview_buttons_frame, text="💾 حفظ كـ JSON",
                  command=self.save_as_json).pack(side=tk.LEFT)

        # إطار النشر
        publish_frame = ttk.LabelFrame(preview_frame, text="نشر المود", padding=10)
        publish_frame.pack(fill=tk.X, padx=10, pady=10)

        # معلومات النشر
        publish_info_label = ttk.Label(publish_frame,
                                      text="تأكد من مراجعة جميع البيانات قبل النشر")
        publish_info_label.pack(anchor=tk.W, pady=(0, 10))

        # أزرار النشر
        publish_buttons_frame = ttk.Frame(publish_frame)
        publish_buttons_frame.pack(fill=tk.X)

        ttk.Button(publish_buttons_frame, text="✅ نشر المود",
                  command=self.publish_mod,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(publish_buttons_frame, text="📤 نشر وانتقال للتالي",
                  command=self.publish_and_next).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(publish_buttons_frame, text="⏭️ تخطي هذا المود",
                  command=self.skip_current_mod).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(publish_buttons_frame, text="🚫 وضع علامة كمشكلة",
                  command=self.mark_as_problematic).pack(side=tk.LEFT)

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # معلومات إضافية
        self.mod_status_label = ttk.Label(status_frame, text="لا يوجد مود محمل", relief=tk.SUNKEN)
        self.mod_status_label.pack(side=tk.RIGHT, padx=(10, 0))

    def load_gemini_keys(self):
        """تحميل مفاتيح Gemini"""
        try:
            config_files = ["enhanced_batch_config.json", "config.json", "api_keys.json"]

            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    keys = config.get("gemini_api_keys", [])
                    valid_keys = [key for key in keys if key and not key.startswith("أدخل")]

                    if valid_keys:
                        self.gemini_fixer = GeminiDescriptionFixer(valid_keys)
                        self.update_status(f"تم تحميل {len(valid_keys)} مفتاح Gemini")
                        return

            self.update_status("⚠️ لم يتم العثور على مفاتيح Gemini صالحة")

        except Exception as e:
            self.update_status(f"❌ خطأ في تحميل مفاتيح Gemini: {e}")

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
        print(message)  # للتطوير

    def load_mods_file(self):
        """تحميل ملف المودات"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف المودات",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # استخراج قائمة المودات
            if isinstance(data, dict) and "mods" in data:
                mods_list = data["mods"]
            elif isinstance(data, list):
                mods_list = data
            else:
                messagebox.showerror("خطأ", "تنسيق ملف غير مدعوم")
                return

            # تحويل إلى كائنات ModData
            self.mods_data = [ModData(mod) for mod in mods_list]
            self.current_mod_index = 0

            if self.mods_data:
                self.load_current_mod()
                self.update_navigation()
                self.update_status(f"تم تحميل {len(self.mods_data)} مود من {os.path.basename(file_path)}")
            else:
                messagebox.showwarning("تحذير", "الملف لا يحتوي على مودات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الملف: {e}")
            self.update_status(f"❌ خطأ في تحميل الملف: {e}")

    def load_current_mod(self):
        """تحميل المود الحالي في الواجهة"""
        if not self.mods_data or self.current_mod_index >= len(self.mods_data):
            return

        self.current_mod = self.mods_data[self.current_mod_index]

        # تحميل المعلومات الأساسية
        self.mod_name_var.set(self.current_mod.get('name', ''))
        self.mod_version_var.set(self.current_mod.get('version', ''))
        self.mod_category_var.set(self.current_mod.get('category', ''))
        self.mod_creator_var.set(self.current_mod.get('creator_name', ''))
        self.mod_download_url_var.set(self.current_mod.get('download_url', ''))
        self.mod_source_url_var.set(self.current_mod.get('source_url', ''))

        # تحميل الأوصاف
        if self.description_ar_text:
            self.description_ar_text.delete("1.0", tk.END)
            self.description_ar_text.insert("1.0", self.current_mod.get('description_ar', ''))

        if self.description_en_text:
            self.description_en_text.delete("1.0", tk.END)
            self.description_en_text.insert("1.0", self.current_mod.get('description_en', ''))

        if self.telegram_ar_text:
            self.telegram_ar_text.delete("1.0", tk.END)
            self.telegram_ar_text.insert("1.0", self.current_mod.get('telegram_description_ar', ''))

        if self.telegram_en_text:
            self.telegram_en_text.delete("1.0", tk.END)
            self.telegram_en_text.insert("1.0", self.current_mod.get('telegram_description_en', ''))

        # تحميل الميزات
        if self.features_text:
            self.features_text.delete("1.0", tk.END)
            features = self.current_mod.get('features', [])
            if isinstance(features, list):
                self.features_text.insert("1.0", '\n'.join(features))
            else:
                self.features_text.insert("1.0", str(features))

        # تحميل الصور
        self.refresh_images()

        # تحديث معلومات الملف
        self.update_file_info()

        # تحديث المعاينة
        self.update_preview()

        # تحديث شريط الحالة
        mod_name = self.current_mod.get('name', 'مود غير معروف')
        status = "معدل" if self.current_mod.modified else "غير معدل"
        self.mod_status_label.config(text=f"{mod_name} - {status}")

    def update_navigation(self):
        """تحديث معلومات التنقل"""
        if self.navigation_label and self.mods_data:
            current = self.current_mod_index + 1
            total = len(self.mods_data)
            self.navigation_label.config(text=f"{current} / {total}")

    def next_mod(self):
        """الانتقال للمود التالي"""
        if self.mods_data and self.current_mod_index < len(self.mods_data) - 1:
            self.save_current_mod_changes()
            self.current_mod_index += 1
            self.load_current_mod()
            self.update_navigation()

    def previous_mod(self):
        """الانتقال للمود السابق"""
        if self.mods_data and self.current_mod_index > 0:
            self.save_current_mod_changes()
            self.current_mod_index -= 1
            self.load_current_mod()
            self.update_navigation()

    def save_current_mod_changes(self):
        """حفظ التغييرات على المود الحالي"""
        if not self.current_mod:
            return

        # حفظ المعلومات الأساسية
        self.current_mod.set('name', self.mod_name_var.get())
        self.current_mod.set('version', self.mod_version_var.get())
        self.current_mod.set('category', self.mod_category_var.get())
        self.current_mod.set('creator_name', self.mod_creator_var.get())
        self.current_mod.set('download_url', self.mod_download_url_var.get())
        self.current_mod.set('source_url', self.mod_source_url_var.get())

        # حفظ الأوصاف
        if self.description_ar_text:
            self.current_mod.set('description_ar', self.description_ar_text.get("1.0", tk.END).strip())

        if self.description_en_text:
            self.current_mod.set('description_en', self.description_en_text.get("1.0", tk.END).strip())

        if self.telegram_ar_text:
            self.current_mod.set('telegram_description_ar', self.telegram_ar_text.get("1.0", tk.END).strip())

        if self.telegram_en_text:
            self.current_mod.set('telegram_description_en', self.telegram_en_text.get("1.0", tk.END).strip())

        # حفظ الميزات
        if self.features_text:
            features_text = self.features_text.get("1.0", tk.END).strip()
            features_list = [f.strip() for f in features_text.split('\n') if f.strip()]
            self.current_mod.set('features', features_list)

    def save_changes(self):
        """حفظ جميع التغييرات"""
        if not self.mods_data:
            messagebox.showwarning("تحذير", "لا توجد مودات للحفظ")
            return

        try:
            # حفظ التغييرات الحالية
            self.save_current_mod_changes()

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف المودات",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # تحضير البيانات للحفظ
            mods_list = [mod.to_dict() for mod in self.mods_data]

            data_to_save = {
                "extraction_date": datetime.now().isoformat(),
                "total_mods": len(mods_list),
                "modified_date": datetime.now().isoformat(),
                "mods": mods_list
            }

            # حفظ الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2, ensure_ascii=False)

            # إعادة تعيين حالة التعديل
            for mod in self.mods_data:
                mod.modified = False

            self.update_status(f"تم حفظ {len(mods_list)} مود في {os.path.basename(file_path)}")
            messagebox.showinfo("نجح", f"تم حفظ {len(mods_list)} مود بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الملف: {e}")
            self.update_status(f"❌ خطأ في الحفظ: {e}")

    def refresh_images(self):
        """تحديث عرض الصور"""
        if not self.current_mod or not self.images_frame:
            return

        # مسح الصور الحالية
        for widget in self.images_frame.winfo_children():
            widget.destroy()

        images = self.current_mod.get_images()

        if not images:
            no_images_label = ttk.Label(self.images_frame, text="لا توجد صور")
            no_images_label.pack(pady=20)
            return

        # عرض الصور
        for i, image_info in enumerate(images):
            self.create_image_widget(image_info, i)

    def create_image_widget(self, image_info, index):
        """إنشاء عنصر واجهة للصورة"""
        image_frame = ttk.LabelFrame(self.images_frame, text=f"صورة {index + 1}", padding=10)
        image_frame.pack(fill=tk.X, padx=10, pady=5)

        # معلومات الصورة
        info_frame = ttk.Frame(image_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # نوع الصورة
        type_label = ttk.Label(info_frame, text=f"النوع: {image_info['type']}")
        type_label.pack(side=tk.LEFT)

        # رابط الصورة
        url_frame = ttk.Frame(image_frame)
        url_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(url_frame, text="الرابط:").pack(side=tk.LEFT)
        url_entry = ttk.Entry(url_frame, width=60)
        url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        url_entry.insert(0, image_info['url'])

        # أزرار التحكم
        buttons_frame = ttk.Frame(image_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="🔄 تحديث",
                  command=lambda: self.update_image_url(index, url_entry.get())).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(buttons_frame, text="🗑️ حذف",
                  command=lambda: self.remove_image(image_info['url'])).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(buttons_frame, text="⭐ جعل رئيسية",
                  command=lambda: self.set_as_primary_image(image_info['url'])).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(buttons_frame, text="👁️ معاينة",
                  command=lambda: self.preview_image(image_info['url'])).pack(side=tk.LEFT)

        # منطقة عرض الصورة المصغرة
        thumbnail_frame = ttk.Frame(image_frame)
        thumbnail_frame.pack(fill=tk.X, pady=(10, 0))

        thumbnail_label = ttk.Label(thumbnail_frame, text="جاري التحميل...")
        thumbnail_label.pack()

        # تحميل الصورة المصغرة
        def on_image_loaded(photo, error):
            if photo:
                thumbnail_label.config(image=photo, text="")
                thumbnail_label.image = photo  # الاحتفاظ بمرجع
            else:
                thumbnail_label.config(text=f"خطأ في التحميل: {error}")

        self.image_manager.load_image_from_url(image_info['url'], on_image_loaded)

    def add_image_from_file(self):
        """إضافة صورة من ملف محلي"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر صورة",
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # هنا يجب رفع الصورة إلى الخادم والحصول على رابط
            # للتبسيط، سنستخدم مسار الملف المحلي
            image_url = f"file://{file_path}"

            if self.current_mod:
                self.current_mod.add_image(image_url)
                self.refresh_images()
                self.update_status("تم إضافة صورة من ملف محلي")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة الصورة: {e}")

    def add_image_from_url(self):
        """إضافة صورة من رابط"""
        url = simpledialog.askstring("إضافة صورة", "أدخل رابط الصورة:")

        if url and url.strip():
            if self.current_mod:
                self.current_mod.add_image(url.strip())
                self.refresh_images()
                self.update_status("تم إضافة صورة من رابط")

    def remove_image(self, image_url):
        """إزالة صورة"""
        if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذه الصورة؟"):
            if self.current_mod:
                self.current_mod.remove_image(image_url)
                self.refresh_images()
                self.update_status("تم حذف الصورة")

    def delete_selected_image(self):
        """حذف الصورة المحددة"""
        # هذه الدالة تحتاج إلى تنفيذ للتعامل مع الصورة المحددة حالياً
        # حالياً سنعرض رسالة خطأ للمستخدم
        messagebox.showinfo("غير مدعوم", "وظيفة حذف الصورة المحددة غير متوفرة حالياً")
        
    def set_as_primary_image(self, image_url):
        """تعيين صورة كصورة رئيسية"""
        if self.current_mod:
            self.current_mod.set('primary_image_url', image_url)
            self.refresh_images()
            self.update_status("تم تعيين الصورة الرئيسية")

    def preview_image(self, image_url):
        """معاينة الصورة في نافذة منفصلة"""
        try:
            preview_window = tk.Toplevel(self.window)
            preview_window.title("معاينة الصورة")
            preview_window.geometry("600x600")

            # تحميل وعرض الصورة
            def load_and_show():
                try:
                    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                    response = requests.get(image_url, headers=headers, timeout=10)
                    response.raise_for_status()

                    image = Image.open(BytesIO(response.content))

                    # تغيير حجم الصورة للمعاينة
                    image.thumbnail((550, 550), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)

                    # عرض الصورة
                    label = ttk.Label(preview_window, image=photo)
                    label.image = photo  # الاحتفاظ بمرجع
                    label.pack(expand=True)

                    # معلومات الصورة
                    info_label = ttk.Label(preview_window, text=f"الحجم: {image.size[0]}x{image.size[1]}")
                    info_label.pack(pady=10)

                except Exception as e:
                    error_label = ttk.Label(preview_window, text=f"خطأ في تحميل الصورة: {e}")
                    error_label.pack(expand=True)

            threading.Thread(target=load_and_show, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في معاينة الصورة: {e}")

    def fix_description_with_gemini(self, language):
        """إصلاح الوصف باستخدام Gemini"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح. تأكد من إعداد مفاتيح API")
            return

        try:
            if language == "ar":
                text_widget = self.description_ar_text
                current_text = text_widget.get("1.0", tk.END).strip()
            else:
                text_widget = self.description_en_text
                current_text = text_widget.get("1.0", tk.END).strip()

            if not current_text:
                messagebox.showwarning("تحذير", "لا يوجد نص للإصلاح")
                return

            self.update_status("جاري إصلاح الوصف باستخدام Gemini...")

            def fix_async():
                try:
                    mod_name = self.mod_name_var.get()
                    fixed_text = self.gemini_fixer.fix_description(current_text, mod_name, language)

                    if fixed_text:
                        # تحديث النص في الواجهة
                        self.window.after(0, lambda: self.update_description_text(text_widget, fixed_text))
                        self.window.after(0, lambda: self.update_status("تم إصلاح الوصف بنجاح"))
                    else:
                        self.window.after(0, lambda: self.update_status("فشل في إصلاح الوصف"))

                except Exception as e:
                    self.window.after(0, lambda: self.update_status(f"خطأ في إصلاح الوصف: {e}"))

            threading.Thread(target=fix_async, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إصلاح الوصف: {e}")

    def update_description_text(self, text_widget, new_text):
        """تحديث نص الوصف"""
        text_widget.delete("1.0", tk.END)
        text_widget.insert("1.0", new_text)

    def generate_telegram_description(self, language):
        """توليد وصف تليجرام باستخدام Gemini"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح")
            return

        try:
            mod_name = self.mod_name_var.get()
            main_desc = self.description_ar_text.get("1.0", tk.END).strip() if language == "ar" else self.description_en_text.get("1.0", tk.END).strip()

            if not main_desc:
                messagebox.showwarning("تحذير", "يجب وجود وصف رئيسي أولاً")
                return

            if language == "ar":
                prompt = f"""
قم بإنشاء وصف تليجرام عربي قصير ومثير للاهتمام لهذا المود:

اسم المود: {mod_name}
الوصف الكامل: {main_desc}

المتطلبات:
- طول الوصف: 300-400 حرف
- أسلوب شبابي وجذاب
- استخدام إيموجي مناسب
- التركيز على أهم الميزات
- لا تذكر إصدارات أخرى أو توافق

أرجع الوصف فقط بدون أي تعليقات.
"""
            else:
                prompt = f"""
Create a short and engaging English Telegram description for this mod:

Mod Name: {mod_name}
Full Description: {main_desc}

Requirements:
- Length: 300-400 characters
- Youthful and attractive style
- Use appropriate emojis
- Focus on key features
- Don't mention other versions or compatibility

Return only the description without any comments.
"""

            self.update_status("جاري توليد وصف التليجرام...")

            def generate_async():
                try:
                    result = self.gemini_fixer.gemini_manager.generate_content(prompt)

                    if result:
                        text_widget = self.telegram_ar_text if language == "ar" else self.telegram_en_text
                        self.window.after(0, lambda: self.update_description_text(text_widget, result))
                        self.window.after(0, lambda: self.update_status("تم توليد وصف التليجرام"))
                    else:
                        self.window.after(0, lambda: self.update_status("فشل في توليد وصف التليجرام"))

                except Exception as e:
                    self.window.after(0, lambda: self.update_status(f"خطأ في توليد الوصف: {e}"))

            threading.Thread(target=generate_async, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد وصف التليجرام: {e}")

    def show_character_count(self, text_widget):
        """عرض عدد الأحرف"""
        text = text_widget.get("1.0", tk.END).strip()
        char_count = len(text)
        messagebox.showinfo("عدد الأحرف", f"عدد الأحرف: {char_count}\nالمطلوب: 300-400 حرف")

    def copy_text(self, text_widget):
        """نسخ النص إلى الحافظة"""
        try:
            text = text_widget.get("1.0", tk.END).strip()
            self.window.clipboard_clear()
            self.window.clipboard_append(text)
            self.update_status("تم نسخ النص إلى الحافظة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في نسخ النص: {e}")

    def update_preview(self):
        """تحديث معاينة المود"""
        if not self.current_mod:
            return

        try:
            # حفظ التغييرات الحالية
            self.save_current_mod_changes()

            # إنشاء معاينة شاملة
            preview_data = self.current_mod.to_dict()

            preview_text = f"""
🎮 معاينة المود
{'=' * 50}

📋 المعلومات الأساسية:
• الاسم: {preview_data.get('name', 'غير محدد')}
• الإصدار: {preview_data.get('version', 'غير محدد')}
• الفئة: {preview_data.get('category', 'غير محدد')}
• المنشئ: {preview_data.get('creator_name', 'غير محدد')}

🔗 الروابط:
• رابط التحميل: {preview_data.get('download_url', 'غير محدد')}
• رابط المصدر: {preview_data.get('source_url', 'غير محدد')}

📝 الوصف العربي:
{preview_data.get('description_ar', 'غير متوفر')}

📝 الوصف الإنجليزي:
{preview_data.get('description_en', 'غير متوفر')}

📱 وصف التليجرام العربي:
{preview_data.get('telegram_description_ar', 'غير متوفر')}

📱 وصف التليجرام الإنجليزي:
{preview_data.get('telegram_description_en', 'غير متوفر')}

⭐ الميزات:
{chr(10).join(f'• {feature}' for feature in preview_data.get('features', [])) if preview_data.get('features') else 'غير متوفر'}

🖼️ الصور:
• الصورة الرئيسية: {preview_data.get('primary_image_url', 'غير محدد')}
• عدد الصور الإضافية: {len(preview_data.get('image_urls', []))}

📊 معلومات إضافية:
• تاريخ الاستخراج: {preview_data.get('extraction_timestamp', 'غير محدد')}
• طريقة الاستخراج: {preview_data.get('extraction_method', 'غير محدد')}
• معدل: {'نعم' if self.current_mod.modified else 'لا'}
"""

            if self.preview_text:
                self.preview_text.delete("1.0", tk.END)
                self.preview_text.insert("1.0", preview_text)

        except Exception as e:
            self.update_status(f"خطأ في تحديث المعاينة: {e}")

    def publish_mod(self):
        """نشر المود"""
        if not self.current_mod:
            messagebox.showwarning("تحذير", "لا يوجد مود للنشر")
            return

        try:
            # التحقق من البيانات المطلوبة
            required_fields = ['name', 'description_ar', 'description_en']
            missing_fields = []

            for field in required_fields:
                if not self.current_mod.get(field):
                    missing_fields.append(field)

            if missing_fields:
                messagebox.showwarning("بيانات ناقصة",
                                     f"الحقول التالية مطلوبة:\n{', '.join(missing_fields)}")
                return

            # تأكيد النشر
            mod_name = self.current_mod.get('name')
            if not messagebox.askyesno("تأكيد النشر", f"هل تريد نشر المود '{mod_name}'؟"):
                return

            # حفظ التغييرات
            self.save_current_mod_changes()

            # هنا يمكن إضافة منطق النشر الفعلي
            # مثل الاتصال بـ API أو قاعدة البيانات

            # للتجربة، سنحفظ المود في ملف منفصل
            published_file = "published_mods.json"
            published_mods = []

            if os.path.exists(published_file):
                with open(published_file, 'r', encoding='utf-8') as f:
                    published_mods = json.load(f)

            # إضافة المود مع معلومات النشر
            mod_data = self.current_mod.to_dict()
            mod_data['published_date'] = datetime.now().isoformat()
            mod_data['published_by'] = 'Enhanced Mod Editor'

            published_mods.append(mod_data)

            with open(published_file, 'w', encoding='utf-8') as f:
                json.dump(published_mods, f, indent=2, ensure_ascii=False)

            self.update_status(f"تم نشر المود '{mod_name}' بنجاح")
            messagebox.showinfo("نجح النشر", f"تم نشر المود '{mod_name}' بنجاح!")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في نشر المود: {e}")
            self.update_status(f"❌ خطأ في النشر: {e}")

    def publish_and_next(self):
        """نشر المود والانتقال للتالي"""
        self.publish_mod()
        self.next_mod()

    def skip_current_mod(self):
        """تخطي المود الحالي"""
        if messagebox.askyesno("تأكيد التخطي", "هل تريد تخطي هذا المود؟"):
            self.next_mod()

    def mark_as_problematic(self):
        """وضع علامة على المود كمشكلة"""
        if not self.current_mod:
            return

        reason = tk.simpledialog.askstring("سبب المشكلة", "أدخل سبب وضع علامة المشكلة:")

        if reason:
            try:
                problematic_file = "problematic_mods.json"
                problematic_mods = []

                if os.path.exists(problematic_file):
                    with open(problematic_file, 'r', encoding='utf-8') as f:
                        problematic_mods = json.load(f)

                mod_data = self.current_mod.to_dict()
                mod_data['problem_reason'] = reason
                mod_data['marked_date'] = datetime.now().isoformat()

                problematic_mods.append(mod_data)

                with open(problematic_file, 'w', encoding='utf-8') as f:
                    json.dump(problematic_mods, f, indent=2, ensure_ascii=False)

                self.update_status("تم وضع علامة مشكلة على المود")
                self.next_mod()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في وضع علامة المشكلة: {e}")

    def reset_current_mod(self):
        """إعادة تعيين المود الحالي"""
        if not self.current_mod:
            return

        if messagebox.askyesno("تأكيد الإعادة", "هل تريد إعادة تعيين جميع التغييرات؟"):
            self.current_mod.data = self.current_mod.original_data.copy()
            self.current_mod.modified = False
            self.load_current_mod()
            self.update_status("تم إعادة تعيين المود")

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        settings_window = tk.Toplevel(self.window)
        settings_window.title("إعدادات أداة التخصيص")
        settings_window.geometry("500x400")
        settings_window.resizable(True, True)

        # إطار مفاتيح Gemini
        gemini_frame = ttk.LabelFrame(settings_window, text="مفاتيح Gemini API", padding=10)
        gemini_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        ttk.Label(gemini_frame, text="أدخل مفاتيح Gemini API (مفتاح واحد في كل سطر):").pack(anchor=tk.W, pady=(0, 5))

        keys_text = scrolledtext.ScrolledText(gemini_frame, height=8, width=60)
        keys_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # تحميل المفاتيح الحالية
        try:
            if os.path.exists("enhanced_batch_config.json"):
                with open("enhanced_batch_config.json", 'r', encoding='utf-8') as f:
                    config = json.load(f)

                keys = config.get("gemini_api_keys", [])
                valid_keys = [key for key in keys if key and not key.startswith("أدخل")]

                if valid_keys:
                    keys_text.insert("1.0", '\n'.join(valid_keys))
        except:
            pass

        # أزرار الإعدادات
        settings_buttons = ttk.Frame(gemini_frame)
        settings_buttons.pack(fill=tk.X)

        def save_settings():
            keys_content = keys_text.get("1.0", tk.END).strip()
            keys = [key.strip() for key in keys_content.split('\n') if key.strip()]

            config = {
                "gemini_api_keys": keys,
                "last_updated": datetime.now().isoformat()
            }

            try:
                with open("enhanced_batch_config.json", 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                # إعادة تحميل مفاتيح Gemini
                self.load_gemini_keys()

                messagebox.showinfo("نجح", f"تم حفظ {len(keys)} مفتاح")
                settings_window.destroy()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {e}")

        ttk.Button(settings_buttons, text="💾 حفظ", command=save_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(settings_buttons, text="إلغاء", command=settings_window.destroy).pack(side=tk.LEFT)

    def export_current_mod(self):
        """تصدير المود الحالي"""
        if not self.current_mod:
            messagebox.showwarning("تحذير", "لا يوجد مود للتصدير")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="تصدير المود",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # حفظ التغييرات الحالية
            self.save_current_mod_changes()

            # تحضير بيانات التصدير
            export_data = {
                "mod": self.current_mod.to_dict(),
                "export_date": datetime.now().isoformat(),
                "exported_by": "Enhanced Mod Editor"
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            self.update_status(f"تم تصدير المود إلى {os.path.basename(file_path)}")
            messagebox.showinfo("نجح", "تم تصدير المود بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير المود: {e}")

    def save_as_json(self):
        """حفظ المعاينة كملف JSON"""
        if not self.current_mod:
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ المعاينة",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            self.save_current_mod_changes()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_mod.to_dict(), f, indent=2, ensure_ascii=False)

            self.update_status("تم حفظ المعاينة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المعاينة: {e}")

    def test_download_link(self):
        """اختبار رابط التحميل"""
        url = self.mod_download_url_var.get().strip()

        if not url:
            messagebox.showwarning("تحذير", "لا يوجد رابط للاختبار")
            return

        def test_link():
            try:
                self.window.after(0, lambda: self.update_status("جاري اختبار رابط التحميل..."))

                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.head(url, headers=headers, timeout=10, allow_redirects=True)

                if response.status_code == 200:
                    content_length = response.headers.get('content-length')
                    content_type = response.headers.get('content-type', 'غير محدد')

                    size_info = ""
                    if content_length:
                        size_mb = int(content_length) / (1024 * 1024)
                        size_info = f"\nحجم الملف: {size_mb:.2f} MB"

                    message = f"الرابط يعمل بشكل صحيح ✅\nنوع المحتوى: {content_type}{size_info}"
                    self.window.after(0, lambda: messagebox.showinfo("نتيجة الاختبار", message))
                    self.window.after(0, lambda: self.update_status("رابط التحميل يعمل بشكل صحيح"))
                else:
                    self.window.after(0, lambda: messagebox.showwarning("مشكلة في الرابط",
                                                                       f"الرابط يعيد رمز الخطأ: {response.status_code}"))
                    self.window.after(0, lambda: self.update_status(f"مشكلة في رابط التحميل: {response.status_code}"))

            except Exception as e:
                self.window.after(0, lambda: messagebox.showerror("خطأ في الاختبار", f"فشل في اختبار الرابط: {e}"))
                self.window.after(0, lambda: self.update_status(f"خطأ في اختبار الرابط: {e}"))

        threading.Thread(target=test_link, daemon=True).start()

    def open_source_in_browser(self):
        """فتح رابط المصدر في المتصفح"""
        url = self.mod_source_url_var.get().strip()

        if not url:
            messagebox.showwarning("تحذير", "لا يوجد رابط مصدر")
            return

        try:
            import webbrowser
            webbrowser.open(url)
            self.update_status("تم فتح رابط المصدر في المتصفح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الرابط: {e}")

    def upload_local_mod_file(self):
        """رفع ملف مود محلي"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف المود",
                filetypes=[
                    ("Mod files", "*.mcpack *.mcaddon *.zip *.rar"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # هنا يجب رفع الملف إلى الخادم والحصول على رابط
            # للتبسيط، سنعرض معلومات الملف المحلي
            file_size = os.path.getsize(file_path)
            file_name = os.path.basename(file_path)

            info = f"تم اختيار الملف:\n"
            info += f"الاسم: {file_name}\n"
            info += f"الحجم: {file_size / (1024*1024):.2f} MB\n"
            info += f"المسار: {file_path}\n\n"
            info += "ملاحظة: يجب رفع الملف إلى خادم للحصول على رابط تحميل"

            if self.file_info_text:
                self.file_info_text.delete("1.0", tk.END)
                self.file_info_text.insert("1.0", info)

            self.update_status(f"تم اختيار ملف: {file_name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اختيار الملف: {e}")

    def upload_mod_from_url(self):
        """رفع مود من رابط"""
        url = tk.simpledialog.askstring("رفع من رابط", "أدخل رابط الملف:")

        if not url or not url.strip():
            return

        def download_and_analyze():
            try:
                self.window.after(0, lambda: self.update_status("جاري تحليل الملف من الرابط..."))

                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.head(url.strip(), headers=headers, timeout=10, allow_redirects=True)

                if response.status_code == 200:
                    content_length = response.headers.get('content-length')
                    content_type = response.headers.get('content-type', 'غير محدد')

                    info = f"معلومات الملف من الرابط:\n"
                    info += f"الرابط: {url}\n"
                    info += f"نوع المحتوى: {content_type}\n"

                    if content_length:
                        size_mb = int(content_length) / (1024 * 1024)
                        info += f"الحجم: {size_mb:.2f} MB\n"

                    info += f"حالة الاستجابة: {response.status_code}\n"

                    # تحديث رابط التحميل
                    self.window.after(0, lambda: self.mod_download_url_var.set(url.strip()))

                    if self.file_info_text:
                        self.window.after(0, lambda: self.file_info_text.delete("1.0", tk.END))
                        self.window.after(0, lambda: self.file_info_text.insert("1.0", info))

                    self.window.after(0, lambda: self.update_status("تم تحليل الملف وتحديث رابط التحميل"))
                else:
                    self.window.after(0, lambda: self.update_status(f"خطأ في الرابط: {response.status_code}"))

            except Exception as e:
                self.window.after(0, lambda: self.update_status(f"خطأ في تحليل الرابط: {e}"))

        threading.Thread(target=download_and_analyze, daemon=True).start()

    def analyze_current_mod_file(self):
        """تحليل ملف المود الحالي"""
        if not self.current_mod:
            return

        download_url = self.current_mod.get('download_url', '')

        if not download_url:
            messagebox.showwarning("تحذير", "لا يوجد رابط تحميل للتحليل")
            return

        def analyze():
            try:
                self.window.after(0, lambda: self.update_status("جاري تحليل ملف المود..."))

                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.head(download_url, headers=headers, timeout=10, allow_redirects=True)

                info = f"تحليل ملف المود:\n"
                info += f"الرابط: {download_url}\n"
                info += f"حالة الاستجابة: {response.status_code}\n"

                if response.status_code == 200:
                    content_type = response.headers.get('content-type', 'غير محدد')
                    content_length = response.headers.get('content-length')
                    last_modified = response.headers.get('last-modified', 'غير محدد')

                    info += f"نوع المحتوى: {content_type}\n"

                    if content_length:
                        size_mb = int(content_length) / (1024 * 1024)
                        info += f"حجم الملف: {size_mb:.2f} MB\n"

                    info += f"آخر تعديل: {last_modified}\n"

                    # تحديد نوع الملف
                    file_extension = ""
                    if download_url.lower().endswith('.mcpack'):
                        file_extension = "Behavior Pack"
                    elif download_url.lower().endswith('.mcaddon'):
                        file_extension = "Add-On Package"
                    elif download_url.lower().endswith('.zip'):
                        file_extension = "ZIP Archive"

                    if file_extension:
                        info += f"نوع الملف المتوقع: {file_extension}\n"

                    info += "\n✅ الملف متاح ويمكن تحميله"
                else:
                    info += f"\n❌ مشكلة في الوصول للملف"

                if self.file_info_text:
                    self.window.after(0, lambda: self.file_info_text.delete("1.0", tk.END))
                    self.window.after(0, lambda: self.file_info_text.insert("1.0", info))

                self.window.after(0, lambda: self.update_status("تم تحليل ملف المود"))

            except Exception as e:
                error_info = f"خطأ في تحليل الملف:\n{str(e)}"
                if self.file_info_text:
                    self.window.after(0, lambda: self.file_info_text.delete("1.0", tk.END))
                    self.window.after(0, lambda: self.file_info_text.insert("1.0", error_info))

                self.window.after(0, lambda: self.update_status(f"خطأ في تحليل الملف: {e}"))

        threading.Thread(target=analyze, daemon=True).start()

    def update_file_info(self):
        """تحديث معلومات الملف"""
        if not self.current_mod or not self.file_info_text:
            return

        info = f"معلومات المود الحالي:\n"
        info += f"الاسم: {self.current_mod.get('name', 'غير محدد')}\n"
        info += f"رابط التحميل: {self.current_mod.get('download_url', 'غير محدد')}\n"
        info += f"رابط المصدر: {self.current_mod.get('source_url', 'غير محدد')}\n"
        info += f"تاريخ الاستخراج: {self.current_mod.get('extraction_timestamp', 'غير محدد')}\n"
        info += f"طريقة الاستخراج: {self.current_mod.get('extraction_method', 'غير محدد')}\n"
        info += f"معدل: {'نعم' if self.current_mod.modified else 'لا'}\n"

        self.file_info_text.delete("1.0", tk.END)
        self.file_info_text.insert("1.0", info)

    def update_image_url(self, index, new_url):
        """تحديث رابط صورة"""
        if not self.current_mod or not new_url.strip():
            return

        images = self.current_mod.get_images()
        if index < len(images):
            old_url = images[index]['url']

            # إزالة الصورة القديمة
            self.current_mod.remove_image(old_url)

            # إضافة الصورة الجديدة
            self.current_mod.add_image(new_url.strip(), images[index]['type'])

            # تحديث العرض
            self.refresh_images()
            self.update_status("تم تحديث رابط الصورة")

    def generate_features_with_gemini(self):
        """توليد ميزات المود باستخدام Gemini"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح")
            return

        try:
            mod_name = self.mod_name_var.get()
            description = self.description_ar_text.get("1.0", tk.END).strip()

            if not description:
                messagebox.showwarning("تحذير", "يجب وجود وصف للمود أولاً")
                return

            prompt = f"""
بناءً على هذا الوصف للمود، قم بإنشاء قائمة بأهم الميزات:

اسم المود: {mod_name}
الوصف: {description}

المطلوب:
- استخرج 5-8 ميزات رئيسية
- اكتب كل ميزة في سطر منفصل
- استخدم لغة واضحة ومباشرة
- ركز على الوظائف والإضافات الجديدة

أرجع قائمة الميزات فقط، ميزة واحدة في كل سطر.
"""

            self.update_status("جاري توليد الميزات باستخدام Gemini...")

            def generate_async():
                try:
                    result = self.gemini_fixer.gemini_manager.generate_content(prompt)

                    if result:
                        self.window.after(0, lambda: self.features_text.delete("1.0", tk.END))
                        self.window.after(0, lambda: self.features_text.insert("1.0", result))
                        self.window.after(0, lambda: self.update_status("تم توليد الميزات بنجاح"))
                    else:
                        self.window.after(0, lambda: self.update_status("فشل في توليد الميزات"))

                except Exception as e:
                    self.window.after(0, lambda: self.update_status(f"خطأ في توليد الميزات: {e}"))

            threading.Thread(target=generate_async, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد الميزات: {e}")

    def format_features(self):
        """تنسيق قائمة الميزات"""
        if not self.features_text:
            return

        features_text = self.features_text.get("1.0", tk.END).strip()

        if not features_text:
            return

        # تنظيف وتنسيق الميزات
        features_lines = features_text.split('\n')
        formatted_features = []

        for line in features_lines:
            line = line.strip()
            if line:
                # إزالة الرموز والأرقام من البداية
                line = re.sub(r'^[-•*\d\.\)\s]+', '', line)

                # إضافة رمز نقطة إذا لم يكن موجود
                if not line.startswith('•'):
                    line = f"• {line}"

                formatted_features.append(line)

        # تحديث النص
        self.features_text.delete("1.0", tk.END)
        self.features_text.insert("1.0", '\n'.join(formatted_features))

        self.update_status("تم تنسيق قائمة الميزات")

    def translate_en_to_ar(self):
        """ترجمة الوصف من الإنجليزي إلى العربي"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح للترجمة")
            return

        en_text = self.description_en_text.get("1.0", tk.END).strip()

        if not en_text:
            messagebox.showwarning("تحذير", "لا يوجد نص إنجليزي للترجمة")
            return

        prompt = f"""
قم بترجمة هذا النص من الإنجليزية إلى العربية مع الحفاظ على المعنى والتنسيق:

{en_text}

المطلوب:
- ترجمة دقيقة ومفهومة
- الحفاظ على التنسيق الأصلي
- استخدام مصطلحات ألعاب مناسبة
- جعل النص طبيعي باللغة العربية

أرجع الترجمة العربية فقط.
"""

        self.update_status("جاري الترجمة إلى العربية...")

        def translate_async():
            try:
                result = self.gemini_fixer.gemini_manager.generate_content(prompt)

                if result:
                    self.window.after(0, lambda: self.description_ar_text.delete("1.0", tk.END))
                    self.window.after(0, lambda: self.description_ar_text.insert("1.0", result))
                    self.window.after(0, lambda: self.update_status("تم الترجمة إلى العربية"))
                else:
                    self.window.after(0, lambda: self.update_status("فشل في الترجمة"))

            except Exception as e:
                self.window.after(0, lambda: self.update_status(f"خطأ في الترجمة: {e}"))

        threading.Thread(target=translate_async, daemon=True).start()

    def translate_ar_to_en(self):
        """ترجمة الوصف من العربي إلى الإنجليزي"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح للترجمة")
            return

        ar_text = self.description_ar_text.get("1.0", tk.END).strip()

        if not ar_text:
            messagebox.showwarning("تحذير", "لا يوجد نص عربي للترجمة")
            return

        prompt = f"""
Translate this Arabic text to English while maintaining meaning and formatting:

{ar_text}

Requirements:
- Accurate and understandable translation
- Maintain original formatting
- Use appropriate gaming terminology
- Make the text natural in English

Return only the English translation.
"""

        self.update_status("جاري الترجمة إلى الإنجليزية...")

        def translate_async():
            try:
                result = self.gemini_fixer.gemini_manager.generate_content(prompt)

                if result:
                    self.window.after(0, lambda: self.description_en_text.delete("1.0", tk.END))
                    self.window.after(0, lambda: self.description_en_text.insert("1.0", result))
                    self.window.after(0, lambda: self.update_status("تم الترجمة إلى الإنجليزية"))
                else:
                    self.window.after(0, lambda: self.update_status("فشل في الترجمة"))

            except Exception as e:
                self.window.after(0, lambda: self.update_status(f"خطأ في الترجمة: {e}"))

        threading.Thread(target=translate_async, daemon=True).start()

    def fix_with_gemini(self):
        """إصلاح شامل للمود باستخدام Gemini"""
        if not self.gemini_fixer:
            messagebox.showwarning("تحذير", "Gemini غير متاح")
            return

        if not self.current_mod:
            messagebox.showwarning("تحذير", "لا يوجد مود للإصلاح")
            return

        # نافذة خيارات الإصلاح
        fix_window = tk.Toplevel(self.window)
        fix_window.title("إصلاح شامل بـ Gemini")
        fix_window.geometry("400x300")
        fix_window.resizable(False, False)

        ttk.Label(fix_window, text="اختر العناصر المراد إصلاحها:", font=("Arial", 12, "bold")).pack(pady=10)

        # متغيرات الخيارات
        fix_ar_desc = tk.BooleanVar(value=True)
        fix_en_desc = tk.BooleanVar(value=True)
        fix_features = tk.BooleanVar(value=True)
        generate_telegram = tk.BooleanVar(value=True)

        # خيارات الإصلاح
        ttk.Checkbutton(fix_window, text="إصلاح الوصف العربي", variable=fix_ar_desc).pack(anchor=tk.W, padx=20, pady=5)
        ttk.Checkbutton(fix_window, text="إصلاح الوصف الإنجليزي", variable=fix_en_desc).pack(anchor=tk.W, padx=20, pady=5)
        ttk.Checkbutton(fix_window, text="توليد/إصلاح الميزات", variable=fix_features).pack(anchor=tk.W, padx=20, pady=5)
        ttk.Checkbutton(fix_window, text="توليد أوصاف التليجرام", variable=generate_telegram).pack(anchor=tk.W, padx=20, pady=5)

        # أزرار التحكم
        buttons_frame = ttk.Frame(fix_window)
        buttons_frame.pack(pady=20)

        def start_comprehensive_fix():
            fix_window.destroy()

            self.update_status("بدء الإصلاح الشامل باستخدام Gemini...")

            def fix_async():
                try:
                    # إصلاح الوصف العربي
                    if fix_ar_desc.get():
                        self.window.after(0, lambda: self.update_status("إصلاح الوصف العربي..."))
                        self.fix_description_with_gemini("ar")
                        threading.Event().wait(3)  # انتظار قصير

                    # إصلاح الوصف الإنجليزي
                    if fix_en_desc.get():
                        self.window.after(0, lambda: self.update_status("إصلاح الوصف الإنجليزي..."))
                        self.fix_description_with_gemini("en")
                        threading.Event().wait(3)

                    # توليد الميزات
                    if fix_features.get():
                        self.window.after(0, lambda: self.update_status("توليد الميزات..."))
                        self.generate_features_with_gemini()
                        threading.Event().wait(3)

                    # توليد أوصاف التليجرام
                    if generate_telegram.get():
                        self.window.after(0, lambda: self.update_status("توليد وصف التليجرام العربي..."))
                        self.generate_telegram_description("ar")
                        threading.Event().wait(3)

                        self.window.after(0, lambda: self.update_status("توليد وصف التليجرام الإنجليزي..."))
                        self.generate_telegram_description("en")
                        threading.Event().wait(3)

                    self.window.after(0, lambda: self.update_status("تم الإصلاح الشامل بنجاح! ✅"))

                except Exception as e:
                    self.window.after(0, lambda: self.update_status(f"خطأ في الإصلاح الشامل: {e}"))

            threading.Thread(target=fix_async, daemon=True).start()

        ttk.Button(buttons_frame, text="🚀 بدء الإصلاح", command=start_comprehensive_fix).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="إلغاء", command=fix_window.destroy).pack(side=tk.LEFT, padx=10)

    def run(self):
        """تشغيل الأداة"""
        if self.window:
            self.window.mainloop()

# دالة لتشغيل الأداة بشكل مستقل
def main():
    app = EnhancedModEditor()
    app.run()

if __name__ == "__main__":
    main()