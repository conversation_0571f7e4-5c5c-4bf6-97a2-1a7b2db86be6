# -*- coding: utf-8 -*-
"""
تحديث الملف الأساسي لدمج النظام المحسن
Enhanced Mod Processor Integration Update
"""

import os
import sys

def integrate_enhanced_system_to_main_file():
    """دمج النظام المحسن مع الملف الأساسي"""
    
    main_file = "mod_processor_broken_final.py"
    
    if not os.path.exists(main_file):
        print(f"❌ لم يتم العثور على الملف الأساسي: {main_file}")
        return False
    
    try:
        # قراءة الملف الأساسي
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود التكامل مسبقاً
        if "# ENHANCED BATCH SYSTEM INTEGRATION" in content:
            print("✅ النظام المحسن مدمج مسبقاً")
            return True
        
        # إضافة الاستيرادات المطلوبة
        import_section = """
# ENHANCED BATCH SYSTEM INTEGRATION - START
try:
    from enhanced_integration import integrate_enhanced_batch_system
    ENHANCED_BATCH_AVAILABLE = True
    print("✅ تم تحميل النظام المحسن للمعالجة المتتالية")
except ImportError as e:
    ENHANCED_BATCH_AVAILABLE = False
    print(f"⚠️ النظام المحسن غير متاح: {e}")
# ENHANCED BATCH SYSTEM INTEGRATION - END

"""
        
        # البحث عن نقطة الإدراج للاستيرادات
        import_insertion_point = content.find("# Global variables for batch processing")
        if import_insertion_point == -1:
            import_insertion_point = content.find("DEFAULT_BATCH_FILENAME = \"batch_urls.txt\"")
        
        if import_insertion_point != -1:
            content = content[:import_insertion_point] + import_section + content[import_insertion_point:]
        else:
            # إضافة في بداية الملف بعد التعليقات الأولى
            lines = content.split('\n')
            insert_line = 0
            for i, line in enumerate(lines):
                if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('import'):
                    insert_line = i
                    break
            
            lines.insert(insert_line, import_section)
            content = '\n'.join(lines)
        
        # إضافة كود التكامل في دالة main
        integration_code = """
    # ENHANCED BATCH SYSTEM INTEGRATION - START
    if ENHANCED_BATCH_AVAILABLE:
        try:
            enhanced_integration, enhanced_frame = integrate_enhanced_batch_system(
                window, update_status, scrollable_frame
            )
            if enhanced_integration:
                print("✅ تم دمج النظام المحسن بنجاح")
            else:
                print("⚠️ فشل في دمج النظام المحسن")
        except Exception as e:
            print(f"❌ خطأ في دمج النظام المحسن: {e}")
            update_status(f"❌ خطأ في دمج النظام المحسن: {e}")
    # ENHANCED BATCH SYSTEM INTEGRATION - END
"""
        
        # البحث عن نقطة الإدراج في دالة main
        main_insertion_point = content.find("if not check_default_batch_file():")
        if main_insertion_point == -1:
            main_insertion_point = content.find("window.mainloop()")
        
        if main_insertion_point != -1:
            # إدراج قبل النقطة المحددة
            content = content[:main_insertion_point] + integration_code + "\n    " + content[main_insertion_point:]
        
        # حفظ الملف المحدث
        backup_file = f"{main_file}.backup_{int(time.time())}"
        
        # إنشاء نسخة احتياطية
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content.replace(import_section, "").replace(integration_code, ""))
        
        # حفظ الملف المحدث
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم دمج النظام المحسن بنجاح")
        print(f"📁 تم إنشاء نسخة احتياطية: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في دمج النظام: {e}")
        return False

def create_enhanced_launcher():
    """إنشاء ملف تشغيل للنظام المحسن"""
    
    launcher_content = '''# -*- coding: utf-8 -*-
"""
مشغل النظام المحسن للمعالجة المتتالية
Enhanced Batch Processing System Launcher
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def main():
    """تشغيل النظام المحسن"""
    try:
        # التحقق من وجود الملفات المطلوبة
        required_files = [
            "enhanced_batch_processor.py",
            "enhanced_batch_gui.py", 
            "enhanced_integration.py"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print("❌ ملفات مفقودة:")
            for file in missing_files:
                print(f"   • {file}")
            return
        
        # تشغيل الواجهة المحسنة
        from enhanced_batch_gui import EnhancedBatchGUI
        
        print("🚀 بدء تشغيل النظام المحسن...")
        
        app = EnhancedBatchGUI()
        app.run()
        
    except ImportError as e:
        error_msg = f"خطأ في الاستيراد: {e}"
        print(f"❌ {error_msg}")
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        
    except Exception as e:
        error_msg = f"خطأ عام: {e}"
        print(f"❌ {error_msg}")
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("run_enhanced_batch.py", 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        print("✅ تم إنشاء مشغل النظام المحسن: run_enhanced_batch.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المشغل: {e}")
        return False

def create_requirements_file():
    """إنشاء ملف المتطلبات للنظام المحسن"""
    
    requirements = """# متطلبات النظام المحسن للمعالجة المتتالية
# Enhanced Batch Processing System Requirements

# المكتبات الأساسية
requests>=2.25.0
beautifulsoup4>=4.9.0
google-generativeai>=0.3.0

# مكتبات اختيارية للتحسينات
cloudscraper>=1.2.60
selenium>=4.0.0

# مكتبات واجهة المستخدم (مدمجة مع Python)
# tkinter (built-in)

# مكتبات إضافية للتحسينات
pillow>=8.0.0
pyperclip>=1.8.0

# قواعد البيانات
supabase>=1.0.0

# Firebase (اختياري)
firebase-admin>=6.0.0
"""
    
    try:
        with open("enhanced_requirements.txt", 'w', encoding='utf-8') as f:
            f.write(requirements)
        
        print("✅ تم إنشاء ملف المتطلبات: enhanced_requirements.txt")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف المتطلبات: {e}")
        return False

def create_installation_guide():
    """إنشاء دليل التثبيت"""
    
    guide_content = """# دليل تثبيت النظام المحسن للمعالجة المتتالية
# Enhanced Batch Processing System Installation Guide

## 📋 المتطلبات الأساسية

### 1. Python 3.8 أو أحدث
تأكد من تثبيت Python 3.8 أو إصدار أحدث على نظامك.

### 2. المكتبات المطلوبة
قم بتثبيت المكتبات المطلوبة باستخدام:
```bash
pip install -r enhanced_requirements.txt
```

أو تثبيت المكتبات الأساسية فقط:
```bash
pip install requests beautifulsoup4 google-generativeai
```

## 🚀 التثبيت والتشغيل

### 1. تحضير الملفات
تأكد من وجود الملفات التالية في نفس المجلد:
- `enhanced_batch_processor.py`
- `enhanced_batch_gui.py`
- `enhanced_integration.py`
- `run_enhanced_batch.py`

### 2. إعداد مفاتيح Gemini API
1. احصل على مفاتيح Gemini API من [Google AI Studio](https://makersuite.google.com/app/apikey)
2. قم بإنشاء ملف `enhanced_batch_config.json` مع المحتوى التالي:
```json
{
  "gemini_api_keys": [
    "your-first-gemini-api-key",
    "your-second-gemini-api-key"
  ],
  "last_updated": "2024-01-01T00:00:00"
}
```

### 3. تحضير ملف الروابط
قم بإنشاء ملف نصي (مثل `link1.txt`) يحتوي على روابط MCPEDL:
```
https://mcpedl.com/mod-url-1/
https://mcpedl.com/mod-url-2/
https://mcpedl.com/mod-url-3/
```

### 4. التشغيل

#### تشغيل النظام المحسن منفرداً:
```bash
python run_enhanced_batch.py
```

#### تشغيل النظام مع الملف الأساسي:
```bash
python mod_processor_broken_final.py
```

## ⚙️ الإعدادات المتقدمة

### تخصيص إعدادات المعالجة
يمكنك تعديل الإعدادات في ملف `enhanced_batch_config.json`:
```json
{
  "gemini_api_keys": ["key1", "key2"],
  "auto_save_progress": true,
  "smart_resume": true,
  "delay_between_requests": 2,
  "max_retries": 3
}
```

### ملفات النظام
- `batch_progress.json`: ملف حفظ التقدم
- `extracted_mods.json`: ملف المودات المستخرجة
- `enhanced_batch_config.json`: ملف الإعدادات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في مفاتيح Gemini**: تأكد من صحة المفاتيح وعدم انتهاء صلاحيتها
2. **خطأ في الاتصال**: تحقق من اتصال الإنترنت
3. **ملفات مفقودة**: تأكد من وجود جميع الملفات المطلوبة

### الحصول على المساعدة:
- تحقق من سجل الأخطاء في الواجهة
- استخدم زر "🧪 اختبار المفاتيح" للتحقق من Gemini
- راجع ملف `batch_progress.json` لمعرفة آخر حالة

## 📊 الميزات المتاحة

### الواجهة المحسنة:
- إدارة ذكية لمفاتيح Gemini API
- حفظ واستئناف التقدم تلقائياً
- معالجة متتالية محسنة
- إحصائيات مفصلة
- إعادة نشر جميع المودات

### التكامل مع النظام الأساسي:
- معالجة سريعة
- استئناف آخر جلسة
- عرض الإحصائيات
- إعدادات متقدمة

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطية**: يتم إنشاء نسخ احتياطية تلقائياً
2. **حدود API**: راقب استخدام مفاتيح Gemini لتجنب تجاوز الحدود
3. **الأداء**: استخدم تأخير مناسب بين الطلبات لتجنب الحظر
4. **الأمان**: احتفظ بمفاتيح API آمنة ولا تشاركها

## 🎯 نصائح للاستخدام الأمثل

1. استخدم عدة مفاتيح Gemini للحصول على أداء أفضل
2. قم بحفظ التقدم بانتظام
3. راجع الإحصائيات لمتابعة الأداء
4. استخدم المعالجة السريعة للملفات الصغيرة
5. استخدم الواجهة المحسنة للتحكم الكامل
"""
    
    try:
        with open("ENHANCED_INSTALLATION_GUIDE.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print("✅ تم إنشاء دليل التثبيت: ENHANCED_INSTALLATION_GUIDE.md")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دليل التثبيت: {e}")
        return False

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🚀 بدء تثبيت النظام المحسن للمعالجة المتتالية")
    print("=" * 60)
    
    success_count = 0
    total_steps = 4
    
    # الخطوة 1: دمج النظام مع الملف الأساسي
    print("\n📝 الخطوة 1: دمج النظام مع الملف الأساسي...")
    if integrate_enhanced_system_to_main_file():
        success_count += 1
    
    # الخطوة 2: إنشاء مشغل النظام
    print("\n🚀 الخطوة 2: إنشاء مشغل النظام...")
    if create_enhanced_launcher():
        success_count += 1
    
    # الخطوة 3: إنشاء ملف المتطلبات
    print("\n📋 الخطوة 3: إنشاء ملف المتطلبات...")
    if create_requirements_file():
        success_count += 1
    
    # الخطوة 4: إنشاء دليل التثبيت
    print("\n📖 الخطوة 4: إنشاء دليل التثبيت...")
    if create_installation_guide():
        success_count += 1
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{total_steps} خطوات نجحت")
    
    if success_count == total_steps:
        print("🎉 تم تثبيت النظام المحسن بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. قم بتثبيت المتطلبات: pip install -r enhanced_requirements.txt")
        print("2. أضف مفاتيح Gemini API في ملف enhanced_batch_config.json")
        print("3. قم بتشغيل النظام: python run_enhanced_batch.py")
    else:
        print("⚠️ تم التثبيت مع بعض المشاكل. راجع الرسائل أعلاه.")

if __name__ == "__main__":
    import time
    main()
