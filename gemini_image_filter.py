# -*- coding: utf-8 -*-
"""
فلتر ذكي للصور باستخدام Gemini AI لاستخراج الصور المتعلقة بالمود فقط من صفحات MCPEDL
"""

import google.generativeai as genai
import requests
import base64
import json
import re
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import time

# محاولة استيراد التحسينات
try:
    from gemini_filter_enhancements import GeminiFilterEnhancements
    ENHANCEMENTS_AVAILABLE = True
except ImportError:
    ENHANCEMENTS_AVAILABLE = False

class GeminiImageFilter:
    """فلتر ذكي للصور باستخدام Gemini AI"""

    def __init__(self, api_key: str):
        """تهيئة Gemini AI"""
        try:
            if not api_key or api_key.strip() == "":
                raise ValueError("مفتاح API فارغ أو غير صالح")

            # تكوين Gemini مع المفتاح
            genai.configure(api_key=api_key.strip())

            # استخدام النموذج الذي يدعم الصور
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.api_key = api_key.strip()

            # اختبار سريع للتأكد من صحة المفتاح
            try:
                test_response = self.model.generate_content("test")
                print("✅ تم التحقق من صحة مفتاح Gemini API")
            except Exception as test_e:
                print(f"⚠️ تحذير: قد تكون هناك مشكلة في مفتاح API: {test_e}")

            # تهيئة التحسينات إذا كانت متوفرة
            if ENHANCEMENTS_AVAILABLE:
                self.enhancements = GeminiFilterEnhancements()
                print("✅ تم تهيئة Gemini Image Filter مع التحسينات")
            else:
                self.enhancements = None
                print("✅ تم تهيئة Gemini Image Filter بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تهيئة Gemini Image Filter: {e}")
            self.model = None
            self.api_key = None
            self.enhancements = None

    def filter_mod_images(self, image_urls: List[str], mod_name: str, mod_description: str = "") -> List[str]:
        """فلترة الصور لاختيار الصور المتعلقة بالمود فقط"""
        if not self.model or not image_urls:
            print("⚠️ Gemini غير متوفر أو لا توجد صور للفلترة")
            return image_urls  # إرجاع جميع الصور بدون فلترة

        start_time = time.time()
        print(f"🔍 بدء فلترة {len(image_urls)} صورة باستخدام Gemini AI...")

        # تطبيق الفلترة الأولية إذا كانت التحسينات متوفرة
        working_images = image_urls
        if self.enhancements:
            working_images = self.enhancements.pre_filter_images(image_urls, mod_name)
            if not working_images:
                print("⚠️ الفلترة الأولية لم تترك أي صور")
                return image_urls[:3]  # إرجاع أول 3 صور كحل احتياطي

        # تقسيم الصور إلى مجموعات صغيرة لتجنب تجاوز حدود API
        batch_size = 5  # معالجة 5 صور في كل مرة
        filtered_images = []

        for i in range(0, len(working_images), batch_size):
            batch = working_images[i:i + batch_size]
            print(f"📦 معالجة المجموعة {i//batch_size + 1}: {len(batch)} صور")

            batch_results = self._filter_image_batch(batch, mod_name, mod_description)
            filtered_images.extend(batch_results)

            # تأخير قصير بين المجموعات لتجنب تجاوز حدود API
            if i + batch_size < len(working_images):
                time.sleep(2)

        # معالجة إضافية للنتائج إذا كانت التحسينات متوفرة
        if self.enhancements:
            filtered_images = self.enhancements.post_process_results(filtered_images, working_images)

            # إنشاء تقرير مفصل
            processing_time = time.time() - start_time
            report = self.enhancements.create_detailed_report(image_urls, filtered_images, processing_time)
            print(f"📊 تقرير الفلترة: {report['summary']['filter_ratio']:.2f} نسبة النجاح")

        print(f"✅ تم فلترة الصور: {len(filtered_images)} من أصل {len(image_urls)}")
        return filtered_images

    def _filter_image_batch(self, image_urls: List[str], mod_name: str, mod_description: str) -> List[str]:
        """فلترة مجموعة من الصور"""
        try:
            # تحميل الصور وتحويلها إلى base64
            image_data_list = []
            valid_urls = []

            for url in image_urls:
                image_data = self._download_and_encode_image(url)
                if image_data:
                    image_data_list.append(image_data)
                    valid_urls.append(url)
                    print(f"✅ تم تحميل الصورة: {url[:50]}...")
                else:
                    print(f"❌ فشل تحميل الصورة: {url[:50]}...")

            if not image_data_list:
                print("⚠️ لم يتم تحميل أي صور صالحة")
                return []

            # إنشاء prompt للذكاء الاصطناعي
            prompt = self._create_filter_prompt(mod_name, mod_description, len(image_data_list))

            # إرسال الطلب إلى Gemini
            response = self._send_gemini_request(prompt, image_data_list)

            if response:
                # تحليل الاستجابة واختيار الصور المناسبة
                selected_indices = self._parse_gemini_response(response)
                selected_images = [valid_urls[i] for i in selected_indices if i < len(valid_urls)]

                print(f"🎯 Gemini اختار {len(selected_images)} صور من أصل {len(valid_urls)}")
                return selected_images
            else:
                print("⚠️ لم يتم الحصول على استجابة من Gemini")
                return valid_urls  # إرجاع جميع الصور الصالحة

        except Exception as e:
            print(f"❌ خطأ في فلترة المجموعة: {e}")
            return image_urls  # إرجاع الصور الأصلية في حالة الخطأ

    def _download_and_encode_image(self, url: str, max_size_mb: float = 2.0) -> Optional[Dict[str, str]]:
        """تحميل الصورة وتحويلها إلى base64 مع تحديد نوع MIME الصحيح"""
        try:
            # تحميل الصورة
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10, stream=True)
            response.raise_for_status()

            # التحقق من حجم الصورة
            content_length = response.headers.get('content-length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                if size_mb > max_size_mb:
                    print(f"⚠️ الصورة كبيرة جداً ({size_mb:.1f}MB): {url[:50]}...")
                    return None

            # قراءة محتوى الصورة
            image_bytes = response.content

            # التحقق من أن الملف صورة فعلاً وتحديد نوع MIME
            mime_type = self._get_image_mime_type(image_bytes)
            if not mime_type:
                print(f"⚠️ الملف ليس صورة صالحة: {url[:50]}...")
                return None

            # تحويل إلى base64
            base64_data = base64.b64encode(image_bytes).decode('utf-8')

            # التحقق من حجم البيانات المُرمزة
            if len(base64_data) > 4 * 1024 * 1024:  # 4MB حد أقصى
                print(f"⚠️ البيانات المُرمزة كبيرة جداً: {url[:50]}...")
                return None

            return {
                "data": base64_data,
                "mime_type": mime_type
            }

        except Exception as e:
            print(f"❌ خطأ في تحميل الصورة {url[:50]}...: {e}")
            return None

    def _get_image_mime_type(self, image_bytes: bytes) -> Optional[str]:
        """تحديد نوع MIME للصورة بناءً على التوقيع"""
        # فحص التوقيعات الشائعة للصور
        if image_bytes.startswith(b'\xFF\xD8\xFF'):
            return "image/jpeg"
        elif image_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
            return "image/png"
        elif image_bytes.startswith(b'GIF87a') or image_bytes.startswith(b'GIF89a'):
            return "image/gif"
        elif image_bytes.startswith(b'RIFF') and b'WEBP' in image_bytes[:12]:
            return "image/webp"
        else:
            return None

    def _is_valid_image(self, image_bytes: bytes) -> bool:
        """التحقق من أن البيانات تمثل صورة صالحة"""
        return self._get_image_mime_type(image_bytes) is not None

    def _create_filter_prompt(self, mod_name: str, mod_description: str, num_images: int) -> str:
        """إنشاء prompt للذكاء الاصطناعي"""
        return f"""أنت خبير في ألعاب Minecraft وتحليل الصور. مهمتك هي فحص الصور المرفقة واختيار الصور التي تتعلق بالمود/الإضافة فقط.

معلومات المود:
- الاسم: {mod_name}
- الوصف: {mod_description[:500]}

لديك {num_images} صور للفحص. يرجى تحليل كل صورة وتحديد ما إذا كانت:

✅ صورة متعلقة بالمود: تُظهر محتوى المود، مميزاته، أو عناصر اللعب الجديدة
❌ صورة غير متعلقة: صور المستخدمين، الإعلانات، أيقونات الموقع، أو مودات أخرى

معايير الاختيار:
1. الصور التي تُظهر عناصر اللعب الجديدة (كتل، عناصر، وحوش، إلخ)
2. لقطات شاشة من داخل اللعبة تُظهر المود
3. صور توضيحية للمميزات
4. واجهات المستخدم الجديدة أو القوائم

معايير الرفض:
1. صور الملفات الشخصية أو الأفاتار
2. صور المودات الأخرى المقترحة
3. إعلانات أو صور ترويجية للموقع
4. أيقونات أو شعارات
5. صور فارغة أو تالفة

أجب بتنسيق JSON فقط:
{{"selected_images": [0, 1, 3]}}

حيث الأرقام تمثل فهارس الصور المختارة (تبدأ من 0).
اختر فقط الصور المتعلقة بالمود بوضوح."""

    def _send_gemini_request(self, prompt: str, image_data_list: List[Dict[str, str]]) -> Optional[str]:
        """إرسال طلب إلى Gemini مع الصور"""
        try:
            # إنشاء محتوى الطلب
            content_parts = [prompt]

            # إضافة الصور مع أنواع MIME الصحيحة
            for i, image_data in enumerate(image_data_list):
                content_parts.append({
                    "mime_type": image_data["mime_type"],
                    "data": image_data["data"]
                })
                print(f"📷 إضافة صورة {i+1} بنوع: {image_data['mime_type']}")

            # إرسال الطلب
            response = self.model.generate_content(content_parts)

            if response and response.text:
                return response.text.strip()
            else:
                print("⚠️ استجابة فارغة من Gemini")
                return None

        except Exception as e:
            print(f"❌ خطأ في إرسال الطلب إلى Gemini: {e}")
            return None

    def _parse_gemini_response(self, response: str) -> List[int]:
        """تحليل استجابة Gemini واستخراج فهارس الصور المختارة"""
        try:
            # البحث عن JSON في الاستجابة
            json_match = re.search(r'\{[^}]*"selected_images"[^}]*\}', response)
            
            if json_match:
                json_str = json_match.group(0)
                data = json.loads(json_str)
                selected_images = data.get("selected_images", [])
                
                # التحقق من صحة الفهارس
                valid_indices = []
                for idx in selected_images:
                    if isinstance(idx, int) and idx >= 0:
                        valid_indices.append(idx)
                
                print(f"🎯 Gemini اختار الفهارس: {valid_indices}")
                return valid_indices
            else:
                print("⚠️ لم يتم العثور على JSON في استجابة Gemini")
                # محاولة استخراج الأرقام مباشرة
                numbers = re.findall(r'\b\d+\b', response)
                indices = [int(n) for n in numbers if int(n) < 10]  # حد أقصى 10 صور
                print(f"🔍 استخراج الأرقام مباشرة: {indices}")
                return indices
                
        except Exception as e:
            print(f"❌ خطأ في تحليل استجابة Gemini: {e}")
            print(f"الاستجابة: {response[:200]}...")
            return []

    def get_api_key_from_config(self) -> Optional[str]:
        """الحصول على مفتاح API من ملف الإعدادات"""
        try:
            import os
            
            # البحث في متغيرات البيئة
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                return api_key
            
            # البحث في ملف config
            config_files = ['config.json', 'settings.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        
                        # البحث في gemini_api_keys (قائمة)
                        if 'gemini_api_keys' in config and config['gemini_api_keys']:
                            keys = config['gemini_api_keys']
                            if isinstance(keys, list) and len(keys) > 0:
                                return keys[0]  # أول مفتاح في القائمة
                        
                        # البحث في gemini_api_key
                        if 'gemini_api_key' in config and config['gemini_api_key']:
                            return config['gemini_api_key']
                            
                    except Exception as e:
                        print(f"خطأ في قراءة {config_file}: {e}")
                        continue
            
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على مفتاح API: {e}")
            return None

def test_gemini_image_filter():
    """اختبار فلتر الصور"""
    print("🧪 اختبار فلتر الصور باستخدام Gemini...")
    
    # إنشاء فلتر (يحتاج مفتاح API حقيقي)
    filter_instance = GeminiImageFilter("your-api-key-here")
    
    if not filter_instance.model:
        print("⚠️ لم يتم تهيئة Gemini - الاختبار متوقف")
        return
    
    # صور تجريبية
    test_images = [
        "https://media.forgecdn.net/attachments/123/456/dragon_screenshot.png",
        "https://mcpedl.com/wp-content/uploads/2023/01/mod_preview.jpg",
        "https://gravatar.com/avatar/user123.png"  # صورة مستخدم (يجب رفضها)
    ]
    
    # معلومات المود التجريبية
    mod_name = "Dragon Mounts"
    mod_description = "Adds rideable dragons to Minecraft"
    
    # تشغيل الفلتر
    filtered_images = filter_instance.filter_mod_images(test_images, mod_name, mod_description)
    
    print(f"✅ النتيجة: {len(filtered_images)} صور مفلترة من أصل {len(test_images)}")
    for img in filtered_images:
        print(f"   - {img}")

if __name__ == "__main__":
    test_gemini_image_filter()
