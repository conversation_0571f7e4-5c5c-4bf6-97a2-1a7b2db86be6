{"minecraft:entity": {"format_version": "1.21.0", "description": {"identifier": "effect99:drone", "is_summonable": true, "is_spawnable": false, "is_experimental": false, "properties": {"outline:display": {"type": "bool", "default": false, "client_sync": true}, "outline:default": {"type": "int", "range": [0, 13], "default": 0, "client_sync": true}, "outline:color_r": {"type": "float", "range": [0.0, 1.0], "default": 0.0, "client_sync": true}, "outline:color_g": {"type": "float", "range": [0.0, 1.0], "default": 0.0, "client_sync": true}, "outline:color_b": {"type": "float", "range": [0.0, 1.0], "default": 0.0, "client_sync": true}}, "scripts": {"animate": ["initSystem"]}, "animations": {"initSystem": "controller.animation.initSystem"}}, "component_groups": {"solarPanelAdded": {"minecraft:is_chested": {}}, "solarPanelOn": {"minecraft:is_sheared": {}}, "flyMode": {"minecraft:variant": {"value": 0}, "minecraft:physics": {"push_towards_closest_space": true, "has_gravity": false}, "minecraft:flying_speed": {"value": 0.1}, "minecraft:can_fly": {}, "minecraft:collision_box": {"width": 0.6, "height": 1}, "minecraft:navigation.hover": {"can_sink": false, "can_path_from_air": true, "can_path_over_water": true, "can_swim": true}, "minecraft:movement.hover": {}, "minecraft:rideable": {"seat_count": 1, "passenger_max_width": 1.375, "pull_in_entities": false, "seats": [{"position": [0.0, -1.3, 0.0], "min_rider_count": 0, "max_rider_count": 1, "rotate_rider_by": 0, "lock_rider_rotation": 0}]}}, "walkMode": {"minecraft:variant": {"value": 1}, "minecraft:physics": {"push_towards_closest_space": true, "has_gravity": true}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:navigation.walk": {"can_path_from_air": false}, "minecraft:movement.basic": {}, "minecraft:rideable": {"seat_count": 1, "passenger_max_width": 1.375, "pull_in_entities": false, "seats": [{"position": [0.0, 1, 0.0], "min_rider_count": 0, "max_rider_count": 1, "rotate_rider_by": 0, "lock_rider_rotation": 0}]}}, "offMode": {"minecraft:variant": {"value": 2}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:physics": {"push_towards_closest_space": true, "has_gravity": true}}, "onMode": {"minecraft:behavior.look_at_player": {"priority": 2, "look_distance": 6, "probability": 1}, "minecraft:behavior.look_at_entity": {"priority": 3, "look_distance": 8, "probability": 0.01, "filters": {"test": "is_family", "subject": "other", "value": "mob"}}}, "attack_target": {"minecraft:behavior.nearest_attackable_target": {"priority": 1, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "target"}, "max_dist": 18, "must_see": true, "must_see_forget_duration": 0, "reevaluate_description": true}], "must_see": true, "reselect_targets": true}}, "attack_monsters": {"minecraft:behavior.nearest_attackable_target": {"priority": 1, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "!=", "value": "enderman"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "creeper"}, {"any_of": [{"test": "is_family", "subject": "other", "value": "monster"}, {"test": "is_family", "subject": "other", "value": "target"}]}]}, "max_dist": 18, "must_see": true, "must_see_forget_duration": 0, "reevaluate_description": true}], "must_see": true, "reselect_targets": true}}, "has_rider": {"minecraft:skin_id": {"value": 2}}, "without_rider": {"minecraft:skin_id": {"value": 0}}, "item_hopper": {"minecraft:item_hopper": {}}, "name_partially_show": {"minecraft:nameable": {"always_show": false}}, "name_always_show": {"minecraft:nameable": {"always_show": true}}, "despawn": {"minecraft:instant_despawn": {}}}, "components": {"minecraft:type_family": {"family": ["drone", "bot", "BOT-Entity"]}, "minecraft:health": {"value": 60, "max": 60, "min": 1}, "minecraft:inventory": {"inventory_size": 5, "container_type": "hopper"}, "minecraft:collision_box": {"width": 0.6, "height": 1}, "minecraft:variant": {"value": 0}, "minecraft:mark_variant": {"value": 0}, "minecraft:skin_id": {"value": 0}, "minecraft:balloonable": {"mass": 0.8}, "minecraft:is_stackable": {}, "minecraft:movement": {"value": 0}, "minecraft:follow_range": {"value": 24, "max": 24}, "minecraft:jump.static": {}, "minecraft:shooter": {"def": "drone:laser", "sound": "shoot"}, "minecraft:behavior.ranged_attack": {"attack_interval": 0.1, "attack_radius": 16}, "minecraft:damage_sensor": {"triggers": [{"cause": "fall", "deals_damage": false}, {"cause": "magic", "deals_damage": false}, {"cause": "wither", "deals_damage": false}, {"cause": "void", "deals_damage": false}, {"cause": "contact", "deals_damage": false}, {"cause": "drowning", "deals_damage": false}, {"cause": "falling_block", "deals_damage": false}, {"cause": "suffocation", "deals_damage": false}, {"cause": "thorns", "deals_damage": false}, {"cause": "projectile", "deals_damage": false}]}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_sneak_held", "subject": "other", "value": false}]}, "target": "self", "event": "interact"}, "interact_text": "action.interact.opencontainer"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "health_amount": 15, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"all_of": [{"test": "is_mark_variant", "value": 4}, {"test": "rider_count", "value": 1}]}, "event": "has_rider"}, {"filters": {"all_of": [{"test": "is_mark_variant", "value": 4}, {"test": "rider_count", "value": 0}]}, "event": "without_rider"}, {"filters": {"test": "is_variant", "value": 2}, "event": "silenced"}, {"filters": {"all_of": [{"test": "has_component", "value": "minecraft:is_chested"}, {"test": "in_overworld"}, {"test": "is_daytime"}, {"test": "has_component", "operator": "!=", "value": "minecraft:is_sheared"}, {"test": "is_brightness", "operator": ">", "value": 0.8}]}, "event": "solarPanelOn"}, {"filters": {"all_of": [{"test": "has_component", "value": "minecraft:is_sheared"}, {"any_of": [{"test": "has_component", "operator": "!=", "value": "minecraft:is_chested"}, {"test": "in_overworld", "value": false}, {"test": "is_daytime", "value": false}, {"test": "is_brightness", "operator": "<", "value": 0.8}]}]}, "event": "solarPanelOff"}]}, "minecraft:fire_immune": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:persistent": {}, "minecraft:tick_world": {}, "minecraft:pushable": {}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["flyMode", "onMode"]}}, "flyMode": {"remove": {"component_groups": "walkMode"}, "add": {"component_groups": "flyMode"}}, "walkMode": {"remove": {"component_groups": "flyMode"}, "add": {"component_groups": "walkMode"}}, "offMode": {"remove": {"component_groups": ["onMode", "flyMode", "walkMode", "attack_target"]}, "add": {"component_groups": "offMode"}}, "attack_target": {"add": {"component_groups": "attack_target"}}, "attack_monsters": {"add": {"component_groups": "attack_monsters"}}, "solarPanelOn": {"add": {"component_groups": "solarPanelOn"}}, "solarPanelOff": {"remove": {"component_groups": "solarPanelOn"}}, "addSolarPanel": {"add": {"component_groups": "solarPanelAdded"}}, "removePanel": {"remove": {"component_groups": ["solarPanelAdded", "solarPanelOn"]}}, "itemHopper": {"add": {"component_groups": "item_hopper"}}, "do_not_show": {"remove": {"component_groups": "name_always_show"}}, "name_always_show": {"add": {"component_groups": "name_always_show"}}, "name_partially_show": {"add": {"component_groups": "name_partially_show"}}, "has_rider": {"add": {"component_groups": "has_rider"}}, "without_rider": {"add": {"component_groups": "without_rider"}}, "default": {"add": {"component_groups": "onMode"}, "remove": {"component_groups": ["attack_target", "item_hopper"]}}, "silenced": {"queue_command": {"command": "stopsound @a[r=16] drone.ambient"}}, "interact": {"queue_command": {"command": "scriptevent drone:interact"}}, "despawn": {"add": {"component_groups": "despawn"}}}}}