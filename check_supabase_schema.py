# -*- coding: utf-8 -*-
"""
فحص بنية جدول Supabase
Check Supabase Table Schema
"""

import json
from supabase import create_client, Client

def check_supabase_schema():
    """فحص بنية جدول mods في Supabase"""
    print("🔍 فحص بنية جدول mods في Supabase")
    print("=" * 50)
    
    try:
        # تحميل إعدادات Supabase
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            api_keys = json.load(f)
        
        supabase_url = api_keys.get('supabase_url')
        supabase_key = api_keys.get('SUPABASE_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ إعدادات Supabase غير مكتملة")
            return False
        
        # إنشاء عميل Supabase
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # محاولة جلب سجل واحد لمعرفة البنية
        print("🔄 جلب سجل واحد لمعرفة بنية الجدول...")
        response = supabase.table('mods').select('*').limit(1).execute()
        
        if response.data:
            print("✅ تم جلب البيانات بنجاح!")
            print("📊 بنية الجدول:")
            
            # عرض الحقول المتاحة
            if response.data:
                sample_record = response.data[0]
                print("🔑 الحقول المتاحة:")
                for key in sample_record.keys():
                    print(f"   - {key}: {type(sample_record[key]).__name__}")
            
            return True
        else:
            print("⚠️ الجدول فارغ، سنحاول إدراج بيانات بسيطة لمعرفة الحقول المطلوبة")
            
            # محاولة إدراج بيانات بسيطة
            simple_data = {
                "name": "Test Schema Check",
                "description": "Testing schema"
            }
            
            try:
                response = supabase.table('mods').insert(simple_data).execute()
                if response.data:
                    print("✅ تم إدراج البيانات البسيطة بنجاح!")
                    print("📊 الحقول المقبولة:")
                    for key in response.data[0].keys():
                        print(f"   - {key}")
                    
                    # حذف السجل التجريبي
                    record_id = response.data[0].get('id')
                    if record_id:
                        supabase.table('mods').delete().eq('id', record_id).execute()
                        print("🗑️ تم حذف السجل التجريبي")
                    
                    return True
            except Exception as insert_error:
                print(f"❌ خطأ في إدراج البيانات البسيطة: {insert_error}")
                return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص بنية Supabase: {e}")
        return False

if __name__ == "__main__":
    check_supabase_schema()
