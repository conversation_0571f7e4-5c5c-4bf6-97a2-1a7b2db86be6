# 🛠️ أداة تخصيص وإصلاح المودات المحسنة
# Enhanced Mod Editor and Repair Tool

## 📋 نظرة عامة

أداة شاملة ومتقدمة لتخصيص وإصلاح مودات Minecraft المستخرجة من MCPEDL. تتيح لك الأداة معاينة وتحرير وإصلاح جميع جوانب المودات قبل نشرها.

## ✨ الميزات الرئيسية

### 🎮 واجهة مستخدم متقدمة
- **تبويبات منظمة**: معلومات أساسية، أوصاف، صور، روابط، معاينة ونشر
- **تنقل سهل**: أزرار للانتقال بين المودات مع حفظ التغييرات تلقائياً
- **معاينة فورية**: عرض شامل للمود قبل النشر

### 📝 تحرير شامل للمحتوى
- **المعلومات الأساسية**: اسم المود، الإصدار، الفئة، المنشئ
- **الأوصاف المتعددة**: عربي، إنجليزي، تليجرام (عربي/إنجليزي)
- **قائمة الميزات**: إدارة وتنسيق ميزات المود
- **الروابط والملفات**: تحرير روابط التحميل والمصدر

### 🖼️ إدارة الصور المتقدمة
- **إضافة الصور**: من ملفات محلية أو روابط
- **معاينة الصور**: عرض مصغر مع إمكانية المعاينة الكاملة
- **إدارة الصور**: حذف، تعديل، تعيين كصورة رئيسية
- **تحليل الصور**: فحص حجم ونوع الصور

### 🤖 ذكاء اصطناعي متكامل (Gemini AI)
- **إصلاح الأوصاف**: تصحيح الأخطاء النحوية والإملائية
- **توليد المحتوى**: إنشاء أوصاف تليجرام وقوائم الميزات
- **الترجمة**: ترجمة بين العربية والإنجليزية
- **إصلاح شامل**: إصلاح جميع جوانب المود بضغطة واحدة

### 📤 نشر وتصدير متقدم
- **نشر المودات**: نشر مع التحقق من البيانات المطلوبة
- **تصدير فردي**: تصدير مود واحد كملف JSON
- **معاينة شاملة**: عرض جميع بيانات المود قبل النشر
- **إدارة المشاكل**: وضع علامات على المودات المشكلة

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
# Python 3.8 أو أحدث
python --version

# المكتبات المطلوبة
pip install Pillow requests google-generativeai
```

### الملفات المطلوبة
- `enhanced_mod_editor.py` - الملف الرئيسي للأداة
- `mod_editor_integration.py` - ملف التكامل مع النظام الأساسي
- `run_mod_editor.py` - مشغل الأداة المستقل

### طرق التشغيل

#### 1. تشغيل مستقل
```bash
python run_mod_editor.py
```

#### 2. تشغيل من النظام الأساسي
```bash
python mod_processor_broken_final.py
# ثم استخدم قسم "أداة تخصيص المودات"
```

#### 3. تشغيل مباشر
```bash
python enhanced_mod_editor.py
```

## ⚙️ الإعداد والتكوين

### إعداد مفاتيح Gemini AI
1. احصل على مفاتيح API من [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ ملف `enhanced_batch_config.json`:
```json
{
  "gemini_api_keys": [
    "your-first-gemini-api-key",
    "your-second-gemini-api-key"
  ],
  "last_updated": "2024-01-01T00:00:00"
}
```

### ملفات البيانات المدعومة
- `extracted_mods.json` - المودات المستخرجة من النظام الأساسي
- `published_mods.json` - المودات المنشورة
- `problematic_mods.json` - المودات التي تحتاج إصلاح
- `sample_mods.json` - بيانات نموذجية للاختبار

## 📖 دليل الاستخدام

### 1. تحميل المودات
- استخدم زر "📂 فتح ملف المودات" لتحميل ملف JSON
- أو استخدم "📝 تحرير المودات المستخرجة" من النظام الأساسي

### 2. تحرير المعلومات الأساسية
- **تبويب "📋 معلومات أساسية"**:
  - اسم المود والإصدار
  - الفئة والمنشئ
  - قائمة الميزات مع إمكانية التوليد التلقائي

### 3. تحرير الأوصاف
- **تبويب "📝 الأوصاف"**:
  - وصف عربي وإنجليزي كامل
  - أوصاف تليجرام قصيرة (300-400 حرف)
  - أزرار الإصلاح والترجمة لكل وصف

### 4. إدارة الصور
- **تبويب "🖼️ الصور"**:
  - إضافة صور من ملفات أو روابط
  - معاينة وحذف الصور
  - تعيين الصورة الرئيسية

### 5. إدارة الروابط
- **تبويب "🔗 روابط وملفات"**:
  - رابط تحميل المود مع اختبار الرابط
  - رابط الصفحة المصدر
  - رفع ملفات جديدة أو من روابط

### 6. المعاينة والنشر
- **تبويب "👁️ معاينة ونشر"**:
  - معاينة شاملة لجميع بيانات المود
  - نشر المود أو وضع علامة مشكلة
  - تصدير كملف JSON

## 🔧 الميزات المتقدمة

### الإصلاح الشامل بـ Gemini
- استخدم زر "🤖 إصلاح بـ Gemini" للإصلاح التلقائي
- اختر العناصر المراد إصلاحها:
  - إصلاح الأوصاف العربية والإنجليزية
  - توليد قائمة الميزات
  - إنشاء أوصاف التليجرام

### التنقل والحفظ
- **التنقل**: أزرار "⬅️ السابق" و "➡️ التالي"
- **الحفظ التلقائي**: يتم حفظ التغييرات عند التنقل
- **إعادة التعيين**: زر "🔄 إعادة تعيين" لإلغاء التغييرات

### النشر المتقدم
- **نشر فردي**: "✅ نشر المود"
- **نشر ومتابعة**: "📤 نشر وانتقال للتالي"
- **تخطي**: "⏭️ تخطي هذا المود"
- **وضع علامة**: "🚫 وضع علامة كمشكلة"

## 📊 إدارة البيانات

### ملفات الإخراج
- `published_mods.json` - المودات المنشورة
- `problematic_mods.json` - المودات المشكلة
- `edited_mods_backup.json` - نسخة احتياطية

### التصدير والاستيراد
- تصدير مود واحد كملف JSON
- استيراد مودات من ملفات مختلفة
- نسخ احتياطية تلقائية

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في تحميل الصور
```
الحل: تحقق من صحة روابط الصور واتصال الإنترنت
```

#### 2. فشل في Gemini AI
```
الحل: تحقق من مفاتيح API في ملف enhanced_batch_config.json
```

#### 3. خطأ في حفظ الملفات
```
الحل: تحقق من صلاحيات الكتابة في المجلد
```

### رسائل الخطأ
- **"Gemini غير متاح"**: أضف مفاتيح API صحيحة
- **"لا يوجد مود للتحرير"**: حمّل ملف مودات أولاً
- **"فشل في تحميل الصورة"**: تحقق من رابط الصورة

## 🎯 نصائح للاستخدام الأمثل

### 1. تحضير البيانات
- تأكد من وجود مفاتيح Gemini AI للميزات المتقدمة
- استخدم ملفات مودات من النظام الأساسي
- احتفظ بنسخ احتياطية من البيانات

### 2. سير العمل المقترح
1. حمّل ملف المودات
2. راجع المعلومات الأساسية
3. استخدم الإصلاح الشامل بـ Gemini
4. راجع وعدّل الأوصاف حسب الحاجة
5. تحقق من الصور والروابط
6. اعرض المعاينة واختبر البيانات
7. انشر المود أو ضع علامة مشكلة

### 3. تحسين الأداء
- استخدم عدة مفاتيح Gemini لتجنب حدود الاستخدام
- احفظ التغييرات بانتظام
- استخدم المعاينة للتحقق من البيانات

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- راجع رسائل شريط الحالة في الأداة
- استخدم ملف `sample_mods.json` للاختبار
- تحقق من ملفات السجل للأخطاء

### التطوير والتحسين
- الأداة قابلة للتوسع والتخصيص
- يمكن إضافة ميزات جديدة بسهولة
- متوافقة مع النظام الأساسي للمعالجة

---

## 📄 الترخيص

هذه الأداة مطورة لاستخدام شخصي وتعليمي. يرجى احترام حقوق المحتوى والمطورين عند استخدام المودات.

---

**تم تطوير هذه الأداة لتحسين تجربة إدارة مودات Minecraft وتسهيل عملية النشر والتخصيص.**
