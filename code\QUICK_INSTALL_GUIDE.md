# دليل التطبيق السريع للإصلاحات

## خطوات التطبيق

### 1. نسخ الملفات الجديدة

انسخ هذه الملفات إلى مجلد أداتك:
- `mcpedl_scraper_fixed.py` 
- `updated_functions.py`

### 2. تعديل الملف الرئيسي

في ملف `mod_processor_broken_final.py`:

#### أ) استبدال الاستيراد
```python
# ابحث عن هذا السطر:
from mcpedl_scraper_module import scrape_mcpedl_mod, MCPEDLScraper

# استبدله بـ:
from mcpedl_scraper_fixed import scrape_mcpedl_mod, MCPEDLScraperFixed
```

#### ب) استبدال دالة clean_basic_description
```python
# ابحث عن دالة clean_basic_description واستبدلها بـ:
def clean_basic_description(description: str) -> str:
    """تنظيف الوصف الأساسي من المعلومات غير المرغوبة"""
    if not description:
        return ""

    # إزالة أي نصوص بين أقواس مربعة مثل [ENGLISH_DESCRIPTION]
    description = re.sub(r'\[.*?\]', '', description)
    
    # إزالة المسافات الكبيرة بين الجمل
    description = re.sub(r'\n\s*\n', ' ', description)
    
    # إزالة المسافات الزائدة
    description = re.sub(r'\s+', ' ', description)
    
    # إزالة النقاط والعلامات الزائدة في بداية النص
    description = re.sub(r'^[\*\-\•\s]+', '', description)
    
    # إزالة أي عناوين أو فقرات منسقة
    description = re.sub(r'^#+\s*', '', description, flags=re.MULTILINE)
    
    # تنظيف النص من الكلمات التسويقية المفرطة
    marketing_words = [
        'fundamentally enhance', 'exceptional experience', 'remarkable addition',
        'innovative gameplay mechanics', 'completely transform', 'effortlessly navigating',
        'expansive landscapes', 'crucial discoveries', 'treasured locations',
        'newfound confidence', 'deepening your immersion', 'transformative mechanics',
        'visual excellence', 'high-quality textures', 'seamlessly into', 
        'elevating its aesthetic appeal', 'engineered for smooth performance',
        'fluid and responsive experience', 'delve into its added layers',
        'exceptionally easy installation', 'absolutely no technical expertise',
        'captivating content', 'unlock a more streamlined', 'true potential'
    ]
    
    for word in marketing_words:
        description = description.replace(word, '')
    
    # تنظيف المسافات الزائدة مرة أخرى
    description = re.sub(r'\s+', ' ', description)
    
    return description.strip()
```

#### ج) استبدال دالة generate_telegram_descriptions_task

ابحث عن دالة `generate_telegram_descriptions_task` واستبدل البرومبت فيها:

```python
# استبدل البرومبت الموجود بـ:
prompt = f"""
اكتب وصفين بسيطين ومختصرين لمود Minecraft:

اسم المود: {mod_name}
النوع: {mod_category}
الوصف الموجود: {existing_description[:300] if existing_description else "غير متوفر"}

متطلبات:
- وصف عربي بسيط في جملة أو جملتين
- وصف إنجليزي بسيط في جملة أو جملتين  
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية
- بدون أي نصوص إضافية أو علامات

قدم الإجابة بالتنسيق التالي:

العربي: [الوصف العربي هنا]

الإنجليزي: [الوصف الإنجليزي هنا]
"""
```

وفي نفس الدالة، استبدل قسم استخراج الأوصاف:

```python
# بدلاً من استخراج [ENGLISH_DESCRIPTION] و [ARABIC_DESCRIPTION]
# استخدم هذا الكود:

arabic_desc = ""
english_desc = ""

lines = response.split('\n')
for line in lines:
    line = line.strip()
    if line.startswith('العربي:'):
        arabic_desc = line.replace('العربي:', '').strip()
    elif line.startswith('الإنجليزي:'):
        english_desc = line.replace('الإنجليزي:', '').strip()

# تنظيف الأوصاف
if arabic_desc:
    arabic_desc = clean_basic_description(arabic_desc)
if english_desc:
    english_desc = clean_basic_description(english_desc)
```

### 3. اختبار التحديثات

1. شغل الأداة
2. استخدم رابط الاختبار: `https://mcpedl.com/take-a-seat/`
3. تحقق من:
   - الوصف بسيط بدون فقرات
   - عدم وجود `[ENGLISH_DESCRIPTION]`
   - استخراج الصورة الرئيسية فقط

## نصائح مهمة

1. **احتفظ بنسخة احتياطية** من الملفات الأصلية
2. **اختبر مع رابط واحد** أولاً قبل الاستخدام الكامل
3. إذا ظهرت أخطاء، تأكد من نسخ الكود بالكامل
4. تأكد من أن الملف `mcpedl_scraper_fixed.py` في نفس مجلد الأداة

## حل المشاكل الشائعة

### خطأ "module not found"
- تأكد من وضع `mcpedl_scraper_fixed.py` في نفس المجلد
- أعد تشغيل الأداة

### لا زالت تظهر النصوص الإضافية
- تأكد من تعديل البرومبت في `generate_telegram_descriptions_task`
- تأكد من استبدال دالة `clean_basic_description`

### الصور لا زالت مقترحة
- تأكد من استبدال استيراد `mcpedl_scraper_module` بـ `mcpedl_scraper_fixed`
- أعد تشغيل الأداة

## للدعم
إذا واجهت مشاكل، تأكد من:
1. نسخ الكود بالكامل
2. حفظ الملفات بترميز UTF-8
3. إعادة تشغيل الأداة بعد التعديلات
