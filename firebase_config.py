# -*- coding: utf-8 -*-
"""
Firebase Integration Module
إدارة Firebase Storage و Firestore لرفع الملفات وإدارة قاعدة البيانات
"""

import os
import json
import time
import random
import string
from typing import Optional, Dict, Any, List
import mimetypes
import tempfile

try:
    import firebase_admin
    from firebase_admin import credentials, storage, firestore
    FIREBASE_SDK_AVAILABLE = True
except ImportError:
    FIREBASE_SDK_AVAILABLE = False
    print("Warning: Firebase Admin SDK not found. Install it using: pip install firebase-admin")

# معلومات مشروع Firebase الخاص بك
FIREBASE_PROJECT_CONFIG = {
    "project_id": "download-e33a2",
    "storage_bucket": "download-e33a2.firebasestorage.app",
    "auth_domain": "download-e33a2.firebaseapp.com",
    "messaging_sender_id": "************",
    "app_id": "1:************:web:df601f640fb82d9c42bc46"
}

class FirebaseManager:
    """مدير Firebase للتعامل مع Storage و Firestore"""
    
    def __init__(self):
        self.app = None
        self.storage_bucket = None
        self.firestore_client = None
        self.is_initialized = False
        self.config_file = "firebase_config.json"
        
    def initialize_firebase(self, service_account_path: str, storage_bucket_name: str = None) -> bool:
        """تهيئة Firebase باستخدام ملف service account"""
        if not FIREBASE_SDK_AVAILABLE:
            print("❌ Firebase SDK غير متوفر")
            return False

        try:
            # استخدام إعدادات المشروع الافتراضية إذا لم يتم تحديد bucket
            if not storage_bucket_name:
                storage_bucket_name = FIREBASE_PROJECT_CONFIG["storage_bucket"]

            # التحقق من وجود ملف service account
            if not os.path.exists(service_account_path):
                print(f"❌ ملف service account غير موجود: {service_account_path}")
                return False

            # إغلاق التطبيق السابق إذا كان موجوداً
            if self.app:
                try:
                    firebase_admin.delete_app(self.app)
                except:
                    pass

            # تهيئة Firebase Admin SDK
            cred = credentials.Certificate(service_account_path)
            self.app = firebase_admin.initialize_app(cred, {
                'storageBucket': storage_bucket_name
            })

            # تهيئة Storage
            self.storage_bucket = storage.bucket()

            # تهيئة Firestore (اختياري)
            try:
                self.firestore_client = firestore.client()
            except Exception as firestore_error:
                print(f"⚠️ تحذير: لم يتم تهيئة Firestore: {firestore_error}")
                self.firestore_client = None

            self.is_initialized = True
            print("✅ تم تهيئة Firebase بنجاح")

            # حفظ الإعدادات
            self.save_config({
                'service_account_path': service_account_path,
                'storage_bucket_name': storage_bucket_name,
                'initialized': True
            })

            return True

        except Exception as e:
            print(f"❌ خطأ في تهيئة Firebase: {e}")
            return False

    def initialize_with_default_config(self, service_account_path: str) -> bool:
        """تهيئة Firebase باستخدام الإعدادات الافتراضية للمشروع"""
        return self.initialize_firebase(
            service_account_path,
            FIREBASE_PROJECT_CONFIG["storage_bucket"]
        )
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات Firebase من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل إعدادات Firebase: {e}")
        return {}
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """حفظ إعدادات Firebase في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات Firebase: {e}")
            return False
    
    def auto_initialize(self) -> bool:
        """محاولة تهيئة Firebase تلقائياً باستخدام ملف الخدمة الافتراضي"""
        service_account_path = "firebase-service-account.json"
        if os.path.exists(service_account_path):
            print("🔄 محاولة التهيئة التلقائية باستخدام firebase-service-account.json...")
            return self.initialize_with_default_config(service_account_path)
        else:
            print(f"❌ ملف خدمة Firebase الافتراضي غير موجود: {service_account_path}")
            return False
    
    def upload_file_to_storage(self, file_content: bytes, file_name: str, 
                              content_type: str = 'application/octet-stream',
                              folder: str = '') -> Optional[str]:
        """رفع ملف إلى Firebase Storage"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
            
        try:
            # إنشاء مسار الملف
            if folder:
                file_path = f"{folder}/{file_name}"
            else:
                file_path = file_name
            
            # رفع الملف
            blob = self.storage_bucket.blob(file_path)
            blob.upload_from_string(file_content, content_type=content_type)
            
            # جعل الملف قابل للوصول العام
            blob.make_public()

            # إرجاع الرابط العام الصحيح
            public_url = blob.public_url
            print(f"✅ تم رفع الملف إلى Firebase Storage: {file_name}")
            return public_url
            
        except Exception as e:
            print(f"❌ خطأ في رفع الملف إلى Firebase Storage: {e}")
            return None
    
    def upload_mod_to_storage(self, mod_content: bytes, original_filename: str) -> Optional[str]:
        """رفع ملف مود إلى Firebase Storage"""
        try:
            # فحص الملفات التجريبية ومنع رفعها
            test_file_patterns = [
                'test_file',
                'test.mcpack',
                'test.mcaddon',
                'sample_',
                'example_',
                'demo_'
            ]

            filename_lower = original_filename.lower()
            for pattern in test_file_patterns:
                if pattern in filename_lower:
                    print(f"🚫 تم منع رفع الملف التجريبي: {original_filename}")
                    print(f"⚠️ الملفات التجريبية لا يُسمح برفعها إلى Firebase")
                    return None

            # فحص محتوى الملف للتأكد من أنه ليس ملف تجريبي
            if len(mod_content) < 100:  # الملفات الصغيرة جداً قد تكون تجريبية
                content_str = mod_content.decode('utf-8', errors='ignore').lower()
                if any(test_word in content_str for test_word in ['test', 'sample', 'example', 'demo']):
                    print(f"🚫 تم منع رفع ملف تجريبي بناءً على المحتوى: {original_filename}")
                    return None

            # إنشاء اسم ملف فريد
            timestamp = int(time.time())
            random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            base_name, ext = os.path.splitext(original_filename)
            if not ext:
                ext = '.mcpack'

            # تنقيح اسم الملف
            safe_base_name = "".join(c for c in base_name if c.isalnum() or c in ('-', '_')).strip()
            if not safe_base_name:
                safe_base_name = "mod"

            unique_filename = f"{safe_base_name}_{timestamp}_{random_str}{ext}"

            print(f"🔄 رفع مود إلى Firebase Storage: {unique_filename}")
            print(f"📦 حجم الملف: {len(mod_content) / (1024*1024):.2f} MB")

            return self.upload_file_to_storage(
                mod_content,
                unique_filename,
                'application/octet-stream',
                'mods'
            )
        except Exception as e:
            print(f"❌ خطأ في رفع المود: {e}")
            return None
    
    def upload_image_to_storage(self, image_content: bytes, original_filename: str) -> Optional[str]:
        """رفع صورة إلى Firebase Storage"""
        # تحديد نوع المحتوى
        content_type = mimetypes.guess_type(original_filename)[0] or 'image/jpeg'
        
        # إنشاء اسم ملف فريد
        timestamp = int(time.time())
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        base_name, ext = os.path.splitext(original_filename)
        if not ext:
            ext = '.jpg'
        unique_filename = f"img_{base_name}_{timestamp}_{random_str}{ext}"
        
        return self.upload_file_to_storage(
            image_content,
            unique_filename,
            content_type,
            'images'
        )
    
    def add_mod_to_firestore(self, mod_data: Dict[str, Any], collection_name: str = 'mods') -> Optional[str]:
        """إضافة مود إلى Firestore"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
            
        try:
            # إضافة timestamp
            mod_data['created_at'] = firestore.SERVER_TIMESTAMP
            mod_data['updated_at'] = firestore.SERVER_TIMESTAMP
            
            # إضافة المود إلى Firestore
            doc_ref = self.firestore_client.collection(collection_name).add(mod_data)
            doc_id = doc_ref[1].id
            
            print(f"✅ تم إضافة المود إلى Firestore بمعرف: {doc_id}")
            return doc_id
            
        except Exception as e:
            print(f"❌ خطأ في إضافة المود إلى Firestore: {e}")
            return None
    
    def get_mods_from_firestore(self, collection_name: str = 'mods', limit: int = 100) -> List[Dict[str, Any]]:
        """جلب المودات من Firestore"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return []
            
        try:
            docs = self.firestore_client.collection(collection_name).limit(limit).stream()
            mods = []
            for doc in docs:
                mod_data = doc.to_dict()
                mod_data['id'] = doc.id
                mods.append(mod_data)
            
            print(f"✅ تم جلب {len(mods)} مود من Firestore")
            return mods
            
        except Exception as e:
            print(f"❌ خطأ في جلب المودات من Firestore: {e}")
            return []
    
    def delete_mod_from_firestore(self, doc_id: str, collection_name: str = 'mods') -> bool:
        """حذف مود من Firestore"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return False
            
        try:
            self.firestore_client.collection(collection_name).document(doc_id).delete()
            print(f"✅ تم حذف المود من Firestore: {doc_id}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حذف المود من Firestore: {e}")
            return False
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بـ Firebase Storage فقط"""
        if not self.is_initialized:
            return False

        try:
            # اختبار Storage فقط
            _ = list(self.storage_bucket.list_blobs(max_results=1))

            print("✅ اختبار الاتصال بـ Firebase Storage نجح")
            return True

        except Exception as e:
            print(f"❌ فشل اختبار الاتصال بـ Firebase Storage: {e}")
            return False

    def get_download_url(self, file_path: str) -> Optional[str]:
        """الحصول على رابط تحميل مباشر للملف"""
        if not self.is_initialized:
            return None

        try:
            blob = self.storage_bucket.blob(file_path)
            if blob.exists():
                # إنشاء رابط تحميل مؤقت صالح لمدة ساعة
                download_url = blob.generate_signed_url(
                    expiration=3600,  # ساعة واحدة
                    method='GET'
                )
                return download_url
            else:
                print(f"❌ الملف غير موجود: {file_path}")
                return None
        except Exception as e:
            print(f"❌ خطأ في الحصول على رابط التحميل: {e}")
            return None

    def list_files(self, folder: str = "", max_results: int = 100) -> List[str]:
        """عرض قائمة بالملفات في مجلد معين"""
        if not self.is_initialized:
            return []

        try:
            blobs = self.storage_bucket.list_blobs(prefix=folder, max_results=max_results)
            return [blob.name for blob in blobs]
        except Exception as e:
            print(f"❌ خطأ في عرض الملفات: {e}")
            return []

    def delete_file(self, file_path: str) -> bool:
        """حذف ملف من Firebase Storage"""
        if not self.is_initialized:
            return False

        try:
            blob = self.storage_bucket.blob(file_path)
            blob.delete()
            print(f"✅ تم حذف الملف: {file_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حذف الملف: {e}")
            return False

# إنشاء مثيل عام من FirebaseManager
firebase_manager = FirebaseManager()
