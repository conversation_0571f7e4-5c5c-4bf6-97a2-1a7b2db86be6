/**
 * All possible MinecraftEntityTypes
 */
export declare enum MinecraftEntityTypes {
    Agent = "minecraft:agent",
    Allay = "minecraft:allay",
    AreaEffectCloud = "minecraft:area_effect_cloud",
    Armadillo = "minecraft:armadillo",
    ArmorStand = "minecraft:armor_stand",
    Arrow = "minecraft:arrow",
    Axolotl = "minecraft:axolotl",
    Bat = "minecraft:bat",
    Bee = "minecraft:bee",
    Blaze = "minecraft:blaze",
    Boat = "minecraft:boat",
    Bogged = "minecraft:bogged",
    Breeze = "minecraft:breeze",
    BreezeWindChargeProjectile = "minecraft:breeze_wind_charge_projectile",
    Camel = "minecraft:camel",
    Cat = "minecraft:cat",
    CaveSpider = "minecraft:cave_spider",
    ChestBoat = "minecraft:chest_boat",
    ChestMinecart = "minecraft:chest_minecart",
    Chicken = "minecraft:chicken",
    Cod = "minecraft:cod",
    CommandBlockMinecart = "minecraft:command_block_minecart",
    Cow = "minecraft:cow",
    Creaking = "minecraft:creaking",
    Creeper = "minecraft:creeper",
    Dolphin = "minecraft:dolphin",
    Don<PERSON> = "minecraft:donkey",
    DragonFireball = "minecraft:dragon_fireball",
    Drowned = "minecraft:drowned",
    Egg = "minecraft:egg",
    ElderGuardian = "minecraft:elder_guardian",
    EnderCrystal = "minecraft:ender_crystal",
    EnderDragon = "minecraft:ender_dragon",
    EnderPearl = "minecraft:ender_pearl",
    Enderman = "minecraft:enderman",
    Endermite = "minecraft:endermite",
    EvocationIllager = "minecraft:evocation_illager",
    EyeOfEnderSignal = "minecraft:eye_of_ender_signal",
    Fireball = "minecraft:fireball",
    FireworksRocket = "minecraft:fireworks_rocket",
    FishingHook = "minecraft:fishing_hook",
    Fox = "minecraft:fox",
    Frog = "minecraft:frog",
    Ghast = "minecraft:ghast",
    GlowSquid = "minecraft:glow_squid",
    Goat = "minecraft:goat",
    Guardian = "minecraft:guardian",
    Hoglin = "minecraft:hoglin",
    HopperMinecart = "minecraft:hopper_minecart",
    Horse = "minecraft:horse",
    Husk = "minecraft:husk",
    IronGolem = "minecraft:iron_golem",
    LightningBolt = "minecraft:lightning_bolt",
    LingeringPotion = "minecraft:lingering_potion",
    Llama = "minecraft:llama",
    LlamaSpit = "minecraft:llama_spit",
    MagmaCube = "minecraft:magma_cube",
    Minecart = "minecraft:minecart",
    Mooshroom = "minecraft:mooshroom",
    Mule = "minecraft:mule",
    Npc = "minecraft:npc",
    Ocelot = "minecraft:ocelot",
    OminousItemSpawner = "minecraft:ominous_item_spawner",
    Panda = "minecraft:panda",
    Parrot = "minecraft:parrot",
    Phantom = "minecraft:phantom",
    Pig = "minecraft:pig",
    Piglin = "minecraft:piglin",
    PiglinBrute = "minecraft:piglin_brute",
    Pillager = "minecraft:pillager",
    Player = "minecraft:player",
    PolarBear = "minecraft:polar_bear",
    Pufferfish = "minecraft:pufferfish",
    Rabbit = "minecraft:rabbit",
    Ravager = "minecraft:ravager",
    Salmon = "minecraft:salmon",
    Sheep = "minecraft:sheep",
    Shulker = "minecraft:shulker",
    ShulkerBullet = "minecraft:shulker_bullet",
    Silverfish = "minecraft:silverfish",
    Skeleton = "minecraft:skeleton",
    SkeletonHorse = "minecraft:skeleton_horse",
    Slime = "minecraft:slime",
    SmallFireball = "minecraft:small_fireball",
    Sniffer = "minecraft:sniffer",
    SnowGolem = "minecraft:snow_golem",
    Snowball = "minecraft:snowball",
    Spider = "minecraft:spider",
    SplashPotion = "minecraft:splash_potion",
    Squid = "minecraft:squid",
    Stray = "minecraft:stray",
    Strider = "minecraft:strider",
    Tadpole = "minecraft:tadpole",
    ThrownTrident = "minecraft:thrown_trident",
    Tnt = "minecraft:tnt",
    TntMinecart = "minecraft:tnt_minecart",
    TraderLlama = "minecraft:trader_llama",
    TripodCamera = "minecraft:tripod_camera",
    Tropicalfish = "minecraft:tropicalfish",
    Turtle = "minecraft:turtle",
    Vex = "minecraft:vex",
    Villager = "minecraft:villager",
    VillagerV2 = "minecraft:villager_v2",
    Vindicator = "minecraft:vindicator",
    WanderingTrader = "minecraft:wandering_trader",
    Warden = "minecraft:warden",
    WindChargeProjectile = "minecraft:wind_charge_projectile",
    Witch = "minecraft:witch",
    Wither = "minecraft:wither",
    WitherSkeleton = "minecraft:wither_skeleton",
    WitherSkull = "minecraft:wither_skull",
    WitherSkullDangerous = "minecraft:wither_skull_dangerous",
    Wolf = "minecraft:wolf",
    XpBottle = "minecraft:xp_bottle",
    XpOrb = "minecraft:xp_orb",
    Zoglin = "minecraft:zoglin",
    Zombie = "minecraft:zombie",
    ZombieHorse = "minecraft:zombie_horse",
    ZombiePigman = "minecraft:zombie_pigman",
    ZombieVillager = "minecraft:zombie_villager",
    ZombieVillagerV2 = "minecraft:zombie_villager_v2"
}
/**
 * Union type equivalent of the MinecraftEntityTypes enum.
 */
export type MinecraftEntityTypesUnion = keyof typeof MinecraftEntityTypes;
