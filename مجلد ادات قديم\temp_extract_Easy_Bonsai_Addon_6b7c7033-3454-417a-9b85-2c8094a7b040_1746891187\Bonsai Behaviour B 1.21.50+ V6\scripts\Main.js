import { EquipmentSlot, ItemStack, ItemTypes, system, world } from "@minecraft/server"

const fertilizedState = "bonsai:fertilized"
const timeSecondState = "bonsai:sec"
const timeMinuteState = "bonsai:min"

world.beforeEvents.worldInitialize.subscribe( initEvent =>{
    bonsaiVersionChecker()
    initEvent.blockComponentRegistry.registerCustomComponent('bonsai:grow', {
        onTick(event){
            const { block, dimension} = event
            const {x,y,z} = block.location
            const bonsaiName = block.typeId.split(":")[1]
            const blockPermutation = block.permutation
            const topBlockLocation = {x:x+0.5,y:y+1,z:z+0.5}
            let fertilized = false

            if(blockPermutation.getState(fertilizedState) != 0){
                fertilized = true
            }
            if(fertilized){
                const sec = block.permutation.getState(timeSecondState)
                const min = block.permutation.getState(timeMinuteState)
                if(sec >= 4){
                    block.setPermutation(block.permutation.withState(timeSecondState, 0))
                    block.setPermutation(block.permutation.withState(timeMinuteState, min+1))
                }
                if(sec <= 4){
                    block.setPermutation(block.permutation.withState(timeSecondState, sec+1))
                }
                if(min >= 10){
                    block.setPermutation(block.permutation.withState(fertilizedState, 0))
                    block.setPermutation(block.permutation.withState(timeSecondState, 0))
                    block.setPermutation(block.permutation.withState(timeMinuteState, 0))
                }
            }
            const lootDropCommand = dimension.runCommand(`loot spawn ${topBlockLocation.x} ${topBlockLocation.y} ${topBlockLocation.z} loot "bonsai/${bonsaiName}"`)
            const lootEntities = dimension.getEntities({type:"minecraft:item", location:topBlockLocation, maxDistance:0.8})
            for(let lootEntity of lootEntities){
                lootEntity.teleport(topBlockLocation)
            }
            dimension.spawnParticle("minecraft:villager_happy", topBlockLocation)
            if(lootDropCommand.successCount == 0){
                console.warn(`${block.typeId} no loot drop. Report to addon creator.`)
            }
        },
        onPlayerInteract(event){
            const {block, player} = event
            const {x,y,z} = block.location
            const dimension = block.dimension
            const topBlockLocation = {x:x+0.5,y:y+1,z:z+0.5}
            const itemStack = player.getComponent("equippable").getEquipment(EquipmentSlot.Mainhand)
            if(itemStack){
                if(itemStack.typeId == "bonsai:enriched_bone_meal" && block.permutation.getState(fertilizedState) == 0){
                    block.setPermutation(block.permutation.withState(fertilizedState, 1))
                    dimension.spawnParticle("minecraft:heart_particle", topBlockLocation)
                    if(itemStack.amount != 1){
                        player.getComponent("equippable").setEquipment(EquipmentSlot.Mainhand, new ItemStack(itemStack.typeId, itemStack.amount-1))
                    }else{
                        player.getComponent("equippable").setEquipment(EquipmentSlot.Mainhand, undefined)
                    }
                }
            }
        }
    });
})

function bonsaiVersionChecker(){
    system.runTimeout(()=>{
        const allItemTypes = ItemTypes.getAll()
        const firefly_bush = allItemTypes.filter(itemType => itemType.id.startsWith("minecraft:") && itemType.id.includes("firefly_bush"))
        if(firefly_bush.length == 0){
            world.sendMessage("§e[Bonsai Addon] §cBonsai Firefly Bush will not work with your current minecraft version. Please update your minecraft version to 1.21.73 or higher.")
        }
    }, 60)   
}