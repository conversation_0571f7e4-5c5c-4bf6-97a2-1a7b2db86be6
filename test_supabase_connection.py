# -*- coding: utf-8 -*-
"""
ملف اختبار الاتصال بـ Supabase
يختبر الاتصال بقاعدة البيانات والتخزين
"""

import os
import json
import sys
from supabase import create_client, Client
import traceback

def load_api_keys():
    """تحميل مفاتيح API من ملف الإعدادات"""
    try:
        config_file = "api_keys.json"
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config
        else:
            print("❌ ملف api_keys.json غير موجود")
            return None
    except Exception as e:
        print(f"❌ خطأ في تحميل مفاتيح API: {e}")
        return None

def test_supabase_connection():
    """اختبار الاتصال بـ Supabase"""
    print("🔄 بدء اختبار الاتصال بـ Supabase...")
    
    # تحميل المفاتيح
    config = load_api_keys()
    if not config:
        return False
    
    supabase_url = config.get('supabase_url', 'https://ytqxxodyecdeosnqoure.supabase.co')
    supabase_key = config.get('SUPABASE_KEY', '')
    
    if not supabase_key:
        print("❌ مفتاح Supabase غير موجود في ملف الإعدادات")
        return False
    
    print(f"📍 URL: {supabase_url}")
    print(f"🔑 Key: {supabase_key[:20]}...")
    
    try:
        # إنشاء عميل Supabase
        print("🔄 إنشاء عميل Supabase...")
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # اختبار الاتصال بقاعدة البيانات
        print("🔄 اختبار الاتصال بقاعدة البيانات...")
        try:
            # محاولة جلب قائمة الجداول أو اختبار بسيط
            response = supabase.table('mods').select("id").limit(1).execute()
            print("✅ نجح الاتصال بقاعدة البيانات")
            print(f"📊 استجابة قاعدة البيانات: {response}")
        except Exception as db_e:
            print(f"❌ فشل الاتصال بقاعدة البيانات: {db_e}")
            print("💡 قد يكون الجدول 'mods' غير موجود أو لا توجد صلاحيات للوصول")
        
        # اختبار الاتصال بالتخزين
        print("🔄 اختبار الاتصال بالتخزين...")
        try:
            buckets = supabase.storage.list_buckets()
            print("✅ نجح الاتصال بالتخزين")
            print(f"📦 عدد الـ buckets: {len(buckets)}")
            
            for bucket in buckets:
                print(f"  - {bucket['name']} (عام: {bucket.get('public', False)})")
            
            # اختبار إنشاء bucket إذا لم يكن موجوداً
            bucket_names = [bucket['name'] for bucket in buckets]
            
            test_buckets = ['image', 'my_new_mods_bucket']
            for bucket_name in test_buckets:
                if bucket_name not in bucket_names:
                    print(f"🔄 محاولة إنشاء bucket: {bucket_name}")
                    try:
                        result = supabase.storage.create_bucket(bucket_name, {'public': True})
                        print(f"✅ تم إنشاء bucket: {bucket_name}")
                    except Exception as bucket_e:
                        print(f"⚠️ فشل إنشاء bucket {bucket_name}: {bucket_e}")
                else:
                    print(f"✅ bucket موجود: {bucket_name}")
                    
        except Exception as storage_e:
            print(f"❌ فشل الاتصال بالتخزين: {storage_e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الاتصال بـ Supabase: {e}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_supabase_auth():
    """اختبار صلاحيات المفتاح"""
    print("\n🔄 اختبار صلاحيات المفتاح...")
    
    config = load_api_keys()
    if not config:
        return False
    
    supabase_url = config.get('supabase_url')
    supabase_key = config.get('SUPABASE_KEY')
    
    try:
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # اختبار صلاحيات مختلفة
        tests = [
            ("قراءة الجداول", lambda: supabase.table('mods').select("*").limit(1).execute()),
            ("قائمة buckets", lambda: supabase.storage.list_buckets()),
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                print(f"✅ {test_name}: نجح")
            except Exception as e:
                print(f"❌ {test_name}: فشل - {e}")
                
    except Exception as e:
        print(f"❌ خطأ في اختبار الصلاحيات: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار الاتصال بـ Supabase")
    print("=" * 50)
    
    # اختبار الاتصال الأساسي
    connection_success = test_supabase_connection()
    
    # اختبار الصلاحيات
    if connection_success:
        test_supabase_auth()
    
    print("\n" + "=" * 50)
    if connection_success:
        print("✅ اكتمل اختبار Supabase بنجاح")
    else:
        print("❌ فشل اختبار Supabase")
    print("=" * 50)

if __name__ == "__main__":
    main()
