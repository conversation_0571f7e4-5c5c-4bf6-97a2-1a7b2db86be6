# -*- coding: utf-8 -*-
"""
وحدة استخراج بيانات المودات من موقع mcpedl.com - إصدار كامل ومحسن
تم إصلاح جميع مشاكل استخراج الصور والأوصاف
"""

import requests
import time
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any

# محاولة استيراد cloudscraper لتجاوز حماية Cloudflare
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
    print("cloudscraper متوفر - سيتم استخدامه لتجاوز الحماية")
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("cloudscraper غير متوفر - سيتم استخدام requests العادي")

class MCPEDLScraperFixed:
    """كلاس استخراج بيانات المودات من mcpedl.com - إصدار محسن كامل"""

    def __init__(self):
        # استخدام cloudscraper إذا كان متوفراً، وإلا استخدام requests العادي
        if CLOUDSCRAPER_AVAILABLE:
            self.session = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
            print("تم إنشاء جلسة cloudscraper")
        else:
            self.session = requests.Session()
            print("تم إنشاء جلسة requests عادية")

        # ترويسات محسنة لتجاوز الحماية
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # إعدادات إضافية للجلسة
        self.session.max_redirects = 10

    def is_suggested_mod_image(self, img_element: BeautifulSoup) -> bool:
        """فحص ما إذا كانت الصورة من المودات المقترحة"""
        
        # فحص إذا كانت الصورة داخل رابط
        parent_link = img_element.find_parent('a')
        if parent_link:
            href = parent_link.get('href', '')
            # إذا كان الرابط يؤدي لصفحة مود آخر
            if href and ('mcpedl.com' in href or href.startswith('/')):
                # فحص إضافي للتأكد أنه ليس رابط داخلي للصورة
                if not href.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    return True
        
        # فحص النص المحيط
        parent_containers = img_element.find_parents(['div', 'section', 'article', 'li', 'ul'])
        for container in parent_containers:
            container_text = container.get_text().lower()
            suggested_keywords = [
                'you may also like',
                'related posts', 
                'similar mods',
                'recommendations',
                'other mods',
                'more mods',
                'suggested'
            ]
            if any(keyword in container_text for keyword in suggested_keywords):
                return True
        
        # فحص بيانات الصورة نفسها
        img_alt = img_element.get('alt', '').lower()
        img_title = img_element.get('title', '').lower()
        img_class = ' '.join(img_element.get('class', [])).lower()
        
        suggestion_indicators = [
            'suggestion', 'recommended', 'related', 'similar',
            'thumbnail', 'preview', 'other'
        ]
        
        for indicator in suggestion_indicators:
            if indicator in img_alt or indicator in img_title or indicator in img_class:
                return True
        
        return False

    def extract_main_mod_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """استخراج الصورة الرئيسية للمود مع تجنب الصور المقترحة"""
        
        print("🔍 البحث عن الصورة الرئيسية للمود...")
        
        # 1. البحث في المحتوى الرئيسي أولاً
        main_content_selectors = [
            '.entry-content img',
            '.post-content img', 
            'article img',
            '.content img',
            'main img'
        ]
        
        for selector in main_content_selectors:
            images = soup.select(selector)
            for img in images:
                # تجاهل الصور المقترحة
                if self.is_suggested_mod_image(img):
                    continue
                
                # تجاهل الصور الصغيرة (أيقونات)
                if self.is_small_icon(img):
                    continue
                
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    url = urljoin(base_url, src)
                    if self.validate_image_url(url):
                        print(f"✅ تم العثور على الصورة الرئيسية في المحتوى: {url}")
                        return url
        
        # 2. البحث في meta tags
        meta_image = soup.find('meta', property='og:image')
        if meta_image and meta_image.get('content'):
            url = urljoin(base_url, meta_image['content'])
            if self.validate_image_url(url):
                print(f"✅ تم العثور على الصورة من meta tag: {url}")
                return url
        
        # 3. البحث في script tags للصور المخفية
        script_tags = soup.find_all('script')
        for script in script_tags:
            if script.string:
                # البحث عن عناوين URL للصور في JavaScript
                image_urls = re.findall(r'https?://[^\s<>"\']+\.(?:jpg|jpeg|png|gif|webp)', script.string)
                for url in image_urls:
                    if self.validate_image_url(url) and ('mcpedl' in url or 'media.forgecdn.net' in url):
                        print(f"✅ تم العثور على صورة مخفية في JavaScript: {url}")
                        return url
        
        # 4. البحث في JSON-LD للصور المهيكلة
        json_ld = soup.find('script', type='application/ld+json')
        if json_ld:
            try:
                data = json.loads(json_ld.string)
                if isinstance(data, dict) and 'image' in data:
                    image_url = data['image']
                    if isinstance(image_url, list):
                        image_url = image_url[0]
                    if isinstance(image_url, str):
                        url = urljoin(base_url, image_url)
                        if self.validate_image_url(url):
                            print(f"✅ تم العثور على الصورة من JSON-LD: {url}")
                            return url
            except json.JSONDecodeError:
                pass
        
        print("⚠️ لم يتم العثور على الصورة الرئيسية")
        return None

    def is_small_icon(self, img_element: BeautifulSoup) -> bool:
        """فحص ما إذا كانت الصورة أيقونة صغيرة"""
        width = img_element.get('width')
        height = img_element.get('height')
        
        if width and height:
            try:
                w, h = int(width), int(height)
                if w < 100 or h < 100:
                    return True
            except ValueError:
                pass
        
        # فحص الأحجام في CSS classes أو style
        style = img_element.get('style', '')
        img_class = ' '.join(img_element.get('class', [])).lower()
        
        small_indicators = ['icon', 'avatar', 'thumb', 'small', 'mini', 'logo']
        for indicator in small_indicators:
            if indicator in img_class or indicator in style.lower():
                return True
        
        return False

    def extract_additional_images(self, soup: BeautifulSoup, base_url: str, main_image_url: str) -> List[str]:
        """استخراج صور إضافية للمود (غير مقترحة)"""
        additional_images = []
        
        # البحث في قسم الصور المخصص
        image_section_selectors = [
            '.mod-gallery img',
            '.mod-screenshots img',
            '.entry-content img',
            '.post-content img'
        ]
        
        for selector in image_section_selectors:
            images = soup.select(selector)
            for img in images:
                # تجاهل الصور المقترحة
                if self.is_suggested_mod_image(img):
                    continue
                
                # تجاهل الأيقونات الصغيرة
                if self.is_small_icon(img):
                    continue
                
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    url = urljoin(base_url, src)
                    if (self.validate_image_url(url) and 
                        url != main_image_url and 
                        url not in additional_images):
                        
                        additional_images.append(url)
                        if len(additional_images) >= 4:  # حد أقصى 4 صور إضافية
                            break
        
        return additional_images

    def validate_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False

        url_lower = url.lower()
        
        # التحقق من امتداد الصورة
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        has_extension = any(ext in url_lower for ext in image_extensions)
        
        # تجنب الكلمات المؤشرة للأيقونات أو الصور غير المرغوبة
        avoid_keywords = ['favicon', 'logo', 'banner', 'ad', 'advertisement']
        has_avoid_keyword = any(keyword in url_lower for keyword in avoid_keywords)
        
        # التحقق من الحجم المعقول في URL (تجنب الصور الصغيرة جداً)
        size_patterns = re.findall(r'(\d+)x(\d+)', url)
        for width_str, height_str in size_patterns:
            try:
                width, height = int(width_str), int(height_str)
                if width < 100 or height < 100:
                    return False
            except ValueError:
                continue
        
        return has_extension and not has_avoid_keyword

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        title_selectors = [
            'h1.entry-title',
            'h1.post-title', 
            '.post-header h1',
            'h1',
            '.entry-header h1',
            '.page-title',
            'title'
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text().strip()
                # تنظيف العنوان من النصوص الإضافية
                title = re.sub(r'\s*-\s*MCPEDL.*$', '', title, flags=re.IGNORECASE)
                title = re.sub(r'\s*\|\s*.*$', '', title)
                title = re.sub(r'\s*–\s*.*$', '', title)
                if title and len(title) > 3:
                    return title
        
        return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود مع تنظيف محسن"""
        
        # البحث في الفقرات الرئيسية
        desc_selectors = [
            '.entry-content p',
            '.post-content p',
            '.content p',
            'article p',
            '.post-body p',
            '.entry-summary p'
        ]
        
        description_parts = []
        
        for selector in desc_selectors:
            elements = soup.select(selector)
            for element in elements[:5]:  # أول 5 فقرات فقط
                text = element.get_text().strip()
                
                # تجاهل النصوص القصيرة أو غير المفيدة
                if len(text) < 20:
                    continue
                
                # تجاهل النصوص التي تحتوي على معلومات تقنية فقط
                if self.is_technical_info_only(text):
                    continue
                
                description_parts.append(text)
                
                # إيقاف جمع الوصف إذا وصلنا لطول كافي
                if len(' '.join(description_parts)) > 500:
                    break
        
        description = ' '.join(description_parts)
        
        # تنظيف الوصف
        description = self.clean_description(description)
        
        return description

    def is_technical_info_only(self, text: str) -> bool:
        """فحص ما إذا كان النص يحتوي على معلومات تقنية فقط"""
        text_lower = text.lower()
        
        technical_keywords = [
            'published on', 'updated on', 'downloads:', 'file size:',
            'supported minecraft versions', 'installation', 'download',
            'join discord', 'uuid', 'manifest.json', 'requirements',
            'compatible with', 'works with'
        ]
        
        # إذا كان النص يحتوي على كلمات تقنية بشكل رئيسي
        technical_count = sum(1 for keyword in technical_keywords if keyword in text_lower)
        words_count = len(text.split())
        
        return technical_count > 0 and words_count < 20

    def clean_description(self, text: str) -> str:
        """تنظيف الوصف من المعلومات غير المرغوبة"""
        if not text:
            return ""
        
        # أولاً إزالة النصوص بين الأقواس المربعة
        text = re.sub(r'\[.*?\]', '', text)
        
        # أنماط النصوص التي يجب إزالتها
        patterns_to_remove = [
            r'Published on.*?\d{4}',
            r'Updated on.*?\d{4}',
            r'Downloads?:\s*\d+',
            r'File size:\s*[\d\.]+\s*MB',
            r'Supported Minecraft versions:.*',
            r'Installation.*',
            r'Download.*',
            r'Join our Discord.*',
            r'You may also like.*',
            r'Related posts.*',
            r'Similar mods.*',
            r'Requirements:.*',
            r'Compatible with.*',
            r'Works with Minecraft.*',
            r'\d+\.\d+\.?\d*\s*-\s*\d+\.\d+\.?\d*',  # نسخ الإصدارات
            r'UUID:.*',
            r'manifest\.json',
            r'Select version for changelog'
        ]
        
        for pattern in patterns_to_remove:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # إزالة الكلمات التسويقية المفرطة
        marketing_words = [
            'fundamentally enhance', 'exceptional experience', 'remarkable addition',
            'innovative gameplay mechanics', 'completely transform', 'effortlessly navigating',
            'expansive landscapes', 'crucial discoveries', 'treasured locations',
            'newfound confidence', 'deepening your immersion', 'transformative mechanics',
            'visual excellence', 'high-quality textures', 'seamlessly into', 
            'elevating its aesthetic appeal', 'engineered for smooth performance',
            'fluid and responsive experience', 'delve into its added layers',
            'exceptionally easy installation', 'absolutely no technical expertise',
            'captivating content', 'unlock a more streamlined', 'true potential'
        ]
        
        for word in marketing_words:
            text = text.replace(word, '')
        
        # إزالة المسافات الزائدة والأسطر الفارغة
        text = re.sub(r'\n\s*\n', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        # إزالة النقاط في بداية النص
        text = re.sub(r'^[\*\-\•\s]+', '', text)
        
        return text.strip()

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        category_selectors = [
            '.breadcrumbs a:last-of-type',
            '.category-link',
            '.post-categories a',
            '.breadcrumb-item a',
            'nav.breadcrumb a',
            '.category'
        ]
        
        for selector in category_selectors:
            element = soup.select_one(selector)
            if element:
                category = element.get_text().strip()
                # تطبيع أسماء الفئات
                category_lower = category.lower()
                if 'addon' in category_lower or 'behavior' in category_lower:
                    return 'Addons'
                elif 'texture' in category_lower or 'resource' in category_lower:
                    return 'Texture Pack'
                elif 'shader' in category_lower:
                    return 'Shaders'
                elif category and len(category) > 2:
                    return category
        
        # البحث في النص للتعرف على الفئة
        page_text = soup.get_text().lower()
        if 'addon' in page_text or 'behavior pack' in page_text:
            return 'Addons'
        elif 'texture pack' in page_text or 'resource pack' in page_text:
            return 'Texture Pack'
        elif 'shader' in page_text:
            return 'Shaders'
        
        return "Addons"  # افتراضي

    def scrape_mod_data(self, url: str) -> Dict[str, Any]:
        """استخراج بيانات المود من الرابط"""
        try:
            print(f"🔄 بدء استخراج البيانات من: {url}")
            
            # طلب الصفحة مع إعادة المحاولة
            max_attempts = 3
            response = None
            
            for attempt in range(max_attempts):
                try:
                    response = self.session.get(url, timeout=30)
                    response.raise_for_status()
                    break
                except requests.RequestException as e:
                    print(f"❌ خطأ في المحاولة {attempt + 1}: {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(2)
                        continue
                    else:
                        raise
            
            if not response:
                raise Exception("فشل في الحصول على استجابة من الخادم")
            
            # تحليل HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # استخراج البيانات
            title = self.extract_title(soup)
            description = self.extract_description(soup)
            category = self.extract_category(soup)
            
            # استخراج الصور
            main_image = self.extract_main_mod_image(soup, url)
            additional_images = []
            
            if main_image:
                additional_images = self.extract_additional_images(soup, url, main_image)
            
            # دمج الصور
            all_images = []
            if main_image:
                all_images.append(main_image)
            all_images.extend(additional_images)
            
            mod_data = {
                'name': title,
                'description': description,
                'category': category,
                'image_urls': all_images,
                'main_image': main_image,
                'additional_images': additional_images,
                'source_url': url,
                'extracted_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'extraction_status': 'success'
            }
            
            print(f"✅ تم استخراج البيانات بنجاح:")
            print(f"   العنوان: {title}")
            print(f"   الفئة: {category}")
            print(f"   عدد الصور الإجمالي: {len(all_images)}")
            if main_image:
                print(f"   الصورة الرئيسية: {main_image}")
            
            return mod_data
            
        except Exception as e:
            print(f"❌ خطأ في استخراج البيانات: {str(e)}")
            return {
                'extraction_status': 'failed',
                'error': str(e),
                'source_url': url,
                'extracted_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }

def scrape_mcpedl_mod(url: str) -> Dict[str, Any]:
    """دالة مساعدة لاستخراج بيانات المود"""
    scraper = MCPEDLScraperFixed()
    return scraper.scrape_mod_data(url)

# مثال للاستخدام والاختبار
if __name__ == "__main__":
    test_urls = [
        "https://mcpedl.com/take-a-seat/",
        # يمكن إضافة المزيد من الروابط للاختبار
    ]
    
    for test_url in test_urls:
        print(f"\n{'='*50}")
        print(f"اختبار الرابط: {test_url}")
        print('='*50)
        
        result = scrape_mcpedl_mod(test_url)
        
        if result.get('extraction_status') == 'success':
            print(f"✅ نجح الاستخراج:")
            print(f"   العنوان: {result.get('name', 'غير متوفر')}")
            print(f"   الفئة: {result.get('category', 'غير متوفر')}")
            print(f"   طول الوصف: {len(result.get('description', ''))} حرف")
            print(f"   عدد الصور: {len(result.get('image_urls', []))}")
            
            if result.get('main_image'):
                print(f"   الصورة الرئيسية: {result.get('main_image')}")
            
            # حفظ النتيجة في ملف JSON للمراجعة
            output_file = f"test_extraction_{int(time.time())}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"   تم حفظ النتائج في: {output_file}")
        else:
            print(f"❌ فشل الاستخراج: {result.get('error', 'خطأ غير معروف')}")
