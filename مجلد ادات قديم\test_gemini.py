#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json

# Test Gemini import
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
    print("✅ Gemini library imported successfully")
except ImportError as e:
    GEMINI_AVAILABLE = False
    print(f"❌ Gemini import failed: {e}")

# Test config loading
CONFIG_FILE = "config.json"

def load_config():
    default_config = {"gemini_api_keys": []}
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                if "gemini_api_keys" not in config or not isinstance(config["gemini_api_keys"], list):
                    config["gemini_api_keys"] = []
                return config
        else:
            print(f"❌ Config file {CONFIG_FILE} not found")
            return default_config
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return default_config

# Change to the correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Test configuration
print(f"📁 Current directory: {os.getcwd()}")
print(f"📄 Config file exists: {os.path.exists(CONFIG_FILE)}")

config = load_config()
api_keys = config.get("gemini_api_keys", [])
print(f"🔑 API keys found: {len(api_keys)}")

if api_keys:
    print(f"🔑 First key preview: {api_keys[0][:20]}...")

    if GEMINI_AVAILABLE:
        # Test Gemini configuration
        try:
            genai.configure(api_key=api_keys[0])
            model = genai.GenerativeModel('gemini-1.5-flash')
            print("✅ Gemini client configured successfully")

            # Test simple generation
            response = model.generate_content("Hello, say 'AI is working!'")
            print(f"✅ Gemini response: {response.text}")

        except Exception as e:
            print(f"❌ Gemini configuration failed: {e}")
    else:
        print("❌ Gemini library not available")
else:
    print("❌ No API keys found in config")

print("\n" + "="*50)
print("DIAGNOSIS COMPLETE")
print("="*50)
