[{"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "extracted_data": {"mod_name": "Pick up & Carry", "version": null, "full_mod_description": "Ever wanted to pick up a mob or player and move it to another location? With this add-on you can do it without any commands, right in survival! This addon allows you to pick up any mobs or players and carry them anywhere without any commands.\n\nCreated By sirob\n\nYou can pick up a mob or player only if you are standing on the ground. Also, you should not have items in both hands. And you shouldn't be in the water. Only in this case you can pick up a mob / player.\n\nWhen you carry a mob / player, you have to move neatly. If the mob is submerged in water, it will fall out of your hands. If you start falling from a height, then after flying 3 blocks, it will fall out of your hands. If you take any item in your hand, the mob will also fall out of your hands.\n\nHow to use:\nTo pick up a mob or player, go to him as close as possible, look at him and sneak. Then jump.\nTo release a mob or player, sneak, then jump.", "mod_features": ["Now it supports 1.21.60!", "Now it supports characters from Character Creator! You can use your character created in the Character Creator! Even animated items and capes are supported!", "Fixed broken vanilla animation of holding heavy core!"], "primary_image_url": "/uploads/posts/2023-01/logo14_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-02/medium/pick-up-carry_2.png", "/uploads/posts/2022-02/medium/pick-up-carry_3.png", "/uploads/posts/2022-02/medium/pick-up-carry_4.png", "/uploads/posts/2022-02/medium/pick-up-carry_8.png", "/uploads/posts/2022-02/medium/pick-up-carry_9.png", "/uploads/posts/2022-02/medium/pick-up-carry_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_logo14_1-520x245_1747225913_ab3tefem.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_2_1747225917_bt7vezbb.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_3_1747225919_nfa63ue8.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_4_1747225921_81wufive.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_9_1747225923_vs348vet.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_10_1747225925_a3mld83i.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:32:06"}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "extracted_data": {"mod_name": "DeadZone", "version": "1.53", "full_mod_description": "DeadZone is a survival addon inspired by the game DayZ that puts you in the role of a survivor in a post-apocalyptic world. The goal is simple: survive as long as possible, search every building for loot, and craft your own story along the way.Created by NekoZack It features unique \"2.5D\" styled weapons, along with a variety of clothing and accessories to customize your look in the post-apocalyptic setting. Plus, it offers a visually appealing texture style that adds to the immersion DeadZone offers a wide variety of items, including melee weapons, firearms, food, drinks, miscellaneous items, and medical supplies. It also includes clothing and accessories, such as shirts, pants, helmets, masks, and more, allowing you to explore buildings in style with your own unique look.", "mod_features": ["Stamina", "Dehydration/Thirst", "Infection (Hunger effect)", "Radiation (Wither effect)", "Bleeding", "Broken bone (Slowness effect)"], "primary_image_url": "/uploads/posts/2025-03/tak-berjudul16820250227165818_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.png", "/uploads/posts/2024-12/deadzone-addon-v141_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_7.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_8.jpg", "/uploads/posts/2024-12/deadzone-addon-v141_10.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_tak-berjudul16820250227165818_1-520x245_1747225934_d2vgxwf5.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v153-12080-support_2_1747225935_rvx52qdh.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v141_2_1747225936_4o2q9bzn.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v153-12080-support_2_1747225937_dl5f14gg.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v152_7_1747225938_5wxj6lqp.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v152_8_1747225939_8zj14ln2.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v141_10_1747225940_xy5pe4am.jpeg?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:32:20"}, {"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "extracted_data": {"mod_name": "Pick up & Carry", "version": null, "full_mod_description": "Ever wanted to pick up a mob or player and move it to another location? With this add-on you can do it without any commands, right in survival! This addon allows you to pick up any mobs or players and carry them anywhere without any commands.\n\nCreated By sirob\n\nYou can pick up a mob or player only if you are standing on the ground. Also, you should not have items in both hands. And you shouldn't be in the water. Only in this case you can pick up a mob / player.\n\nWhen you carry a mob / player, you have to move neatly. If the mob is submerged in water, it will fall out of your hands. If you start falling from a height, then after flying 3 blocks, it will fall out of your hands. If you take any item in your hand, the mob will also fall out of your hands.", "mod_features": ["Now it supports 1.21.60!", "Now it supports characters from Character Creator! You can use your character created in the Character Creator! Even animated items and capes are supported!", "Fixed broken vanilla animation of holding heavy core!"], "primary_image_url": "/uploads/posts/2023-01/logo14_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-02/medium/pick-up-carry_2.png", "/uploads/posts/2022-02/medium/pick-up-carry_3.png", "/uploads/posts/2022-02/medium/pick-up-carry_4.png", "/uploads/posts/2022-02/medium/pick-up-carry_8.png", "/uploads/posts/2022-02/medium/pick-up-carry_9.png", "/uploads/posts/2022-02/medium/pick-up-carry_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_logo14_1-520x245_1747226771_jc4vcdsi.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_2_1747226774_ctrk9dzy.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_3_1747226776_6egjns3a.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_4_1747226778_oqob0g0e.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_9_1747226780_h6ecyfkv.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pick-up-carry_10_1747226782_id32s5zi.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:46:22"}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "extracted_data": {"mod_name": "DeadZone", "version": "v162", "full_mod_description": "DeadZone is a survival addon inspired by the game DayZ that puts you in the role of a survivor in a post-apocalyptic world. The goal is simple: survive as long as possible, search every building for loot, and craft your own story along the way.Created by NekoZack It features unique \"2.5D\" styled weapons, along with a variety of clothing and accessories to customize your look in the post-apocalyptic setting. Plus, it offers a visually appealing texture style that adds to the immersion DeadZone offers a wide variety of items, including melee weapons, firearms, food, drinks, miscellaneous items, and medical supplies. It also includes clothing and accessories, such as shirts, pants, helmets, masks, and more, allowing you to explore buildings in style with your own unique look.", "mod_features": ["Stamina", "Dehydration/Thirst", "Infection (Hunger effect)", "Radiation (Wither effect)", "Bleeding", "Broken bone (Slowness effect)"], "primary_image_url": "/uploads/posts/2025-03/tak-berjudul16820250227165818_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.png", "/uploads/posts/2024-12/deadzone-addon-v141_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_7.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_8.jpg", "/uploads/posts/2024-12/new-deadzone-addon_8.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_tak-berjudul16820250227165818_1-520x245_1747226790_msxlp1al.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v153-12080-support_2_1747226792_2l2p0mfo.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v141_2_1747226793_rra79tte.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v153-12080-support_2_1747226793_3vjfq3a3.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v152_7_1747226794_4ze8wpa8.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_deadzone-addon-v152_8_1747226795_fo2t0nvg.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_new-deadzone-addon_8_1747226797_8o5fodl8.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:46:37"}, {"article_url": "https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html", "extracted_data": {"mod_name": "Survivor's Airdrop", "version": null, "full_mod_description": "This amazing addon lets you summon airdrops directly into your Minecraft world, providing valuable items to enhance your survival!There are three types of airdrops, each offering specific resources for different stages of progression.", "mod_features": ["Convenience: Get essential items wherever you are.", "Versatility: Choose the type of airdrop based on your current needs.", "Enhanced Survival: Access items that boost your chances in combat and exploration."], "primary_image_url": "/uploads/posts/2025-03/srsy_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/survivors-airdrop-addon_2.png", "/uploads/posts/2025-03/medium/survivors-airdrop-addon_3.png", "/uploads/posts/2025-03/survivors-airdrop-addon_4.png", "/uploads/posts/2025-03/survivors-airdrop-addon_5.png", "/uploads/posts/2025-03/survivors-airdrop-addon_6.png", "/uploads/posts/2025-03/survivors-airdrop-addon_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2291&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_srsy_1-520x245_1747226804_vxukrbg9.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_2_1747226806_hh3gqeai.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_3_1747226807_ivnh6bgs.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_4_1747226808_90p5vehg.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_5_1747226809_99xpz2uf.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_6_1747226810_isai4mag.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survivors-airdrop-addon_7_1747226811_hkiz0bcr.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2291&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:46:51"}, {"article_url": "https://mcpeland.io/en/mods/1520-addon-sofa-amp-couch-furnitures.html", "extracted_data": {"mod_name": "Sofa & Couch Furnitures", "version": null, "full_mod_description": "Spruce Up Your Builds with Sofas & Couches!This addon brings two delightful furniture sets to your Minecraft world, perfect for creating cozy living spaces! where players can now enhance their virtual homes with two stunning sets of furniture: the luxurious Comfy Chair Couch and the cozy Small Sofa.<br><br><b>Created By <PERSON> <br><br></b>", "mod_features": ["The Comfy Chair Couch: Relax in style with this plush couch and matching chair.", "Small Sofa: Add a touch of comfort to smaller rooms with this compact sofa."], "primary_image_url": "/uploads/posts/2025-03/gfgfgf_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/medium/sofa-couch-furnitures_2.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_5.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_4.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_390.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_290.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2295&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_gfgfgf_1-520x245_1747226818_fvjjj22g.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_sofa-couch-furnitures_2_1747226819_qhdbmr22.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_sofa-couch-furnitures_5_1747226820_oglcpvyh.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_sofa-couch-furnitures_4_1747226821_ow8ttxod.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_sofa-couch-furnitures_390_1747226822_xrxndtrh.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_sofa-couch-furnitures_290_1747226824_edpnehal.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2295&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:47:04"}, {"article_url": "https://mcpeland.io/en/mods/1521-addon-the-slenderman.html", "extracted_data": {"mod_name": "The Slenderman", "version": null, "full_mod_description": "The <PERSON><PERSON>derman is a Fictional  Supernatural Character and <PERSON><PERSON><PERSON><PERSON><PERSON> who is invincible and  has  strong attack powers and special abilities when attacking. This fantastic add-on adds this C<PERSON><PERSON> <PERSON><PERSON> who has extremely strong strength and Amazing Attack Abilities! Created By <PERSON> PE Addons He Will Attack mostly the Monsters Family Mobs in and others in your Minecraft world", "mod_features": ["Improved Character AI – <PERSON><PERSON><PERSON> is now smarter, faster, and more unpredictable. He will teleport more strategically and become more aggressive over time.", "New Sound Effects – Added eerie ambient sounds and Slenderman-specific noises to intensify the atmosphere.", "New Animations – <PERSON><PERSON><PERSON> now has more fluid movements, making his presence even more terrifying."], "primary_image_url": "/uploads/posts/2025-03/the-slenderman_1.png", "other_image_urls": ["/uploads/posts/2025-03/medium/the-slenderman_3.png", "/uploads/posts/2025-03/medium/the-slenderman_4.png", "/uploads/posts/2025-03/medium/the-slenderman_10.png", "/uploads/posts/2025-03/medium/the-slenderman_9.png", "/uploads/posts/2025-03/medium/the-slenderman_8.png", "/uploads/posts/2025-03/medium/the-slenderman_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2296&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_1_1747226832_jcgohmp3.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_3_1747226833_mbex2ejf.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_4_1747226834_fyg1s3qi.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_10_1747226835_q0kd0k39.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_9_1747226837_pk0rpxcb.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_8_1747226838_vk9hpbsx.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_the-slenderman_7_1747226839_di2wrlym.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2296&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:47:20"}, {"article_url": "https://mcpeland.io/en/mods/1523-addon-survival-frozen-update.html", "extracted_data": {"mod_name": "Survival+", "version": null, "full_mod_description": "Survival+ Add-On makes Minecraft tougher and more exciting! Face new wild animals, dangerous mobs, and the powerful Phantom Master. Craft spears, collect rare loot, and survive in a world full of threats. Are you ready for the challenge?\n\nCreated By WildForge Studios \n\nThe Frozen Update is here, bringing a chilling new survival experience with icy mobs, blocks, and items! Get ready to face the cold like never before:", "mod_features": ["<PERSON> C<PERSON>per – A new creeper variant that leaves icy explosions behind!", "Frozen Zombie – These undead freeze anything they touch.", "Ice Revenant – A mysterious and dangerous creature that lurks in the cold.", "Arctic Fox – Hunts small mobs in the snow and ice.", "Heart of the Frost – A rare item that grants Resistance against freezing mobs and the cold.", "Frozen Flesh – Dropped by the Frozen Zombie, can be used for crafting or potions.", "Frosted Stone – A new block found in icy biomes.", "Snow Crystal – A rare, decorative block dropped by Ice Revenants.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "White Endermen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Polished Endstone", "Ender <PERSON>", "ducks and raccoons", "grizzly bears and crocodiles", "dread zombies", "phantom creatures", "skeletons, spiders, and zombies", "powerful spears (wood, stone, iron, diamond, netherite, and more!)", "Phantom Fangs, Ghost Souls, and Crocodile Scutes", "Phantom Master", "Phantom Master Nose"], "primary_image_url": "/uploads/posts/2025-03/6aaa9b167e5e46a1a9e8be43cd4f4c6e_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/medium/survival-v030-end-update_2.jpg", "/uploads/posts/2025-03/medium/survival-v030-end-update_3.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_5.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_4.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_3.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_2.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2299&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_6aaa9b167e5e46a1a9e8be43cd4f4c6e_1-520x245_1747226849_8esttoj1.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v030-end-update_2_1747226851_uodnpfrs.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v030-end-update_3_1747226852_89mhrk1s.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v025-variants-update_5_1747226853_3gm2l9zg.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v025-variants-update_4_1747226854_9yty2bhg.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v025-variants-update_3_1747226855_igynsg27.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_survival-v025-variants-update_2_1747226856_832asxau.jpeg?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2299&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:47:36"}, {"article_url": "https://mcpeland.io/en/mods/1527-addon-village-generator.html", "extracted_data": {"mod_name": "Village Generator", "version": null, "full_mod_description": "Build villages anywhere in your world with the run of a single command. Village Generator adds various functions to the game to help you design your own village from scratch quickly and easily. Now, go bring more life into your superflat worlds!", "mod_features": ["Individual structures", "Instant village", "Multiple village types", "Old village"], "primary_image_url": "/uploads/posts/2025-04/mcpedl-png.png", "other_image_urls": ["/uploads/posts/2025-04/medium/description_441532e7-4c6c-4cc5-9692-6f7bab692903.png", "/uploads/posts/2025-04/medium/description_2b2b8bf5-a256-4f51-a2ae-2944f469c6d4.png", "/uploads/posts/2025-04/medium/description_d9cb8eea-5d41-4f16-81be-5c45b6505698.png", "/uploads/posts/2025-04/medium/description_5c58f2cd-4912-485b-a413-0aeb532c6616.png", null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2308&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mcpedl-png_1747226864_u83tn2g0.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_description_441532e7-4c6c-4cc5-9692-6f7bab692903_1747226865_ilfqrg1b.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_description_2b2b8bf5-a256-4f51-a2ae-2944f469c6d4_1747226867_v8xofdhl.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_description_d9cb8eea-5d41-4f16-81be-5c45b6505698_1747226868_27hlxz8x.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_description_5c58f2cd-4912-485b-a413-0aeb532c6616_1747226869_g0uu6uzv.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2308&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:47:49"}, {"article_url": "https://mcpeland.io/en/mods/1542-addon-cursed-brainrot-italian-udin-din-din-dun-chimpanzini-bananini-update.html", "extracted_data": {"mod_name": "Cursed <PERSON>", "version": null, "full_mod_description": "Cursed Brainrot Italian is a chaos-driven, meme-heavy addon that introduces powerful, absurd, and terrifying entities based on a surreal mix of \"brainrot\" internet humor and exaggerated Italian cultural tropes. This mod is designed for players who want to break the limits of Minecraft’s seriousness and experience unpredictable, high-stakes encounters with over-the-top mobs.", "mod_features": ["<PERSON><PERSON> Tung <PERSON>hur", "<PERSON><PERSON><PERSON><PERSON>", "Brr Brr <PERSON><PERSON><PERSON>", "Chimpanzin<PERSON>", "<PERSON><PERSON>", "2 anomalies incoming", "Has 8,000 HP (divided by 400 if hit by player)", "Deals instant-kill damage (except to player)", "Heals for the same amount as the target's max HP", "Chases players across a 500-block radius", "Climbs walls like spiders", "Sprints at high speed", "Emits distinct, recognizable sounds (e.g., tral<PERSON><PERSON> tralala, tung tung tung tung sahur, brr brr patapim)"], "primary_image_url": "/uploads/posts/2025-04/1745959948_screenshot_2025-04-26-16-01-13-617_com-mojang.jpg", "other_image_urls": ["/uploads/posts/2025-04/medium/screenshot_2025-04-26-16-17-22-108_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-35-027_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-26-16-17-03-785_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-53-019_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-20-865_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-05-32-862_com-miui.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2329&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_1745959948_screenshot_2025-04-26-16-01-13-617_com_1747226878_det76sgy.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-26-16-17-22-108_com-miui_1747226880_2iqpmsra.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-11-21-04-35-027_com-miui_1747226881_4qxzpv1x.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-26-16-17-03-785_com-miui_1747226882_micmudlz.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-11-21-04-53-019_com-miui_1747226883_zywnd2xe.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-11-21-04-20-865_com-miui_1747226883_fkvj0zfz.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot_2025-04-11-21-05-32-862_com-miui_1747226884_7buet7an.jpeg?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2329&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:48:05"}, {"article_url": "https://mcpeland.io/en/mods/669-addon-ben-10-add-on.html", "extracted_data": {"mod_name": "Ben 10 Add-on", "version": null, "full_mod_description": "Ben 10! The protector of Earth! Is now in Minecraft with this add-on. <PERSON> is the kid who has destined to have the Omnitrix among his cousin <PERSON> and his grandpa <PERSON>. Now that <PERSON> got the omnitrix, he is now able to transform into an alien to fight monsters and villains. But <PERSON> can't save the Minecraft world without the help of <PERSON>'s magic powers, and <PERSON>'s combat and weapon skills. <PERSON>'s enemies have also extraordinary powers like <PERSON> and his companions have, but our hero <PERSON> will not let them win and conquer the Minecraft world. So, what are you waiting for, discover now how <PERSON> and his companions use their powers and skills to defeat their powerful enemies in the Minecraft world.", "mod_features": ["Has own spawn egg", "Doesn't replace any entity in the game", "Summon command: /summon ben10:ben10", "Health: 60", "Can melee attack", "Melee Attack Damage: 4", "Attacks monsters", "Can get the omnitrix once it sees it near to him", "Drops omnitrix on death if it has omnitrix", "Durability/Health: 30", "Fire immune", "Knockback resistance: 100", "Can be obtained by <PERSON>, <PERSON>, and <PERSON>.", "Explodes on fatal damage", "Rises once it is being obtained or taken"], "primary_image_url": "/uploads/posts/2022-07/featuredimage_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-07/medium/ben-10-addon_2.png", "/uploads/posts/2022-07/ben-10-addon_3.png", "/uploads/posts/2022-07/ben-10-addon_4.png", "/uploads/posts/2022-07/medium/ben-10-addon_5.png", "/uploads/posts/2022-07/medium/ben-10-addon_6.png", "/uploads/posts/2022-07/medium/ben-10-addon_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=988&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_featuredimage_1-520x245_1747226898_puw0ornv.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_2_1747226900_v5ci2760.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_3_1747226901_sn9iof6c.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_4_1747226901_lfl2cwir.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_5_1747226904_ne6zkjzk.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_6_1747226907_jv8c0cd3.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ben-10-addon_7_1747226908_36986l6h.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=988&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:48:28"}, {"article_url": "https://mcpeland.io/en/mods/1538-addon-hole-filler.html", "extracted_data": {"mod_name": "<PERSON> Filler", "version": null, "full_mod_description": "The Hole Filler Add-on introduces a new item to Minecraft Bedrock Edition designed to instantly fill in holes created by TNT blasts, creeper explosions, and other destruction. Simply throw the Hole Filler item into any crater, and it will quickly fill the space with blocks.<br><br>Created By DARKBLOCK GAMING <br><br>To obtain the Hole Filler item, type \"?dbgHoleFiller\" in the chat.<br>This add-on offers extensive customization options, including multiple animations for the filling process, allowing you to choose your favorite style.Note: This is the first beta version, and the hole-filling logic may contain some bugs.", "mod_features": [], "primary_image_url": "/uploads/posts/2025-04/holefilleraddon_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-04/medium/hole-filler-addon_2.jpg", "/uploads/posts/2025-04/hole-filler-addon_3.gif", "/uploads/posts/2025-04/medium/hole-filler-addon_3.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2324&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_holefilleraddon_1-520x245_1747226914_2mln5z98.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hole-filler-addon_2_1747226916_z5al721j.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hole-filler-addon_3_1747226923_wd7z00bi.jpeg?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2324&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:48:44"}, {"article_url": "https://mcpeland.io/en/mods/1445-addon-totem-plus.html", "extracted_data": {"mod_name": "Totem Plus", "version": null, "full_mod_description": "Introducing Totem Plus, the newest addon for Minecraft Bedrock that will revolutionize your gaming adventures! With Totem Plus, you'll have access to a variety of new totems, each granting special effects that can change the course of your battles and explorations. The totem only works when equipped in the second hand", "mod_features": [], "primary_image_url": "/uploads/posts/2024-12/semtddtulo1_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/totem-plus_2.png", "/uploads/posts/2024-12/medium/totem-plus_3.png", "/uploads/posts/2024-12/medium/totem-plus_4.png", "/uploads/posts/2024-12/medium/totem-plus_5.png", "/uploads/posts/2024-12/medium/totem-plus_6.png", "/uploads/posts/2024-12/medium/totem-plus_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2307&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_semtddtulo1_1-520x245_1747226932_xajoepwf.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_2_1747226933_0hxphav3.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_3_1747226934_zmwjbks6.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_4_1747226935_st8a9uck.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_5_1747226936_uo52vlaz.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_6_1747226937_cxpmm4ql.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_totem-plus_7_1747226937_tcq6k07g.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2307&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:48:58"}, {"article_url": "https://mcpeland.io/en/mods/1475-poppy-playtime-4-by-grecher.html", "extracted_data": {"mod_name": "Poppy Playtime 4", "version": null, "full_mod_description": "Poppy Playtime 4 by <PERSON><PERSON><PERSON>", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2024-12/kokok.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2224&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_kokok_1747226944_xdcvvke1.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2224&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:05"}, {"article_url": "https://mcpeland.io/en/mods/1474-poppy-playtime-4-by-maker.html", "extracted_data": {"mod_name": "Poppy Playtime 4", "version": null, "full_mod_description": "Poppy Playtime 4 by Maker    ", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2024-12/poppy5.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2223&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_poppy5_1747226951_ktvll8pt.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2223&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:12"}, {"article_url": "https://mcpeland.io/en/maps/1472-shin-sonic-map-by-heftycorn.html", "extracted_data": {"mod_name": "Shin Sonic", "version": null, "full_mod_description": "Shin Sonic map  ", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2024-12/shinmap.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2221&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_shinmap_1747226957_ppvfn9b7.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2221&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:18"}, {"article_url": "https://mcpeland.io/en/mods/1485-mimicer-addon-minecraft.html", "extracted_data": {"mod_name": "<PERSON><PERSON>", "version": null, "full_mod_description": "<div class=\"content-box\">\n    <div class=\"download-item border-radius\" style=\"overflow: hidden;\">\n        <div class=\"item-content\">\n            <span class=\"black medium\">Download the-mimicer-v4-update-animations-all-versions.mcaddon (.mcaddon)</span>\n            <div class=\"flex-sm middle-sm\">\n                <a target=\"_blank\" rel=\"nofollow\" class=\"flex middle center green-bg medium white\" href=\"https://mcpeland.io/index.php?do=download&amp;id=2238&amp;lang=en\" role=\"button\">\n                    <div class=\"flex middle center\">\n                        <span class=\"flex middle center\"><span class=\"icon download-link-white\"></span></span>\n                        <div class=\"flex middle center\">Download</div>\n                    </div>\n                </a>\n                <p class=\"regular transparent-grey\">[3.83 Mb] Downloads: 30313</p>\n            </div>\n        </div>\n    </div>\n</div>", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2025-01/mimiv.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2238&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mimiv_1747226965_jldx457e.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2238&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:26"}, {"article_url": "https://mcpeland.io/en/mods/1484-all-mimicers-in-minecraft-bedrock.html", "extracted_data": {"mod_name": "All Mimicers in Minecraft Bedrock", "version": null, "full_mod_description": null, "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2025-01/shshsukfdd.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2234&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_shshsukfdd_1747226971_mp1n3suc.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2234&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:32"}, {"article_url": "https://mcpeland.io/en/mods/1483-parasites-by-detro.html", "extracted_data": {"mod_name": "Parasites", "version": null, "full_mod_description": "<div class=\"content-box\">\n    <div class=\"download-item border-radius\" style=\"overflow: hidden;\">\n        <div class=\"item-content\">\n            <span class=\"black medium\">Download demo-red-rot-by-detro.mcaddon (.mcaddon)</span>\n            <div class=\"flex-sm middle-sm\">\n                <a target=\"_blank\" rel=\"nofollow\" class=\"flex middle center green-bg medium white\" href=\"https://mcpeland.io/index.php?do=download&amp;id=2233&amp;lang=en\" role=\"button\">\n                    <div class=\"flex middle center\">\n                        <span class=\"flex middle center\"><span class=\"icon download-link-white\"></span></span>\n                        <div class=\"flex middle center\">Download</div>\n                    </div>\n                </a>\n                <p class=\"regular transparent-grey\">[1.58 Mb] Downloads: 1395</p>\n            </div>\n        </div>\n    </div>\n</div>", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2025-01/pl.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2233&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_pl_1747226979_5z98gkdz.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2233&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:40"}, {"article_url": "https://mcpeland.io/en/mods/1489-addon-mc-fnf-indie-cross.html", "extracted_data": {"mod_name": "MC FNF Indie Cross", "version": null, "full_mod_description": "INDIE CROSS is a \"Friday Night Funkin mod\" featuring characters from Indie games. The main games represented in the mod currently are Cuphead, Undertale, and <PERSON><PERSON> and the Ink Machine.", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/icv2_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/medium/mc-fnf-indie-cross-v2-addon-official-port_5.png", "/uploads/posts/2025-02/medium/mc-fnf-indie-cross-v2-addon-official-port_7.png", "/uploads/posts/2025-02/medium/mc-fnf-indie-cross-v2-addon-official-port_8.png", null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2245&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_icv2_1-520x245_1747226986_pawfnphr.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mc-fnf-indie-cross-v2-addon-official-port_5_1747226988_9k7dxj5l.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mc-fnf-indie-cross-v2-addon-official-port_7_1747226989_0jlut5y8.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mc-fnf-indie-cross-v2-addon-official-port_8_1747226990_crg7vj04.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2245&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:49:51"}, {"article_url": "https://mcpeland.io/en/mods/1486-addon-random-loot-blocks.html", "extracted_data": {"mod_name": "Random Loot - Blocks", "version": null, "full_mod_description": "A modScript/addon that will drop items, place blocks or spawn random mobs by breaking any block, dropping items like enchanted apples, nether stars and many more.\n\nCreated By Effect99DL\n\nHow to play\nSimply break any block and random items will be dropped, mobs will appear, or blocks of any kind will be placed.\nThe only purpose is to have fun getting random items and survive the enemy mobs that might appear.\nCrouching will temporarily disable the addon.\nMobs like dragon, wither, warden, and the elder guardian will die instantly to avoid disturbing players.", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/captura-de-pantalla-2024-02-01-160521.png", "other_image_urls": ["/uploads/posts/2025-02/2024-02-01-17-03-31_1.gif", null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2239&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_captura-de-pantalla-2024-02-01-160521_1747226997_abmqyypf.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2239&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:50:04"}, {"article_url": "https://mcpeland.io/en/mods/1487-addon-gravestone.html", "extracted_data": {"mod_name": "GraveStone", "version": null, "full_mod_description": "How does it work?\n\nWhen you die you will get a key that when placed in your hand will tell you the coordinates and the distance in blocks where you died. When you go to the grave you must interact with it while having the key in your hand and you will obtain your entire inventory. The keys are unique and only work with the correct gravestone. Only the owner of the gravestone can open it, regardless of whether another player has the correct key. If for some reason you lose the key you can craft another one. If you fall into the void in the end dimension, the gravestone will be placed at height 1 along with some blocks to make it easier for you to obtain. If you die in the same position as another player, the gravestone will be placed next to that gravestone. Using the `admin` tag administrators will be able to open any grave. (V2.0) There are 8 graves that will appear depending on the player's XP level when they die. (V2.0)", "mod_features": ["When you die you will get a key that when placed in your hand will tell you the coordinates and the distance in blocks where you died.", "When you go to the grave you must interact with it while having the key in your hand and you will obtain your entire inventory.", "The keys are unique and only work with the correct gravestone.", "Only the owner of the gravestone can open it, regardless of whether another player has the correct key.", "If for some reason you lose the key you can craft another one.", "If you fall into the void in the end dimension, the gravestone will be placed at height 1 along with some blocks to make it easier for you to obtain.", "If you die in the same position as another player, the gravestone will be placed next to that gravestone.", "Using the `admin` tag administrators will be able to open any grave. (V2.0)", "There are 8 graves that will appear depending on the player's XP level when they die. (V2.0)"], "primary_image_url": "/uploads/posts/2025-02/captura-de-pantalla-2024-01-14-223412.png", "other_image_urls": ["/uploads/posts/2025-02/medium/description_3821d61b-5260-42ad-bb9d-b27c577e2c9c.png", "/uploads/posts/2025-02/medium/grave-addon_2.png", "/uploads/posts/2025-02/medium/grave-addon_3.png", "/uploads/posts/2025-02/medium/grave-addon_4.png", "/uploads/posts/2025-02/medium/grave-addon_27.png", "/uploads/posts/2025-02/medium/grave-addon_36.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2241&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_captura-de-pantalla-2024-01-14-223412_1747227013_32k7r4is.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_description_3821d61b-5260-42ad-bb9d-b27c577e2c9c_1747227014_hbtgi0ps.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_grave-addon_2_1747227017_jpm0wo4b.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_grave-addon_3_1747227018_mnhbiyeo.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_grave-addon_4_1747227019_yll4e7pp.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_grave-addon_27_1747227020_qn27rbnz.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_grave-addon_36_1747227021_8je1ud7b.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2241&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:50:22"}, {"article_url": "https://mcpeland.io/en/mods/1364-addon-ags-morph-mobs.html", "extracted_data": {"mod_name": "AG's <PERSON><PERSON><PERSON>", "version": null, "full_mod_description": "Ever wanted to stalk the night as a creeper or soar through the sky as a majestic eagle? This addon grants you the power of transformation! With a variety of craftable tools, you can become any mob in Minecraft! OVER 70+ MOBS!", "mod_features": ["Hold OR right-click on a mob to morph into it! This item helps you to change into the mob by going near it and striking the item to transform into it. Take on the abilities and appearance of any creature!", "Use this tool to transform into a random mob! Who knows what you'll become!", "Need to switch back to your normal self? Use the Unmorph Item to shed your disguise and return to human form.", "This amazing tool lets you harness the special abilities of the mob you're morphed into! For example enderman, snow golem, <PERSON><PERSON><PERSON> and more!", "The Player only equips this item after transforming into the mob with ability", "New Item: Name Tag Remover", "This Item in the update basically removes the player morph when morphing"], "primary_image_url": "/uploads/posts/2024-05/fererrere_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-05/medium/ags-morph-mobs_4.png", "/uploads/posts/2024-05/medium/ags-morph-mobs_5.png", "/uploads/posts/2024-05/medium/ags-morph-mobs_6.png", "/uploads/posts/2024-05/medium/ags-morph-mobs_8.png", "/uploads/posts/2024-05/medium/ags-morph-mobs_14.png", "/uploads/posts/2024-05/ags-morph-mobs_12.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2256&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_fererrere_1-520x245_1747227029_vg7kt6t9.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_4_1747227030_n4je0ov4.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_5_1747227031_nzw913r1.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_6_1747227034_s38d5aa7.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_8_1747227035_fk4t8a7z.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_14_1747227036_tpen1a4y.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ags-morph-mobs_12_1747227037_joqujq09.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2256&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:50:37"}, {"article_url": "https://mcpeland.io/en/mods/1493-godzilla-bloodbath.html", "extracted_data": {"mod_name": "Godzilla Bloodbath", "version": "1.0", "full_mod_description": null, "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2025-02/godzillablood.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2250&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_godzillablood_1747227042_jacg3bmb.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2250&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:50:42"}, {"article_url": "https://mcpeland.io/en/mods/1490-addon-ancient-fish.html", "extracted_data": {"mod_name": "Ancient Fish", "version": null, "full_mod_description": "Ancient fish is an addon themed around ancient fish that lived in prehistoric times. All fish can be found in the ocean and swamp biomes.", "mod_features": ["These are ginsu sharks, they are as big as the great white shark, but they have a weakness, namely they cannot survive long on land, therefore if you want to kill them, run to land.This shark is neutral, it will not attack if, but if it attacks you while swimming, it means you are entering its territory.", "This is an onychodus fish that is almost the same size as a ginsu shark, it also has sharp teeth and can kill players in seconds but don't worry because this fish It will not eat players because it only eats fish and small sharks.", "This is the ancient barracuda fish Bir<PERSON>ia because it has the speed of a barracuda, but it is good because it does not attack players, instead they will run away if they meet you if you want to kill it you have to race with this fish because if you succeed in killing it you will get the victory trophy head.", "This is a cladoselache shark, a small shark that has a medium size. They are not aggressive and they also stay away from players, but if circumstances force them to attack, it is the player himself Because it has entered its territory These sharks only eat small fish, you can also feed them by throwing the fish into the water and they will automatically eat them themselves.", "This is an acanthodii fish, a type of fish that is said to be related to sharks, but I am not sure that they are similar to sharks, but they have poisonous spines on their bodies.These fish don't attack players, they just chill in the game, but if you approach them, be prepared to be stung by their spines, so I hope you keep your distance from them.", "This is a palaeoniscus fish, a type of fish that has a group when swimming, they have light on their bodies to scare predators into thinking they are poisonous. This fish has 3 variants, namely Blue, Red, & Hollow Each variant has an ability but I'm not sure about that because they just run away if approached.", "Let me introduce you to the Dip<PERSON>rus fish, one of the fish in the Mawsonia family. Its small body is able to produce light in the dark. This fish has a sleep time, But if you see them sleeping, let me know immediately because I myself have never seen them sleep :)", "This is a coelacanth fish, a fish that is already familiar to us. This fish really avoids sunlight because this fish really loves its territory, Because if they swim too far they will return to their home. These fish are slow and have tough meat which many other fish don't want to eat.", "And the last one is the Mawsonia fish that I mentioned earlier, they are quite large in size.They are bad swimmers that's why players will find it very easy to kill them."], "primary_image_url": "/uploads/posts/2025-02/screenshot2024123013225380dba69a5e82e939c3ddef13f99a115ca3_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/medium/ancient-fish_2.png", "/uploads/posts/2025-02/medium/ancient-fish_3.png", "/uploads/posts/2025-02/medium/ancient-fish_4.png", "/uploads/posts/2025-02/medium/ancient-fish_5.png", "/uploads/posts/2025-02/medium/ancient-fish_6.png", "/uploads/posts/2025-02/medium/ancient-fish_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2246&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_screenshot2024123013225380dba69a5e82e939c3ddef13f9_1747227053_m5htebjx.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_2_1747227055_ik3ujz8t.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_3_1747227057_di7aj0ec.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_4_1747227059_3u1gc5wc.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_5_1747227061_iiok2al2.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_6_1747227062_vmvpjys1.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ancient-fish_7_1747227064_vcqmj4b0.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2246&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:51:05"}, {"article_url": "https://mcpeland.io/en/mods/1500-addon-kaiju-craft.html", "extracted_data": {"mod_name": "Kaiju Craft", "version": null, "full_mod_description": "Unleash the power of giant monsters in Kaiju Craft! This mod adds legendary kaiju like <PERSON><PERSON>, King <PERSON><PERSON><PERSON><PERSON>, and other colossal beasts to your world. Build massive cities, defend them from destruction, or become the ultimate kaiju and wreak havoc! Epic battles, powerful abilities, and unstoppable chaos await. Will you fight or flee?", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/ctg0j0l_gdihd.jpg", "other_image_urls": ["/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-025239.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-025522.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-025358.png", null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2260&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_ctg0j0l_gdihd_1747227071_x05j3zxy.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-025239_1747227074_ycv07vov.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-025522_1747227077_b32ti7li.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-025358_1747227079_mnikc3ec.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2260&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:51:20"}, {"article_url": "https://mcpeland.io/en/mods/1499-addon-skibidi-toilet-v9.html", "extracted_data": {"mod_name": "<PERSON><PERSON><PERSON>", "version": "9", "full_mod_description": "Dive into the wacky world of the <strong><PERSON><PERSON><PERSON></strong> meme! This mod brings legendary singing toilets, epic battles with cameras, and talking heads to Minecraft. Summon unique mobs, discover new items, and join the ultimate clash between Skibidi Toilets and Titans! Can you survive the chaos? Created By <PERSON><PERSON>", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/giwhkuifni8hd.jpg", "other_image_urls": ["/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-020450.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-020714.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-020817.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-0209567.png", null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2259&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_giwhkuifni8hd_1747227088_1q42cclg.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-020450_1747227090_y9z2qj1z.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-020714_1747227092_qo88e1fc.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-020817_1747227094_yqwsr12i.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-0209567_1747227097_65cdropf.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2259&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:51:38"}, {"article_url": "https://mcpeland.io/en/mods/1497-addon-hyper-drones.html", "extracted_data": {"mod_name": "Hyper Drones", "version": null, "full_mod_description": "Hyper Drones introduces a fleet of controllable drones to your Minecraft experience, allowing you to soar through the skies and conquer new heights!\n\nCreated By Shadow Blaze\n\nExploration Scout uncharted territories, discover hidden caves, and gain a new vantage point on your world.\nCombat Take to the skies and rain down destruction on your enemies from a safe distance.\nResource Gathering Effortlessly collect resources from high places or hard-to-reach areas.\nBuilding Get a bird's-eye view while constructing your next masterpiece.", "mod_features": ["Exploration Scout uncharted territories, discover hidden caves, and gain a new vantage point on your world.", "Combat Take to the skies and rain down destruction on your enemies from a safe distance.", "Resource Gathering Effortlessly collect resources from high places or hard-to-reach areas.", "Building Get a bird's-eye view while constructing your next masterpiece."], "primary_image_url": "/uploads/posts/2025-02/hrgf_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/medium/hyper-drones_2.png", "/uploads/posts/2025-02/medium/hyper-drones_3.png", "/uploads/posts/2025-02/medium/hyper-drones_4.png", "/uploads/posts/2025-02/medium/hyper-drones_5.png", "/uploads/posts/2025-02/medium/hyper-drones_6.png", "/uploads/posts/2025-02/medium/hyper-drones_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2257&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hrgf_1-520x245_1747227105_exnjp2a1.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_2_1747227107_yjag07kj.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_3_1747227111_umxf99ld.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_4_1747227113_qfumgqq1.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_5_1747227115_3sv8c4ll.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_6_1747227118_mzrq1k50.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_hyper-drones_7_1747227120_akm0tysh.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2257&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:52:02"}, {"article_url": "https://mcpeland.io/en/mods/1507-addon-battle-craft-20.html", "extracted_data": {"mod_name": "Battle Craft", "version": "2.0", "full_mod_description": "Welcome to the world of Battle Craft! This addon adds 29 firearms, 3 explosives, 2 non lethal grenades, 2 hostile npcs and bunch of clothing/armor.\n\nNow this pack's main focus is on realism and accurate detailed weaponry, based off firearms used in real life combat and wars today.\n\nCreated By ArsenalCraftStudios \n\nThe pack is multiplayer friendly\nAll 29 Guns\nThere are 11 different ammo types in the pack, with the strongest being .338 Lapua Magnum\nThe different clothing /armor options (this pic doesnt even include em all) \nThis pack is survival friendly, it has crafting recipes", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/project20241228210247501_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/medium/battlecraft-20_2.png", "/uploads/posts/2025-02/medium/battlecraft-20_2.jpg", "/uploads/posts/2025-02/medium/battlecraft-20_3.png", "/uploads/posts/2025-02/medium/battlecraft-20_3.jpg", "/uploads/posts/2025-02/medium/battlecraft-20_4.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2268&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_project20241228210247501_1-520x245_1747227129_qaj6btuy.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_battlecraft-20_2_1747227133_3urht444.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_battlecraft-20_2_1747227135_wvctrdf0.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_battlecraft-20_3_1747227137_h7h9th2h.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_battlecraft-20_3_1747227140_0gldamw4.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_battlecraft-20_4_1747227143_fcqgx1b6.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2268&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:52:25"}, {"article_url": "https://mcpeland.io/en/mods/1505-addon-auto-miner.html", "extracted_data": {"mod_name": "Auto Miner", "version": null, "full_mod_description": "The Auto Miner is a machine that will dig for you. It can also place planks, rails, powered rails, and red-stone torches as it mines.\n\nCreated By Cocoa Warrior\n\nThe miner will pick up items as it mines. It won't pick up anything that doesn't stack to 64. It will use planks to bridge so that it doesn't fall. It can use any kind of planks, including planks from other add-ons. If it has rails, it will place them as it goes. If it has powered rails, it will place one every 16 blocks. If it has red-stone torches, it will place them next to the powered rails. To power it, use Coal, Charcoal, Blaze Rods, or a Lava bucket. The MineRe version can also use Nether Coal and Ender Plasma. The drill breaks after 6000 blocks are broken. It can be repaired with another diamond drill or by using diamonds on the miner. Durability and fuel are remembered when the miner is broken into item form. The miner can be started and stopped by hitting it. It will start when given fuel.", "mod_features": ["The miner will pick up items as it mines. It won't pick up anything that doesn't stack to 64.", "It will use planks to bridge so that it doesn't fall. It can use any kind of planks, including planks from other add-ons.", "If it has rails, it will place them as it goes.", "If it has powered rails, it will place one every 16 blocks.", "If it has red-stone torches, it will place them next to the powered rails.", "To power it, use Coal, Charcoal, Blaze Rods, or a Lava bucket. The MineRe version can also use Nether Coal and Ender Plasma.", "The drill breaks after 6000 blocks are broken. It can be repaired with another diamond drill or by using diamonds on the miner.", "Durability and fuel are remembered when the miner is broken into item form.", "The miner can be started and stopped by hitting it. It will start when given fuel."], "primary_image_url": "/uploads/posts/2025-02/autominerlogo_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/cocoawarriors-auto-miner_2.gif", "/uploads/posts/2025-02/medium/cocoawarriors-auto-miner_2.png", "/uploads/posts/2025-02/cocoawarriors-auto-miner_3.png", "/uploads/posts/2025-02/cocoawarriors-auto-miner_4.png", "/uploads/posts/2025-02/cocoawarriors-auto-miner_5.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2266&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_autominerlogo_1-520x245_1747227154_lmb85w9s.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_cocoawarriors-auto-miner_2_1747227160_u3h457md.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_cocoawarriors-auto-miner_3_1747227163_vtozraps.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_cocoawarriors-auto-miner_4_1747227165_1jmdf7mu.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_cocoawarriors-auto-miner_5_1747227166_wotceq01.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2266&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:52:48"}, {"article_url": "https://mcpeland.io/en/mods/1501-addon-the-walking-siren.html", "extracted_data": {"mod_name": "The Walking Siren", "version": null, "full_mod_description": "Prepare to face terrifying siren-headed creatures in The Walking Siren! This mod brings horrifying monsters with blaring sirens for heads that hunt players by sound. Explore eerie locations, survive in a nightmare-filled world, and try not to fall into the clutches of these terrifying beings. Can you escape their deadly grasp?", "mod_features": [], "primary_image_url": "/uploads/posts/2025-02/o3vvvaerepkhd.jpg", "other_image_urls": ["/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-030633.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-030702.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-030736.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-030801.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-17-030858.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2261&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_o3vvvaerepkhd_1747227175_iq1f47mi.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-030633_1747227178_blt0c53k.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-030702_1747227180_uikzylhx.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-030736_1747227181_ws4m2xmz.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-030801_1747227182_nksehqp0.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-17-030858_1747227185_dsfn7rpd.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2261&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:53:07"}, {"article_url": "https://mcpeland.io/en/mods/1508-addon-the-boiled-one.html", "extracted_data": {"mod_name": "The Boiled One", "version": null, "full_mod_description": "Boiled One is a new horror game with unique abilities that will bring a scary and challenging experience to players сompatible with most other mods", "mod_features": ["Own Jumpscare Animation", "When In Attack State Movement Speed Increases Randomly Approaching You Will Be Surprised", "Connecting Glass Block", "Only 1 Entity Can Spawn at a Time Only 1 Entity Can Spawn at a Time The Boiled One Will Spawn After 1 Minute Only 1 Entity Can Spawn At A Time"], "primary_image_url": "/uploads/posts/2025-02/2025021607263216_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-24-021029.png", "/uploads/posts/2025-02/medium/snimok-jekrana-2025-02-24-020914.png", null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2269&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_2025021607263216_1-520x245_1747227193_dyddmoha.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-24-021029_1747227196_4t4mjf41.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_snimok-jekrana-2025-02-24-020914_1747227197_r85fgna0.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2269&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:53:17"}, {"article_url": "https://mcpeland.io/en/mods/733-addon-mythical-biomes.html", "extracted_data": {"mod_name": "Mythical Biomes", "version": "0.10", "full_mod_description": "Mythical Biomes is a mod  that will add some new biomes to your world, that look like a fantasy land. Including new mobs, features & blocks.", "mod_features": ["Mossy Forest currently replaced the Swampland biome.", "Corrupted Forest currently replaced the Wooden Badland biome.", "Overgrown Jungle currently replaced the Jungle biome. But the Sparse Jungle & Bamboo Jungle are still can be found.", "Generate in Mossy Forest surface.", "Generate in Corrupted Forest surface.", "Generate in Overgrown Jungle surface.", "Generate in Mossy Forest surface.", "Generate in Overgrown Jungle surface.", "Generate in Corrupted Forest surface.", "Generate in Mossy Forest surface.", "Generate in Overgrown Jungle surface.", "Generate in Corrupted Forest surface."], "primary_image_url": "/uploads/posts/2022-08/00-thumbnail_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_2.jpg", "/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_3.jpg", "/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_4.jpg", "/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_5.jpg", "/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_6.jpg", "/uploads/posts/2022-08/medium/mythical-biomes-beta-v010_7.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2180&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_00-thumbnail_1-520x245_1747227212_3sst3akv.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_2_1747227213_eep6yt9l.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_3_1747227216_2i1b1wpe.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_4_1747227217_1zfwulkt.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_5_1747227218_zqk1lj1s.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_6_1747227219_qjv1lqci.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_mythical-biomes-beta-v010_7_1747227220_s6p4762s.jpeg?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2180&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:53:40"}, {"article_url": "https://mcpeland.io/en/mods/1230-addon-furnideco-refurbished.html", "extracted_data": {"mod_name": "FurniDeco: Refurbished", "version": "1.21", "full_mod_description": "FurniDeco: Refurbished introduces 27 new pieces of furniture that are both functional and visually appealing, seamlessly blending into the vanilla style. Offering over 100 distinct variations, this remake provides limitless opportunities to furnish and beautify your constructions!\n\nCreated By kana<PERSON><PERSON><PERSON>", "mod_features": ["💺 27 Unique pieces of furniture & decorations, up to 100+ variants.", "🎨 Fully customizable.", "🎮 Interactive and functional.", "🍦 Fits with the Minecraft Vanilla feel.", "🪓 Craft-able in survival mode.", "🧊 Uses blocks."], "primary_image_url": "/uploads/posts/2023-12/medium/furnideco-refurbished_2.png", "other_image_urls": ["/uploads/posts/2023-12/medium/furnideco-refurbished_3.png", "/uploads/posts/2023-12/medium/furnideco-refurbished_4.jpg", "/uploads/posts/2023-12/medium/furnideco-refurbished_4.png", "/uploads/posts/2023-12/medium/furnideco-refurbished_5.jpg", "/uploads/posts/2023-12/medium/furnideco-refurbished_5.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2179&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_2_1747227239_nc8200zx.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_3_1747227245_sifomipd.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_4_1747227247_iuyh41f5.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_4_1747227248_8w44o49s.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_5_1747227251_k2hk9ci5.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_furnideco-refurbished_5_1747227252_wm9de5d4.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2179&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:54:13"}, {"article_url": "https://mcpeland.io/en/mods/1449-parasite-paradise.html", "extracted_data": {"mod_name": "Parasite Paradise", "version": "1.21", "full_mod_description": "Parasite Paradise", "mod_features": [], "primary_image_url": "https://mcpeland.io/uploads/posts/2024-12/newparasite.png", "other_image_urls": [null, null, null, null, null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2173&lang=en", "bp_download_link": null, "rp_download_link": null}, "mod_category": "Addons", "final_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_newparasite_1747227260_dbwuygqe.png?"], "final_mod_url": "https://mcpeland.io/index.php?do=download&id=2173&lang=en", "final_mod_size": "N/A (not downloaded)", "timestamp": "2025-05-14 12:54:21"}]