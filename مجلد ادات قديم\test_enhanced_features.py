# -*- coding: utf-8 -*-
"""
اختبار المميزات المحسنة: استخراج الصور المتعددة والأوصاف الذكية
"""

import sys
import os
import json

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_image_extraction():
    """اختبار استخراج الصور المحسن"""
    print("🖼️ اختبار استخراج الصور المحسن...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            images = result.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            for i, img_url in enumerate(images, 1):
                print(f"   {i}. {img_url}")
            
            if len(images) >= 3:
                print("✅ تم استخراج صور متعددة بنجاح")
                return True
            elif len(images) >= 1:
                print("⚠️ تم استخراج صورة واحدة فقط")
                return False
            else:
                print("❌ لم يتم استخراج أي صور")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصور: {e}")
        return False

def test_ai_descriptions():
    """اختبار إنشاء الأوصاف الذكية"""
    print("\n🤖 اختبار إنشاء الأوصاف الذكية...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            english_desc = result.get('description', '')
            arabic_desc = result.get('description_arabic', '')
            
            print(f"📝 الوصف الإنجليزي ({len(english_desc)} حرف):")
            print(f"   {english_desc[:200]}...")
            
            print(f"📝 الوصف العربي ({len(arabic_desc)} حرف):")
            print(f"   {arabic_desc[:200]}...")
            
            # التحقق من جودة الأوصاف
            english_quality = check_description_quality(english_desc, 'english')
            arabic_quality = check_description_quality(arabic_desc, 'arabic')
            
            if english_quality and arabic_quality:
                print("✅ تم إنشاء أوصاف عالية الجودة")
                return True
            elif english_quality or arabic_quality:
                print("⚠️ تم إنشاء وصف واحد عالي الجودة")
                return True
            else:
                print("❌ الأوصاف تحتاج تحسين")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأوصاف: {e}")
        return False

def check_description_quality(description: str, language: str) -> bool:
    """فحص جودة الوصف"""
    if not description or len(description) < 100:
        print(f"   ❌ الوصف {language} قصير جداً")
        return False
    
    if language == 'english':
        # فحص الكلمات الإنجليزية المهمة
        keywords = ['minecraft', 'addon', 'features', 'download', 'mod']
        if not any(keyword in description.lower() for keyword in keywords):
            print(f"   ⚠️ الوصف الإنجليزي لا يحتوي على كلمات مهمة")
            return False
    
    elif language == 'arabic':
        # فحص الكلمات العربية المهمة
        keywords = ['ماين كرافت', 'مود', 'إضافة', 'مميزات', 'تحميل']
        if not any(keyword in description for keyword in keywords):
            print(f"   ⚠️ الوصف العربي لا يحتوي على كلمات مهمة")
            return False
    
    print(f"   ✅ الوصف {language} عالي الجودة")
    return True

def test_fallback_descriptions():
    """اختبار الأوصاف الاحتياطية"""
    print("\n🔄 اختبار الأوصاف الاحتياطية...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # بيانات تجريبية
        test_mod_data = {
            'name': 'Test Dragon Mod',
            'category': 'Addons',
            'creator_name': 'TestCreator'
        }
        
        english, arabic = extractor.get_fallback_descriptions(test_mod_data)
        
        print(f"📝 الوصف الاحتياطي الإنجليزي ({len(english)} حرف):")
        print(f"   {english[:150]}...")
        
        print(f"📝 الوصف الاحتياطي العربي ({len(arabic)} حرف):")
        print(f"   {arabic[:150]}...")
        
        if len(english) > 100 and len(arabic) > 100:
            print("✅ الأوصاف الاحتياطية تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في الأوصاف الاحتياطية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأوصاف الاحتياطية: {e}")
        return False

def test_complete_extraction():
    """اختبار الاستخراج الكامل"""
    print("\n🔍 اختبار الاستخراج الكامل...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        result = scrape_mcpedl_mod(test_url)
        
        if not result:
            print("❌ فشل في الاستخراج")
            return False
        
        # فحص جميع الحقول المطلوبة
        required_fields = [
            'name', 'description', 'category', 'image_urls', 
            'version', 'creator_name', 'source_url'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not result.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"⚠️ حقول مفقودة: {missing_fields}")
        else:
            print("✅ جميع الحقول الأساسية متوفرة")
        
        # فحص جودة البيانات
        quality_score = 0
        total_checks = 7
        
        # فحص الاسم
        if result.get('name') and len(result['name']) > 3:
            quality_score += 1
            print("   ✅ الاسم")
        else:
            print("   ❌ الاسم")
        
        # فحص الوصف الإنجليزي
        if result.get('description') and len(result['description']) > 100:
            quality_score += 1
            print("   ✅ الوصف الإنجليزي")
        else:
            print("   ❌ الوصف الإنجليزي")
        
        # فحص الوصف العربي
        if result.get('description_arabic') and len(result['description_arabic']) > 100:
            quality_score += 1
            print("   ✅ الوصف العربي")
        else:
            print("   ❌ الوصف العربي")
        
        # فحص الفئة
        if result.get('category'):
            quality_score += 1
            print("   ✅ الفئة")
        else:
            print("   ❌ الفئة")
        
        # فحص الصور
        if result.get('image_urls') and len(result['image_urls']) >= 1:
            quality_score += 1
            print(f"   ✅ الصور ({len(result['image_urls'])})")
        else:
            print("   ❌ الصور")
        
        # فحص المطور
        if result.get('creator_name'):
            quality_score += 1
            print("   ✅ المطور")
        else:
            print("   ❌ المطور")
        
        # فحص الإصدار
        if result.get('version'):
            quality_score += 1
            print("   ✅ الإصدار")
        else:
            print("   ❌ الإصدار")
        
        # حساب النتيجة
        quality_percentage = (quality_score / total_checks) * 100
        print(f"\n📊 نتيجة الجودة: {quality_score}/{total_checks} ({quality_percentage:.1f}%)")
        
        if quality_percentage >= 80:
            print("🎉 جودة ممتازة!")
            return True
        elif quality_percentage >= 60:
            print("👍 جودة جيدة")
            return True
        else:
            print("⚠️ تحتاج تحسين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار الكامل: {e}")
        return False

def save_test_results(results: dict):
    """حفظ نتائج الاختبار"""
    try:
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print("💾 تم حفظ نتائج الاختبار في test_results.json")
    except Exception as e:
        print(f"⚠️ خطأ في حفظ النتائج: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار المميزات المحسنة لـ MCPEDL Scraper")
    print("=" * 60)
    
    results = {}
    
    # اختبار استخراج الصور
    results['image_extraction'] = test_enhanced_image_extraction()
    
    # اختبار الأوصاف الذكية
    results['ai_descriptions'] = test_ai_descriptions()
    
    # اختبار الأوصاف الاحتياطية
    results['fallback_descriptions'] = test_fallback_descriptions()
    
    # اختبار الاستخراج الكامل
    results['complete_extraction'] = test_complete_extraction()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    # حساب النتيجة الإجمالية
    success_rate = sum(results.values()) / len(results)
    print(f"\nمعدل النجاح الإجمالي: {success_rate*100:.1f}%")
    
    # حفظ النتائج
    save_test_results(results)
    
    # تقييم نهائي
    if success_rate >= 0.8:
        print("\n🎉 المميزات المحسنة تعمل بشكل ممتاز!")
        print("✅ استخراج صور متعددة")
        print("✅ أوصاف ذكية بالعربية والإنجليزية") 
        print("✅ جودة بيانات عالية")
    elif success_rate >= 0.6:
        print("\n👍 المميزات المحسنة تعمل بشكل جيد")
        print("⚠️ قد تحتاج بعض التحسينات الطفيفة")
    else:
        print("\n⚠️ المميزات المحسنة تحتاج مراجعة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
