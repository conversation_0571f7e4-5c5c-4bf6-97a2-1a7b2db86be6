# -*- coding: utf-8 -*-
"""
اختبار استخراج صور description من forgecdn
"""

import sys
import os
import re

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_description_patterns():
    """اختبار أنماط البحث الجديدة لصور description"""
    print("🧪 اختبار أنماط البحث لصور description")
    print("=" * 60)
    
    # HTML محاكي يحتوي على صور description
    test_html = """
    <html>
    <body>
        <div class="post-page__content">
            <p>Some content here</p>
            
            <!-- الصور المفقودة التي يجب استخراجها -->
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_679882ad-adb9-4fe9-93c2-02d7006b40e7.PNG" alt="Description 1">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_baadb589-414f-4f86-92fa-a22127245790.PNG" alt="Description 2">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_70434282-50e1-4dea-8a86-32e1762cdaec.PNG" alt="Description 3">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_1a774b05-e8af-47cd-9a40-123492413ed4.PNG" alt="Description 4">
            
            <!-- صور عادية من forgecdn -->
            <img src="https://media.forgecdn.net/attachments/1154/487/mcpedl-png.PNG" alt="Regular">
            
            <!-- صور يجب رفضها -->
            <img src="https://mcpedl.com/img/empty.png" alt="Empty">
        </div>
        
        <!-- في النص أيضاً -->
        <script>
            var images = [
                "https://media.forgecdn.net/attachments/description/1229982/description_extra.PNG",
                "https://media.forgecdn.net/attachments/1180/337/slide1-png.PNG"
            ];
        </script>
    </body>
    </html>
    """
    
    # الأنماط الجديدة
    new_patterns = [
        # forgecdn patterns - شامل لجميع المجلدات
        r'https://media\.forgecdn\.net/attachments/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
        r'media\.forgecdn\.net/attachments/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
        
        # forgecdn patterns محددة
        r'https://media\.forgecdn\.net/attachments/\d+/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',
        r'https://media\.forgecdn\.net/attachments/description/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',
        r'media\.forgecdn\.net/attachments/description/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',
    ]
    
    print("🔍 اختبار الأنماط الجديدة:")
    
    all_found_images = set()
    
    for i, pattern in enumerate(new_patterns, 1):
        found_urls = re.findall(pattern, test_html, re.IGNORECASE)
        print(f"\n   [{i}] نمط: {pattern[:60]}...")
        print(f"       وجد {len(found_urls)} رابط:")
        
        for url in found_urls:
            # تنظيف الرابط
            clean_url = url.strip('"\'<>)')
            if clean_url.startswith('media.forgecdn.net'):
                clean_url = 'https://' + clean_url
            
            all_found_images.add(clean_url)
            print(f"         - {clean_url}")
    
    print(f"\n📊 إجمالي الصور المكتشفة: {len(all_found_images)}")
    
    # الصور المتوقعة
    expected_images = [
        "https://media.forgecdn.net/attachments/description/1229982/description_679882ad-adb9-4fe9-93c2-02d7006b40e7.PNG",
        "https://media.forgecdn.net/attachments/description/1229982/description_baadb589-414f-4f86-92fa-a22127245790.PNG", 
        "https://media.forgecdn.net/attachments/description/1229982/description_70434282-50e1-4dea-8a86-32e1762cdaec.PNG",
        "https://media.forgecdn.net/attachments/description/1229982/description_1a774b05-e8af-47cd-9a40-123492413ed4.PNG",
        "https://media.forgecdn.net/attachments/description/1229982/description_extra.PNG",
        "https://media.forgecdn.net/attachments/1154/487/mcpedl-png.PNG",
        "https://media.forgecdn.net/attachments/1180/337/slide1-png.PNG"
    ]
    
    print("\n🎯 فحص الصور المتوقعة:")
    found_count = 0
    
    for i, expected in enumerate(expected_images, 1):
        if expected in all_found_images:
            print(f"   ✅ [{i}] تم العثور على: {expected}")
            found_count += 1
        else:
            print(f"   ❌ [{i}] مفقود: {expected}")
    
    print(f"\n📈 النتيجة: {found_count}/{len(expected_images)} صورة تم العثور عليها")
    
    success = found_count >= 6  # على الأقل 6 من 7
    
    if success:
        print("🎉 الأنماط الجديدة تعمل بشكل ممتاز!")
    else:
        print("⚠️ الأنماط تحتاج تحسين")
    
    return success

def test_with_real_extractor():
    """اختبار مع المستخرج الحقيقي"""
    print("\n" + "=" * 60)
    print("🧪 اختبار مع المستخرج الحقيقي")
    print("=" * 60)
    
    # HTML محاكي للصفحة الحقيقية
    real_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta property="og:image" content="https://media.forgecdn.net/attachments/1154/487/mcpedl-png.PNG">
        <title>Hardcore Starter House - Bedrock Edition | MCPEDL</title>
    </head>
    <body>
        <div class="post-page__content">
            <h1>Hardcore Starter House - Bedrock Edition</h1>
            
            <!-- صور description المفقودة -->
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_679882ad-adb9-4fe9-93c2-02d7006b40e7.PNG" alt="House 1">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_baadb589-414f-4f86-92fa-a22127245790.PNG" alt="House 2">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_70434282-50e1-4dea-8a86-32e1762cdaec.PNG" alt="House 3">
            <img src="https://media.forgecdn.net/attachments/description/1229982/description_1a774b05-e8af-47cd-9a40-123492413ed4.PNG" alt="House 4">
            
            <!-- صور عادية -->
            <img src="https://media.forgecdn.net/attachments/1180/337/slide1-png.PNG" alt="Slide">
            
            <!-- صور يجب رفضها -->
            <img src="https://mcpedl.com/img/empty.png" alt="Empty">
        </div>
        
        <!-- في JavaScript أيضاً -->
        <script>
            var gallery = {
                images: [
                    "https://media.forgecdn.net/attachments/description/1229982/description_extra.PNG",
                    "https://media.forgecdn.net/attachments/1158/457/slide1-png.PNG"
                ]
            };
        </script>
    </body>
    </html>
    """
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        print("🔄 اختبار استخراج كامل...")
        mod_data = extractor.extract_mod_data(real_html, "https://mcpedl.com/hardcore-starter-houses/", generate_ai_descriptions=False)
        
        if mod_data:
            images = mod_data.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            if images:
                print("   📋 الصور المستخرجة:")
                
                description_count = 0
                regular_count = 0
                
                for i, img in enumerate(images, 1):
                    print(f"      [{i}] {img}")
                    
                    if '/attachments/description/' in img:
                        print(f"           🎯 صورة description (هذا ما كان مفقود!)")
                        description_count += 1
                    elif 'media.forgecdn.net/attachments' in img:
                        print(f"           ✅ صورة forgecdn عادية")
                        regular_count += 1
                    else:
                        print(f"           ⚠️ صورة أخرى")
                
                print(f"\n📊 تحليل النتائج:")
                print(f"   🎯 صور description: {description_count}")
                print(f"   ✅ صور forgecdn عادية: {regular_count}")
                print(f"   📊 إجمالي: {len(images)}")
                
                # تقييم النجاح
                if description_count >= 4:
                    print("🎉 ممتاز! تم استخراج صور description المفقودة")
                    return True
                elif description_count >= 2:
                    print("👍 جيد! تم استخراج بعض صور description")
                    return True
                else:
                    print("❌ لم يتم استخراج صور description")
                    return False
            else:
                print("   ❌ لم يتم استخراج أي صور")
                return False
        else:
            print("❌ فشل الاستخراج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار استخراج صور description المفقودة")
    print("=" * 80)
    
    # اختبار الأنماط
    success1 = test_description_patterns()
    
    # اختبار المستخرج
    success2 = test_with_real_extractor()
    
    print("\n" + "=" * 80)
    print("📊 النتائج النهائية:")
    print(f"   🎯 أنماط البحث: {'✅ نجح' if success1 else '❌ فشل'}")
    print(f"   🎯 المستخرج الحقيقي: {'✅ نجح' if success2 else '❌ فشل'}")
    
    if success1 and success2:
        print("\n🎉 تم إصلاح مشكلة صور description!")
        print("💡 الآن ستحصل على جميع الصور من forgecdn بما في ذلك مجلد description")
    else:
        print("\n⚠️ هناك مشكلة تحتاج مراجعة")

if __name__ == "__main__":
    main()
