#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت Selenium وإعداده لحل مشكلة استخراج MCPEDL
"""

import subprocess
import sys
import os
import platform

def install_selenium():
    """تثبيت Selenium"""
    print("🔧 تثبيت Selenium...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium"])
        print("✅ تم تثبيت Selenium بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت Selenium: {e}")
        return False

def install_webdriver_manager():
    """تثبيت webdriver-manager لإدارة تلقائية للمتصفحات"""
    print("🔧 تثبيت webdriver-manager...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        print("✅ تم تثبيت webdriver-manager بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت webdriver-manager: {e}")
        return False

def check_chrome_installation():
    """التحقق من تثبيت Chrome"""
    print("🔍 التحقق من تثبيت Chrome...")
    
    chrome_paths = []
    
    if platform.system() == "Windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
    elif platform.system() == "Darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]
    else:  # Linux
        chrome_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser"
        ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ تم العثور على Chrome في: {path}")
            return True
    
    print("❌ لم يتم العثور على Chrome")
    print("💡 يرجى تثبيت Google Chrome من: https://www.google.com/chrome/")
    return False

def test_selenium_basic():
    """اختبار Selenium الأساسي"""
    print("🧪 اختبار Selenium الأساسي...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ تم استيراد Selenium بنجاح")
        
        # إعداد Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # تشغيل في الخلفية
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # محاولة إنشاء driver
        try:
            driver = webdriver.Chrome(options=chrome_options)
            print("✅ تم إنشاء Chrome driver بنجاح")
            
            # اختبار بسيط
            driver.get("https://www.google.com")
            title = driver.title
            print(f"✅ اختبار الصفحة نجح: {title}")
            
            driver.quit()
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء Chrome driver: {e}")
            
            # محاولة مع webdriver-manager
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                from selenium.webdriver.chrome.service import Service
                
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
                print("✅ تم إنشاء Chrome driver باستخدام webdriver-manager")
                
                driver.get("https://www.google.com")
                title = driver.title
                print(f"✅ اختبار الصفحة نجح: {title}")
                
                driver.quit()
                return True
                
            except Exception as e2:
                print(f"❌ فشل مع webdriver-manager أيضاً: {e2}")
                return False
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد Selenium: {e}")
        return False

def test_mcpedl_selenium():
    """اختبار استخراج MCPEDL باستخدام Selenium"""
    print("🌐 اختبار استخراج MCPEDL باستخدام Selenium...")
    
    try:
        from mcpedl_selenium_scraper import test_selenium_scraper
        
        if test_selenium_scraper():
            print("✅ نجح اختبار استخراج MCPEDL باستخدام Selenium")
            return True
        else:
            print("❌ فشل اختبار استخراج MCPEDL")
            return False
            
    except ImportError:
        print("❌ لا يمكن استيراد وحدة mcpedl_selenium_scraper")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار MCPEDL: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد Selenium لحل مشكلة استخراج MCPEDL")
    print("=" * 60)
    
    success_steps = 0
    total_steps = 5
    
    # الخطوة 1: تثبيت Selenium
    print("الخطوة 1/5: تثبيت Selenium")
    if install_selenium():
        success_steps += 1
    
    # الخطوة 2: تثبيت webdriver-manager
    print("\nالخطوة 2/5: تثبيت webdriver-manager")
    if install_webdriver_manager():
        success_steps += 1
    
    # الخطوة 3: التحقق من Chrome
    print("\nالخطوة 3/5: التحقق من Chrome")
    if check_chrome_installation():
        success_steps += 1
    
    # الخطوة 4: اختبار Selenium
    print("\nالخطوة 4/5: اختبار Selenium")
    if test_selenium_basic():
        success_steps += 1
    
    # الخطوة 5: اختبار MCPEDL
    print("\nالخطوة 5/5: اختبار استخراج MCPEDL")
    if test_mcpedl_selenium():
        success_steps += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_steps}/{total_steps} خطوات نجحت")
    
    if success_steps >= 4:
        print("🎉 تم إعداد Selenium بنجاح!")
        print("✅ يمكن الآن استخدام Selenium كبديل لاستخراج MCPEDL")
        
        print("\n💡 الخطوات التالية:")
        print("1. شغل الأداة الرئيسية: python mod_processor.py")
        print("2. جرب استخراج مود من MCPEDL")
        print("3. ستجرب الأداة الطريقة العادية أولاً، ثم Selenium كبديل")
        
    elif success_steps >= 2:
        print("⚠️ تم إعداد جزئي لـ Selenium")
        print("💡 قد يعمل لكن بنجاح محدود")
        
    else:
        print("❌ فشل في إعداد Selenium")
        
        print("\n🔧 حلول بديلة:")
        print("1. تثبيت Google Chrome يدوياً")
        print("2. تحديث pip: python -m pip install --upgrade pip")
        print("3. استخدام الاستخراج اليدوي")
    
    return success_steps >= 4

if __name__ == "__main__":
    try:
        success = main()
        
        print(f"\n{'🎯 الإعداد مكتمل!' if success else '⚠️ الإعداد جزئي'}")
        
        input("\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
