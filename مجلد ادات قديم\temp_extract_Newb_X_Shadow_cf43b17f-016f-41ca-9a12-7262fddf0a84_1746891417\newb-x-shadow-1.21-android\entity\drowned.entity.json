{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:drowned", "min_engine_version": "1.16.0", "materials": {"default": "spider"}, "textures": {"default": "textures/entity/zombie/drowned"}, "geometry": {"default": "geometry.zombie.drowned.v1.16"}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"], "animate": [{"humanoid_big_head": "query.is_baby"}, "look_at_target", "move", {"riding.arms": "query.is_riding"}, {"riding.legs": "query.is_riding"}, "holding", {"charging": "query.is_charging"}, "attack_controller", {"brandish_spear": "variable.is_brandishing_spear"}, "bob", {"damage_nearby_mobs": "variable.damage_nearby_mobs"}, {"use_item_progress": "( variable.use_item_interval_progress > 0.0 ) || ( variable.use_item_startup_progress > 0.0 )"}, {"swimming": "variable.swim_amount > 0.0"}]}, "animations": {"humanoid_big_head": "animation.humanoid.big_head", "look_at_target": "controller.animation.humanoid.look_at_target", "look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "move": "animation.humanoid.move", "riding.arms": "animation.humanoid.riding.arms", "riding.legs": "animation.humanoid.riding.legs", "holding": "animation.humanoid.holding", "brandish_spear": "animation.humanoid.brandish_spear", "charging": "animation.humanoid.charging", "bob": "animation.humanoid.bob", "damage_nearby_mobs": "animation.humanoid.damage_nearby_mobs", "use_item_progress": "animation.humanoid.use_item_progress", "attack_controller": "controller.animation.drowned.attack.rotations", "zombie_attack_bare_hand": "animation.zombie.attack_bare_hand", "attack_rotations": "animation.drowned.attack.rotations", "swimming": "animation.zombie.swimming"}, "render_controllers": ["controller.render.drowned"], "enable_attachables": true, "spawn_egg": {"texture": "spawn_egg", "texture_index": 48}}}}