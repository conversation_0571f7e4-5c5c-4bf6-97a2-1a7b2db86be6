# -*- coding: utf-8 -*-
"""
تكامل أداة تخصيص المودات مع النظام الأساسي
Mod Editor Integration with Main System
"""

import os
import json
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from datetime import datetime

try:
    from enhanced_mod_editor import EnhancedModEditor
    MOD_EDITOR_AVAILABLE = True
except ImportError:
    MOD_EDITOR_AVAILABLE = False

class ModEditorIntegration:
    """تكامل أداة تخصيص المودات"""
    
    def __init__(self, main_window, main_update_status_func):
        self.main_window = main_window
        self.main_update_status = main_update_status_func
        self.mod_editor = None
        
    def add_mod_editor_section(self, parent_frame):
        """إضافة قسم أداة تخصيص المودات للواجهة الأساسية"""
        
        # إطار أداة تخصيص المودات
        editor_frame = ttk.LabelFrame(parent_frame, text="🛠️ أداة تخصيص وإصلاح المودات")
        editor_frame.pack(pady=5, padx=10, fill="x")
        
        # معلومات الأداة
        info_frame = ttk.Frame(editor_frame)
        info_frame.pack(fill="x", padx=5, pady=5)
        
        info_label = ttk.Label(info_frame, 
                              text="أداة شاملة لتخصيص وإصلاح المودات المستخرجة مع إمكانيات متقدمة",
                              font=("Arial", 9), foreground="blue")
        info_label.pack(anchor="w")
        
        # الصف الأول - أزرار التحكم الرئيسية
        main_buttons_frame = ttk.Frame(editor_frame)
        main_buttons_frame.pack(fill="x", padx=5, pady=5)
        
        # زر فتح أداة التخصيص
        open_editor_button = ttk.Button(main_buttons_frame, 
                                       text="🛠️ فتح أداة التخصيص",
                                       command=self.open_mod_editor)
        open_editor_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تحرير المودات المستخرجة
        edit_extracted_button = ttk.Button(main_buttons_frame,
                                          text="📝 تحرير المودات المستخرجة",
                                          command=self.edit_extracted_mods)
        edit_extracted_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر معاينة سريعة
        quick_preview_button = ttk.Button(main_buttons_frame,
                                         text="👁️ معاينة سريعة",
                                         command=self.quick_preview_mods)
        quick_preview_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # الصف الثاني - أدوات إضافية
        tools_buttons_frame = ttk.Frame(editor_frame)
        tools_buttons_frame.pack(fill="x", padx=5, pady=5)
        
        # زر إصلاح المودات المشكلة
        fix_problematic_button = ttk.Button(tools_buttons_frame,
                                           text="🔧 إصلاح المودات المشكلة",
                                           command=self.fix_problematic_mods)
        fix_problematic_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر تصدير المودات المحررة
        export_edited_button = ttk.Button(tools_buttons_frame,
                                         text="📤 تصدير المودات المحررة",
                                         command=self.export_edited_mods)
        export_edited_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # زر إحصائيات التحرير
        stats_button = ttk.Button(tools_buttons_frame,
                                 text="📊 إحصائيات التحرير",
                                 command=self.show_editing_stats)
        stats_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # الصف الثالث - معلومات الحالة
        status_frame = ttk.Frame(editor_frame)
        status_frame.pack(fill="x", padx=5, pady=5)
        
        self.editor_status_label = ttk.Label(status_frame, text="جاهز للتحرير", 
                                            font=("Arial", 9), foreground="green")
        self.editor_status_label.pack(anchor="w")
        
        # تحديث معلومات الحالة
        self.update_editor_status()
        
        return editor_frame
    
    def open_mod_editor(self):
        """فتح أداة تخصيص المودات"""
        try:
            if not MOD_EDITOR_AVAILABLE:
                messagebox.showerror("خطأ", "أداة تخصيص المودات غير متاحة\nتأكد من وجود ملف enhanced_mod_editor.py")
                return
            
            if self.mod_editor is None or not hasattr(self.mod_editor, 'window') or not self.mod_editor.window.winfo_exists():
                self.mod_editor = EnhancedModEditor(self.main_window)
                self.main_update_status("🛠️ تم فتح أداة تخصيص المودات")
            else:
                # إحضار النافذة للمقدمة
                self.mod_editor.window.lift()
                self.mod_editor.window.focus_force()
                self.main_update_status("🔄 تم إحضار أداة التخصيص للمقدمة")
                
        except Exception as e:
            self.main_update_status(f"❌ خطأ في فتح أداة التخصيص: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح أداة التخصيص: {e}")
    
    def edit_extracted_mods(self):
        """تحرير المودات المستخرجة"""
        try:
            # البحث عن ملف المودات المستخرجة
            extracted_files = ["extracted_mods.json", "batch_extracted_mods.json", "mods_data.json"]
            selected_file = None
            
            for filename in extracted_files:
                if os.path.exists(filename):
                    selected_file = filename
                    break
            
            if not selected_file:
                messagebox.showwarning("تحذير", "لم يتم العثور على ملف مودات مستخرجة\nتأكد من وجود ملف extracted_mods.json")
                return
            
            # فتح أداة التخصيص مع الملف
            if not MOD_EDITOR_AVAILABLE:
                messagebox.showerror("خطأ", "أداة تخصيص المودات غير متاحة")
                return
            
            self.mod_editor = EnhancedModEditor(self.main_window)
            
            # تحميل الملف تلقائياً
            def load_file():
                try:
                    with open(selected_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # استخراج قائمة المودات
                    if isinstance(data, dict) and "mods" in data:
                        mods_list = data["mods"]
                    elif isinstance(data, list):
                        mods_list = data
                    else:
                        self.main_update_status("❌ تنسيق ملف غير مدعوم")
                        return
                    
                    # تحويل إلى كائنات ModData
                    from enhanced_mod_editor import ModData
                    self.mod_editor.mods_data = [ModData(mod) for mod in mods_list]
                    self.mod_editor.current_mod_index = 0
                    
                    if self.mod_editor.mods_data:
                        self.mod_editor.load_current_mod()
                        self.mod_editor.update_navigation()
                        self.main_update_status(f"تم تحميل {len(self.mod_editor.mods_data)} مود للتحرير")
                    
                except Exception as e:
                    self.main_update_status(f"❌ خطأ في تحميل الملف: {e}")
            
            # تحميل الملف بعد فترة قصيرة لضمان تهيئة الواجهة
            self.mod_editor.window.after(1000, load_file)
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في تحرير المودات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحرير المودات: {e}")
    
    def quick_preview_mods(self):
        """معاينة سريعة للمودات"""
        try:
            # البحث عن ملف المودات
            extracted_files = ["extracted_mods.json", "batch_extracted_mods.json"]
            selected_file = None
            
            for filename in extracted_files:
                if os.path.exists(filename):
                    selected_file = filename
                    break
            
            if not selected_file:
                messagebox.showwarning("تحذير", "لم يتم العثور على ملف مودات")
                return
            
            with open(selected_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # استخراج قائمة المودات
            if isinstance(data, dict) and "mods" in data:
                mods_list = data["mods"]
            elif isinstance(data, list):
                mods_list = data
            else:
                messagebox.showerror("خطأ", "تنسيق ملف غير مدعوم")
                return
            
            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.main_window)
            preview_window.title("معاينة سريعة للمودات")
            preview_window.geometry("800x600")
            preview_window.resizable(True, True)
            
            # إطار المعاينة
            main_frame = ttk.Frame(preview_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # عنوان
            title_label = ttk.Label(main_frame, text=f"معاينة {len(mods_list)} مود", 
                                   font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))
            
            # قائمة المودات
            from tkinter import scrolledtext
            preview_text = scrolledtext.ScrolledText(main_frame, height=30, width=100)
            preview_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            # إنشاء نص المعاينة
            preview_content = ""
            for i, mod in enumerate(mods_list, 1):
                preview_content += f"{'='*60}\n"
                preview_content += f"المود رقم {i}: {mod.get('name', 'غير محدد')}\n"
                preview_content += f"{'='*60}\n"
                preview_content += f"الفئة: {mod.get('category', 'غير محدد')}\n"
                preview_content += f"المنشئ: {mod.get('creator_name', 'غير محدد')}\n"
                preview_content += f"الإصدار: {mod.get('version', 'غير محدد')}\n"
                preview_content += f"رابط التحميل: {mod.get('download_url', 'غير محدد')}\n"
                preview_content += f"رابط المصدر: {mod.get('source_url', 'غير محدد')}\n"
                
                # الوصف العربي (مقتطف)
                ar_desc = mod.get('description_ar', '')
                if ar_desc:
                    ar_preview = ar_desc[:200] + "..." if len(ar_desc) > 200 else ar_desc
                    preview_content += f"الوصف العربي: {ar_preview}\n"
                
                # عدد الصور
                images_count = len(mod.get('image_urls', []))
                if mod.get('primary_image_url'):
                    images_count += 1
                preview_content += f"عدد الصور: {images_count}\n"
                
                preview_content += "\n"
            
            preview_text.insert("1.0", preview_content)
            preview_text.config(state=tk.DISABLED)
            
            # أزرار التحكم
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X)
            
            ttk.Button(buttons_frame, text="🛠️ فتح للتحرير", 
                      command=lambda: [preview_window.destroy(), self.edit_extracted_mods()]).pack(side=tk.LEFT, padx=(0, 10))
            
            ttk.Button(buttons_frame, text="📋 نسخ المعاينة", 
                      command=lambda: self.copy_preview_text(preview_text)).pack(side=tk.LEFT, padx=(0, 10))
            
            ttk.Button(buttons_frame, text="إغلاق", 
                      command=preview_window.destroy).pack(side=tk.RIGHT)
            
            self.main_update_status(f"تم عرض معاينة {len(mods_list)} مود")
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في المعاينة: {e}")
            messagebox.showerror("خطأ", f"فشل في المعاينة: {e}")
    
    def copy_preview_text(self, text_widget):
        """نسخ نص المعاينة"""
        try:
            text_widget.config(state=tk.NORMAL)
            content = text_widget.get("1.0", tk.END)
            text_widget.config(state=tk.DISABLED)
            
            self.main_window.clipboard_clear()
            self.main_window.clipboard_append(content)
            self.main_update_status("تم نسخ المعاينة إلى الحافظة")
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في النسخ: {e}")
    
    def fix_problematic_mods(self):
        """إصلاح المودات المشكلة"""
        try:
            problematic_file = "problematic_mods.json"
            
            if not os.path.exists(problematic_file):
                messagebox.showinfo("معلومات", "لا توجد مودات مشكلة للإصلاح")
                return
            
            with open(problematic_file, 'r', encoding='utf-8') as f:
                problematic_mods = json.load(f)
            
            if not problematic_mods:
                messagebox.showinfo("معلومات", "لا توجد مودات مشكلة")
                return
            
            # نافذة المودات المشكلة
            problems_window = tk.Toplevel(self.main_window)
            problems_window.title("المودات المشكلة")
            problems_window.geometry("700x500")
            problems_window.resizable(True, True)
            
            # قائمة المودات المشكلة
            main_frame = ttk.Frame(problems_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            ttk.Label(main_frame, text=f"المودات المشكلة ({len(problematic_mods)} مود)", 
                     font=("Arial", 12, "bold")).pack(pady=(0, 10))
            
            # جدول المودات المشكلة
            columns = ("الاسم", "سبب المشكلة", "التاريخ")
            tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=15)
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=200)
            
            # إضافة البيانات
            for mod in problematic_mods:
                tree.insert("", tk.END, values=(
                    mod.get('name', 'غير محدد'),
                    mod.get('problem_reason', 'غير محدد'),
                    mod.get('marked_date', 'غير محدد')[:10]  # التاريخ فقط
                ))
            
            tree.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            # أزرار التحكم
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X)
            
            def fix_selected():
                selection = tree.selection()
                if not selection:
                    messagebox.showwarning("تحذير", "اختر مود للإصلاح")
                    return
                
                # هنا يمكن إضافة منطق الإصلاح
                messagebox.showinfo("قيد التطوير", "ميزة الإصلاح التلقائي قيد التطوير")
            
            ttk.Button(buttons_frame, text="🔧 إصلاح المحدد", command=fix_selected).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="🗑️ حذف المحدد", command=lambda: self.delete_problematic_mod(tree)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(buttons_frame, text="إغلاق", command=problems_window.destroy).pack(side=tk.RIGHT)
            
            self.main_update_status(f"تم عرض {len(problematic_mods)} مود مشكلة")
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في عرض المودات المشكلة: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض المودات المشكلة: {e}")
    
    def delete_problematic_mod(self, tree):
        """حذف مود مشكلة من القائمة"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "اختر مود للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", "هل تريد حذف هذا المود من قائمة المشاكل؟"):
            tree.delete(selection[0])
            self.main_update_status("تم حذف المود من قائمة المشاكل")
    
    def export_edited_mods(self):
        """تصدير المودات المحررة"""
        try:
            # البحث عن ملفات المودات المحررة
            edited_files = ["published_mods.json", "edited_mods.json"]
            
            for filename in edited_files:
                if os.path.exists(filename):
                    messagebox.showinfo("تصدير", f"تم العثور على ملف المودات المحررة: {filename}")
                    self.main_update_status(f"ملف المودات المحررة متاح: {filename}")
                    return
            
            messagebox.showinfo("معلومات", "لا توجد مودات محررة للتصدير")
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في التصدير: {e}")
    
    def show_editing_stats(self):
        """عرض إحصائيات التحرير"""
        try:
            stats = self.collect_editing_stats()
            
            stats_text = f"""📊 إحصائيات أداة التخصيص:

📁 الملفات:
   • مودات مستخرجة: {stats['extracted_mods']}
   • مودات منشورة: {stats['published_mods']}
   • مودات مشكلة: {stats['problematic_mods']}

🛠️ التحرير:
   • جلسات التحرير: {stats['editing_sessions']}
   • آخر تحرير: {stats['last_editing']}

📊 الحالة:
   • أداة التخصيص: {'متاحة' if MOD_EDITOR_AVAILABLE else 'غير متاحة'}
   • ملفات النظام: {stats['system_files']} متاح
"""
            
            messagebox.showinfo("إحصائيات التحرير", stats_text)
            
        except Exception as e:
            self.main_update_status(f"❌ خطأ في الإحصائيات: {e}")
    
    def collect_editing_stats(self):
        """جمع إحصائيات التحرير"""
        stats = {
            "extracted_mods": 0,
            "published_mods": 0,
            "problematic_mods": 0,
            "editing_sessions": 0,
            "last_editing": "لا يوجد",
            "system_files": 0
        }
        
        try:
            # إحصائيات المودات المستخرجة
            if os.path.exists("extracted_mods.json"):
                with open("extracted_mods.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, dict) and "mods" in data:
                    stats["extracted_mods"] = len(data["mods"])
                elif isinstance(data, list):
                    stats["extracted_mods"] = len(data)
                
                stats["system_files"] += 1
            
            # إحصائيات المودات المنشورة
            if os.path.exists("published_mods.json"):
                with open("published_mods.json", 'r', encoding='utf-8') as f:
                    published_data = json.load(f)
                
                stats["published_mods"] = len(published_data) if isinstance(published_data, list) else 0
                stats["system_files"] += 1
            
            # إحصائيات المودات المشكلة
            if os.path.exists("problematic_mods.json"):
                with open("problematic_mods.json", 'r', encoding='utf-8') as f:
                    problematic_data = json.load(f)
                
                stats["problematic_mods"] = len(problematic_data) if isinstance(problematic_data, list) else 0
                stats["system_files"] += 1
            
        except Exception as e:
            print(f"خطأ في جمع الإحصائيات: {e}")
        
        return stats
    
    def update_editor_status(self):
        """تحديث حالة أداة التخصيص"""
        try:
            if not MOD_EDITOR_AVAILABLE:
                status = "❌ أداة التخصيص غير متاحة"
                color = "red"
            else:
                stats = self.collect_editing_stats()
                
                if stats["extracted_mods"] > 0:
                    status = f"✅ {stats['extracted_mods']} مود جاهز للتحرير"
                    color = "green"
                else:
                    status = "⚠️ لا توجد مودات للتحرير"
                    color = "orange"
            
            if hasattr(self, 'editor_status_label'):
                self.editor_status_label.config(text=status, foreground=color)
                
        except Exception as e:
            if hasattr(self, 'editor_status_label'):
                self.editor_status_label.config(text="❌ خطأ في تحديث الحالة", foreground="red")

def integrate_mod_editor_system(main_window, main_update_status_func, parent_frame):
    """دالة لتكامل أداة تخصيص المودات مع النظام الأساسي"""
    try:
        integration = ModEditorIntegration(main_window, main_update_status_func)
        editor_frame = integration.add_mod_editor_section(parent_frame)
        
        main_update_status_func("✅ تم تكامل أداة تخصيص المودات بنجاح")
        
        return integration, editor_frame
        
    except Exception as e:
        main_update_status_func(f"❌ خطأ في تكامل أداة التخصيص: {e}")
        return None, None
