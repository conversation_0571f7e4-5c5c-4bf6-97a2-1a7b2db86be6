# -*- coding: utf-8 -*-
"""
اختبار Firebase فقط - بعد إزالة Supabase
"""

import os
import json

def test_firebase_connection():
    """اختبار اتصال Firebase"""
    print("🔥 اختبار Firebase...")
    
    try:
        import firebase_admin
        from firebase_admin import credentials, storage, firestore
        
        # التحقق من ملف الخدمة
        service_account_file = "firebase-service-account.json"
        if not os.path.exists(service_account_file):
            print(f"❌ ملف خدمة Firebase غير موجود: {service_account_file}")
            return False
        
        print(f"✅ ملف خدمة Firebase موجود: {service_account_file}")
        
        # قراءة معلومات المشروع
        with open(service_account_file, 'r') as f:
            service_account_info = json.load(f)
            project_id = service_account_info.get('project_id')
            print(f"📋 معرف المشروع: {project_id}")
        
        # التحقق من وجود تطبيق مُهيأ
        try:
            app = firebase_admin.get_app()
            print("✅ تطبيق Firebase موجود مسبقاً")
        except ValueError:
            # تهيئة تطبيق جديد
            cred = credentials.Certificate(service_account_file)
            app = firebase_admin.initialize_app(cred, {
                'storageBucket': f'{project_id}.firebasestorage.app'
            })
            print("✅ تم تهيئة تطبيق Firebase جديد")
        
        # اختبار Firebase Storage
        print("\n📦 اختبار Firebase Storage...")
        try:
            bucket = storage.bucket()
            print(f"✅ Firebase Storage متصل - Bucket: {bucket.name}")
            
            # محاولة إدراج ملفات (للاختبار فقط)
            blobs = list(bucket.list_blobs(max_results=1))
            print(f"📁 عدد الملفات في Storage: {len(blobs)}")
            
        except Exception as storage_e:
            print(f"❌ خطأ في Firebase Storage: {storage_e}")
            return False
        
        # اختبار Firestore
        print("\n🗄️ اختبار Firestore...")
        try:
            db = firestore.client()
            print("✅ عميل Firestore تم إنشاؤه")
            
            # محاولة قراءة مجموعة (للاختبار)
            test_collection = db.collection('test')
            docs = list(test_collection.limit(1).stream())
            print(f"📄 اختبار قراءة Firestore: نجح")
            
            # محاولة كتابة وثيقة اختبار
            test_doc_ref = db.collection('test').document('connection_test')
            test_doc_ref.set({
                'message': 'اختبار الاتصال',
                'timestamp': firestore.SERVER_TIMESTAMP
            })
            print("✅ اختبار كتابة Firestore: نجح")
            
            # حذف وثيقة الاختبار
            test_doc_ref.delete()
            print("✅ اختبار حذف Firestore: نجح")
            
        except Exception as firestore_e:
            print(f"❌ خطأ في Firestore: {firestore_e}")
            print("💡 قد تحتاج إلى تفعيل Firestore في Google Cloud Console")
            print("🔗 اذهب إلى: https://console.firebase.google.com/")
            return False
        
        print("\n✅ جميع اختبارات Firebase نجحت!")
        return True
        
    except ImportError as import_e:
        print(f"❌ خطأ في استيراد مكتبات Firebase: {import_e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في Firebase: {e}")
        return False

def test_gemini_connection():
    """اختبار اتصال Gemini AI"""
    print("\n🤖 اختبار Gemini AI...")
    
    try:
        import google.generativeai as genai
        
        # تحميل مفاتيح API
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        gemini_keys = config.get('gemini_api_keys', [])
        if not gemini_keys:
            print("❌ لا توجد مفاتيح Gemini")
            return False
        
        print(f"🔑 عدد مفاتيح Gemini: {len(gemini_keys)}")
        
        # اختبار أول مفتاح
        test_key = gemini_keys[0]
        genai.configure(api_key=test_key)
        
        # استخدام نموذج أحدث
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content("مرحبا")
        
        print(f"✅ Gemini AI يعمل بشكل صحيح")
        print(f"📝 رد الاختبار: {response.text[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في Gemini AI: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل للتطبيق بعد إزالة Supabase")
    print("=" * 60)
    
    results = {}
    
    # اختبار Firebase
    results['firebase'] = test_firebase_connection()
    
    # اختبار Gemini
    results['gemini'] = test_gemini_connection()
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for service, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {service}: {'يعمل' if status else 'لا يعمل'}")
    
    print(f"\n📈 معدل النجاح: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الرسائل أعلاه لحل المشاكل.")
        
        if not results.get('firebase'):
            print("\n💡 لحل مشكلة Firebase:")
            print("   1. اذهب إلى: https://console.firebase.google.com/")
            print("   2. اختر مشروع: download-e33a2")
            print("   3. فعّل Firestore Database")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
