# -*- coding: utf-8 -*-
"""
اختبار بسيط للتأكد من عمل الإصلاحات
"""

import json
import time
from complete_mcpedl_scraper_fixed import scrape_mcpedl_mod

def test_description_cleaning():
    """اختبار تنظيف الوصف"""
    from complete_mcpedl_scraper_fixed import MCPEDLScraperFixed
    
    scraper = MCPEDLScraperFixed()
    
    # مثال على وصف يحتوي على مشاكل
    dirty_description = """
    [ENGLISH_DESCRIPTION]
    Elevate your Minecraft journey with Easy Waypoints, the essential addon designed to fundamentally enhance your entire gameplay experience. This remarkable addition injects your world with a suite of exciting new features and compelling content.
    
    
    Published on January 15, 2024
    Downloads: 5000
    File size: 2.5 MB
    Supported Minecraft versions: 1.20.0 - 1.21.0
    [/ENGLISH_DESCRIPTION]
    """
    
    cleaned = scraper.clean_description(dirty_description)
    
    print("=== اختبار تنظيف الوصف ===")
    print(f"الوصف الأصلي ({len(dirty_description)} حرف):")
    print(dirty_description[:200] + "...")
    print(f"\nالوصف بعد التنظيف ({len(cleaned)} حرف):")
    print(cleaned)
    
    # فحص أن النصوص غير المرغوبة تم حذفها
    issues_found = []
    if "[ENGLISH_DESCRIPTION]" in cleaned:
        issues_found.append("لا زالت تحتوي على [ENGLISH_DESCRIPTION]")
    if "[/ENGLISH_DESCRIPTION]" in cleaned:
        issues_found.append("لا زالت تحتوي على [/ENGLISH_DESCRIPTION]")
    if "Published on" in cleaned:
        issues_found.append("لا زالت تحتوي على معلومات النشر")
    if "fundamentally enhance" in cleaned:
        issues_found.append("لا زالت تحتوي على كلمات تسويقية")
    
    if issues_found:
        print(f"\n❌ مشاكل لم يتم إصلاحها:")
        for issue in issues_found:
            print(f"   - {issue}")
    else:
        print(f"\n✅ تم تنظيف الوصف بنجاح!")
    
    return len(issues_found) == 0

def test_mcpedl_extraction():
    """اختبار استخراج البيانات من mcpedl"""
    
    test_url = "https://mcpedl.com/take-a-seat/"
    
    print(f"\n=== اختبار استخراج البيانات من {test_url} ===")
    
    start_time = time.time()
    result = scrape_mcpedl_mod(test_url)
    end_time = time.time()
    
    print(f"⏱️ وقت الاستخراج: {end_time - start_time:.2f} ثانية")
    
    if result.get('extraction_status') == 'failed':
        print(f"❌ فشل الاستخراج: {result.get('error')}")
        return False
    
    # فحص النتائج
    issues = []
    
    if not result.get('name'):
        issues.append("لم يتم استخراج العنوان")
    
    if not result.get('description'):
        issues.append("لم يتم استخراج الوصف")
    elif len(result.get('description', '')) < 50:
        issues.append("الوصف قصير جداً")
    
    if not result.get('category'):
        issues.append("لم يتم استخراج الفئة")
    
    if not result.get('image_urls'):
        issues.append("لم يتم استخراج أي صور")
    
    if not result.get('main_image'):
        issues.append("لم يتم استخراج الصورة الرئيسية")
    
    # فحص جودة الوصف
    description = result.get('description', '')
    if '[ENGLISH_DESCRIPTION]' in description:
        issues.append("الوصف يحتوي على [ENGLISH_DESCRIPTION]")
    if '[/ENGLISH_DESCRIPTION]' in description:
        issues.append("الوصف يحتوي على [/ENGLISH_DESCRIPTION]")
    
    # عرض النتائج
    print(f"📝 العنوان: {result.get('name', 'غير متوفر')}")
    print(f"📂 الفئة: {result.get('category', 'غير متوفر')}")
    print(f"📖 طول الوصف: {len(description)} حرف")
    print(f"🖼️ عدد الصور: {len(result.get('image_urls', []))}")
    
    if result.get('main_image'):
        print(f"🎯 الصورة الرئيسية: {result.get('main_image')}")
    
    if result.get('additional_images'):
        print(f"➕ صور إضافية: {len(result.get('additional_images', []))}")
    
    # عرض أول 200 حرف من الوصف
    if description:
        print(f"📄 بداية الوصف: {description[:200]}...")
    
    # فحص الصور للتأكد من عدم وجود مقترحات
    suspicious_images = []
    for img_url in result.get('image_urls', []):
        if any(keyword in img_url.lower() for keyword in ['thumb', 'thumbnail', 'suggestion', 'related']):
            suspicious_images.append(img_url)
    
    if suspicious_images:
        issues.append(f"تم العثور على {len(suspicious_images)} صور مشكوك أنها مقترحة")
    
    if issues:
        print(f"\n❌ مشاكل تم العثور عليها:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print(f"\n✅ تم استخراج البيانات بنجاح!")
        
        # حفظ النتائج للمراجعة
        output_file = f"test_result_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"💾 تم حفظ النتائج في: {output_file}")
        
        return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الإصلاحات...\n")
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار تنظيف الوصف
    if test_description_cleaning():
        tests_passed += 1
    
    # اختبار استخراج البيانات
    if test_mcpedl_extraction():
        tests_passed += 1
    
    print(f"\n{'='*50}")
    print(f"📊 نتائج الاختبارات: {tests_passed}/{total_tests} نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح.")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الرسائل أعلاه لمعرفة المشاكل.")
    
    print("="*50)

if __name__ == "__main__":
    main()
