-- SQL script to clean up and optimize the mods table
-- This script removes unnecessary data and optimizes the database

-- 1. Remove mods with no downloads and no likes (potentially spam or test entries)
DELETE FROM mods 
WHERE downloads = 0 
  AND likes = 0 
  AND clicks < 5 
  AND created_at < NOW() - INTERVAL '30 days';

-- 2. Remove mods with empty or invalid names
DELETE FROM mods 
WHERE name IS NULL 
   OR TRIM(name) = '' 
   OR name = 'Test' 
   OR name = 'test' 
   OR name ILIKE '%test%mod%'
   OR name ILIKE '%sample%';

-- 3. Remove mods with no download URL
DELETE FROM mods 
WHERE download_url IS NULL 
   OR TRIM(download_url) = '' 
   OR download_url = 'null';

-- 4. Clean up invalid image URLs
UPDATE mods 
SET image_urls = '[]'::jsonb 
WHERE image_urls IS NULL 
   OR image_urls = 'null'::jsonb 
   OR jsonb_array_length(image_urls) = 0;

-- 5. Clean up empty creator social channels
UPDATE mods 
SET creator_social_channels = '[]'::jsonb 
WHERE creator_social_channels IS NULL 
   OR creator_social_channels = 'null'::jsonb 
   OR jsonb_array_length(creator_social_channels) = 0;

-- 6. Remove mods with invalid categories
DELETE FROM mods 
WHERE category IS NULL 
   OR category NOT IN ('Addons', 'Shaders', 'Texture Pack', 'Maps', 'Skins');

-- 7. Clean up duplicate mods (same name and creator)
WITH duplicate_mods AS (
    SELECT id, 
           ROW_NUMBER() OVER (
               PARTITION BY LOWER(TRIM(name)), LOWER(TRIM(creator_name)) 
               ORDER BY downloads DESC, likes DESC, created_at DESC
           ) as rn
    FROM mods
    WHERE name IS NOT NULL AND creator_name IS NOT NULL
)
DELETE FROM mods 
WHERE id IN (
    SELECT id FROM duplicate_mods WHERE rn > 1
);

-- 8. Remove mods with suspicious download URLs (broken links)
DELETE FROM mods 
WHERE download_url ILIKE '%example.com%'
   OR download_url ILIKE '%test.com%'
   OR download_url ILIKE '%localhost%'
   OR download_url ILIKE '%127.0.0.1%';

-- 9. Clean up creator contact info (remove empty or placeholder text)
UPDATE mods 
SET creator_contact_info = NULL 
WHERE creator_contact_info IS NOT NULL 
  AND (
      TRIM(creator_contact_info) = '' 
      OR creator_contact_info ILIKE '%no contact%'
      OR creator_contact_info ILIKE '%unknown%'
      OR creator_contact_info = 'null'
  );

-- 10. Remove very old mods with no activity
DELETE FROM mods 
WHERE created_at < NOW() - INTERVAL '1 year'
  AND downloads = 0 
  AND likes = 0 
  AND clicks = 0;

-- 11. Update statistics for remaining mods (optional)
UPDATE mods 
SET clicks = GREATEST(clicks, downloads) 
WHERE clicks < downloads;

-- 12. Vacuum and analyze the table for better performance
VACUUM ANALYZE mods;

-- 13. Show cleanup results
SELECT 
    'Total mods after cleanup' as description,
    COUNT(*) as count
FROM mods
UNION ALL
SELECT 
    'Mods with downloads' as description,
    COUNT(*) as count
FROM mods WHERE downloads > 0
UNION ALL
SELECT 
    'Mods with social channels' as description,
    COUNT(*) as count
FROM mods WHERE creator_social_channels IS NOT NULL 
  AND jsonb_array_length(creator_social_channels) > 0
UNION ALL
SELECT 
    'Mods by category' as description,
    COUNT(*) as count
FROM mods GROUP BY category
ORDER BY description;

-- 14. Show top categories
SELECT 
    category,
    COUNT(*) as mod_count,
    AVG(downloads) as avg_downloads,
    SUM(downloads) as total_downloads
FROM mods 
GROUP BY category 
ORDER BY mod_count DESC;

-- 15. Show recent activity
SELECT 
    DATE(created_at) as date,
    COUNT(*) as mods_added,
    SUM(downloads) as total_downloads
FROM mods 
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 16. Identify mods that might need attention
SELECT 
    name,
    creator_name,
    downloads,
    likes,
    clicks,
    created_at
FROM mods 
WHERE (
    -- High clicks but low downloads (might be misleading)
    (clicks > 100 AND downloads < 10)
    OR 
    -- Very popular mods (might need special attention)
    (downloads > 1000)
    OR
    -- Recent mods with good performance
    (created_at > NOW() - INTERVAL '7 days' AND downloads > 50)
)
ORDER BY downloads DESC, created_at DESC
LIMIT 20;

-- 17. Check for potential issues
SELECT 
    'Mods with very long names' as issue,
    COUNT(*) as count
FROM mods WHERE LENGTH(name) > 100
UNION ALL
SELECT 
    'Mods with no creator name' as issue,
    COUNT(*) as count
FROM mods WHERE creator_name IS NULL OR TRIM(creator_name) = ''
UNION ALL
SELECT 
    'Mods with invalid version format' as issue,
    COUNT(*) as count
FROM mods WHERE version IS NOT NULL 
  AND version !~ '^[0-9]+(\.[0-9]+)*$'
  AND TRIM(version) != '';

-- 18. Create backup before major cleanups (optional)
/*
CREATE TABLE mods_backup AS 
SELECT * FROM mods 
WHERE created_at > NOW() - INTERVAL '1 year';
*/
