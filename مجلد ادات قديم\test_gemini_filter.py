# -*- coding: utf-8 -*-
"""
اختبار فلتر الصور الذكي باستخدام Gemini AI
"""

import json
import os
from typing import List, Dict

def test_gemini_image_filter_basic():
    """اختبار أساسي لفلتر الصور"""
    print("🧪 اختبار فلتر الصور الذكي باستخدام Gemini AI")
    print("=" * 60)
    
    try:
        from gemini_image_filter import GeminiImageFilter
        
        # التحقق من وجود مفتاح API
        api_key = get_test_api_key()
        if not api_key:
            print("❌ لا يوجد مفتاح Gemini API للاختبار")
            print("💡 أضف مفتاح API في ملف config.json أو متغير البيئة GEMINI_API_KEY")
            return False
        
        # إنشاء فلتر
        print("🔧 إنشاء فلتر Gemini...")
        filter_instance = GeminiImageFilter(api_key)
        
        if not filter_instance.model:
            print("❌ فشل في تهيئة Gemini")
            return False
        
        print("✅ تم تهيئة فلتر Gemini بنجاح")
        
        # صور تجريبية (روابط وهمية للاختبار)
        test_images = [
            "https://media.forgecdn.net/attachments/123/456/dragon_screenshot.png",
            "https://mcpedl.com/wp-content/uploads/2023/01/mod_preview.jpg",
            "https://gravatar.com/avatar/user123.png",  # صورة مستخدم (يجب رفضها)
            "https://mcpedl.com/img/shield.png",  # صورة موقع (يجب رفضها)
            "https://media.forgecdn.net/attachments/789/012/gameplay_feature.png"
        ]
        
        # معلومات المود التجريبية
        mod_name = "Dragon Mounts"
        mod_description = "Adds rideable dragons with unique abilities to Minecraft"
        
        print(f"\n📋 اختبار فلترة {len(test_images)} صور للمود: {mod_name}")
        print("🔍 الصور المرسلة:")
        for i, img in enumerate(test_images):
            print(f"   [{i}] {img}")
        
        # تشغيل الفلتر (ملاحظة: هذا سيفشل مع روابط وهمية)
        print("\n🤖 بدء الفلترة...")
        try:
            filtered_images = filter_instance.filter_mod_images(test_images, mod_name, mod_description)
            
            print(f"\n✅ نتائج الفلترة:")
            print(f"   📊 تم اختيار {len(filtered_images)} من أصل {len(test_images)} صور")
            
            if filtered_images:
                print("   🎯 الصور المختارة:")
                for i, img in enumerate(filtered_images):
                    print(f"      [{i}] {img}")
            else:
                print("   ⚠️ لم يتم اختيار أي صور")
            
            return True
            
        except Exception as e:
            print(f"⚠️ خطأ في الفلترة (متوقع مع روابط وهمية): {e}")
            print("💡 هذا طبيعي لأن الروابط وهمية ولا يمكن تحميلها")
            return True  # نعتبر هذا نجاح لأن الخطأ متوقع
        
    except ImportError:
        print("❌ لا يمكن استيراد gemini_image_filter")
        print("💡 تأكد من وجود الملف وتثبيت google-generativeai")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_gemini_filter_with_real_mcpedl():
    """اختبار مع صفحة MCPEDL حقيقية"""
    print("\n🌐 اختبار مع صفحة MCPEDL حقيقية")
    print("=" * 60)
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # رابط مود حقيقي للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"🔗 اختبار مع: {test_url}")
        print("📥 بدء استخراج البيانات...")
        
        # استخراج البيانات مع الفلترة الذكية
        mod_data = scrape_mcpedl_mod(test_url)
        
        if mod_data:
            print("✅ تم استخراج البيانات بنجاح")
            print(f"📋 اسم المود: {mod_data.get('name', 'غير معروف')}")
            print(f"🖼️ عدد الصور: {len(mod_data.get('image_urls', []))}")
            
            images = mod_data.get('image_urls', [])
            if images:
                print("📸 الصور المستخرجة:")
                for i, img in enumerate(images[:5]):  # أول 5 صور فقط
                    print(f"   [{i+1}] {img[:80]}...")
                    
                if len(images) > 5:
                    print(f"   ... و {len(images) - 5} صور أخرى")
            
            return True
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except ImportError:
        print("❌ لا يمكن استيراد mcpedl_scraper_module")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_config_loading():
    """اختبار تحميل الإعدادات"""
    print("\n⚙️ اختبار تحميل الإعدادات")
    print("=" * 60)
    
    try:
        from gemini_image_filter import GeminiImageFilter
        
        # إنشاء فلتر بدون مفتاح لاختبار تحميل الإعدادات
        filter_instance = GeminiImageFilter("")
        api_key = filter_instance.get_api_key_from_config()
        
        if api_key:
            print("✅ تم العثور على مفتاح API في الإعدادات")
            print(f"🔑 المفتاح: {api_key[:20]}...")
            return True
        else:
            print("⚠️ لم يتم العثور على مفتاح API")
            print("💡 أضف مفتاح في config.json أو متغير البيئة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def get_test_api_key():
    """الحصول على مفتاح API للاختبار"""
    # البحث في متغيرات البيئة
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        return api_key
    
    # البحث في ملف config
    config_files = ['config.json', 'settings.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # البحث في gemini_api_keys (قائمة)
                if 'gemini_api_keys' in config and config['gemini_api_keys']:
                    keys = config['gemini_api_keys']
                    if isinstance(keys, list) and len(keys) > 0:
                        return keys[0]
                
                # البحث في gemini_api_key
                if 'gemini_api_key' in config and config['gemini_api_key']:
                    return config['gemini_api_key']
                    
            except Exception:
                continue
    
    return None

def create_sample_config():
    """إنشاء ملف إعدادات نموذجي"""
    print("\n📝 إنشاء ملف إعدادات نموذجي")
    print("=" * 60)
    
    sample_config = {
        "gemini_api_keys": [
            "your-gemini-api-key-here"
        ],
        "note": "أضف مفتاح Gemini API الخاص بك هنا"
    }
    
    try:
        with open('config_sample.json', 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء ملف config_sample.json")
        print("💡 انسخه إلى config.json وأضف مفتاح API الخاص بك")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء تشغيل جميع اختبارات فلتر Gemini")
    print("=" * 80)
    
    tests = [
        ("اختبار تحميل الإعدادات", test_config_loading),
        ("اختبار فلتر الصور الأساسي", test_gemini_image_filter_basic),
        ("اختبار مع MCPEDL حقيقي", test_gemini_filter_with_real_mcpedl),
        ("إنشاء ملف إعدادات نموذجي", create_sample_config)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n📊 ملخص نتائج الاختبارات")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
    
    print(f"\n🎯 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
    elif passed > 0:
        print("⚠️ بعض الاختبارات فشلت - راجع التفاصيل أعلاه")
    else:
        print("💥 جميع الاختبارات فشلت - تحقق من الإعداد")

if __name__ == "__main__":
    run_all_tests()
