# -*- coding: utf-8 -*-
"""
اختبار المستخرج المحسن لـ MCPEDL
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_extraction():
    """اختبار استخراج الصور المحسن"""
    print("🧪 اختبار استخراج الصور المحسن من MCPEDL")
    print("=" * 60)
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        # رابط اختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"🔗 رابط الاختبار: {test_url}")
        print()
        
        # محاولة جلب الصفحة
        try:
            import cloudscraper
            scraper = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
            
            print("🌐 جلب الصفحة...")
            response = scraper.get(test_url, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ تم جلب الصفحة بنجاح ({len(response.text)} حرف)")
                
                # استخراج البيانات
                extractor = MCPEDLExtractorFixed()
                mod_data = extractor.extract_mod_data(response.text, test_url, generate_ai_descriptions=False)
                
                if mod_data:
                    print("\n📊 نتائج الاستخراج:")
                    print(f"   📝 الاسم: {mod_data.get('name', 'غير محدد')}")
                    print(f"   📂 الفئة: {mod_data.get('category', 'غير محدد')}")
                    print(f"   👤 المطور: {mod_data.get('creator_name', 'غير محدد')}")
                    print(f"   📦 الإصدار: {mod_data.get('version', 'غير محدد')}")
                    print(f"   📏 الحجم: {mod_data.get('size', 'غير محدد')}")
                    
                    # تفاصيل الصور
                    images = mod_data.get('image_urls', [])
                    print(f"\n🖼️ الصور المستخرجة: {len(images)} صورة")
                    
                    if images:
                        print("\n📋 قائمة الصور:")
                        for i, img_url in enumerate(images, 1):
                            print(f"   [{i:2d}] {img_url}")
                            
                            # تحليل نوع الصورة
                            if 'media.forgecdn.net/attachments' in img_url:
                                print(f"        ✅ صورة مود حقيقية (forgecdn)")
                            elif 'mcpedl.com/wp-content' in img_url:
                                print(f"        ✅ صورة محتوى (wp-content)")
                            elif 'r2.mcpedl.com' in img_url and '/users/' not in img_url:
                                print(f"        ✅ صورة محتوى (r2.mcpedl)")
                            else:
                                print(f"        ⚠️ صورة أخرى")
                        
                        # إحصائيات الصور
                        forgecdn_count = sum(1 for img in images if 'media.forgecdn.net/attachments' in img)
                        mcpedl_content_count = sum(1 for img in images if 'mcpedl.com/wp-content' in img)
                        r2_content_count = sum(1 for img in images if 'r2.mcpedl.com' in img and '/users/' not in img)
                        
                        print(f"\n📈 إحصائيات الصور:")
                        print(f"   🔥 صور forgecdn: {forgecdn_count}")
                        print(f"   📄 صور wp-content: {mcpedl_content_count}")
                        print(f"   🗂️ صور r2.mcpedl: {r2_content_count}")
                        print(f"   📊 إجمالي: {len(images)}")
                        
                        # تقييم الجودة
                        quality_score = (forgecdn_count * 3 + mcpedl_content_count * 2 + r2_content_count * 1)
                        print(f"\n⭐ نقاط الجودة: {quality_score}")
                        
                        if quality_score >= 10:
                            print("🎉 ممتاز! تم استخراج صور عالية الجودة")
                        elif quality_score >= 5:
                            print("👍 جيد! تم استخراج صور مقبولة")
                        else:
                            print("⚠️ متوسط! قد تحتاج لتحسين الاستخراج")
                    else:
                        print("❌ لم يتم استخراج أي صور")
                    
                    # تفاصيل الوصف
                    description = mod_data.get('description', '')
                    print(f"\n📝 الوصف: {len(description)} حرف")
                    if description:
                        print(f"   المقطع الأول: {description[:100]}...")
                    
                    return True
                else:
                    print("❌ فشل في استخراج البيانات")
                    return False
            else:
                print(f"❌ فشل في جلب الصفحة: {response.status_code}")
                return False
                
        except ImportError:
            print("❌ cloudscraper غير متوفر. تثبيت: pip install cloudscraper")
            return False
        except Exception as e:
            print(f"❌ خطأ في جلب الصفحة: {e}")
            return False
            
    except ImportError:
        print("❌ لا يمكن استيراد MCPEDLExtractorFixed")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_image_filtering():
    """اختبار فلترة الصور"""
    print("\n🔍 اختبار فلترة الصور")
    print("=" * 40)
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # صور للاختبار
        test_images = [
            # صور يجب قبولها
            "https://media.forgecdn.net/attachments/1180/464/ezrtx-png.png",
            "https://mcpedl.com/wp-content/uploads/2024/dragon-mod.jpg",
            "https://r2.mcpedl.com/content/mod-screenshot.png",
            
            # صور يجب رفضها
            "https://mcpedl.com/_nuxt/img/shield.6982c20.png",
            "https://r2.mcpedl.com/users/3205066/avatar.png",
            "https://secure.gravatar.com/avatar/4d709253272132cf114cba539b5fa0b0",
            "data:image/svg+xml;base64,PHN2ZyB4bWxucz0i...",
        ]
        
        print("📋 اختبار فلترة الصور:")
        
        correct_accepts = 0
        correct_rejects = 0
        
        for i, img_url in enumerate(test_images, 1):
            is_valid = extractor.is_definitely_mod_image(img_url)
            
            # تحديد ما إذا كانت النتيجة صحيحة
            should_accept = i <= 3  # أول 3 صور يجب قبولها
            is_correct = (is_valid and should_accept) or (not is_valid and not should_accept)
            
            status = "✅" if is_correct else "❌"
            action = "قُبلت" if is_valid else "رُفضت"
            
            print(f"   {status} [{i}] {action}: {img_url[:60]}...")
            
            if is_correct:
                if should_accept:
                    correct_accepts += 1
                else:
                    correct_rejects += 1
        
        print(f"\n📊 نتائج الفلترة:")
        print(f"   ✅ قبول صحيح: {correct_accepts}/3")
        print(f"   ❌ رفض صحيح: {correct_rejects}/4")
        print(f"   🎯 دقة إجمالية: {(correct_accepts + correct_rejects)}/7")
        
        return (correct_accepts + correct_rejects) >= 6
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفلترة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار المستخرج المحسن لـ MCPEDL")
    print("=" * 80)
    
    # اختبار استخراج الصور
    extraction_success = test_image_extraction()
    
    # اختبار فلترة الصور
    filtering_success = test_image_filtering()
    
    # النتيجة النهائية
    print("\n" + "=" * 80)
    print("📊 النتائج النهائية:")
    print(f"   🖼️ استخراج الصور: {'✅ نجح' if extraction_success else '❌ فشل'}")
    print(f"   🔍 فلترة الصور: {'✅ نجح' if filtering_success else '❌ فشل'}")
    
    if extraction_success and filtering_success:
        print("\n🎉 جميع الاختبارات نجحت! المستخرج المحسن يعمل بشكل ممتاز.")
    elif extraction_success or filtering_success:
        print("\n⚠️ بعض الاختبارات نجحت. هناك مجال للتحسين.")
    else:
        print("\n❌ فشلت جميع الاختبارات. يحتاج المستخرج لمراجعة.")

if __name__ == "__main__":
    main()
