# -*- coding: utf-8 -*-
"""
معالج الصور المتقدم
يتعامل مع رفع الصور إلى Firebase وإدارتها
"""

import os
import io
import json
import time
import hashlib
import requests
from PIL import Image
from typing import List, Dict, Optional, Tuple, Any
from urllib.parse import urlparse

class ImageProcessor:
    """معالج الصور المتقدم"""
    
    def __init__(self, firebase_manager=None):
        """تهيئة معالج الصور"""
        self.firebase_manager = firebase_manager
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        self.max_image_size = 5 * 1024 * 1024  # 5MB
        self.compression_quality = 85
        self.processed_images = []
        self.duplicate_hashes = set()
        
    def is_image_url(self, url: str) -> bool:
        """فحص ما إذا كان الرابط يشير إلى صورة"""
        try:
            parsed = urlparse(url)
            path = parsed.path.lower()
            return any(path.endswith(ext) for ext in self.supported_formats)
        except:
            return False
    
    def download_image(self, url: str) -> Optional[bytes]:
        """تحميل صورة من رابط"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # فحص نوع المحتوى
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                print(f"⚠️ المحتوى ليس صورة: {content_type}")
                return None
                
            return response.content
        except Exception as e:
            print(f"❌ خطأ في تحميل الصورة من {url}: {e}")
            return None
    
    def get_image_hash(self, image_data: bytes) -> str:
        """حساب hash للصورة لاكتشاف التكرار"""
        return hashlib.md5(image_data).hexdigest()
    
    def is_duplicate_image(self, image_data: bytes) -> bool:
        """فحص ما إذا كانت الصورة مكررة"""
        image_hash = self.get_image_hash(image_data)
        if image_hash in self.duplicate_hashes:
            return True
        self.duplicate_hashes.add(image_hash)
        return False
    
    def compress_image(self, image_data: bytes, max_size: int = None) -> bytes:
        """ضغط الصورة"""
        try:
            if max_size is None:
                max_size = self.max_image_size
                
            # إذا كانت الصورة أصغر من الحد المطلوب، لا نحتاج لضغط
            if len(image_data) <= max_size:
                return image_data
            
            # فتح الصورة
            image = Image.open(io.BytesIO(image_data))
            
            # تحويل إلى RGB إذا كانت RGBA
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # ضغط الصورة
            output = io.BytesIO()
            quality = self.compression_quality
            
            while quality > 10:
                output.seek(0)
                output.truncate()
                image.save(output, format='JPEG', quality=quality, optimize=True)
                
                if output.tell() <= max_size:
                    break
                    
                quality -= 10
            
            return output.getvalue()
            
        except Exception as e:
            print(f"❌ خطأ في ضغط الصورة: {e}")
            return image_data
    
    def upload_image_to_firebase(self, image_data: bytes, filename: str) -> Optional[str]:
        """رفع صورة إلى Firebase Storage"""
        try:
            if not self.firebase_manager or not self.firebase_manager.is_initialized:
                print("❌ Firebase غير مهيأ")
                return None
            
            # ضغط الصورة قبل الرفع
            compressed_data = self.compress_image(image_data)
            
            # إنشاء اسم ملف فريد
            timestamp = int(time.time())
            unique_filename = f"images/{timestamp}_{filename}"
            
            # رفع الصورة
            blob = self.firebase_manager.storage_bucket.blob(unique_filename)
            blob.upload_from_string(compressed_data, content_type='image/jpeg')
            
            # جعل الصورة عامة
            blob.make_public()
            
            # إرجاع الرابط العام
            public_url = blob.public_url
            print(f"✅ تم رفع الصورة: {public_url}")
            
            return public_url
            
        except Exception as e:
            print(f"❌ خطأ في رفع الصورة إلى Firebase: {e}")
            return None
    
    def process_image_url(self, url: str) -> Optional[str]:
        """معالجة رابط صورة ورفعها إلى Firebase"""
        try:
            # تحميل الصورة
            image_data = self.download_image(url)
            if not image_data:
                return None
            
            # فحص التكرار
            if self.is_duplicate_image(image_data):
                print(f"⚠️ صورة مكررة تم تجاهلها: {url}")
                return None
            
            # استخراج اسم الملف
            parsed = urlparse(url)
            filename = os.path.basename(parsed.path) or f"image_{int(time.time())}.jpg"
            
            # رفع إلى Firebase
            firebase_url = self.upload_image_to_firebase(image_data, filename)
            
            if firebase_url:
                # حفظ معلومات الصورة
                image_info = {
                    'original_url': url,
                    'firebase_url': firebase_url,
                    'filename': filename,
                    'size': len(image_data),
                    'processed_at': time.time()
                }
                self.processed_images.append(image_info)
                
            return firebase_url
            
        except Exception as e:
            print(f"❌ خطأ في معالجة الصورة {url}: {e}")
            return None
    
    def process_multiple_images(self, image_urls: List[str]) -> List[str]:
        """معالجة عدة صور"""
        firebase_urls = []
        
        for url in image_urls:
            if self.is_image_url(url):
                firebase_url = self.process_image_url(url)
                if firebase_url:
                    firebase_urls.append(firebase_url)
            else:
                print(f"⚠️ رابط غير صالح للصورة: {url}")
        
        return firebase_urls
    
    def get_processed_images_info(self) -> List[Dict]:
        """الحصول على معلومات الصور المعالجة"""
        return self.processed_images.copy()
    
    def clear_processed_images(self):
        """مسح قائمة الصور المعالجة"""
        self.processed_images.clear()
        self.duplicate_hashes.clear()
    
    def save_processed_images_log(self, filename: str = "processed_images_log.json"):
        """حفظ سجل الصور المعالجة"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.processed_images, f, indent=2, ensure_ascii=False)
            print(f"✅ تم حفظ سجل الصور في {filename}")
        except Exception as e:
            print(f"❌ خطأ في حفظ سجل الصور: {e}")
