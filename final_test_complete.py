# -*- coding: utf-8 -*-
"""
اختبار شامل نهائي لجميع المكونات
Final comprehensive test for all components
"""

import os
import sys
import time

def test_imports():
    """اختبار استيراد جميع المكونات"""
    print("📦 اختبار استيراد المكونات...")
    
    try:
        # اختبار Firebase
        from firebase_config import firebase_manager
        print("✅ Firebase Config")
        
        # اختبار Firebase Storage Manager
        from firebase_storage_manager import create_firebase_manager
        print("✅ Firebase Storage Manager")
        
        # اختبار التطبيق الرئيسي
        from mod_processor_broken_final import FIREBASE_STORAGE_AVAILABLE, DATABASE_PROVIDER
        print("✅ التطبيق الرئيسي")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_firebase_storage():
    """اختبار Firebase Storage"""
    print("\n🔥 اختبار Firebase Storage...")
    
    try:
        from firebase_config import firebase_manager
        
        # تهيئة Firebase
        if not firebase_manager.auto_initialize():
            print("❌ فشل تهيئة Firebase")
            return False
        
        print("✅ تهيئة Firebase")
        
        # اختبار الاتصال
        if not firebase_manager.test_connection():
            print("❌ فشل اختبار الاتصال")
            return False
        
        print("✅ اختبار الاتصال")
        
        # اختبار رفع ملف
        test_content = b"Final test file content"
        public_url = firebase_manager.upload_file_to_storage(
            test_content,
            "final_test.txt",
            'text/plain',
            'final_test'
        )
        
        if public_url:
            print("✅ رفع ملف")
            
            # حذف الملف التجريبي
            firebase_manager.delete_file("final_test/final_test.txt")
            print("✅ حذف ملف")
            
            return True
        else:
            print("❌ فشل رفع الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Firebase Storage: {e}")
        return False

def test_database_config():
    """اختبار إعدادات قاعدة البيانات"""
    print("\n📊 اختبار إعدادات قاعدة البيانات...")
    
    try:
        from mod_processor_broken_final import DATABASE_PROVIDER

        if DATABASE_PROVIDER == "supabase":
            print("✅ قاعدة البيانات: Supabase")
        elif DATABASE_PROVIDER == "firebase":
            print("✅ قاعدة البيانات: Firebase")
        else:
            print(f"⚠️ قاعدة بيانات غير معروفة: {DATABASE_PROVIDER}")

        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعدادات قاعدة البيانات: {e}")
        return False

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("\n🚀 اختبار بدء تشغيل التطبيق...")
    
    try:
        # محاولة استيراد التطبيق الرئيسي
        import mod_processor_broken_final
        
        # فحص المتغيرات المهمة
        if hasattr(mod_processor_broken_final, 'FIREBASE_STORAGE_AVAILABLE'):
            firebase_available = mod_processor_broken_final.FIREBASE_STORAGE_AVAILABLE
            print(f"✅ Firebase Storage: {'متاح' if firebase_available else 'غير متاح'}")
        
        if hasattr(mod_processor_broken_final, 'DATABASE_PROVIDER'):
            db_provider = mod_processor_broken_final.DATABASE_PROVIDER
            print(f"✅ مزود قاعدة البيانات: {db_provider}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في بدء تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🧪 بدء الاختبار الشامل النهائي...")
    print("=" * 60)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("Firebase Storage", test_firebase_storage),
        ("إعدادات قاعدة البيانات", test_database_config),
        ("بدء تشغيل التطبيق", test_application_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار النهائي:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ Firebase Storage جاهز لرفع الملفات")
        print("✅ التطبيق جاهز للاستخدام")
        return True
    else:
        print(f"\n⚠️ فشل {total - passed} اختبار من أصل {total}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
