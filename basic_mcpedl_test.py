#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json
import re

def test_mcpedl_basic():
    """اختبار أساسي لاستخراج بيانات MCPEDL"""
    url = "https://mcpedl.com/classic-textures-fix/"
    
    print(f"🔍 اختبار استخراج بيانات من: {url}")
    print("=" * 60)
    
    try:
        # تحميل الصفحة
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        print("📥 تحميل الصفحة...")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print("✅ تم تحميل الصفحة بنجاح")
        
        # استخراج اسم المود
        print("\n📝 استخراج اسم المود:")
        title_element = soup.find('h1', class_='entry-title') or soup.find('h1') or soup.find('title')
        mod_name = title_element.get_text(strip=True) if title_element else "غير محدد"
        print(f"   اسم المود: '{mod_name}'")
        
        # استخراج اسم المطور
        print("\n👤 استخراج اسم المطور:")
        # البحث في عدة أماكن محتملة
        creator_patterns = [
            r'by\s+(\w+)',
            r'author:\s*(\w+)',
            r'creator:\s*(\w+)',
            r'/users/\d+/(\w+)'
        ]
        
        creator_name = "غير محدد"
        page_text = soup.get_text()
        
        for pattern in creator_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                creator_name = match.group(1)
                print(f"   اسم المطور: '{creator_name}' (من pattern: {pattern})")
                break
        
        if creator_name == "غير محدد":
            print("   ❌ لم يتم العثور على اسم المطور")
        
        # استخراج الوصف
        print("\n📄 استخراج الوصف:")
        content_div = soup.find('div', class_='entry-content') or soup.find('article')
        if content_div:
            description = content_div.get_text(strip=True)
            print(f"   طول الوصف: {len(description)} حرف")
            print(f"   أول 200 حرف: '{description[:200]}...'")
        else:
            description = "غير محدد"
            print("   ❌ لم يتم العثور على الوصف")
        
        # استخراج الصور
        print("\n🖼️ استخراج الصور:")
        images = soup.find_all('img')
        image_urls = []
        
        for img in images:
            src = img.get('src') or img.get('data-src')
            if src:
                if src.startswith('/'):
                    src = 'https://mcpedl.com' + src
                if src.startswith('http'):
                    image_urls.append(src)
        
        # إزالة التكرارات
        unique_images = list(dict.fromkeys(image_urls))
        print(f"   تم العثور على {len(unique_images)} صورة:")
        
        for i, img in enumerate(unique_images[:5], 1):
            print(f"   {i}. {img}")
        
        if len(unique_images) > 5:
            print(f"   ... و {len(unique_images) - 5} صورة أخرى")
        
        # استخراج روابط التواصل الاجتماعي
        print("\n🌐 استخراج روابط التواصل:")
        social_domains = [
            'youtube.com', 'youtu.be', 'discord.gg', 'discord.com',
            'twitter.com', 'x.com', 'github.com', 'facebook.com',
            'instagram.com', 'tiktok.com', 'twitch.tv', 'patreon.com'
        ]
        
        all_links = soup.find_all('a', href=True)
        social_links = []
        
        for link in all_links:
            href = link['href']
            if any(domain in href for domain in social_domains):
                social_links.append(href)
        
        unique_social = list(dict.fromkeys(social_links))
        print(f"   تم العثور على {len(unique_social)} رابط تواصل:")
        
        for i, link in enumerate(unique_social, 1):
            print(f"   {i}. {link}")
        
        # تحليل المشاكل
        print("\n🔍 تحليل المشاكل المحتملة:")
        print("-" * 40)
        
        issues = []
        
        if mod_name == "غير محدد" or not mod_name:
            issues.append("❌ اسم المود غير محدد")
        
        if creator_name == "غير محدد":
            issues.append("❌ اسم المطور غير محدد")
        
        if description == "غير محدد" or len(description) < 50:
            issues.append("❌ الوصف غير كافي")
        
        if len(unique_images) == 0:
            issues.append("❌ لا توجد صور")
        
        if len(unique_social) == 0:
            issues.append("❌ لا توجد روابط تواصل اجتماعي")
        
        if issues:
            for issue in issues:
                print(f"   {issue}")
        else:
            print("   ✅ لا توجد مشاكل واضحة")
        
        # ملخص النتائج
        print(f"\n📊 ملخص النتائج:")
        print("=" * 60)
        
        results = {
            'url': url,
            'mod_name': mod_name,
            'creator_name': creator_name,
            'description_length': len(description) if description != "غير محدد" else 0,
            'images_count': len(unique_images),
            'social_links_count': len(unique_social),
            'issues': issues,
            'success': len(issues) == 0
        }
        
        for key, value in results.items():
            if key != 'issues':
                print(f"   {key}: {value}")
        
        # حفظ النتائج
        with open('basic_test_results.json', 'w', encoding='utf-8') as f:
            json.dump({
                'results': results,
                'mod_name': mod_name,
                'creator_name': creator_name,
                'description': description[:500] if description != "غير محدد" else None,
                'images': unique_images,
                'social_links': unique_social
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ النتائج في basic_test_results.json")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار استخراج MCPEDL الأساسي")
    
    success = test_mcpedl_basic()
    
    print(f"\n🏁 النتيجة النهائية: {'نجح' if success else 'فشل'}")
    
    if not success:
        print("\n💡 تحقق من الملف basic_test_results.json لمراجعة التفاصيل")
        print("💡 قد تحتاج إلى تحسين محددات CSS أو منطق الاستخراج")
