{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:vindicator", "min_engine_version": "1.8.0", "materials": {"default": "spider"}, "textures": {"default": "textures/entity/vindicator"}, "geometry": {"default": "geometry.vindicator.v1.8"}, "spawn_egg": {"texture": "spawn_egg", "texture_index": 39}, "scripts": {"scale": "0.9375", "animate": ["vindicator_base", "vindicator_walk", "controller_look_at_target", "controller_vindicator_base", "controller_riding"]}, "animations": {"look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "riding.arms": "animation.vindicator.riding.arms", "riding.legs": "animation.vindicator.riding.legs", "vindicator_base": "animation.vindicator.base", "vindicator_attack": "animation.vindicator.attack", "vindicator_hand_attack": "animation.vindicator.hand_attack", "vindicator_walk": "animation.vindicator.walk", "celebrating": "animation.humanoid.celebrating", "controller_look_at_target": "controller.animation.humanoid.look_at_target", "controller_vindicator_base": "controller.animation.vindicator.base", "controller_riding": "controller.animation.humanoid.riding"}, "render_controllers": ["controller.render.vindicator"], "enable_attachables": true, "hide_armor": true}}}