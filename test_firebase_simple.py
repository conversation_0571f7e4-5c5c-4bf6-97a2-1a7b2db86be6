# -*- coding: utf-8 -*-
"""
اختبار Firebase Storage المبسط
Simple Firebase Storage Test
"""

import os
import json
from firebase_web_storage import create_firebase_web_storage

def test_simple_firebase():
    """اختبار Firebase Storage المبسط"""
    print("🧪 اختبار Firebase Storage المبسط")
    print("=" * 50)
    
    # فحص ملف الإعدادات
    config_file = "firebase_config.json"
    if not os.path.exists(config_file):
        print(f"❌ ملف الإعدادات غير موجود: {config_file}")
        print("💡 سيتم إنشاء ملف إعدادات تجريبي...")
        
        # إنشاء ملف إعدادات تجريبي
        sample_config = {
            "project_id": "download-e33a2",
            "storage_bucket": "download-e33a2.firebasestorage.app",
            "apiKey": "AIzaSyB3t2Ae-24DWUQJxwZ5LCFZVZov0ncaC8c",
            "authDomain": "download-e33a2.firebaseapp.com",
            "messagingSenderId": "258780355379",
            "appId": "1:258780355379:web:df601f640fb82d9c42bc46"
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            print(f"✅ تم إنشاء ملف الإعدادات: {config_file}")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف الإعدادات: {e}")
            return False
    
    # إنشاء مدير Firebase
    print("\n🔄 إنشاء مدير Firebase...")
    storage = create_firebase_web_storage()
    
    if not storage:
        print("❌ فشل في إنشاء مدير Firebase")
        return False
    
    if not storage.is_initialized:
        print("❌ Firebase غير مهيأ")
        return False
    
    print("✅ تم إنشاء مدير Firebase بنجاح")
    
    # فحص الاتصال
    print("\n🔍 فحص الاتصال...")
    status = storage.check_connection()
    
    print(f"📊 نتائج الفحص:")
    print(f"   - متصل: {'✅' if status['connected'] else '❌'}")
    print(f"   - يمكن الوصول للـ bucket: {'✅' if status['bucket_accessible'] else '❌'}")
    print(f"   - يمكن الرفع: {'✅' if status['can_upload'] else '❌'}")
    print(f"   - يمكن التحميل: {'✅' if status['can_download'] else '❌'}")
    print(f"   - اسم الـ bucket: {status.get('bucket_name', 'غير محدد')}")
    
    if status['error']:
        print(f"   - خطأ: {status['error']}")
    
    # اختبار قائمة الملفات
    if status['connected']:
        print("\n📋 اختبار قائمة الملفات...")
        try:
            mods = storage.list_uploaded_mods(5)
            print(f"📊 تم العثور على {len(mods)} ملف:")
            
            for i, mod in enumerate(mods, 1):
                name = mod['name'].split('/')[-1] if '/' in mod['name'] else mod['name']
                size_mb = mod.get('size', 0) / (1024 * 1024)
                print(f"   {i}. {name} ({size_mb:.2f} MB)")
                
        except Exception as e:
            print(f"❌ خطأ في جلب قائمة الملفات: {e}")
    
    # اختبار الإحصائيات
    if status['connected']:
        print("\n📊 اختبار الإحصائيات...")
        try:
            stats = storage.get_storage_stats()
            if stats:
                print(f"📈 إحصائيات التخزين:")
                print(f"   - إجمالي الملفات: {stats.get('total_files', 0)}")
                print(f"   - ملفات المودات: {stats.get('mod_files', 0)}")
                print(f"   - إجمالي الحجم: {stats.get('total_size', 0) / (1024*1024):.2f} MB")
                print(f"   - حجم المودات: {stats.get('mod_size', 0) / (1024*1024):.2f} MB")
            else:
                print("⚠️ لم يتم الحصول على إحصائيات")
        except Exception as e:
            print(f"❌ خطأ في جلب الإحصائيات: {e}")
    
    # اختبار رفع ملف صغير (اختياري)
    if status['can_upload']:
        print("\n📤 اختبار رفع ملف صغير...")
        test_url = "https://httpbin.org/bytes/1024"  # ملف 1KB للاختبار
        
        try:
            upload_info = storage.upload_mod_file(test_url, "Test File", "test")
            
            if upload_info:
                print("✅ تم رفع الملف التجريبي بنجاح!")
                print(f"   - الرابط: {upload_info['firebase_url']}")
                print(f"   - الحجم: {upload_info['file_size']} بايت")
                
                # حذف الملف التجريبي
                print("🗑️ حذف الملف التجريبي...")
                if hasattr(storage, '_delete_file'):
                    deleted = storage._delete_file(upload_info['file_path'])
                    if deleted:
                        print("✅ تم حذف الملف التجريبي")
                    else:
                        print("⚠️ لم يتم حذف الملف التجريبي")
            else:
                print("❌ فشل رفع الملف التجريبي")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الرفع: {e}")
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    if storage and storage.is_initialized:
        success_count += 1
        print("✅ تهيئة Firebase: نجح")
    else:
        print("❌ تهيئة Firebase: فشل")
    
    if status['connected']:
        success_count += 1
        print("✅ اختبار الاتصال: نجح")
    else:
        print("❌ اختبار الاتصال: فشل")
    
    if status['bucket_accessible']:
        success_count += 1
        print("✅ الوصول للـ bucket: نجح")
    else:
        print("❌ الوصول للـ bucket: فشل")
    
    if status['can_upload'] and status['can_download']:
        success_count += 1
        print("✅ عمليات الرفع والتحميل: متاحة")
    else:
        print("❌ عمليات الرفع والتحميل: غير متاحة")
    
    print(f"\n📈 النتيجة النهائية: {success_count}/{total_tests} اختبارات نجحت")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت! Firebase Storage جاهز للاستخدام.")
        return True
    elif success_count >= 2:
        print("⚠️ بعض الاختبارات نجحت. Firebase Storage يعمل جزئياً.")
        return True
    else:
        print("❌ معظم الاختبارات فشلت. راجع الإعدادات والاتصال.")
        return False

def show_troubleshooting_tips():
    """عرض نصائح حل المشاكل"""
    print("\n🔧 نصائح حل المشاكل:")
    print("-" * 30)
    print("1. تأكد من صحة معرف المشروع في firebase_config.json")
    print("2. تأكد من تفعيل Firebase Storage في مشروعك")
    print("3. راجع قواعد الأمان في Firebase Console")
    print("4. تأكد من اتصالك بالإنترنت")
    print("5. راجع دليل FIREBASE_STORAGE_GUIDE.md للتفاصيل")
    
    print("\n🌐 روابط مفيدة:")
    print("- Firebase Console: https://console.firebase.google.com/")
    print("- Firebase Storage Rules: https://firebase.google.com/docs/storage/security")
    print("- Firebase Web API: https://firebase.google.com/docs/reference/rest/storage")

if __name__ == "__main__":
    print("🚀 بدء اختبار Firebase Storage المبسط")
    print("=" * 60)
    
    success = test_simple_firebase()
    
    if not success:
        show_troubleshooting_tips()
    
    print("\n🏁 انتهى الاختبار!")
