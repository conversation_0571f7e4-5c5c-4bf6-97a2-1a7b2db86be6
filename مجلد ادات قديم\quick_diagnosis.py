# -*- coding: utf-8 -*-
"""
تشخيص سريع لمشاكل MCPEDL scraper
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print("📦 فحص التبعيات...")

    dependencies = {
        'requests': 'pip install requests',
        'bs4': 'pip install beautifulsoup4',  # beautifulsoup4 يُستورد كـ bs4
        'cloudscraper': 'pip install cloudscraper',
        'selenium': 'pip install selenium',
        'lxml': 'pip install lxml'
    }

    missing = []

    for dep, install_cmd in dependencies.items():
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - {install_cmd}")
            missing.append(dep)

    return missing

def test_simple_request():
    """اختبار طلب بسيط"""
    print("\n🌐 اختبار طلب بسيط...")

    try:
        import requests

        # اختبار Google أولاً
        response = requests.get("https://www.google.com", timeout=10)
        if response.status_code == 200:
            print("✅ الاتصال بالإنترنت يعمل")
        else:
            print(f"⚠️ مشكلة في الاتصال: {response.status_code}")
            return False

        # اختبار MCPEDL
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        response = requests.get("https://mcpedl.com", headers=headers, timeout=15)
        print(f"MCPEDL رمز الاستجابة: {response.status_code}")
        print(f"حجم المحتوى: {len(response.text)} حرف")

        if response.status_code == 200 and len(response.text) > 1000:
            print("✅ يمكن الوصول إلى MCPEDL")
            return True
        else:
            print("❌ مشكلة في الوصول إلى MCPEDL")
            return False

    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_cloudscraper_simple():
    """اختبار cloudscraper بسيط"""
    print("\n☁️ اختبار cloudscraper...")

    try:
        import cloudscraper

        scraper = cloudscraper.create_scraper()
        response = scraper.get("https://mcpedl.com", timeout=20)

        print(f"رمز الاستجابة: {response.status_code}")
        print(f"حجم المحتوى: {len(response.text)} حرف")

        if response.status_code == 200 and len(response.text) > 1000:
            print("✅ cloudscraper يعمل")
            return True
        else:
            print("❌ cloudscraper لا يعمل بشكل صحيح")
            return False

    except ImportError:
        print("❌ cloudscraper غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في cloudscraper: {e}")
        return False

def test_specific_url():
    """اختبار رابط محدد"""
    print("\n🎯 اختبار رابط محدد...")

    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

    try:
        import cloudscraper
        from bs4 import BeautifulSoup

        scraper = cloudscraper.create_scraper()
        response = scraper.get(test_url, timeout=30)

        print(f"رمز الاستجابة: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'غير محدد')}")
        print(f"حجم المحتوى: {len(response.text)} حرف")

        if response.status_code == 200:
            # محاولة تحليل HTML
            try:
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')

                if title:
                    print(f"✅ العنوان: {title.get_text()[:100]}...")

                    # حفظ للفحص
                    with open('debug_specific_url.html', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print("💾 تم حفظ المحتوى في debug_specific_url.html")

                    return True
                else:
                    print("❌ لا يوجد عنوان في الصفحة")
                    return False

            except Exception as parse_error:
                print(f"❌ خطأ في تحليل HTML: {parse_error}")

                # حفظ المحتوى الخام للفحص
                with open('debug_raw_content.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("💾 تم حفظ المحتوى الخام في debug_raw_content.html")

                return False
        else:
            print(f"❌ رمز استجابة غير صحيح: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def analyze_html_content():
    """تحليل محتوى HTML المحفوظ"""
    print("\n🔍 تحليل محتوى HTML...")

    files_to_check = [
        'debug_specific_url.html',
        'debug_raw_content.html',
        'debug_mcpedl_page.html'
    ]

    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"\n📄 فحص {filename}:")

            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()

                print(f"حجم الملف: {len(content)} حرف")

                # فحص المحتوى
                content_lower = content.lower()

                if 'cloudflare' in content_lower:
                    print("⚠️ يحتوي على Cloudflare")

                if 'access denied' in content_lower:
                    print("⚠️ يحتوي على Access Denied")

                if '<title>' in content_lower:
                    # استخراج العنوان
                    start = content_lower.find('<title>') + 7
                    end = content_lower.find('</title>', start)
                    if end > start:
                        title = content[start:end].strip()
                        print(f"العنوان: {title[:100]}...")

                if 'dragon' in content_lower and 'mount' in content_lower:
                    print("✅ يحتوي على محتوى المود المطلوب")

            except Exception as e:
                print(f"❌ خطأ في قراءة {filename}: {e}")

def provide_recommendations():
    """تقديم توصيات للحل"""
    print("\n💡 التوصيات:")
    print("1. تأكد من تثبيت جميع التبعيات المطلوبة")
    print("2. جرب تشغيل الاختبارات من terminal منفصل")
    print("3. تحقق من ملفات HTML المحفوظة للتشخيص")
    print("4. إذا كان cloudscraper يعمل، فالمشكلة في تحليل HTML")
    print("5. إذا كان cloudscraper لا يعمل، جرب VPN أو proxy")

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص سريع لمشاكل MCPEDL scraper")
    print("=" * 50)

    # فحص التبعيات
    missing_deps = check_dependencies()

    if missing_deps:
        print(f"\n⚠️ تبعيات مفقودة: {', '.join(missing_deps)}")
        print("يرجى تثبيت التبعيات المفقودة أولاً")
        return

    # اختبار الطلبات
    simple_works = test_simple_request()
    cloudscraper_works = test_cloudscraper_simple()
    specific_works = test_specific_url()

    # تحليل المحتوى
    analyze_html_content()

    # ملخص النتائج
    print("\n📊 ملخص النتائج:")
    print(f"طلب بسيط: {'✅' if simple_works else '❌'}")
    print(f"cloudscraper: {'✅' if cloudscraper_works else '❌'}")
    print(f"رابط محدد: {'✅' if specific_works else '❌'}")

    # توصيات
    provide_recommendations()

    if all([simple_works, cloudscraper_works, specific_works]):
        print("\n🎉 جميع الاختبارات نجحت! المشكلة قد تكون في كود الاستخراج")
    else:
        print("\n⚠️ هناك مشاكل في الاتصال أو cloudscraper")

if __name__ == "__main__":
    main()
