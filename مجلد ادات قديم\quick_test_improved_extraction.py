# -*- coding: utf-8 -*-
"""
اختبار سريع للتحسينات الجديدة في استخراج الصور من MCPEDL
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """اختبار سريع للتحسينات"""
    print("🚀 اختبار سريع للتحسينات الجديدة")
    print("=" * 50)
    
    # اختبار استيراد الوحدات
    print("📦 اختبار استيراد الوحدات...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        print("✅ تم استيراد MCPEDLExtractorFixed")
    except ImportError as e:
        print(f"❌ فشل استيراد MCPEDLExtractorFixed: {e}")
        return False
    
    try:
        from mcpedl_image_filter_enhanced import MCPEDLImageFilter
        print("✅ تم استيراد MCPEDLImageFilter")
        filter_available = True
    except ImportError as e:
        print(f"⚠️ فشل استيراد MCPEDLImageFilter: {e}")
        filter_available = False
    
    # اختبار إنشاء المستخرج
    print("\n🔧 اختبار إنشاء المستخرج...")
    try:
        extractor = MCPEDLExtractorFixed()
        print("✅ تم إنشاء المستخرج بنجاح")
    except Exception as e:
        print(f"❌ فشل إنشاء المستخرج: {e}")
        return False
    
    # اختبار فلترة الصور
    print("\n🔍 اختبار فلترة الصور...")
    
    test_images = [
        "https://media.forgecdn.net/attachments/1180/464/dragon-mod.png",  # يجب قبولها
        "https://mcpedl.com/_nuxt/img/shield.6982c20.png",  # يجب رفضها
        "https://r2.mcpedl.com/users/123/avatar.png",  # يجب رفضها
        "https://mcpedl.com/wp-content/uploads/2024/texture-pack.jpg",  # يجب قبولها
    ]
    
    correct_results = 0
    
    for i, img_url in enumerate(test_images):
        should_accept = i in [0, 3]  # الصورة الأولى والرابعة يجب قبولهما
        
        # اختبار is_valid_mod_image
        is_valid = extractor.is_valid_mod_image(img_url)
        
        # اختبار is_definitely_mod_image
        is_definitely = extractor.is_definitely_mod_image(img_url)
        
        result_valid = "✅" if is_valid == should_accept else "❌"
        result_definitely = "✅" if is_definitely == should_accept else "❌"
        
        print(f"   {result_valid} is_valid_mod_image: {img_url[:50]}...")
        print(f"   {result_definitely} is_definitely_mod_image: {img_url[:50]}...")
        
        if is_definitely == should_accept:
            correct_results += 1
    
    print(f"\n📊 نتائج فلترة الصور: {correct_results}/4 صحيحة")
    
    # اختبار HTML تجريبي
    print("\n🌐 اختبار HTML تجريبي...")
    
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta property="og:image" content="https://media.forgecdn.net/attachments/1180/464/main-image.png">
        <title>Dragon Mounts Mod | MCPEDL</title>
    </head>
    <body>
        <article>
            <h1 class="post-page__title">Dragon Mounts: Community Edition</h1>
            <div class="post-page__content">
                <p>This amazing mod adds dragons to your Minecraft world!</p>
                <img src="https://media.forgecdn.net/attachments/1113/871/dragon-screenshot1.jpg" alt="Dragon Screenshot 1">
                <img src="https://media.forgecdn.net/attachments/1113/870/dragon-screenshot2.jpg" alt="Dragon Screenshot 2">
                <img src="https://mcpedl.com/wp-content/uploads/2024/dragon-feature.png" alt="Dragon Feature">
            </div>
        </article>
        
        <div class="related-posts">
            <h3>You may also like</h3>
            <img src="https://mcpedl.com/wp-content/uploads/2024/other-mod.jpg" alt="Other Mod">
        </div>
        
        <div class="comments">
            <img src="https://r2.mcpedl.com/users/123/avatar.png" alt="User Avatar">
        </div>
        
        <img src="https://mcpedl.com/_nuxt/img/shield.6982c20.png" alt="Shield">
    </body>
    </html>
    """
    
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(test_html, 'html.parser')
        
        # اختبار استخراج البيانات
        mod_data = extractor.extract_mod_data(test_html, "https://mcpedl.com/test/", generate_ai_descriptions=False)
        
        if mod_data:
            print("✅ تم استخراج البيانات بنجاح")
            print(f"   📝 الاسم: {mod_data.get('name', 'غير محدد')}")
            print(f"   🖼️ عدد الصور: {len(mod_data.get('image_urls', []))}")
            
            images = mod_data.get('image_urls', [])
            if images:
                print("   📋 الصور المستخرجة:")
                for i, img in enumerate(images, 1):
                    print(f"      [{i}] {img}")
                
                # تحليل جودة الصور
                forgecdn_count = sum(1 for img in images if 'media.forgecdn.net' in img)
                unwanted_count = sum(1 for img in images if any(bad in img.lower() for bad in ['shield.png', '/users/', '_nuxt']))
                
                print(f"   📊 تحليل الجودة:")
                print(f"      🔥 صور forgecdn: {forgecdn_count}")
                print(f"      ❌ صور غير مرغوبة: {unwanted_count}")
                
                if unwanted_count == 0 and forgecdn_count >= 2:
                    print("   🎉 ممتاز! تم تجنب الصور غير المرغوبة واستخراج صور عالية الجودة")
                    quality_good = True
                elif unwanted_count <= 1:
                    print("   👍 جيد! معظم الصور صالحة")
                    quality_good = True
                else:
                    print("   ⚠️ يحتاج تحسين! تم استخراج صور غير مرغوبة")
                    quality_good = False
            else:
                print("   ❌ لم يتم استخراج أي صور")
                quality_good = False
        else:
            print("❌ فشل في استخراج البيانات")
            quality_good = False
            
    except ImportError:
        print("❌ BeautifulSoup غير متوفر")
        quality_good = False
    except Exception as e:
        print(f"❌ خطأ في اختبار HTML: {e}")
        quality_good = False
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   📦 استيراد الوحدات: {'✅' if filter_available else '⚠️'}")
    print(f"   🔍 فلترة الصور: {'✅' if correct_results >= 3 else '❌'}")
    print(f"   🌐 اختبار HTML: {'✅' if quality_good else '❌'}")
    
    overall_success = correct_results >= 3 and quality_good
    
    if overall_success:
        print("\n🎉 التحسينات تعمل بشكل ممتاز!")
        print("💡 يمكنك الآن استخدام المستخرج المحسن لاستخراج صور أفضل من MCPEDL")
    else:
        print("\n⚠️ هناك مجال للتحسين")
        print("💡 راجع الأخطاء أعلاه وتأكد من تثبيت جميع المتطلبات")
    
    return overall_success

def test_specific_url():
    """اختبار رابط محدد"""
    print("\n🔗 اختبار رابط محدد...")
    
    test_url = input("أدخل رابط MCPEDL للاختبار (أو اضغط Enter للتخطي): ").strip()
    
    if not test_url:
        print("تم تخطي اختبار الرابط المحدد")
        return
    
    try:
        import cloudscraper
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        print(f"🌐 جلب الصفحة: {test_url}")
        
        scraper = cloudscraper.create_scraper()
        response = scraper.get(test_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ تم جلب الصفحة بنجاح")
            
            extractor = MCPEDLExtractorFixed()
            mod_data = extractor.extract_mod_data(response.text, test_url, generate_ai_descriptions=False)
            
            if mod_data:
                print("✅ تم استخراج البيانات")
                print(f"   📝 الاسم: {mod_data.get('name', 'غير محدد')}")
                print(f"   🖼️ عدد الصور: {len(mod_data.get('image_urls', []))}")
                
                images = mod_data.get('image_urls', [])[:5]  # أول 5 صور
                if images:
                    print("   📋 أول 5 صور:")
                    for i, img in enumerate(images, 1):
                        print(f"      [{i}] {img}")
            else:
                print("❌ فشل في استخراج البيانات")
        else:
            print(f"❌ فشل في جلب الصفحة: {response.status_code}")
            
    except ImportError:
        print("❌ cloudscraper غير متوفر. تثبيت: pip install cloudscraper")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    success = quick_test()
    
    if success:
        test_specific_url()

if __name__ == "__main__":
    main()
