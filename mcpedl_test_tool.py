#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json

def test_mcpedl_extraction():
    """اختبار استخراج بيانات MCPEDL"""
    url = "https://mcpedl.com/classic-textures-fix/"
    
    print(f"🔍 اختبار استخراج بيانات من: {url}")
    print("=" * 60)
    
    try:
        # تحميل الصفحة
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        print("📥 تحميل الصفحة...")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print("✅ تم تحميل الصفحة بنجاح")
        
        # استخراج البيانات الأساسية
        print("\n📊 استخراج البيانات الأساسية:")
        print("-" * 40)
        
        # اسم المود
        title_selectors = [
            'h1.entry-title',
            'h1',
            '.entry-title',
            'title'
        ]
        
        mod_name = None
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                mod_name = element.get_text(strip=True)
                print(f"📝 اسم المود: '{mod_name}' (من {selector})")
                break
        
        if not mod_name:
            print("❌ لم يتم العثور على اسم المود")
        
        # اسم المطور
        creator_selectors = [
            '.entry-meta .author',
            '.author',
            '[rel="author"]',
            '.post-author'
        ]
        
        creator_name = None
        for selector in creator_selectors:
            element = soup.select_one(selector)
            if element:
                creator_name = element.get_text(strip=True)
                print(f"👤 اسم المطور: '{creator_name}' (من {selector})")
                break
        
        if not creator_name:
            print("❌ لم يتم العثور على اسم المطور")
        
        # الوصف
        desc_selectors = [
            '.entry-content',
            '.post-content',
            '.content',
            'article'
        ]
        
        description = None
        for selector in desc_selectors:
            element = soup.select_one(selector)
            if element:
                description = element.get_text(strip=True)
                print(f"📄 الوصف: {len(description)} حرف (من {selector})")
                print(f"   أول 200 حرف: '{description[:200]}...'")
                break
        
        if not description:
            print("❌ لم يتم العثور على الوصف")
        
        # الصور
        print(f"\n🖼️ استخراج الصور:")
        print("-" * 40)
        
        img_selectors = [
            '.entry-content img',
            '.post-content img', 
            'article img',
            'img'
        ]
        
        all_images = []
        for selector in img_selectors:
            images = soup.select(selector)
            for img in images:
                src = img.get('src') or img.get('data-src')
                if src:
                    if src.startswith('/'):
                        src = 'https://mcpedl.com' + src
                    all_images.append(src)
        
        # إزالة التكرارات
        unique_images = list(dict.fromkeys(all_images))
        
        print(f"📋 تم العثور على {len(unique_images)} صورة فريدة:")
        for i, img in enumerate(unique_images[:10], 1):
            print(f"   {i}. {img}")
        
        if len(unique_images) > 10:
            print(f"   ... و {len(unique_images) - 10} صورة أخرى")
        
        # روابط التواصل الاجتماعي
        print(f"\n🌐 استخراج روابط التواصل:")
        print("-" * 40)
        
        social_domains = [
            'youtube.com', 'youtu.be',
            'discord.gg', 'discord.com',
            'twitter.com', 'x.com',
            'github.com',
            'facebook.com',
            'instagram.com',
            'tiktok.com',
            'twitch.tv',
            'patreon.com'
        ]
        
        all_links = soup.find_all('a', href=True)
        social_links = []
        
        for link in all_links:
            href = link['href']
            if any(domain in href for domain in social_domains):
                social_links.append(href)
        
        # إزالة التكرارات
        unique_social = list(dict.fromkeys(social_links))
        
        print(f"📋 تم العثور على {len(unique_social)} رابط تواصل:")
        for i, link in enumerate(unique_social, 1):
            print(f"   {i}. {link}")
        
        # ملخص النتائج
        print(f"\n📊 ملخص النتائج:")
        print("=" * 60)
        print(f"✅ اسم المود: {'✓' if mod_name else '✗'}")
        print(f"✅ اسم المطور: {'✓' if creator_name else '✗'}")
        print(f"✅ الوصف: {'✓' if description else '✗'}")
        print(f"✅ الصور: {len(unique_images)} صورة")
        print(f"✅ روابط التواصل: {len(unique_social)} رابط")
        
        # حفظ النتائج في ملف
        results = {
            'url': url,
            'mod_name': mod_name,
            'creator_name': creator_name,
            'description': description[:500] if description else None,
            'images': unique_images,
            'social_links': unique_social
        }
        
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ النتائج في test_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_extractor():
    """اختبار المستخرج المحسن"""
    print(f"\n🔧 اختبار المستخرج المحسن:")
    print("=" * 60)
    
    try:
        # تحميل مفتاح Gemini
        try:
            with open("api_keys.json", 'r') as f:
                data = json.load(f)
            
            key = data.get("GEMINI_API_KEY")
            if not key:
                keys = data.get("gemini_api_keys", [])
                if keys:
                    key = keys[0]
            
            print(f"🔑 مفتاح Gemini: {key[:12]}...{key[-8:] if key else 'غير متوفر'}")
        except:
            key = None
            print("⚠️ لا يمكن تحميل مفتاح Gemini")
        
        # اختبار المستخرج المحسن
        from enhanced_mcpedl_extractor import extract_mcpedl_mod_enhanced
        
        url = "https://mcpedl.com/classic-textures-fix/"
        print(f"🔗 اختبار الرابط: {url}")
        
        result = extract_mcpedl_mod_enhanced(url, key)
        
        if result:
            print("✅ المستخرج المحسن نجح!")
            print(f"📝 اسم المود: '{result.get('name', 'غير محدد')}'")
            print(f"👤 اسم المطور: '{result.get('creator_name', 'غير محدد')}'")
            print(f"📂 الفئة: '{result.get('category', 'غير محدد')}'")
            print(f"📄 طول الوصف: {len(result.get('description', ''))}")
            print(f"📄 طول الوصف العربي: {len(result.get('description_arabic', ''))}")
            print(f"📱 طول تليجرام EN: {len(result.get('telegram_description_en', ''))}")
            print(f"📱 طول تليجرام AR: {len(result.get('telegram_description_ar', ''))}")
            print(f"🖼️ عدد الصور: {len(result.get('image_urls', []))}")
            print(f"🌐 روابط التواصل: {len(result.get('creator_social_channels', []))}")
            
            # حفظ النتائج المحسنة
            with open('enhanced_results.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ النتائج المحسنة في enhanced_results.json")
            return True
        else:
            print("❌ المستخرج المحسن فشل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المستخرج المحسن: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار استخراج MCPEDL")
    print("=" * 60)
    
    # اختبار الاستخراج الأساسي
    basic_success = test_mcpedl_extraction()
    
    # اختبار المستخرج المحسن
    enhanced_success = test_enhanced_extractor()
    
    print(f"\n📊 النتائج النهائية:")
    print("=" * 60)
    print(f"✅ الاستخراج الأساسي: {'نجح' if basic_success else 'فشل'}")
    print(f"✅ المستخرج المحسن: {'نجح' if enhanced_success else 'فشل'}")
    
    if basic_success and enhanced_success:
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
    
    print("\n💡 تحقق من الملفات المحفوظة لمراجعة النتائج")
