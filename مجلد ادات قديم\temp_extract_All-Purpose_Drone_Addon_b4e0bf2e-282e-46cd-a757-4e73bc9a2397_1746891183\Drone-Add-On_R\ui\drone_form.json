{
  "namespace": "drone_form",

  "drone_custom_form": {
    "type": "panel",
    "size": ["100%", "100%"],
    "controls": [
      {
        "settings@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.drone_custom_form_setts",
          "$close_button": false,
          "layer": 2,
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§g') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      }
    ]
  },

  "drone_long_form": {
    "type": "panel",
    "size": ["100%", "100%"],
    "controls": [
      {
        "main_menu@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.drone_main_menu_panel",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§m') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "antenna_menu@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.antenna_main_menu_panel",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§a') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "tasks@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.drone_tasks_panel",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§t') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "current_task@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.current_task_panel",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§c') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "remote_control_main@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.remote_control_main",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§r') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "drone_list@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.drones_scrolling",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§h') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      },
      {
        "swarms_main@common_dialogs.drone_main_panel": {
          "$child_control": "drone_form.swarms_panel",
          "bindings": [
            { "binding_name": "#title_text" },
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§s') = #title_text))",
              "target_property_name": "#visible"
            }
          ]
        }
      }
    ]
  },

  "current_task_panel": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "anchor_from": "center",
    "anchor_to": "center",
    "controls": [
      {
        "label_offset_panel": {
          "type": "panel",
          "anchor_from": "center",
          "anchor_to": "center",
          "size": ["100%", "100%c"],
          "controls": [
            {
              "main_label": {
                "type": "label",
                "offset": [ 2, -15 ],
                "layer": 10,
                "color": "$main_header_text_color",
                "size": ["100%", "default"],
                "text": "#form_text"
              }
            }
          ]
        }
      },
      {
        "panel_1": {
          "type": "stack_panel",
          "orientation": "vertical",
          "collection_name": "form_buttons",
          "controls": [
            {
              "button0@drone_form.drone_dynamic_button": {
                "$set_size": [ "90%", 64 ],
                "$icon_size": [ 48, 48 ],
                "$pos": [ 13, -12 ],
                "$pos_icon": [ -72, -12 ],
                "$postext": [ 0, 0 ],
                "collection_index": 0
              }
            },
            {
              "button1@drone_form.drone_dynamic_button": {
                "$set_size": [ "90%", 32 ],
                "$icon_size": [ 0, 0 ],
                "$pos": [ 13, -6 ],
                "$pos_icon": [ 0, 0 ],
                "$postext": [ 0, 0 ],
                "collection_index": 1
              }
            }
          ]
        }
      }
    ]
  },

  "remote_control_main": {
    "type": "stack_panel",
    "size": [ "100%", "100%" ],
    "orientation": "vertical",
    "anchor_from": "top_middle",
    "anchor_to": "top_middle",
    "collection_name": "form_buttons",
    "controls": [
      {
        "buttonSetts@drone_dynamic_button": {
          "$set_size": [ "100%", 28 ],
          "$icon_size": [ 26, 26 ],
          "$pos": [ 0, 0 ],
          "$pos_icon": [ -113, 0 ],
          "$postext": [ 0, 0 ],
          "collection_index": 0,
          "$show_hide_btt": true
        }
      },
      {
        "offsetScroll@drone_form.drone_tasks_panel": {
          "$siz": [ "100% - 6px", "88%" ],
          "$text_set|default": [ 2, -41 ],
          "$set": [ 0, 0 ]
        }
      }
    ]
  },

  "drones_scrolling": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "controls": [
      {
        "offsetScroll@drone_form.drone_tasks_panel": {
          "$siz": [ "100% - 6px", "98%" ],
          "$set": [ 0, 6 ]
        }
      }
    ]
  },

  "drone_tasks_panel": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "anchor_from": "top_left",
    "anchor_to": "top_left",
    "$set|default": [ 0, 0 ],
    "$text_set|default": [ 2, -15 ],
    "$siz|default": [ "100% - 6px", "100% + 1px" ],
    "controls": [
      {
        "label_panel": {
          "type": "panel",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "size": ["100%", "100%c"],
          "controls": [
            {
              "main_label": {
                "type": "label",
                "offset": "$text_set",
                "layer": 10,
                "color": "$main_header_text_color",
                "size": ["100%", "default"],
                "text": "#form_text"
              }
            }
          ]
        }
      },
      {
        "scrolling_tasks@common.scrolling_panel": {
          "anchor_to": "top_left",
          "anchor_from": "top_left",
          "$show_background": false,
          "$scroll_bar_contained_touch": false,
          "size": [ "100% + 7px", "100%" ],
          "$scrolling_content": "drone_form.tasks_scrolling_content",
          "$scroll_size": [ 5, "99%" ],
          "$scrolling_pane_size": "$siz",
          "$scrolling_pane_offset": "$set",
          "$scroll_view_port_offset": [ 2, 0 ],
          "$scroll_view_control_size": [ "100%", "100%" ]
        }
      }
    ]
  },

  "tasks_scrolling_content": {
    "type": "stack_panel",
    "size": [ "100% - 4px", "100%c" ],
    "orientation": "vertical",
    "anchor_from": "top_left",
    "anchor_to": "top_left",

    "controls": [
      {
        "task_buttons_panel": {
          "type": "stack_panel",
          "size": ["100% - 4px", "100%c"],
          "orientation": "vertical",
          "anchor_from": "top_middle",
          "anchor_to": "top_middle",

          "factory": {
            "name": "buttons",
            "control_name": "drone_form.buttons_from_panel"
          },
          "collection_name": "form_buttons",
          "bindings": [
            {
              "binding_name": "#form_button_length",
              "binding_name_override": "#collection_length"
            }
          ]
        }
      }
    ]
  },

  "buttons_from_panel": {
    "type": "panel",
    "size": [ "100% + 2px", "100%c + 3px" ],
    "controls": [
      {
        "buttonSetts@drone_dynamic_button": {
          "$set_size": [ 210, 26 ],
          "$icon_size": [ 24, 24 ],
          "$pos": [ 15, 0 ],
          "$pos_icon": [ -108, 0 ],
          "$postext": [ 0, 0 ]
        }
      }
    ]
  },

  "drone_main_menu_panel": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "controls": [
      {
        "label_offset_panel": {
          "type": "panel",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "size": ["100%", "100%c"],
          "controls": [
            {
              "main_label": {
                "type": "label",
                "offset": [ 2, -15 ],
                "layer": 10,
                "color": "$main_header_text_color",
                "size": ["100%", "default"],
                "text": "#form_text"
              }
            }
          ]
        }
      },
      {
        "panel_1": {
          "type": "stack_panel",
          "orientation": "horizontal",
          "collection_name": "form_buttons",
          "controls": [
            {
              "button0@drone_form.drone_dynamic_button": {
                "$set_size": [ 147, 48 ],
                "$icon_size": [ 46, 46 ],
                "$pos": [ -3, 0 ],
                "$pos_icon": [ -52, 0 ],
                "$postext": [ 6, 0 ],
                "collection_index": 0
              }
            },
            {
              "button1@drone_form.drone_dynamic_button": {
                "$set_size": [ 97, 48 ],
                "$icon_size": [ 32, 32 ],
                "$pos": [ 3, 0 ],
                "$pos_icon": [ -23, 0 ],
                "$postext": [ 12, 0 ],
                "collection_index": 1
              }
            }
          ]
        }
      },
      {
        "panel_2": {
          "type": "stack_panel",
          "orientation": "vertical",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "offset": [ 0, 52 ],
          "collection_name": "form_buttons",
          "controls": [
            {
              "button4@drone_form.drone_dynamic_button": {
                "$set_size": [ 115, 36 ],
                "$icon_size": [ 36, 36 ],
                "$pos": [ 2, 0 ],
                "$pos_icon": [ -38, 0 ],
                "$postext": [ 9, 0 ],
                "collection_index": 4
              }
            },
            {
              "button2@drone_form.drone_dynamic_button": {
                "$set_size": [ 130, 36 ],
                "$icon_size": [ 32, 32 ],
                "$pos": [ 122, -36 ],
                "$pos_icon": [ 76, -36 ],
                "$postext": [ 6, 0 ],
                "collection_index": 2
              }
            },
            {
              "button3@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 36 ],
                "$icon_size": [ 250, 48 ],
                "$pos": [ 2, -32 ],
                "$pos_icon": [ 2, -32 ],
                "$postext": [ 4, 0 ],
                "collection_index": 3
              }
            }
          ]
        }
      },
      {
        "panel_3": {
          "type": "stack_panel",
          "orientation": "horizontal",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "offset": [ 0, 132 ],
          "collection_name": "form_buttons",
          "controls": [
            {
              "button5@drone_form.drone_dynamic_button": {
                "$set_size": [ 56, 56 ],
                "$icon_size": [ 54, 54 ],
                "$pos": [ 2, 0 ],
                "$pos_icon": [ 2, 0 ],
                "$postext": [ 0, 0 ],
                "$show_text": false,
                "collection_index": 5
              }
            },
            {
              "panel_4": {
                "type": "stack_panel",
                "orientation": "vertical",
                "anchor_from": "top_left",
                "anchor_to": "top_left",
                "collection_name": "form_buttons",
                "controls": [
                  {
                    "button6@drone_form.drone_dynamic_button": {
                      "$set_size": [ 188, 24 ],
                      "$icon_size": [ 40, 40 ],
                      "$pos": [ 8, 0 ],
                      "$pos_icon": [ -60, 1 ],
                      "$postext": [ 0, 0 ],
                      "collection_index": 6
                    }
                  },
                  {
                    "button7@drone_form.drone_dynamic_button": {
                      "$set_size": [ 188, 24 ],
                      "$icon_size": [ 22, 22 ],
                      "$pos": [ 8, 6 ],
                      "$pos_icon": [ -60, 6 ],
                      "$postext": [ 0, 0 ],
                      "collection_index": 7
                    }
                  }
                ]
              }
            }
          ]
        }
      }
    ]
  },

  "antenna_main_menu_panel": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "controls": [
      {
        "label_offset_panel": {
          "type": "panel",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "size": ["100%", "100%c"],
          "controls": [
            {
              "main_label": {
                "type": "label",
                "offset": [ 2, -15 ],
                "layer": 10,
                "color": "$main_header_text_color",
                "size": ["100%", "default"],
                "text": "#form_text"
              }
            }
          ]
        }
      },
      {
        "panel_1": {
          "type": "stack_panel",
          "orientation": "vertical",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "collection_name": "form_buttons",
          "controls": [
            {
              "button0@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 48 ],
                "$icon_size": [ 46, 46 ],
                "$pos": [ 3, 0 ],
                "$pos_icon": [ -95, 0 ],
                "$postext": [ 0, 0 ],
                "collection_index": 0
              }
            }
          ]
        }
      },
      {
        "panel_2": {
          "type": "stack_panel",
          "orientation": "vertical",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "offset": [ 0, 52 ],
          "collection_name": "form_buttons",
          "controls": [
            {
              "button1@drone_form.drone_dynamic_button": {
                "$set_size": [ 115, 36 ],
                "$icon_size": [ 36, 36 ],
                "$pos": [ 2, 0 ],
                "$pos_icon": [ -34, 0 ],
                "$postext": [ 9, 0 ],
                "collection_index": 1
              }
            },
            {
              "button2@drone_form.drone_dynamic_button": {
                "$set_size": [ 130, 36 ],
                "$icon_size": [ 32, 32 ],
                "$pos": [ 122, -36 ],
                "$pos_icon": [ 76, -36 ],
                "$postext": [ 6, 0 ],
                "collection_index": 2
              }
            },
            {
              "button3@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 36 ],
                "$icon_size": [ 42, 42 ],
                "$pos": [ 2, -32 ],
                "$pos_icon": [ -98, -32 ],
                "$postext": [ 4, 0 ],
                "collection_index": 3
              }
            }
          ]
        }
      }
    ]
  },

  "swarms_panel": {
    "type": "panel",
    "size": [ "100%", "100%" ],
    "controls": [
      {
        "label_offset_panel": {
          "type": "panel",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "size": ["100%", "100%c"],
          "controls": [
            {
              "main_label": {
                "type": "label",
                "offset": [ 2, -15 ],
                "layer": 10,
                "color": "$main_header_text_color",
                "size": ["100%", "default"],
                "text": "#form_text"
              }
            }
          ]
        }
      },
      {
        "panelX": {
          "type": "stack_panel",
          "orientation": "vertical",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "collection_name": "form_buttons",
          "controls": [
            {
              "button0@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 48 ],
                "$icon_size": [ 46, 46 ],
                "$pos": [ 2, 0 ],
                "$pos_icon": [ -95, 0 ],
                "$postext": [ 0, 0 ],
                "collection_index": 0
              }
            }
          ]
        }
      },
      {
        "panelZ": {
          "type": "stack_panel",
          "orientation": "vertical",
          "anchor_from": "top_left",
          "anchor_to": "top_left",
          "offset": [ 0, 52 ],
          "collection_name": "form_buttons",
          "controls": [
            {
              "button1@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 36 ],
                "$icon_size": [ 36, 36 ],
                "$pos": [ 2, 0 ],
                "$pos_icon": [ -100, 0 ],
                "$postext": [ 4, 0 ],
                "collection_index": 2
              }
            },
            {
              "button2@drone_form.drone_dynamic_button": {
                "$set_size": [ 250, 36 ],
                "$icon_size": [ 36, 36 ],
                "$pos": [ 2, 4 ],
                "$pos_icon": [ -102, 4 ],
                "$postext": [ 4, 0 ],
                "collection_index": 3
              }
            },
            {
              "button3@drone_form.drone_dynamic_button": {
                "$set_size": [ 115, 36 ],
                "$icon_size": [ 34, 34 ],
                "$pos": [ 2, 8 ],
                "$pos_icon": [ -38, 8 ],
                "$postext": [ 9, 0 ],
                "collection_index": 1
              }
            },
            {
              "button4@drone_form.drone_dynamic_button": {
                "$set_size": [ 130, 36 ],
                "$icon_size": [ 32, 32 ],
                "$pos": [ 122, -28 ],
                "$pos_icon": [ 76, -28 ],
                "$postext": [ 6, 0 ],
                "collection_index": 4
              }
            }
          ]
        }
      }
    ]
  },

  "drone_dynamic_button": {
    "type": "panel",
    "$set_size|default": [ 64, 64 ],
    "$icon_size|default": [ 32, 32 ],
    "$pos|default": [ 0, 0 ],
    "$pos_icon|default": [ 0, 0 ],
    "$postext|default": [ 0, 0 ],
    "$border_colour|default": [ 0.10, 0.28, 0.30 ],
    "$button_texture|default": "textures/ui/button_borderless_drone",
    "$button_alpha|default": 0.5,
    "size": "$set_size",
    "$show_text": true,
    "$show_hide_btt": false,
    "bindings": [
      {
        "binding_name": "#form_button_text",
        "binding_type": "collection",
        "binding_collection_name": "form_buttons"
      },
      {
        "binding_type": "view",
        "source_property_name": "(((not (#form_button_text = '')) and (((#form_button_text - '§h§i§d§e') = #form_button_text))) or $show_hide_btt)",
        "target_property_name": "#visible"
      }
    ],
    "controls": [
      {
        "panel_image": {
          "type": "panel",
          "size": [0, 0],
          "bindings": [
            {
              "binding_type": "view",
              "source_control_name": "image",
              "resolve_sibling_scope": true,
              "source_property_name": "(not (#texture = ''))",
              "target_property_name": "#visible"
            }
          ],
          "controls": [
            {
              "image": {
                "type": "image",
                "layer": 10,
                "size": "$icon_size",
                "offset": "$pos_icon",
                "controls": [
                  {
                    "progress@progress.progress_loading_spinner": {
                      "size": [12, 12],
                      "offset": [0, 0],
                      "bindings": [
                        {
                          "binding_type": "view",
                          "source_control_name": "image",
                          "resolve_sibling_scope": true,
                          "source_property_name": "(#texture = 'loading')",
                          "target_property_name": "#visible"
                        }
                      ]
                    }
                  }
                ],
                "bindings": [
                  {
                    "binding_name": "#form_button_texture",
                    "binding_name_override": "#texture",
                    "binding_type": "collection",
                    "binding_collection_name": "form_buttons"
                  },
                  {
                    "binding_name": "#form_button_texture_file_system",
                    "binding_name_override": "#texture_file_system",
                    "binding_type": "collection",
                    "binding_collection_name": "form_buttons"
                  },
                  {
                    "binding_type": "view",
                    "source_property_name": "(not ((#texture = '') or (#texture = 'loading')))",
                    "target_property_name": "#visible"
                  }
                ]
              }
            }
          ]
        }
      },
      {
        "form_button@common_buttons.drone_text_button": {
          "$pressed_button_name": "button.form_button_click",
          "$default_button_texture": "$button_texture",
          "$default_button_alpha|default": "$button_alpha",
          "offset": "$pos",
          "$show": "$show_text",
          "$drone_border_color": "$border_colour",
          "$pos_text": "$postext",
          "$button_text": "#null",
          "$button_text_binding_type": "collection",
          "$button_text_grid_collection_name": "form_buttons",
          "$button_text_max_size": [ 0, 0 ],
          "bindings": [
            {
              "binding_type": "collection_details",
              "binding_collection_name": "form_buttons"
            }
          ]
        }
      }
    ]
  },

  "drone_custom_form_setts": {
    "type": "panel",
    "size": ["100%", "100%"],
    "controls": [
      {
        "content@drone_form.drone_custom_form_panel": {}
      },
      {
      "select_button_view": {
        "type": "panel",
        "size": ["100%", "100%"],
          "bindings": [
            {
              "binding_type": "view",
              "source_property_name": "(not ((#title_text - '§d§r§0§n§e§g§f§f') = #title_text))",
              "target_property_name": "#visible"
            }
          ],
          "controls": [
            {
              "select_button@common_buttons.drone_text_button": {
                "$pressed_button_name": "button.form_button_click",
                "anchor_from": "bottom_right",
                "anchor_to": "bottom_right",
                "size": [ 32, 32 ],
                "offset": [ 36, -164 ],
      
                "$default_button_texture": "textures/ui/TabRightDrone",
                "$hover_button_texture": "textures/ui/TabRightDronePressed",
                "$icon_texture": "textures/ui/xyz_axis",
                "$colour": "$generic_button_text_color",
                "$default_button_alpha": 1,
                "$border": false,
                "bindings": [
                  { ////Intentional error
                    "binding_type": "collection_details",
                    "binding_collection_name": "custom_form"
                  }
                ]
              }
            }
          ]
        }
      },
      {
        "close_button@common_buttons.drone_text_button": {
          "$pressed_button_name": "button.menu_exit",
          "anchor_from": "bottom_right",
          "anchor_to": "bottom_right",
          "size": [ 32, 32 ],
          "offset": [ 36, -28 ],

          "$default_button_texture": "textures/ui/TabRightDrone",
          "$hover_button_texture": "textures/ui/TabRightDronePressed",
          "$icon_texture": "textures/ui/realms_red_x",
          "$colour": "$generic_button_text_color",
          "$default_button_alpha": 1,
          "$border": false
        }
      },
      {
        "submit_button@common_buttons.drone_text_button": {
          "$pressed_button_name": "button.submit_custom_form",
          "anchor_from": "bottom_right",
          "anchor_to": "bottom_right",
          "size": [ 32, 32 ],
          "offset": [ 36, 6 ],

          "$default_button_texture": "textures/ui/TabRightDrone",
          "$hover_button_texture": "textures/ui/TabRightDronePressed",
          "$icon_texture": "textures/ui/realms_green_check",
          "$colour": "$generic_button_text_color",
          "$default_button_alpha": 1,
          "$border": false,

          "$button_text": "#null",
          "$button_text_binding_type": "global",
          "$button_binding_condition": "once",

          "bindings": [
            {
              "binding_name": "#submit_button_visible",
              "binding_name_override": "#visible"
            }
          ]
        }
      }
    ]
  },

  "drone_custom_form_panel@common.scrolling_panel" : {
    "anchor_to": "top_left",
    "anchor_from": "top_left",
    "$show_background": false,
    "size": [ "100% + 7px", "100%" ],
    "$scrolling_content": "drone_form.drone_custom_form_scrolling",
    "$scroll_size": [ 5, "89%" ],
    "$scrolling_pane_size": [ "100% - 6px", "100% + 23px" ],
    "$scrolling_pane_offset": [ 0, -18 ],
    "$scroll_view_port_offset": [ 0, 0 ],
    "$scroll_view_control_size": [ "100%", "100%" ],
    "$scroll_bar_left_padding_size": [ 6, 0 ]
  },

  "drone_custom_form_scrolling": {
    "type": "stack_panel",
    "size": ["100% - 4px", "100%c"],
    "offset": [2,0],
    "orientation": "vertical",
    "anchor_from": "top_left",
    "anchor_to": "top_left",

    "controls": [
      {
        "generated_form@server_form.generated_contents": {}
      }
    ]    
  }
}