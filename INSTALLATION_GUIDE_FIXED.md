# دليل التثبيت والإعداد

## 1. تثبيت المتطلبات
```bash
pip install -r requirements_fixed.txt
```

## 2. إعداد Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. فعّل Firebase Storage و Firestore
4. أنشئ Service Account وحمّل ملف JSON
5. ضع الملف في المجلد وأعد تسميته إلى `firebase-service-account.json`

## 3. إعداد Gemini API
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. أضف المفتاح إلى ملف `api_keys.json`

## 4. تشغيل الأداة
```bash
python mod_processor_broken_final.py
```

## 5. اختبار الإصلاحات
```bash
python test_all_fixes.py
```

## المشاكل التي تم حلها:
- ✅ ERR_MODULE_IMPORT_FAILED
- ✅ ERR_JSON_PARSE_FAILED  
- ✅ ERR_GEMINI_API_UNAVAILABLE
- ✅ ERR_DESC_GENERATOR_DISABLED
- ✅ ERR_DUPLICATE_IMAGES
- ❌ ERR_SOCIAL_EXTRACTION_DISABLED (معطل كما هو مطلوب)
- ✅ ERR_YOUTUBE_VIDEOS_NOT_FOUND
- ✅ معالجة الصور ورفعها على Firebase
