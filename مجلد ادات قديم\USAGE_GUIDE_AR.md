# دليل استخدام فلتر الصور الذكي باستخدام Gemini AI

## 🎯 نظرة عامة

تم تطوير ميزة جديدة ثورية تستخدم الذكاء الاصطناعي **Gemini من Google** لفلترة الصور المستخرجة من صفحات MCPEDL بذكاء فائق. هذه الميزة تضمن اختيار **الصور المتعلقة بالمود فقط** وتجاهل الصور غير المرغوبة مثل صور المستخدمين والإعلانات والمودات الأخرى.

## ✨ المميزات الجديدة

### 🤖 فلترة ذكية متقدمة
- **تحليل بصري ذكي**: يحلل محتوى كل صورة باستخدام AI
- **اختيار دقيق**: يختار فقط الصور المتعلقة بالمود المحدد
- **رفض ذكي**: يتجاهل الصور غير المرغوبة تلقائياً
- **فلترة متعددة المراحل**: فلترة أولية + فلترة AI + معالجة نهائية

### 🎯 معايير الاختيار الذكية
✅ **صور يتم قبولها:**
- لقطات شاشة من داخل اللعبة تُظهر المود
- صور عناصر اللعب الجديدة (كتل، عناصر، وحوش)
- صور توضيحية للمميزات والقدرات
- واجهات المستخدم الجديدة أو القوائم
- صور المعاينة والعرض التوضيحي

❌ **صور يتم رفضها:**
- صور الملفات الشخصية أو الأفاتار
- صور المودات الأخرى المقترحة
- إعلانات أو صور ترويجية للموقع
- أيقونات أو شعارات الموقع
- صور فارغة أو تالفة أو صغيرة

## 🚀 التثبيت والإعداد

### 1. تثبيت المتطلبات
```bash
pip install google-generativeai requests pillow beautifulsoup4
```

### 2. الحصول على مفتاح Gemini API
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. احفظ المفتاح بأمان

### 3. إعداد مفتاح API

#### الطريقة الأولى: ملف الإعدادات
أنشئ أو حدث ملف `config.json`:
```json
{
  "gemini_api_keys": ["your-gemini-api-key-here"]
}
```

#### الطريقة الثانية: متغير البيئة
```bash
# Windows
set GEMINI_API_KEY=your-gemini-api-key-here

# Linux/Mac
export GEMINI_API_KEY="your-gemini-api-key-here"
```

## 📖 كيفية الاستخدام

### 🔄 الاستخدام التلقائي (موصى به)
الفلتر يعمل تلقائياً عند استخراج الصور من MCPEDL:

```python
from mcpedl_scraper_module import scrape_mcpedl_mod

# استخراج بيانات المود مع فلترة ذكية للصور
url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
mod_data = scrape_mcpedl_mod(url)

# الصور المستخرجة ستكون مفلترة بذكاء
if mod_data:
    images = mod_data['image_urls']
    print(f"تم استخراج {len(images)} صورة مفلترة")
```

### 🎛️ الاستخدام المباشر
```python
from gemini_image_filter import GeminiImageFilter

# إنشاء فلتر
filter = GeminiImageFilter("your-api-key")

# قائمة الصور للفلترة
image_urls = [
    "https://media.forgecdn.net/attachments/123/456/dragon_screenshot.png",
    "https://mcpedl.com/wp-content/uploads/2023/01/mod_preview.jpg",
    "https://gravatar.com/avatar/user123.png"  # سيتم رفضها
]

# فلترة الصور
filtered_images = filter.filter_mod_images(
    image_urls, 
    "Dragon Mounts",  # اسم المود
    "Adds rideable dragons to Minecraft"  # وصف المود
)

print(f"تم اختيار {len(filtered_images)} صور من أصل {len(image_urls)}")
```

### 🧪 تشغيل الاختبارات
```python
# اختبار شامل للميزة
from test_gemini_filter import run_all_tests
run_all_tests()

# اختبار فلتر الصور فقط
from gemini_image_filter import test_gemini_image_filter
test_gemini_image_filter()
```

## 🔧 كيفية عمل النظام

### المرحلة 1: الفلترة الأولية 🔍
```
صور أصلية (10) → فلترة أولية → صور محتملة (6)
```
- رفض الصور الواضحة غير المرغوبة
- قبول الصور من مصادر موثوقة
- تحليل أسماء الملفات والروابط

### المرحلة 2: تحليل Gemini AI 🤖
```
صور محتملة (6) → تحليل AI → صور مختارة (3)
```
- تحميل الصور وتحويلها لـ base64
- إرسال prompt مفصل باللغة العربية
- تحليل محتوى كل صورة بصرياً
- اختيار الصور المتعلقة بالمود فقط

### المرحلة 3: المعالجة النهائية ✨
```
صور مختارة (3) → معالجة نهائية → نتيجة نهائية (3)
```
- ترتيب الصور حسب الأولوية
- إزالة الصور المكررة
- تطبيق اختيار احتياطي إذا لزم الأمر

## 📊 مثال على Prompt المرسل لـ Gemini

```arabic
أنت خبير في ألعاب Minecraft وتحليل الصور. مهمتك هي فحص الصور المرفقة 
واختيار الصور التي تتعلق بالمود/الإضافة فقط.

معلومات المود:
- الاسم: Dragon Mounts
- الوصف: Adds rideable dragons with unique abilities to Minecraft
- الفئة: Addons

لديك 5 صور للفحص. يرجى تحليل كل صورة وتحديد ما إذا كانت:

✅ صورة متعلقة بالمود: تُظهر محتوى المود، مميزاته، أو عناصر اللعب الجديدة
❌ صورة غير متعلقة: صور المستخدمين، الإعلانات، أيقونات الموقع، أو مودات أخرى

معايير الاختيار:
1. الصور التي تُظهر عناصر اللعب الجديدة (كتل، عناصر، وحوش، إلخ)
2. لقطات شاشة من داخل اللعبة تُظهر المود
3. صور توضيحية للمميزات
4. واجهات المستخدم الجديدة أو القوائم

أجب بتنسيق JSON فقط:
{"selected_images": [0, 1, 3]}

حيث الأرقام تمثل فهارس الصور المختارة (تبدأ من 0).
```

## 🛡️ المعالجة الذكية للأخطاء

### عدم توفر مفتاح API
```
⚠️ مفتاح Gemini API غير متوفر للفلترة الذكية
→ يعود للفلترة التقليدية
```

### فشل تحميل الصور
```
❌ فشل تحميل الصورة: https://example.com/image.jpg
→ يتجاهل الصورة التالفة ويواصل مع الباقي
```

### خطأ في Gemini
```
❌ خطأ في تطبيق فلتر Gemini: [تفاصيل الخطأ]
→ يعود للصور الأصلية مع تسجيل الخطأ
```

### اختيار احتياطي
```
⚠️ فلتر Gemini لم يختر أي صور
→ يطبق اختيار احتياطي ذكي:
   1. أولوية للصور من forgecdn
   2. ثم صور من مصادر موثوقة أخرى
   3. أخيراً أول 3 صور من القائمة
```

## ⚡ الأداء والحدود

### حدود API
- **معالجة**: 5 صور في كل مرة
- **تأخير**: 2 ثانية بين المجموعات
- **حجم الصورة**: حد أقصى 2MB لكل صورة
- **البيانات المُرمزة**: حد أقصى 4MB base64

### التحسينات المدمجة
- ضغط الصور الكبيرة تلقائياً
- تخزين مؤقت للنتائج
- معالجة متوازية للمجموعات
- فلترة أولية لتوفير استهلاك API

## 📈 رسائل النظام

### رسائل النجاح ✅
```
✅ تم تهيئة Gemini Image Filter مع التحسينات
🤖 بدء فلترة الصور باستخدام Gemini AI للمود: Dragon Mounts
🎯 Gemini AI فلتر الصور: 3 من أصل 8
📊 تقرير الفلترة: 0.75 نسبة النجاح
```

### رسائل التحذير ⚠️
```
⚠️ مفتاح Gemini API غير متوفر للفلترة الذكية
⚠️ الفلترة الأولية لم تترك أي صور
⚠️ فلتر Gemini لم يختر أي صور، سيتم إرجاع الصور الأصلية
```

### رسائل الخطأ ❌
```
❌ خطأ في تطبيق فلتر Gemini: [تفاصيل الخطأ]
❌ فشل تحميل الصورة: [رابط الصورة]
❌ خطأ في تحليل استجابة Gemini: [تفاصيل]
```

## 🧪 الاختبار والتشخيص

### اختبار سريع
```python
python test_gemini_filter.py
```

### اختبار مفصل
```python
from test_gemini_filter import run_all_tests
run_all_tests()
```

### اختبار التحسينات
```python
from gemini_filter_enhancements import test_enhancements
test_enhancements()
```

## 🔧 استكشاف الأخطاء

### المشكلة: لا يعمل الفلتر
**الحلول:**
1. تأكد من صحة مفتاح Gemini API
2. تحقق من الاتصال بالإنترنت
3. راجع رسائل الخطأ في وحدة التحكم

### المشكلة: لا يتم اختيار أي صور
**الحلول:**
1. تحقق من جودة الصور الأصلية
2. تأكد من أن الصور متعلقة بالمود فعلاً
3. جرب مع مود آخر للمقارنة

### المشكلة: بطء في المعالجة
**الحلول:**
1. قلل عدد الصور المرسلة
2. تأكد من سرعة الإنترنت
3. استخدم الفلترة الأولية

## 📞 الدعم والمساعدة

في حالة مواجهة مشاكل:
1. راجع هذا الدليل أولاً
2. شغل الاختبارات المدمجة
3. تحقق من رسائل الخطأ
4. تأكد من تحديث جميع المكتبات

---

**🎉 تهانينا!** أصبح لديك الآن أقوى أداة لفلترة صور المودات بذكاء اصطناعي متقدم!
