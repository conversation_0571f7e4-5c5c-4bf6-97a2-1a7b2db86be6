-- SQL script to create the mods table with all required fields
-- This script creates a table compatible with Supabase PostgreSQL

-- Drop table if exists (optional - remove this line if you want to preserve existing data)
-- DROP TABLE IF EXISTS mods;

-- Create the mods table
CREATE TABLE IF NOT EXISTS mods (
    -- Primary key with UUID and auto-generation
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Timestamp for creation tracking
    created_at TIMESTAMPTZ DEFAULT NOW(),

    -- Basic mod information
    name TEXT,
    description TEXT,
    category TEXT,
    version TEXT,
    size TEXT,

    -- Download information
    download_url TEXT,
    downloads BIGINT DEFAULT 0,
    likes BIGINT DEFAULT 0,
    clicks BIGINT DEFAULT 0,

    -- Image URLs stored as JSONB array
    image_urls JSONB DEFAULT '[]'::jsonb,

    -- Creator information
    creator_name TEXT,
    creator_contact_info TEXT,
    creator_social_channels JSONB DEFAULT '[]'::jsonb
);

-- Enable Row Level Security (RLS) - Recommended by <PERSON>pa<PERSON>
ALTER TABLE mods ENABLE ROW LEVEL SECURITY;

-- Create a policy to allow public read access (adjust as needed for your security requirements)
CREATE POLICY "Allow public read access" ON mods
    FOR SELECT USING (true);

-- Create a policy to allow public insert access (adjust as needed for your security requirements)
CREATE POLICY "Allow public insert access" ON mods
    FOR INSERT WITH CHECK (true);

-- Create a policy to allow public update access (adjust as needed for your security requirements)
CREATE POLICY "Allow public update access" ON mods
    FOR UPDATE USING (true);

-- Create a policy to allow public delete access (adjust as needed for your security requirements)
CREATE POLICY "Allow public delete access" ON mods
    FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_creator_name ON mods(creator_name);
CREATE INDEX IF NOT EXISTS idx_mods_downloads ON mods(downloads DESC);
CREATE INDEX IF NOT EXISTS idx_mods_likes ON mods(likes DESC);

-- Enable Realtime for the table (optional - for real-time subscriptions)
-- This requires Supabase Realtime to be enabled for your project
-- ALTER PUBLICATION supabase_realtime ADD TABLE mods;

-- Comments for documentation
COMMENT ON TABLE mods IS 'Table storing Minecraft mod information with creator details and social links';
COMMENT ON COLUMN mods.id IS 'Unique identifier for each mod';
COMMENT ON COLUMN mods.created_at IS 'Timestamp when the mod was added to the database';
COMMENT ON COLUMN mods.name IS 'Name of the mod';
COMMENT ON COLUMN mods.description IS 'Detailed description of the mod';
COMMENT ON COLUMN mods.category IS 'Category of the mod (Addons, Shaders, Texture Pack)';
COMMENT ON COLUMN mods.version IS 'Version number of the mod';
COMMENT ON COLUMN mods.size IS 'File size of the mod';
COMMENT ON COLUMN mods.download_url IS 'Direct download URL for the mod file';
COMMENT ON COLUMN mods.downloads IS 'Number of times the mod has been downloaded';
COMMENT ON COLUMN mods.likes IS 'Number of likes the mod has received';
COMMENT ON COLUMN mods.clicks IS 'Number of times the mod page has been viewed';
COMMENT ON COLUMN mods.image_urls IS 'Array of image URLs associated with the mod';
COMMENT ON COLUMN mods.creator_name IS 'Name of the mod creator';
COMMENT ON COLUMN mods.creator_contact_info IS 'Contact information for the mod creator';
COMMENT ON COLUMN mods.creator_social_channels IS 'Array of custom social media sites and links for the creator (format: ["Site Name: URL"])';

-- Sample data insertion (optional - remove if not needed)
/*
INSERT INTO mods (
    name,
    description,
    category,
    version,
    size,
    download_url,
    image_urls,
    creator_name,
    creator_contact_info,
    creator_social_channels
) VALUES (
    'Sample Furniture Mod',
    'This mod adds various furniture items to Minecraft Bedrock Edition including chairs, tables, and decorative items.',
    'Addons',
    '1.21.0',
    '2.5 MB',
    'https://example.com/download/furniture-mod.mcaddon',
    '["https://example.com/images/furniture1.jpg", "https://example.com/images/furniture2.jpg"]'::jsonb,
    'ModCreator123',
    'For support, join my Discord server: https://discord.gg/creator123',
    '["Discord Server: https://discord.gg/creator123", "YouTube Channel: https://youtube.com/creator123", "Personal Website: https://creator-website.com"]'::jsonb
);
*/

-- Query examples for testing:
/*
-- Select all mods
SELECT * FROM mods ORDER BY created_at DESC;

-- Select mods by category
SELECT * FROM mods WHERE category = 'Addons';

-- Select mods with custom social sites
SELECT name, creator_name, creator_contact_info, creator_social_channels
FROM mods
WHERE creator_social_channels IS NOT NULL AND jsonb_array_length(creator_social_channels) > 0;

-- Count mods by category
SELECT category, COUNT(*) as mod_count
FROM mods
GROUP BY category;
*/
