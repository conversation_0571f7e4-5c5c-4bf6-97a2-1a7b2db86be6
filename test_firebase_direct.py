# -*- coding: utf-8 -*-
"""
اختبار Firebase Storage مباشر
Direct Firebase Storage Test
"""

import os
import sys
from firebase_config import firebase_manager

def main():
    """اختبار Firebase Storage مباشر"""
    print("🚀 بدء اختبار Firebase Storage المباشر...")
    print("=" * 50)
    
    # فحص ملف service account
    service_account_path = "firebase-service-account.json"
    if not os.path.exists(service_account_path):
        print(f"❌ ملف service account غير موجود: {service_account_path}")
        return False
    
    print("✅ ملف service account موجود")
    
    # محاولة تهيئة Firebase
    print("🔄 تهيئة Firebase...")
    try:
        if firebase_manager.auto_initialize():
            print("✅ تم تهيئة Firebase بنجاح")
        else:
            print("❌ فشل في تهيئة Firebase")
            return False
    except Exception as e:
        print(f"❌ خطأ في تهيئة Firebase: {e}")
        return False
    
    # اختبار الاتصال
    print("🔄 اختبار الاتصال...")
    try:
        if firebase_manager.test_connection():
            print("✅ اختبار الاتصال نجح")
        else:
            print("❌ فشل اختبار الاتصال")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False
    
    # اختبار رفع ملف صغير
    print("📤 اختبار رفع ملف...")
    try:
        test_content = b"Test file content for Firebase Storage"
        test_filename = "test_file.txt"
        
        public_url = firebase_manager.upload_file_to_storage(
            test_content,
            test_filename,
            'text/plain',
            'test_uploads'
        )
        
        if public_url:
            print(f"✅ تم رفع الملف بنجاح: {public_url}")
            
            # محاولة حذف الملف
            file_path = f"test_uploads/{test_filename}"
            if firebase_manager.delete_file(file_path):
                print("✅ تم حذف الملف التجريبي")
            
            return True
        else:
            print("❌ فشل في رفع الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في رفع الملف: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 جميع الاختبارات نجحت! Firebase Storage يعمل بشكل صحيح")
    else:
        print("\n❌ فشل في الاختبار")
    
    sys.exit(0 if success else 1)
