{
  "format_version": "1.21.20",
  "minecraft:block": {
    "description": {
      "identifier": "bonsai:bonsai_torchflower",
	  "states":{ 
		"bonsai:fertilized":[0,1],
		"bonsai:sec": [0,1,2,3,4], //rapid drops is 15 secs, 60/15 = 4 = 1 min
		"bonsai:min": [0,1,2,3,4,5,6,7,8,9,10] //mins
	  }
    },
    "components": {
	"tag:stone": {},
	"minecraft:display_name":"Bonsai Torch Flower",
      "minecraft:loot": "loot_tables/bonsai_pot/torchflower.json",
      "minecraft:destructible_by_mining": {
			"seconds_to_destroy": 1
		},
	  "minecraft:geometry": "geometry.bonsai_torch_flower",
		"minecraft:material_instances" : {
			"*": {
			  "texture": "bonsai_torchflower",
			  "render_method" : "alpha_test"
			}
		},
		"minecraft:collision_box": {
			"origin": [-8, 0, -8],
			"size": [16, 8, 16]
		},
		"minecraft:selection_box":{
			"origin":[-8.0, 0.0, -8.0],
			"size":[16,16,16]
		},		
		"minecraft:tick": {
		  "looping": true,
		  "interval_range": [600, 600]
		},
		"minecraft:custom_components": ["bonsai:grow"]
    },
	"permutations":[
		{
			"condition": "query.block_state('bonsai:fertilized') == 0",
			"components": {
				"minecraft:tick": {
				  "looping": true,
				  "interval_range": [600, 600]
				}
			}
		},
		{
			"condition": "query.block_state('bonsai:fertilized') == 1",
			"components": {
				"minecraft:tick": {
				  "looping": true,
				  "interval_range": [300,300]
				}
			}
		}
	]
  }
}