"""
تحسينات وإصلاحات لأداة معالجة مودات Minecraft
"""

import re
import os
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# --- 1. تحسين دالة تنظيف الوصف ---
def improved_clean_basic_description(description: str) -> str:
    """
    تنظيف الوصف الأساسي من المعلومات غير المرغوبة وتنسيق الفقرات
    
    التحسينات:
    - إزالة العناوين والفقرات المنسقة
    - تحويل النص إلى فقرة واحدة متصلة
    - إزالة علامات [ENGLISH_DESCRIPTION] و [/ENGLISH_DESCRIPTION]
    """
    if not description:
        return ""

    # إزالة علامات [ENGLISH_DESCRIPTION] و [/ENGLISH_DESCRIPTION]
    description = re.sub(r'\[ENGLISH_DESCRIPTION\]|\[\/ENGLISH_DESCRIPTION\]', '', description)
    
    # تقسيم النص إلى أسطر
    lines = description.split('\n')
    cleaned_lines = []

    # أنماط الأسطر التي يجب تجنبها
    avoid_patterns = [
        r'minecraft pe texture packs',
        r'published on',
        r'updated on',
        r'skip to downloads',
        r'select version for changelog',
        r'changelog',
        r'installation',
        r'downloads',
        r'join discord',
        r'download packs',
        r'supported minecraft versions',
        r'resolutions',
        r'you may also like',
        r'installation guides',
        r'android',
        r'ios',
        r'windows 10',
        r'by\w+',
        r'\d{1,2}\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)',
        r'\d+\.\d+',
        r'mb\)',
        r'manifest\.json',
        r'pack uuid',
        r'mcc\)',
    ]

    for line in lines:
        line = line.strip()
        if not line:
            continue

        line_lower = line.lower()

        # تجنب الأسطر غير المرغوبة
        should_skip = False
        for pattern in avoid_patterns:
            if re.search(pattern, line_lower):
                should_skip = True
                break

        if should_skip:
            continue

        # تجنب الأسطر القصيرة جداً أو التي تحتوي على أرقام فقط
        if len(line) < 10 or line.isdigit():
            continue

        # تجنب الأسطر التي تحتوي على معلومات تقنية
        if any(tech in line_lower for tech in ['1.21', '16x', 'mb', 'uuid', 'manifest']):
            continue

        # تجنب الأسطر التي تبدأ بعلامات العناوين
        if re.match(r'^#+\s+', line) or re.match(r'^[A-Z\s]{10,}$', line):
            continue

        cleaned_lines.append(line)

    # دمج الأسطر المنظفة في فقرة واحدة
    cleaned_text = ' '.join(cleaned_lines)
    
    # تنظيف إضافي
    cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)  # تقليل المسافات المتعددة
    cleaned_text = re.sub(r'^\s*[\*\-\•]\s*', '', cleaned_text, flags=re.MULTILINE)  # إزالة النقاط
    
    return cleaned_text.strip()

# --- 2. تحسين دالة إنشاء أوصاف التيليجرام ---
def improved_generate_telegram_prompt(mod_name: str, mod_category: str, existing_description: str) -> str:
    """
    إنشاء بروميت محسن لإنشاء أوصاف التيليجرام بدون علامات [ENGLISH_DESCRIPTION]
    """
    prompt = f"""
    أنت كاتب محتوى ماين كرافت خبير متخصص في كتابة الأوصاف الجذابة. مهمتك هي كتابة وصفين مفصلين ومقنعين لنفس المحتوى: وصف باللغة العربية ووصف باللغة الإنجليزية.

    **اسم المحتوى:** {mod_name if mod_name else "غير محدد"}
    **نوع المحتوى:** {mod_category}

    **معلومات المصدر (الوصف الكامل للمود و/أو الميزات الرئيسية):**
    {existing_description}

    **التعليمات العامة:**
    - أنشئ وصفين منفصلين: وصف عربي ووصف إنجليزي لنفس المحتوى
    - اكتب وصف بسيط كلاسيكي من دون فقرات او مسافات كبيرة
    - استخدم أسلوباً سرديًا جذاباً يشبه المقالات الاحترافية
    - اجعل الوصف فقرة واحدة متصلة بدون عناوين أو تنسيقات
    - ⚠️ **مهم جداً: لا تذكر أي أرقام إصدارات أو معلومات توافق أو متطلبات تقنية**
    - ⚠️ **مهم جداً: لا تذكر إصدارات ماين كرافت (مثل 1.20، 1.21، إلخ) أو توافق الأجهزة**
    - ⚠️ **مهم جداً: ركز فقط على وصف المحتوى والميزات وتجربة اللعب**

    **إرشادات الكتابة:**
    - يجب أن تذكر صراحة جميع الميزات الرئيسية من معلومات المصدر
    - استخدم لغة طبيعية وسلسة مع تنوع في بنية الجمل
    - أدرج أمثلة محددة عن كيفية عمل الميزات في اللعبة
    - حافظ على نبرة مهنية لكن جذابة ومحادثية
    - تجنب التكرار والمحتوى الزائد
    - تجنب اضافة اية فقرات او مسافات كبيرة
    - إذا كانت معلومات المصدر قليلة، توسع بإبداع مع البقاء صادقاً للتفاصيل المتاحة

    قدم إجابتك بهذا التنسيق بالضبط:

    ENGLISH:
    Your comprehensive flowing English description here in a single paragraph without headers or formatting.

    ARABIC:
    وصفك الشامل والمتدفق باللغة العربية هنا في فقرة واحدة بدون عناوين أو تنسيقات.
    """
    return prompt

# --- 3. تحسين دالة استخراج الصور ---
def improved_extract_images(soup: BeautifulSoup, base_url: str) -> List[str]:
    """
    استخراج روابط الصور مع تحسينات لتجنب صور المودات المقترحة واستخراج الصورة الرئيسية
    """
    image_urls = []
    image_sources = {}  # لتتبع مصدر كل صورة

    # 1. البحث عن الصورة الرئيسية المخفية (صورة ForgeCD)
    # هذه الصور عادة ما تكون في وسم img مع src يحتوي على media.forgecdn.net
    forgecdn_patterns = [
        'img[src*="media.forgecdn.net/attachments"]',
        'img[src*="forgecdn.net"]',
        '.featured-image img',
        '.post-thumbnail img'
    ]
    
    for pattern in forgecdn_patterns:
        main_images = soup.select(pattern)
        for img in main_images:
            src = img.get('src') or img.get('data-src')
            if src:
                url = urljoin(base_url, src)
                if validate_image_url(url) and url not in image_urls:
                    image_urls.append(url)
                    image_sources[url] = 'main'
    
    # 2. البحث عن صور المعرض
    gallery_selectors = [
        '.gallery img', 
        '.wp-block-gallery img', 
        '.entry-content img'
    ]
    
    for selector in gallery_selectors:
        images = soup.select(selector)
        for img in images[:10]:  # حد أقصى 10 صور
            src = img.get('src') or img.get('data-src')
            if src:
                url = urljoin(base_url, src)
                if validate_image_url(url) and url not in image_urls:
                    # تجنب صور المودات المقترحة
                    if not is_suggested_mod_image(url, img):
                        image_urls.append(url)
                        image_sources[url] = 'gallery'

    # 3. فلترة الصور
    filtered_images = filter_images(image_urls, image_sources)
    
    return filtered_images

def validate_image_url(url: str) -> bool:
    """التحقق من صحة رابط الصورة"""
    if not url:
        return False

    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    url_lower = url.lower()

    # تجنب الصور الصغيرة والأيقونات
    avoid_patterns = [
        'logo.', 'icon.', 'favicon.', 'avatar', 'profile', 
        'empty.png', 'button', 'banner', 'ad-', 'ads/'
    ]
    
    if any(pattern in url_lower for pattern in avoid_patterns):
        return False

    return any(ext in url_lower for ext in image_extensions)

def is_suggested_mod_image(url: str, img_tag: Any) -> bool:
    """
    تحديد ما إذا كانت الصورة تنتمي إلى مود مقترح
    """
    # فحص URL للصورة
    suggested_patterns = [
        'empty.png',  # صور فارغة تستخدم غالباً للمودات المقترحة
        'related-', 
        'suggested-'
    ]
    
    if any(pattern in url.lower() for pattern in suggested_patterns):
        return True
    
    # فحص العناصر الأب للصورة
    parent = img_tag.parent
    for _ in range(5):  # فحص حتى 5 مستويات من العناصر الأب
        if not parent:
            break
            
        # فحص النص والفئات للعناصر الأب
        parent_text = parent.get_text().lower() if hasattr(parent, 'get_text') else ''
        parent_class = ' '.join(parent.get('class', [])).lower() if hasattr(parent, 'get') else ''
        
        suggested_section_patterns = [
            'you may also like', 
            'related', 
            'suggested', 
            'similar', 
            'more from'
        ]
        
        if any(pattern in parent_text for pattern in suggested_section_patterns) or \
           any(pattern in parent_class for pattern in suggested_section_patterns):
            return True
            
        parent = parent.parent
    
    return False

def filter_images(image_urls: List[str], image_sources: Dict[str, str]) -> List[str]:
    """
    فلترة وترتيب الصور حسب الأولوية
    """
    # ترتيب الصور حسب الأولوية
    priority_images = []
    gallery_images = []
    
    for url in image_urls:
        source = image_sources.get(url, '')
        if source == 'main':
            priority_images.append(url)
        else:
            gallery_images.append(url)
    
    # دمج القوائم مع الحفاظ على الترتيب
    filtered_images = priority_images + gallery_images
    
    # تحديد عدد الصور المرجعة
    max_images = min(8, len(filtered_images))
    return filtered_images[:max_images]