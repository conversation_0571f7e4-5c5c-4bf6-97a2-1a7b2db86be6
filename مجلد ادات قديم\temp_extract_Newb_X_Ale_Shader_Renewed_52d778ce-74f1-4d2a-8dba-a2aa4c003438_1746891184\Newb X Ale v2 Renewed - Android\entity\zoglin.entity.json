{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:zoglin", "materials": {"default": "phantom"}, "textures": {"default": "textures/entity/zoglin/zoglin"}, "geometry": {"default": "geometry.hoglin"}, "spawn_egg": {"base_color": "#c66e55", "overlay_color": "#e6e6e6"}, "scripts": {"pre_animation": ["variable.tcos_right_side = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;", "variable.tcos_left_side = -variable.tcos_right_side;", "variable.attack_head_rot =  Math.sin(variable.attack_time * 180.0) * -37.3;"], "animate": ["walk", "look_at_target", {"attack": "variable.has_target && variable.attack_time >= 0.0"}]}, "animations": {"walk": "animation.hoglin.walk", "look_at_target": "animation.hoglin.look_at_target", "attack": "animation.hoglin.attack"}, "render_controllers": ["controller.render.zoglin"]}}}