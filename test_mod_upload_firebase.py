# -*- coding: utf-8 -*-
"""
اختبار رفع ملف مود إلى Firebase Storage
Test uploading a mod file to Firebase Storage
"""

import os
import sys
import requests
import tempfile
from firebase_config import firebase_manager

def download_test_mod():
    """تحميل ملف مود تجريبي من الإنترنت"""
    print("📥 تحميل ملف مود تجريبي...")
    
    # رابط ملف مود صغير للاختبار
    test_mod_url = "https://mcpedl.com/download/better-inventory-b69/"
    
    try:
        # محاولة تحميل الملف
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(test_mod_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ تم تحميل الملف بنجاح - الحجم: {len(response.content)} بايت")
            return response.content, "test_mod.mcpack"
        else:
            print(f"❌ فشل في تحميل الملف - كود الاستجابة: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"❌ خطأ في تحميل الملف: {e}")
        return None, None

def create_dummy_mod():
    """إنشاء ملف مود وهمي للاختبار"""
    print("🔧 إنشاء ملف مود وهمي...")
    
    # إنشاء محتوى ملف ZIP بسيط
    import zipfile
    import io
    
    zip_buffer = io.BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # إضافة ملف manifest.json
        manifest = {
            "format_version": 2,
            "header": {
                "description": "Test Mod for Firebase Upload",
                "name": "Test Mod",
                "uuid": "12345678-1234-1234-1234-123456789012",
                "version": [1, 0, 0],
                "min_engine_version": [1, 16, 0]
            },
            "modules": [
                {
                    "description": "Test Module",
                    "type": "resources",
                    "uuid": "*************-4321-4321-************",
                    "version": [1, 0, 0]
                }
            ]
        }
        
        import json
        zip_file.writestr("manifest.json", json.dumps(manifest, indent=2))
        
        # إضافة ملف نصي بسيط
        zip_file.writestr("pack_icon.png", b"PNG fake content for testing")
        zip_file.writestr("README.txt", "This is a test mod file for Firebase upload testing.")
    
    zip_buffer.seek(0)
    content = zip_buffer.getvalue()
    
    print(f"✅ تم إنشاء ملف مود وهمي - الحجم: {len(content)} بايت")
    return content, "test_mod_firebase.mcpack"

def test_firebase_mod_upload():
    """اختبار رفع ملف مود إلى Firebase"""
    print("🚀 بدء اختبار رفع ملف مود إلى Firebase Storage...")
    print("=" * 60)
    
    # تهيئة Firebase
    print("🔄 تهيئة Firebase...")
    if not firebase_manager.auto_initialize():
        print("❌ فشل في تهيئة Firebase")
        return False
    
    print("✅ تم تهيئة Firebase بنجاح")
    
    # اختبار الاتصال
    print("🔄 اختبار الاتصال...")
    if not firebase_manager.test_connection():
        print("❌ فشل اختبار الاتصال")
        return False
    
    print("✅ اختبار الاتصال نجح")
    
    # الحصول على ملف مود للاختبار
    print("\n📦 الحصول على ملف مود للاختبار...")
    
    # أولاً: محاولة تحميل ملف حقيقي
    mod_content, mod_filename = download_test_mod()
    
    # إذا فشل التحميل، إنشاء ملف وهمي
    if not mod_content:
        print("⚠️ فشل تحميل ملف حقيقي، سيتم إنشاء ملف وهمي...")
        mod_content, mod_filename = create_dummy_mod()
    
    if not mod_content:
        print("❌ فشل في الحصول على ملف للاختبار")
        return False
    
    # رفع الملف إلى Firebase
    print(f"\n📤 رفع الملف إلى Firebase: {mod_filename}")
    print(f"📊 حجم الملف: {len(mod_content) / 1024:.2f} KB")
    
    try:
        public_url = firebase_manager.upload_mod_to_storage(mod_content, mod_filename)
        
        if public_url:
            print(f"✅ تم رفع الملف بنجاح!")
            print(f"🔗 رابط الملف: {public_url}")
            
            # اختبار الوصول للملف
            print("\n🔍 اختبار الوصول للملف المرفوع...")
            try:
                response = requests.head(public_url, timeout=10)
                if response.status_code == 200:
                    print("✅ الملف متاح للتحميل")
                    print(f"📊 حجم الملف المرفوع: {response.headers.get('content-length', 'غير معروف')} بايت")
                else:
                    print(f"⚠️ مشكلة في الوصول للملف - كود الاستجابة: {response.status_code}")
            except Exception as e:
                print(f"⚠️ خطأ في اختبار الوصول للملف: {e}")
            
            return True
        else:
            print("❌ فشل في رفع الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في رفع الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    success = test_firebase_mod_upload()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 نجح اختبار رفع ملف المود إلى Firebase Storage!")
        print("✅ Firebase Storage جاهز لاستقبال ملفات المودات")
    else:
        print("\n" + "=" * 60)
        print("❌ فشل اختبار رفع ملف المود")
        print("💡 راجع الأخطاء أعلاه وتأكد من إعدادات Firebase")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
