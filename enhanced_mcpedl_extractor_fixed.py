"""
مستخرج MCPEDL المحسن مع إصلاحات لاستخراج الصور
"""

import re
import os
import json
import requests
from typing import Dict, List, Optional, Any, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

class EnhancedMCPEDLExtractor:
    """
    مستخرج محسن لمودات MCPEDL مع تركيز على استخراج الصور بشكل صحيح
    """
    
    def __init__(self, gemini_model=None, enhancements=None):
        """تهيئة المستخرج المحسن"""
        self.gemini_model = gemini_model
        self.enhancements = enhancements
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://mcpedl.com/'
        }
        
        # تحسين محددات CSS
        self.selectors = {
            'title': 'h1.entry-title, h1.post-title, .post-header h1, h1, .entry-header h1, .page-title',
            'description': '.entry-content p, .post-content p, .content p, article p, .post-body p, .entry-summary',
            'category': '.breadcrumbs a:last-of-type, .category-link, .post-categories a, .breadcrumb-item a, nav.breadcrumb a',
            'main_image': '.featured-image img, .post-thumbnail img, .wp-post-image, img[src*="media.forgecdn.net/attachments"]',
            'gallery_images': '.gallery img, .wp-block-gallery img, .entry-content img, .post-content img, .attachment-thumbnail, .size-thumbnail',
            'version': '.supported-versions, .minecraft-version, .version-info, .compatibility, .mcpe-version, .version-support',
            'download_link': '.download-link, .btn-download, a[href*="download"], .download-button, .dl-link, a[href*="mediafire"], a[href*="drive.google"]',
            'file_size': '.file-size, .download-size, .size-info, .filesize',
            'creator': '.author-info, .post-author, .creator-name, .by-author, .author-name, .post-meta .author',
            'social_links': '.social-links a, .author-social a, .contact-links a, .social-media a',
            'downloads_count': '.download-count, .downloads, .download-stats, .dl-count',
            'likes_count': '.likes-count, .rating, .votes, .post-rating'
        }
    
    def extract_mod_data(self, url: str) -> Optional[Dict[str, Any]]:
        """
        استخراج بيانات المود من صفحة MCPEDL
        """
        print(f"🔍 استخراج بيانات المود من: {url}")
        
        try:
            # جلب محتوى الصفحة
            response = requests.get(url, headers=self.headers, timeout=15)
            if response.status_code != 200:
                print(f"❌ فشل في جلب الصفحة: {response.status_code}")
                return None
                
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # استخراج البيانات الأساسية
            mod_name = self._extract_title(soup)
            if not mod_name:
                print("❌ لم يتم العثور على اسم المود")
                return None
                
            print(f"✅ تم العثور على المود: {mod_name}")
            
            # استخراج باقي البيانات
            mod_data = {
                'name': mod_name,
                'url': url,
                'description': self._extract_description(soup),
                'category': self._extract_category(soup),
                'version': self._extract_version(soup),
                'creator': self._extract_creator(soup),
                'download_links': self._extract_download_links(soup, url),
                'file_size': self._extract_file_size(soup),
                'image_urls': self._extract_images_smart(soup, url, mod_name, html_content)
            }
            
            # استخراج الروابط الاجتماعية إذا كان متاحاً
            if self.enhancements and hasattr(self.enhancements, 'extract_social_links'):
                mod_data['social_links'] = self.enhancements.extract_social_links(soup, url)
            
            return mod_data
            
        except Exception as e:
            print(f"❌ خطأ في استخراج بيانات المود: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        for selector in self.selectors['title'].split(', '):
            title_elem = soup.select_one(selector)
            if title_elem:
                return title_elem.get_text().strip()
        return ""
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        description_parts = []
        
        for selector in self.selectors['description'].split(', '):
            desc_elems = soup.select(selector)
            for elem in desc_elems:
                text = elem.get_text().strip()
                if text and len(text) > 20:  # تجاهل النصوص القصيرة جداً
                    description_parts.append(text)
        
        return "\n\n".join(description_parts)
    
    def _extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        for selector in self.selectors['category'].split(', '):
            category_elem = soup.select_one(selector)
            if category_elem:
                category = category_elem.get_text().strip()
                # تنظيف الفئة
                if "addon" in category.lower() or "add-on" in category.lower():
                    return "Addons"
                elif "texture" in category.lower() or "pack" in category.lower():
                    return "Texture Pack"
                elif "shader" in category.lower():
                    return "Shaders"
                else:
                    return category
        
        # محاولة تحديد الفئة من URL
        url_path = urlparse(soup.get('url', '')).path
        if "addon" in url_path.lower():
            return "Addons"
        elif "texture" in url_path.lower():
            return "Texture Pack"
        elif "shader" in url_path.lower():
            return "Shaders"
            
        return "Addons"  # القيمة الافتراضية
    
    def _extract_version(self, soup: BeautifulSoup) -> str:
        """استخراج إصدار المود"""
        for selector in self.selectors['version'].split(', '):
            version_elem = soup.select_one(selector)
            if version_elem:
                return version_elem.get_text().strip()
        
        # البحث عن نمط الإصدار في النص
        version_pattern = r'(1\.\d+\.\d+)'
        for p in soup.select('p'):
            match = re.search(version_pattern, p.get_text())
            if match:
                return match.group(1)
                
        return ""
    
    def _extract_creator(self, soup: BeautifulSoup) -> str:
        """استخراج اسم منشئ المود"""
        for selector in self.selectors['creator'].split(', '):
            creator_elem = soup.select_one(selector)
            if creator_elem:
                return creator_elem.get_text().strip()
        return ""
    
    def _extract_download_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط التحميل"""
        download_links = []
        
        # البحث عن روابط التحميل المباشرة
        for selector in self.selectors['download_link'].split(', '):
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    url = urljoin(base_url, href)
                    if self._is_valid_download_link(url) and url not in download_links:
                        download_links.append(url)
        
        return download_links
    
    def _is_valid_download_link(self, url: str) -> bool:
        """التحقق من صحة رابط التحميل"""
        valid_patterns = [
            'download', 'mediafire', 'drive.google', 'dropbox', 
            '.mcpack', '.mcaddon', '.mcworld', '.zip'
        ]
        return any(pattern in url.lower() for pattern in valid_patterns)
    
    def _extract_file_size(self, soup: BeautifulSoup) -> str:
        """استخراج حجم ملف المود"""
        for selector in self.selectors['file_size'].split(', '):
            size_elem = soup.select_one(selector)
            if size_elem:
                return size_elem.get_text().strip()
        
        # البحث عن نمط حجم الملف في النص
        size_pattern = r'(\d+(\.\d+)?\s*(KB|MB|GB))'
        for p in soup.select('p'):
            match = re.search(size_pattern, p.get_text())
            if match:
                return match.group(1)
                
        return ""
    
    def _extract_images_smart(self, soup: BeautifulSoup, base_url: str, mod_name: str, html_content: str) -> List[str]:
        """استخراج الصور مع فلترة ذكية محسنة"""
        print("🖼️ بدء استخراج الصور بفلترة ذكية...")
        all_images = []
        image_sources = {}  # لتتبع مصدر كل صورة

        # 1. استخراج جميع الصور المحتملة بأولوية
        image_selectors_priority = [
            # أولوية عالية - صور رئيسية
            ('.featured-image img', 'featured'),
            ('.post-thumbnail img', 'thumbnail'),
            ('.mod-main-image img', 'main'),
            ('img[src*="media.forgecdn.net/attachments/description/"]', 'forgecdn_description'), # صور المود الأصلية
            ('img[src*="forgecdn"]', 'forgecdn'),  # صور ForgeCD (موثوقة)
            ('img[src*="media.forgecdn.net"]', 'forgecdn'),  # صور ForgeCD
            ('img[src*="r2.mcpedl.com/submissions"]', 'mcpedl_submission'),  # صور MCPEDL الرسمية

            # أولوية متوسطة - معارض الصور
            ('.gallery img', 'gallery'),
            ('.mod-gallery img', 'mod_gallery'),
            ('.screenshots img', 'screenshots'),
            ('.wp-block-gallery img', 'wp_gallery'),
            ('img[src*="r2.mcpedl.com"]', 'mcpedl'),  # صور MCPEDL

            # أولوية منخفضة - صور المحتوى
            ('.entry-content img', 'content'),
            ('.content img', 'content'),
            ('.post-content img', 'post_content'),
        ]

        # إضافة صور ForgeCDN Description مباشرة باستخدام regex
        forgecdn_desc_pattern = r'https://media\.forgecdn\.net/attachments/\w+/\w+/[a-f0-9-]+\.(?:png|jpe?g|gif|webp)'
        found_forgecdn_desc_images = re.findall(forgecdn_desc_pattern, html_content)
        for url in found_forgecdn_desc_images:
            if self._is_valid_image_url(url) and url not in all_images:
                all_images.append(url)
                image_sources[url] = 'forgecdn_description'
                print(f"   ✅ صورة من forgecdn_description (regex): {url[:60]}...")

        # البحث عن صور MCPEDL الرسمية
        mcpedl_submission_pattern = r'https://r2\.mcpedl\.com/submissions/\d+/images/[a-zA-Z0-9_-]+\.[a-zA-Z]+'
        found_mcpedl_submissions = re.findall(mcpedl_submission_pattern, html_content)
        for url in found_mcpedl_submissions:
            if self._is_valid_image_url(url) and url not in all_images:
                all_images.append(url)
                image_sources[url] = 'mcpedl_submission'
                print(f"   ✅ صورة من mcpedl_submission (regex): {url[:60]}...")

        for selector, source_type in image_selectors_priority:
            images = soup.select(selector)
            for img in images:
                # البحث عن مصدر الصورة بطرق متعددة
                src = (img.get('src') or
                      img.get('data-src') or
                      img.get('data-lazy-src') or
                      img.get('data-original') or
                      img.get('data-srcset', '').split(',')[0].strip().split(' ')[0])

                if src:
                    full_url = urljoin(base_url, src)
                    if self._is_valid_image_url(full_url) and full_url not in all_images:
                        # تجنب صور المودات المقترحة
                        if not self._is_suggested_mod_image(full_url, img):
                            all_images.append(full_url)
                            image_sources[full_url] = source_type
                            print(f"   ✅ صورة من {source_type}: {full_url[:60]}...")

        print(f"📋 تم العثور على {len(all_images)} صورة محتملة")

        # 2. فلترة أولية محسنة
        filtered_images = self._enhanced_pre_filter_images(all_images, image_sources)
        print(f"🔍 بعد الفلترة الأولية المحسنة: {len(filtered_images)} صورة")

        # 3. ترتيب الصور حسب الأولوية
        final_images = self._prioritize_images(filtered_images, image_sources)
        return final_images[:8]  # حد أقصى 8 صور

    def _is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()

        # تجنب الصور الصغيرة والأيقونات
        avoid_patterns = [
            'logo.', 'icon.', 'favicon.', 'avatar', 'profile', 
            'empty.png', 'button', 'banner', 'ad-', 'ads/'
        ]
        
        if any(pattern in url_lower for pattern in avoid_patterns):
            return False

        return any(ext in url_lower for ext in image_extensions)

    def _is_suggested_mod_image(self, url: str, img_tag: Any) -> bool:
        """
        تحديد ما إذا كانت الصورة تنتمي إلى مود مقترح
        """
        # فحص URL للصورة
        suggested_patterns = [
            'empty.png',  # صور فارغة تستخدم غالباً للمودات المقترحة
            'related-', 
            'suggested-'
        ]
        
        if any(pattern in url.lower() for pattern in suggested_patterns):
            return True
        
        # فحص العناصر الأب للصورة
        parent = img_tag.parent
        for _ in range(5):  # فحص حتى 5 مستويات من العناصر الأب
            if not parent:
                break
                
            # فحص النص والفئات للعناصر الأب
            parent_text = parent.get_text().lower() if hasattr(parent, 'get_text') else ''
            parent_class = ' '.join(parent.get('class', [])).lower() if hasattr(parent, 'get') else ''
            
            suggested_section_patterns = [
                'you may also like', 
                'related', 
                'suggested', 
                'similar', 
                'more from'
            ]
            
            if any(pattern in parent_text for pattern in suggested_section_patterns) or \
               any(pattern in parent_class for pattern in suggested_section_patterns):
                return True
                
            parent = parent.parent
        
        return False

    def _enhanced_pre_filter_images(self, image_urls: List[str], image_sources: Dict[str, str]) -> List[str]:
        """فلترة أولية محسنة للصور"""
        filtered = []

        # أنماط محسنة للصور غير المرغوبة
        avoid_patterns = [
            # أيقونات وشعارات واضحة
            'empty.png', 'arrow.svg', 'icon.png', 'logo.png', 'favicon',
            'sprite.png', 'button.png', 'bg.jpg', 'background.jpg',

            # صور المستخدمين
            'gravatar', 'avatar', 'profile', 'comment', 'user-image', 'author-photo', 'users/', # إضافة 'users/' لتجنب صور المستخدمين من mcpedl

            # إعلانات ومحتوى ترويجي
            'advertisement', 'ad-', '/ads/', 'sponsor', 'promo', 'banner',

            # صور صغيرة أو غير مفيدة
            'thumb_', 'thumbnail_', '_thumb', '_small', '_mini',
            '16x16', '32x32', '48x48', '64x64'
        ]

        # أنماط للصور المقترحة (فلترة أقل صرامة)
        suggested_patterns = [
            'related-posts', 'suggested-mods', 'other-mods', 'recommended',
            'similar-addons', 'you-might-like', 'more-from-author'
        ]

        # أنماط للصور المفضلة
        preferred_patterns = [
            'forgecdn.net', 'media.forgecdn.net', 'r2.mcpedl.com/submissions',
            'screenshot', 'gallery', 'preview', 'showcase'
        ]

        for url in image_urls:
            url_lower = url.lower()

            # تجنب الصور غير المرغوبة
            should_avoid = any(pattern in url_lower for pattern in avoid_patterns)
            if should_avoid:
                continue

            # تحديد أولوية الصورة
            source_type = image_sources.get(url, 'general')
            is_preferred = any(pattern in url_lower for pattern in preferred_patterns)
            is_high_priority = source_type in ['featured', 'thumbnail', 'main', 'forgecdn', 'mcpedl_submission']

            # إضافة الصورة مع ترتيب حسب الأولوية
            if is_high_priority or is_preferred:
                filtered.insert(0, url)  # إضافة في المقدمة
            else:
                filtered.append(url)

        return filtered

    def _prioritize_images(self, image_urls: List[str], image_sources: Dict[str, str]) -> List[str]:
        """ترتيب الصور حسب الأولوية"""
        # تصنيف الصور حسب المصدر
        priority_order = {
            'featured': 100,
            'thumbnail': 90,
            'main': 85,
            'forgecdn_description': 80,
            'forgecdn': 75,
            'mcpedl_submission': 70,
            'gallery': 65,
            'mod_gallery': 60,
            'screenshots': 55,
            'wp_gallery': 50,
            'mcpedl': 45,
            'content': 40,
            'post_content': 35,
            'general': 20
        }
        
        # ترتيب الصور حسب الأولوية
        return sorted(
            image_urls,
            key=lambda url: priority_order.get(image_sources.get(url, 'general'), 0),
            reverse=True
        )

    def clean_description(self, description: str) -> str:
        """
        تنظيف الوصف الأساسي من المعلومات غير المرغوبة وتنسيق الفقرات
        """
        if not description:
            return ""

        # إزالة علامات [ENGLISH_DESCRIPTION] و [/ENGLISH_DESCRIPTION]
        description = re.sub(r'\[ENGLISH_DESCRIPTION\]|\[\/ENGLISH_DESCRIPTION\]', '', description)
        
        # تقسيم النص إلى أسطر
        lines = description.split('\n')
        cleaned_lines = []

        # أنماط الأسطر التي يجب تجنبها
        avoid_patterns = [
            r'minecraft pe texture packs',
            r'published on',
            r'updated on',
            r'skip to downloads',
            r'select version for changelog',
            r'changelog',
            r'installation',
            r'downloads',
            r'join discord',
            r'download packs',
            r'supported minecraft versions',
            r'resolutions',
            r'you may also like',
            r'installation guides',
            r'android',
            r'ios',
            r'windows 10',
            r'by\w+',
            r'\d{1,2}\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)',
            r'\d+\.\d+',
            r'mb\)',
            r'manifest\.json',
            r'pack uuid',
            r'mcc\)',
        ]

        for line in lines:
            line = line.strip()
            if not line:
                continue

            line_lower = line.lower()

            # تجنب الأسطر غير المرغوبة
            should_skip = False
            for pattern in avoid_patterns:
                if re.search(pattern, line_lower):
                    should_skip = True
                    break

            if should_skip:
                continue

            # تجنب الأسطر القصيرة جداً أو التي تحتوي على أرقام فقط
            if len(line) < 10 or line.isdigit():
                continue

            # تجنب الأسطر التي تحتوي على معلومات تقنية
            if any(tech in line_lower for tech in ['1.21', '16x', 'mb', 'uuid', 'manifest']):
                continue

            # تجنب الأسطر التي تبدأ بعلامات العناوين
            if re.match(r'^#+\s+', line) or re.match(r'^[A-Z\s]{10,}$', line):
                continue

            cleaned_lines.append(line)

        # دمج الأسطر المنظفة في فقرة واحدة
        cleaned_text = ' '.join(cleaned_lines)
        
        # تنظيف إضافي
        cleaned_text = re.sub(r'\s{2,}', ' ', cleaned_text)  # تقليل المسافات المتعددة
        cleaned_text = re.sub(r'^\s*[\*\-\•]\s*', '', cleaned_text, flags=re.MULTILINE)  # إزالة النقاط
        
        return cleaned_text.strip()