#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لوظيفة رفع الملفات بعد الإصلاحات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_upload_functionality():
    """اختبار وظيفة رفع الملفات"""
    
    print("🧪 اختبار وظيفة رفع الملفات...")
    
    try:
        # استيراد المدير
        from firebase_storage_manager import create_firebase_manager
        
        # إنشاء مدير Firebase
        firebase_manager = create_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في إنشاء مدير Firebase")
            return False
            
        print(f"✅ تم إنشاء مدير Firebase: {type(firebase_manager).__name__}")
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = ['upload_mod_to_storage', 'upload_image_to_storage']
        
        for method in required_methods:
            if hasattr(firebase_manager, method):
                print(f"✅ الدالة {method} متوفرة")
            else:
                print(f"❌ الدالة {method} غير متوفرة")
                return False
        
        # اختبار رفع ملف تجريبي
        test_content = b"This is a test mod file content"
        test_filename = "test_mod.mcpack"
        
        print(f"🔄 اختبار رفع ملف: {test_filename}")
        
        result = firebase_manager.upload_mod_to_storage(test_content, test_filename)
        
        if result:
            print(f"✅ تم رفع الملف بنجاح: {result}")
            return True
        else:
            print("❌ فشل في رفع الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاحات رفع الملفات...")
    
    success = test_upload_functionality()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! رفع الملفات يعمل بشكل صحيح.")
    else:
        print("\n❌ فشل في بعض الاختبارات. يحتاج إلى مراجعة.")
    
    return success

if __name__ == "__main__":
    main()
