# -*- coding: utf-8 -*-
"""
اختبار تحسينات استخراج الصور من MCPEDL
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mcpedl_scraper_module import scrape_mcpedl_mod
    print("✅ تم تحميل وحدة MCPEDL scraper بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل وحدة MCPEDL scraper: {e}")
    sys.exit(1)

def test_image_extraction():
    """اختبار استخراج الصور من رابط MCPEDL"""

    # رابط اختبار (يمكنك تغييره)
    test_url = "https://mcpedl.com/parkour-add-on-by-s7d/"

    print(f"🧪 اختبار استخراج الصور من: {test_url}")
    print("=" * 60)

    try:
        # استخراج البيانات
        mod_data = scrape_mcpedl_mod(test_url)

        if mod_data:
            print("✅ نجح الاستخراج!")
            print(f"📋 اسم المود: {mod_data.get('name', 'غير محدد')}")
            print(f"📂 الفئة: {mod_data.get('category', 'غير محدد')}")
            print(f"👤 المطور: {mod_data.get('creator_name', 'غير محدد')}")
            print(f"📏 الحجم: {mod_data.get('size', 'غير محدد')}")
            print(f"🔢 الإصدار: {mod_data.get('version', 'غير محدد')}")

            # عرض الصور المستخرجة
            images = mod_data.get('image_urls', [])
            print(f"\n🖼️ الصور المستخرجة ({len(images)} صورة):")
            print("-" * 40)

            if images:
                for i, image_url in enumerate(images, 1):
                    print(f"{i:2d}. {image_url}")

                    # تحليل مصدر الصورة
                    if 'media.forgecdn.net' in image_url:
                        print(f"    📍 المصدر: Forgecdn (موثوق)")
                    elif 'r2.mcpedl.com' in image_url:
                        if '/users/' in image_url:
                            print(f"    📍 المصدر: MCPEDL Users (مرفوض)")
                        else:
                            print(f"    📍 المصدر: MCPEDL CDN (موثوق)")
                    elif 'mcpedl.com' in image_url:
                        print(f"    📍 المصدر: MCPEDL (موثوق)")
                    else:
                        print(f"    📍 المصدر: خارجي")
                    print()
            else:
                print("❌ لم يتم العثور على صور")

            # عرض الأوصاف
            print(f"\n📝 الوصف الإنجليزي ({len(mod_data.get('description', ''))} حرف):")
            print("-" * 40)
            print(mod_data.get('description', 'غير متوفر')[:200] + "...")

            if mod_data.get('description_arabic'):
                print(f"\n📝 الوصف العربي ({len(mod_data.get('description_arabic', ''))} حرف):")
                print("-" * 40)
                print(mod_data.get('description_arabic', 'غير متوفر')[:200] + "...")

        else:
            print("❌ فشل في استخراج البيانات")

    except Exception as e:
        print(f"❌ خطأ أثناء الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_image_extraction()
