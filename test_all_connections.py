# -*- coding: utf-8 -*-
"""
ملف اختبار شامل لجميع الاتصالات
يختبر Firebase, Supabase, و Gemini AI
"""

import os
import json
import sys
import traceback
import subprocess

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n📋 {title}")
    print("-" * 40)

def check_python_packages():
    """فحص المكتبات المطلوبة"""
    print_section("فحص المكتبات المطلوبة")
    
    required_packages = {
        'supabase': 'supabase',
        'firebase_admin': 'firebase-admin',
        'google.generativeai': 'google-generativeai',
        'requests': 'requests',
        'json': 'built-in'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            if install_name == 'built-in':
                __import__(package)
                print(f"✅ {package}: متوفر (مدمج)")
            else:
                __import__(package)
                print(f"✅ {package}: متوفر")
        except ImportError:
            print(f"❌ {package}: غير متوفر")
            if install_name != 'built-in':
                missing_packages.append(install_name)
    
    if missing_packages:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_config_files():
    """فحص ملفات الإعدادات"""
    print_section("فحص ملفات الإعدادات")
    
    config_files = {
        'api_keys.json': 'ملف مفاتيح API',
        'firebase-service-account.json': 'ملف خدمة Firebase'
    }
    
    all_files_exist = True
    
    for file_path, description in config_files.items():
        if os.path.exists(file_path):
            print(f"✅ {description}: موجود ({file_path})")
            
            # فحص محتوى الملف
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                if file_path == 'api_keys.json':
                    supabase_key = data.get('SUPABASE_KEY', '')
                    gemini_keys = data.get('gemini_api_keys', [])
                    print(f"  📊 Supabase Key: {'✅ موجود' if supabase_key else '❌ مفقود'}")
                    print(f"  📊 Gemini Keys: {len(gemini_keys)} مفتاح")
                    
                elif file_path == 'firebase-service-account.json':
                    project_id = data.get('project_id', '')
                    client_email = data.get('client_email', '')
                    print(f"  📊 Project ID: {project_id}")
                    print(f"  📊 Client Email: {client_email}")
                    
            except Exception as e:
                print(f"  ⚠️ خطأ في قراءة الملف: {e}")
                
        else:
            print(f"❌ {description}: غير موجود ({file_path})")
            all_files_exist = False
    
    return all_files_exist

def run_test_script(script_name, description):
    """تشغيل سكريبت اختبار"""
    print_section(f"تشغيل {description}")
    
    if not os.path.exists(script_name):
        print(f"❌ ملف الاختبار غير موجود: {script_name}")
        return False
    
    try:
        # تشغيل السكريبت
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, encoding='utf-8')
        
        print("📤 مخرجات الاختبار:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ رسائل الخطأ:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description}: نجح")
            return True
        else:
            print(f"❌ {description}: فشل (كود الخروج: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل {description}: {e}")
        return False

def test_gemini_connection():
    """اختبار الاتصال بـ Gemini AI"""
    print_section("اختبار Gemini AI")
    
    try:
        # تحميل مفاتيح API
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        gemini_keys = config.get('gemini_api_keys', [])
        if not gemini_keys:
            print("❌ لا توجد مفاتيح Gemini في ملف الإعدادات")
            return False
        
        print(f"🔑 عدد مفاتيح Gemini: {len(gemini_keys)}")
        
        # اختبار استيراد المكتبة
        import google.generativeai as genai
        print("✅ تم استيراد مكتبة Gemini بنجاح")
        
        # اختبار أول مفتاح
        test_key = gemini_keys[0]
        print(f"🔄 اختبار المفتاح الأول: {test_key[:20]}...")
        
        genai.configure(api_key=test_key)
        model = genai.GenerativeModel('gemini-pro')
        
        # اختبار بسيط
        response = model.generate_content("مرحبا")
        print("✅ نجح الاتصال بـ Gemini AI")
        print(f"📝 رد الاختبار: {response.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال بـ Gemini AI: {e}")
        return False

def generate_summary_report(results):
    """إنشاء تقرير ملخص"""
    print_header("تقرير ملخص الاختبارات")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 إجمالي الاختبارات: {total_tests}")
    print(f"✅ نجح: {passed_tests}")
    print(f"❌ فشل: {failed_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 تفاصيل النتائج:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
    
    # توصيات
    print("\n💡 التوصيات:")
    if not results.get('packages', True):
        print("  - قم بتثبيت المكتبات المفقودة")
    if not results.get('config_files', True):
        print("  - تأكد من وجود ملفات الإعدادات")
    if not results.get('supabase', True):
        print("  - راجع إعدادات Supabase ومفاتيح API")
    if not results.get('firebase', True):
        print("  - راجع إعدادات Firebase وملف service account")
    if not results.get('gemini', True):
        print("  - راجع مفاتيح Gemini AI")

def main():
    """الدالة الرئيسية"""
    print_header("اختبار شامل لجميع الاتصالات")
    
    results = {}
    
    # فحص المكتبات
    results['packages'] = check_python_packages()
    
    # فحص ملفات الإعدادات
    results['config_files'] = check_config_files()
    
    # اختبار Supabase
    results['supabase'] = run_test_script('test_supabase_connection.py', 'اختبار Supabase')
    
    # اختبار Firebase
    results['firebase'] = run_test_script('test_firebase_connection.py', 'اختبار Firebase')
    
    # اختبار Gemini
    results['gemini'] = test_gemini_connection()
    
    # إنشاء التقرير
    generate_summary_report(results)
    
    # حفظ النتائج في ملف
    try:
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 تم حفظ نتائج الاختبار في: test_results.json")
    except Exception as e:
        print(f"\n⚠️ فشل حفظ نتائج الاختبار: {e}")

if __name__ == "__main__":
    main()
