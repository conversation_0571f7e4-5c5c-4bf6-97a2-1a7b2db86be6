﻿## Comments can be added anywhere on a valid line by starting with '##'
##
## Note, trailing spaces will NOT be trimmed. If you want room between the end of the string and the start of a
## comment on the same line, use TABs.
accessibility.disableTTS=Text To Speech disabled
accessibility.enableTTS=Text To Speech enabled

## Translator note:
## This section of phrases will be given audibly to the user using text to speech

##Bonsai Add-on texts---------------------------------------------------------------------------------------------------------
item.bonsai:pot.name=Bonsai Pot
item.bonsai:acacia_leaves.name=Acacia Leaves
item.bonsai:acacia_logs.name=Acacia Logs
item.bonsai:dark_oak_logs.name=Dark Oak Logs
item.bonsai:dark_oak_leaves.name=Dark Oak Leaves
item.bonsai:jungle_logs.name=Jungle Logs
item.bonsai:jungle_leaves.name=Jungle Leaves
item.bonsai:birch_logs.name=Birch Logs
item.bonsai:birch_leaves.name=Birch Leaves
item.bonsai:oak_logs.name=Oak Logs
item.bonsai:oak_leaves.name=Oak Leaves
item.bonsai:spruce_logs.name=Spruce Logs
item.bonsai:spruce_leaves.name=Spruce Leaves

tile.Bonsai acacia.name=Bonsai Acacia
tile.Bonsai birch.name=Bonsai Birch
tile.Bonsai brown Mushroom.name=Bonsai Brown Mushroom
tile.Bonsai crimson.name=Bonsai Crimson
tile.Bonsai dark_oak.name=Bonsai Dark Oak
tile.Bonsai jungle.name=Bonsai jungle
tile.Bonsai oak.name=Bonsai Oak
tile.Bonsai spruce.name=Bonsai Spruce
tile.Bonsai warped.name=Bonsai Warped
tile.Bonsai Red Mushroom.name=Bonsai Red Mushroom

item.Sheared Bonsai vines.name=Sheared Bonsai Vines
item.Sheared Bonsai spore_blossom.name=Sheared Bonsai Spore Blossom
item.Sheared Bonsai glow_lichen.name=Sheared Bonsai Glow Lichen
item.Sheared Bonsai drip_leaf.name=Sheared Bonsai Drip Leaf
item.Sheared Bonsai azalea.name=Sheared Bonsai Azalea
tile.Sheared Bonsai acacia.name=Sheared Bonsai Acacia
tile.Sheared Bonsai birch.name=Sheared Bonsai Birch
tile.Sheared Bonsai brown Mushroom.name=Sheared Bonsai Brown Mushroom
tile.Sheared Bonsai crimson.name=Sheared Bonsai Crimson
tile.Sheared Bonsai dark_oak.name=Sheared Bonsai Dark Oak
tile.Sheared Bonsai jungle.name=Sheared Bonsai jungle
tile.Sheared Bonsai oak.name=Sheared Bonsai Oak
tile.Sheared Bonsai spruce.name=Sheared Bonsai Spruce
tile.Sheared Bonsai warped.name=Sheared Bonsai Warped
tile.Sheared Bonsai Red Mushroom.name=Sheared Bonsai Red Mushroom
tile.Sheared Bonsai glow berries.name=Sheared Bonsai Glow Berries
tile.Sheared Bonsai bamboo.name=Sheared Bonsai Bamboo
tile.Sheared Bonsai sugar cane.name=Sheared Bonsai Sugar Cane

##----------------------------------------------------------------------------------------------------------------------------
accessibility.chat.tts.says=%s says %s
accessibility.chat.tts.hideChat=Hide chat
accessibility.chat.tts.keyboard=Keyboard
accessibility.chat.tts.muteChatToggle=Mute All
accessibility.chat.tts.sendChatMessage=Send
accessibility.chat.tts.textboxTitle=Chat

accessibility.loading.done=Done
accessibility.saving.done=Done
accessibility.downloading.start=Download begun
accessibility.downloading.Progress=Downloading %s percent
accessibility.downloading.canceled=Download canceled
accessibility.downloading.complete=Download complete
accessibility.importing.start=Import begun
accessibility.importing.Progress=Importing %s percent
accessibility.importing.canceled=Import canceled
accessibility.importing.complete=Import complete
accessibility.signin.xbl=Signing into X box live
accessibility.chat.howtoopen=Press %s to open chat
accessibility.key.mouseButton=Mouse Button %s
accessibility.key.gamepad=Controller %s
accessibility.list.or.two=%s or %s
accessibility.list.or.three_or_more.first=%s
accessibility.list.or.three_or_more.inner=, %s
accessibility.list.or.three_or_more.last=, or %s

accessibility.button.tts.title=Button
accessibility.checkbox.tts.title=Checkbox
accessibility.checkbox.tts.status=status %s		## Status will be checked or unchecked
accessibility.dropdown.tts.title=Dropdown
accessibility.slider.tts.title=Slider
accessibility.tab.tts.title=Tab
accessibility.textbox.tts.title=Textbox
accessibility.toggle.tts.title=Toggle

accessibility.button.close=close
accessibility.button.back=back
accessibility.button.exit=Exit

accessibility.dropdown.listItem=List item

accessibility.sectionName=Section %s: 

accessibility.state.on=: on
accessibility.state.off=: off

accessibility.state.checked=: checked
accessibility.state.unchecked=: unchecked

accessibility.state.selected=: selected
accessibility.state.unselected=: unselected

accessibility.index = %s of %s  # This is numbering, which number out of the total number of objects is available for interaction. Example: 1 of 3

accessibility.slider.tts.percentValue=%s percent

accessibility.play.editWorld=Edit World
accessibility.play.editRealm=Edit Realm
accessibility.play.editServer=Edit Server
accessibility.play.importWorld=Import World
accessibility.play.leaveRealm=Leave Realm
accessibility.play.realmFeed=Realm Feed
accessibility.play.tabLeft=Press the %s to tab left
accessibility.play.tabRight=Press the %s to tab right
accessibility.play.legacyWorldsDelete=Delete Legacy World

accessibility.pause.permissions=Permissions

accessibility.screenName.achievements=Achievements Screen
accessibility.screenName.chat=Chat Screen
accessibility.screenName.createNew=Create New Screen
accessibility.screenName.createWorld=Create World Settings Screen
accessibility.screenName.editWorldSettings=Edit World Settings Screen
accessibility.screenName.error=Error Screen
accessibility.screenName.howToPlay=How To Play Screen
accessibility.screenName.modalDialog=Popup dialog
accessibility.screenName.patchNotes=Patch Notes Screen
accessibility.screenName.pauase=Pause Screen
accessibility.screenName.play=Play Screen
accessibility.screenName.realmSettings=Realm Settings Screen
accessibility.screenName.settings=Game Settings Screen
accessibility.screenName.skinPicker=Choose Skin Screen
accessibility.screenName.start=Main Menu
accessibility.screenName.vrAlignment=VR Alignment Screen 

accessibility.settings.reset=reset
accessibility.settings.chooseSeed=Choose Seed

accessibility.start.feedback=Submit Feedback
accessibility.start.new=New
accessibility.start.profile=Choose Profile
accessibility.start.skinPicker=Choose Skin

accessibility.store.tag=Tag

accessibility.textbox.editing=Editing

accessibility.text.period=Period
accessibility.text.comma=Comma
accessibility.text.colon=Colon
accessibility.text.apostrophe=Apostrophe
accessibility.text.semiColon=Semi Colon
accessibility.text.questionMark=Question Mark
accessibility.text.quotationMark=Quotation Mark
accessibility.text.space=Space
accessibility.text.tilde=Tilde
accessibility.text.graveAccent=Grave Accent
accessibility.text.lessThan=Less Than
accessibility.text.greaterThan=Greater Than
accessibility.text.leftBrace=Left Brace
accessibility.text.rightBrace=Right Brace
accessibility.text.rightBracket=Right Bracket
accessibility.text.leftBracket=Left Bracket
accessibility.text.verticalBar=Vertical Bar
accessibility.text.forwardSlash=Slash
accessibility.text.backwardSlash=Back Slash

accessibility.text.exclamationPoint=Exclamation Point
accessibility.text.atSign=At
accessibility.text.hashTag=Hash Tag
accessibility.text.dollarSign=Dollar
accessibility.text.percent=Percent
accessibility.text.caret=Caret
accessibility.text.ampersand=And
accessibility.text.asterisk=Asterisk
accessibility.text.leftParenthesis=Left Parenthesis
accessibility.text.rightParenthesis=Right Parenthesis
accessibility.text.hyphen=Hyphen
accessibility.text.underScore=Under Score
accessibility.text.plus=Plus
accessibility.text.equals=Equals

accessibility.text.unsupported=Unknown Character

accessibility.toast=Notification	## This is the name of the popup that appears when a player gets a notification for a game invite or achievement unlocked

accessibility.worldTemplates.help=Help

accessibility.popup.title=Turn Off Screen Reader?
accessibility.popup.message.line1=Welcome to Minecraft!
accessibility.popup.message.default=On your device, screen reader support is enabled by default.
accessibility.popup.message.platform=We've detected that your system has a screen reader enabled and have automatically enabled Minecraft's screen reader.
accessibility.popup.message.touch=Touch and drag to find controls on the screen, and double tap controls to select. To scroll on a screen, double tap and hold while swiping up or down.
accessibility.popup.message.xbl=We've detected that your account has "Let games read to me" enabled and have automatically enabled Minecraft's screen reader.
accessibility.popup.message.line3=Would you like to turn this off?
accessibility.popup.left_button_text=Turn Off
accessibility.popup.right_button_text=Leave On

accessibility.controllerLayoutScreen.buttonRemapped=%s is now bound to %s
accessibility.controllerLayoutScreen.buttonBoundTo=%s button: %s
accessibility.controllerLayoutScreen.buttonRemapping=Select input to bind for %s

accessibility.gamepad.faceButton.down=A Button
accessibility.gamepad.faceButton.right=B Button
accessibility.gamepad.faceButton.left=X Button
accessibility.gamepad.faceButton.up=Y Button
accessibility.gamepad.button.systemLeft=View Button
accessibility.gamepad.button.systemRight=Menu Button
accessibility.gamepad.dpad.down=D-Pad Down
accessibility.gamepad.dpad.up=D-Pad Up
accessibility.gamepad.dpad.left=D-Pad Left
accessibility.gamepad.dpad.right=D-Pad Right
accessibility.gamepad.stick.left=Left Stick
accessibility.gamepad.stick.right=Right Stick
accessibility.gamepad.trigger.right=Right Trigger
accessibility.gamepad.trigger.left=Left Trigger
accessibility.gamepad.bumper.right=Right Bumper
accessibility.gamepad.bumper.left=Left Bumper

accessibility.keyboard.leftBracket=Left square bracket key		## left square bracket means '[' on the english keyboard
accessibility.keyboard.rightBracket=Right square bracket key	## right square bracket means ']' on the english keyboard

accessibility.keyboard.leftBracket=Left square bracket key		## left square bracket means '[' on the english keyboard
accessibility.keyboard.rightBracket=Right square bracket key	## right square bracket means ']' on the english keyboard

## End of audible phrases section

accounts.name=Name: %s (%s)
accounts.signedInAs=Signed in as
accounts.signOutConfirmation=Do you want to sign out and switch accounts?
accounts.switch=Switch Accounts

achievement.alternativeFuel=Alternative Fuel
achievement.alternativeFuel.desc=Power a furnace with a kelp block
achievement.acquireIron=Acquire Hardware
achievement.acquireIron.desc=Smelt an iron ingot
achievement.bakeCake=The Lie
achievement.bakeCake.desc=Wheat, sugar, milk and eggs!
achievement.blaze_rod=Into Fire
achievement.blaze_rod.desc=Relieve a Blaze of its rod
achievement.bookcase=Librarian
achievement.bookcase.desc=Build some bookshelves to improve your enchantment table
achievement.breedCow=Repopulation
achievement.breedCow.desc=Breed two cows with wheat
achievement.buildBetterPickaxe=Getting an Upgrade
achievement.buildBetterPickaxe.desc=Construct a better pickaxe
achievement.buildFurnace=Hot Topic
achievement.buildFurnace.desc=Construct a furnace out of eight stone blocks
achievement.buildHoe=Time to Farm!
achievement.buildHoe.desc=Use planks and sticks to make a hoe
achievement.buildPickaxe=Time to Mine!
achievement.buildPickaxe.desc=Use planks and sticks to make a pickaxe
achievement.buildSword=Time to Strike!
achievement.buildSword.desc=Use planks and sticks to make a sword
achievement.buildWorkBench=Benchmarking
achievement.buildWorkBench.desc=Craft a workbench with four blocks of planks
achievement.cookFish=Delicious Fish
achievement.cookFish.desc=Catch and cook fish!
achievement.diamonds=DIAMONDS!
achievement.diamonds.desc=Acquire diamonds with your iron tools
achievement.diamondsToYou=Diamonds to you!
achievement.diamondsToYou.desc=Throw diamonds at another player.
achievement.enchantments=Enchanter
achievement.enchantments.desc=Use a book, obsidian and diamonds to construct an enchantment table
achievement.exploreAllBiomes=Adventuring Time
achievement.exploreAllBiomes.desc=Discover all biomes
achievement.flyPig=When Pigs Fly
achievement.flyPig.desc=Fly a pig off a cliff
achievement.fullBeacon=Beaconator
achievement.fullBeacon.desc=Create a full beacon
achievement.get=Achievement get!
achievement.ghast=Return to Sender
achievement.ghast.desc=Destroy a Ghast with a fireball
achievement.killCow=Cow Tipper
achievement.killCow.desc=Harvest some leather
achievement.killEnemy=Monster Hunter
achievement.killEnemy.desc=Attack and destroy a monster
achievement.killWither=The Beginning.
achievement.killWither.desc=Kill the Wither
achievement.makeBread=Bake Bread
achievement.makeBread.desc=Turn wheat into bread
achievement.mineWood=Getting Wood
achievement.mineWood.desc=Attack a tree until a block of wood pops out
achievement.notification.description=Achievement unlocked
achievement.onARail=On A Rail
achievement.onARail.desc=Travel by minecart at least 1 km from where you started
achievement.openInventory=Taking Inventory
achievement.openInventory.desc=Press '%1$s' to open your inventory.
achievement.overkill=Overkill
achievement.overkill.desc=Deal nine hearts of damage in a single hit
achievement.overpowered=Overpowered
achievement.overpowered.desc=Build a Notch apple
achievement.portal=We Need to Go Deeper
achievement.portal.desc=Build a portal to the Nether
achievement.potion=Local Brewery
achievement.potion.desc=Brew a potion
achievement.requires=Requires '%1$s'
achievement.snipeSkeleton=Sniper Duel
achievement.snipeSkeleton.desc=Kill a skeleton with an arrow from more than 50 meters
achievement.spawnWither=The Beginning?
achievement.spawnWither.desc=Spawn the Wither
achievement.taken=Taken!
achievement.theEnd=The End?
achievement.theEnd.desc=Locate the End
achievement.theEnd2=The End.
achievement.theEnd2.desc=Defeat the Ender Dragon
achievement.unknown=???
achievement.uninitScore=--

achievementScreen.achievements:=Achievements:
achievementScreen.gamerscore:=Gamerscore:
achievementScreen.hour=%d hour
achievementScreen.hours=%d hours
achievementScreen.timeplayed:=Time played:
achievementScreen.fetchingAchievements=Fetching Achievements...

action.hint.exit.boat=Tap jump to exit the boat
action.hint.exit.minecart=Tap jump to exit the minecart
action.hint.exit.pig=Tap sneak to dismount
action.hint.exit.horse=Tap sneak to dismount
action.hint.exit.donkey=Tap sneak to dismount
action.hint.exit.mule=Tap sneak to dismount
action.hint.exit.llama=Tap sneak to dismount
action.hint.exit.skeleton_horse=Tap sneak to dismount
action.interact.creeper=Ignite
action.interact.edit=Edit
action.interact.exit.boat=Leave boat
action.interact.feed=Feed
action.interact.fishing=Fish
action.interact.milk=Milk
action.interact.mooshear=Shear
action.interact.moostew=Milk Stew
action.interact.ride.boat=Board
action.interact.ride.minecart=Ride
action.interact.ride.horse=Ride
action.interact.shear=Shear
action.interact.sit=Sit
action.interact.stand=Stand
action.interact.talk=Talk
action.interact.tame=Tame
action.interact.dye=Dye
action.interact.cure=Cure
action.interact.opencontainer=Open
action.interact.createMap=Create Map
action.interact.takepicture=Take Picture
action.interact.saddle=Saddle
action.interact.mount=Mount
action.interact.boost=Boost
action.interact.write=Write
action.interact.leash=Leash
action.interact.unleash=Unleash
action.interact.name=Name
action.interact.attachchest=Attach Chest
action.interact.trade=Trade
action.interact.armorstand.pose=Pose
action.interact.armorstand.equip=Equip
action.interact.read=Read
action.interact.wakevillager=Wake Villager

advMode.allEntities=@e = all entities
advMode.allPlayers=@a = all players
advMode.command=Command Input
advMode.nearestPlayer=@p = nearest player
advMode.notAllowed=Must be an opped player in creative mode
advMode.notEnabled=Command blocks are not enabled on this server
advMode.previousOutput=Previous Output
advMode.randomPlayer=@r = random player
advMode.self=@s = self
advMode.setCommand=Set Console Command for Block
advMode.setCommand.success=Command set: %s

apple.iCloudDisabled.title=Don't Lose Your Worlds!
apple.iCloudDisabled.message=Your worlds are not getting saved properly. They might not be here next time you play Minecraft. Go to your Apple TV settings and turn on iCloud to make sure all of your worlds get saved.
apple.iCloudDisabled.button.turnOnICloud=Turn on iCloud
apple.iCloudNoSpace.message=You do not have enough iCloud space free to save your worlds properly. They might not be here next time you play Minecraft. Free up space on your iCloud account to make sure all of your worlds get saved.
apple.iCloudNoSpace.button.manageICloud=Manage iCloud
apple.iCloudNoInternet.message=You need an internet connection to save your worlds properly. They might not be here next time you play Minecraft. Reconnect to the internet to make sure all of your worlds get saved.
apple.iCloudSignInRequired.title=Sign In
apple.iCloudSignInRequired.message=You need to be signed in to iCloud to play Minecraft. Go to your Apple TV settings and turn on iCloud.
apple.iCloudUserChanged.message=A new iCloud account has signed in. You will need to restart Minecraft to play.

attribute.modifier.plus.0=+%d %s
attribute.modifier.plus.1=+%d%% %s
attribute.modifier.plus.2=+%d%% %s
attribute.modifier.take.0=-%d %s
attribute.modifier.take.1=-%d%% %s
attribute.modifier.take.2=-%d%% %s
attribute.name.minecraft:attack_damage=Attack Damage
attribute.name.minecraft:follow_range=Mob Follow Range
attribute.name.minecraft:knockback_resistance=Knockback Resistance
attribute.name.generic.maxHealth=Max Health
attribute.name.generic.attackDamage=Attack Damage
attribute.name.minecraft:movement=Speed
attribute.name.horse.jumpStrength=Horse Jump Strength
attribute.name.zombie.spawnReinforcements=Zombie Reinforcements

attribution.goBack=Go Back
attribution.viewAttribution=To view attributions, please visit https://minecraft.net/attribution in any web browser.

authentication.demo.body.default=Starting the demo experience...
authentication.demo.body.error=We're sorry, but this lesson is not currently available.
authentication.demo.title=Loading the Demo
authentication.demo.title.error=Lesson Not Available
authentication.pleaseSignIn=You must sign in with your School account in order to play Minecraft: Education Edition.
authentication.loggingin=Signing in...
authentication.signIn=Sign in
authentication.signIn.tryAgain=Try again
authentication.signingInTo=Signing into %s
authentication.unableToConnect=Unable to Connect
authentication.unauthenticated=This account is not eligible to use Minecraft: Education Edition.
authentication.location=For more information:
authentication.hyperlink=http://education.minecraft.net/eligibility
authentication.tryagain=Sign in with a different account
authentication.welcome=Welcome, %s!
authentication.exitingGame=Goodbye, come back soon.
authentication.trialMessageTitle=Minecraft: Education Edition Free Trial
authentication.trialWelcome=Welcome! You have %d trials of Minecraft: Education Edition before your school will need to purchase a license. Every time you open Minecraft: Education Edition, you will use one trial. Enjoy!
authentication.trialWarning=You have %d trials of Minecraft: Education Edition remaining after this session. After these trial plays, your school will need to purchase a license. Thanks!
authentication.trialEnded=Your trial of Minecraft: Education Edition is complete. Your school will need to purchase a license for you to continue to use Minecraft: Education Edition. Thanks!
authentication.trialEndedTitle=Trial Ended
authentication.clickToPurchaseMessage=Ask your Educator to purchase a license for you at: 
authentication.clickToPurchaseStudent=http://aka.ms/meestore
authentication.clickToPurchaseTeacher=Purchase license
authentication.adalException=We can't connect to the service you need right now. Please check your internet connection and try again.
authentication.buyMinecraft=Go to the App Store
authentication.educationOnly=If you are not an education user, go to the app store to download the standard version.
authentication.minecraftInstead=App Store
authentication.signInButton=Sign in with your school account
authentication.signInRequired=Sign-in Required
authentication.store.confirm.button=Confirm
authentication.store.confirmPurchase=Confirm Purchase
authentication.store.intro=Your trial of Minecraft: Education Edition is complete. To continue playing Minecraft: Education Edition, you will need to purchase a license.
authentication.store.popup.purchaseFailed.title=Something Went Wrong
authentication.store.popup.purchaseFailed.msg=Sorry, we were unable to complete your purchase. Maybe check your internet connection?
authentication.store.purchase.info1=The payment will be charged to your iTunes account at confirmation of purchase and the subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period. Your account will be charged for renewal within 24-hours prior to the end of the current period, at the subscription price option you have previously selected. 
authentication.store.purchase.info2=You can manage your subscription and turn off auto-renewal by accessing your iTunes Account settings after purchase. If you cancel after your subscription has activated, you won't be refunded for the remaining active period of the subscription. Your iTunes account will be linked to your O365 Education Account for this subscription, so you will not be eligible to purchase additional Minecraft: Education Edition licenses with this iTunes account.
authentication.store.purchase.button=Purchase license (%s/year)
authentication.store.terms=Terms of Use
authentication.store.viewTermsAndConditions=Terms and Conditions
authentication.store.viewPrivacyPolicy=Privacy Policy
authentication.toast.refreshFailed.title=Account
authentication.toast.refreshFailed.body=There's something wrong with your account info. Please sign in again to continue using multiplayer features.

eula.intro=To use Minecraft: Education Edition you must accept the End-User Licensing Agreement.
eula.location=The EULA is at:
eula.hyperlink=http://education.minecraft.net/eula
eula.title=End-User Licensing Agreement
eula.view=View EULA
eula.callToAction=Click Accept to accept the terms of this agreement.
eula.acceptButton=Accept

book.byAuthor=by
book.defaultAuthor=Author Unknown
book.editTitle=Enter Book Title:
book.export=Export
book.finalizeButton=Sign and Close
book.finalizeWarning=Note! When you sign the book, it will no longer be editable.
book.generation.0=Original
book.generation.1=Copy of original
book.generation.2=Copy of a copy
book.generation.3=Tattered
book.pageIndicator=Page %1$s of %2$s
book.signButton=Sign
book.titleHere=[Enter title here]

build.tooHigh=Height limit for building is %s blocks

chalkboardScreen.header=Edit Text
chalkboardScreen.locked=Locked
chalkboardScreen.unlocked=Unlocked

chat.cannotSend=Cannot send chat message
chat.copy=Copy to Clipboard
chat.link.confirm=Are you sure you want to open the following website?
chat.link.confirmTrusted=Do you want to open this link or copy it to your clipboard?
chat.link.open=Open in browser
chat.link.warning=Never open links from people that you don't trust!
chat.mentions.autocomplete.allPlayers=mention all players
chat.mute=Mute Chat
chat.realmsFilterDisabled=This Realm has chat filtering turned off.
chat.settings=Chat Settings
chat.settings.defaultChatColor=Default Chat Color
chat.settings.chatColor=Chat Color
chat.settings.chatFont=Chat Font
chat.settings.color=Color
chat.settings.font=Font
chat.settings.fontColor=Font Color
chat.settings.fontSize=Size: %s
chat.settings.fontSize.disabled=Size: Available with %s
chat.settings.lineSpacing=Line Spacing
chat.settings.lineSpacingNumber=x%s
chat.settings.mentions=My Mentions
chat.settings.mentionsColor=My Mentions Color
chat.settings.muteAll=Mute All Chat
chat.settings.unmuteAll=Unmute All Chat
chat.settings.tts=Text To Speech For Chat
chat.stream.emote=(%s) * %s %s
chat.stream.text=(%s) <%s> %s
chat.title=Chat
chat.title.cheats=Chat and Commands
chat.type.achievement=%s has just earned the achievement %s
chat.type.achievement.taken=%s has lost the achievement %s
chat.type.admin=[%s: %s]
chat.type.announcement=[%s] %s
chat.type.emote=* %s %s
chat.type.sleeping=%s is sleeping in a bed. To skip to dawn, all players need to sleep in beds at the same time.
chat.type.text=<%s> %s
chat.renamed=You have been temporarily renamed to '%s' on this server

chestScreen.header.large=Large Chest
chestScreen.header.player=Inventory
chestScreen.header.small=Chest

chooseRealmScreen.header=Choose a Realm Server
chooseRealmScreen.realmsplusbuttontext=Add a 10 player Realm
chooseRealmScreen.realmsbuttontext=Add a 2 player Realm

customTemplatesScreen.header=Imported Templates

craftingScreen.tab.search=All
craftingScreen.tab.search.filter=Craftable
craftingScreen.tab.construction=Construction
craftingScreen.tab.nature=Nature
craftingScreen.tab.equipment=Equipment
craftingScreen.tab.items=Items
craftingScreen.tab.survival=Inventory
craftingScreen.tab.armor=Armor


credits.skip=Skip

cauldronScreen.header=Cauldron

codeScreen.chooseEditor=Choose your editor:
codeScreen.editor.description.makeCode=Microsoft MakeCode brings computer science to life with Minecraft
codeScreen.editor.description.tynker=Mod your world with Tynker! Build mini-games, create instant structures, and make your own mods with code.
codeScreen.memoryWarning=This device does not meet the recommended memory amount. Editors may not work as expected.
codeScreen.memoryError=Error in editor process, check if your device has enough memory.
codeScreen.needCheats=Cheats must be enabled in the level to code!
codeScreen.networkError=Unable to connect to network, check your network settings.
codeScreen.resetWarning=Are you sure you want to reset Code Builder? All unsaved work will be lost...
codeScreen.title=Code Builder
codeScreen.buttonTTS.splitLeft=Move Left
codeScreen.buttonTTS.splitRight=Move Right
codeScreen.buttonTTS.selectEditor=Select Editor
codeScreen.buttonTTS.maximize=Maximize
codeScreen.buttonTTS.restore=Restore

color.black=Black
color.dark_blue=Dark Blue
color.dark_green=Dark Green
color.dark_aqua=Dark Aqua
color.dark_red=Dark Red
color.dark_purple=Dark Purple
color.gold=Gold
color.gray=Gray
color.dark_gray=Dark Gray
color.blue=Blue
color.green=Green
color.aqua=Aqua
color.red=Red
color.light_purple=Light Purple
color.yellow=Yellow
color.white=White

commandBlockScreen.blockType=Block Type:
commandBlockScreen.blockType.impulse=Impulse
commandBlockScreen.blockType.chain=Chain
commandBlockScreen.blockType.repeat=Repeat
commandBlockScreen.condition=Condition:
commandBlockScreen.condition.conditional=Conditional
commandBlockScreen.condition.unconditional=Unconditional
commandBlockScreen.redstone=Redstone:
commandBlockScreen.redstone.needs_redstone=Needs Redstone
commandBlockScreen.redstone.always_on=Always Active
commandBlockScreen.tickDelay=Delay in Ticks:
commandBlockScreen.executeFirstTick=Execute on First Tick
commandBlockScreen.displayOutputMode=O
commandBlockScreen.hideOutputMode=X
commandBlockScreen.hoverNote=Hover Note
commandBlockScreen.title=Command Block

seargeSays.searge=Searge says: %s
seargeSays.searge1=Yolo
seargeSays.searge2=/achievement take achievement.understandCommands @p
seargeSays.searge3=Ask for help on twitter
seargeSays.searge4=/deop @p
seargeSays.searge5=Scoreboard deleted, commands blocked
seargeSays.searge6=Contact helpdesk for help
seargeSays.searge7=/testfornoob @p
seargeSays.searge8=/trigger warning
seargeSays.searge9=Oh my god, it's full of stats
seargeSays.searge10=/kill @p[name=!Searge]
seargeSays.searge11=Have you tried turning it off and on again?
seargeSays.searge12=Sorry, no help today

commandBlock.shortName=@
commandBlock.genericName=Command Block

commands.ability.description=Sets a player's ability.
commands.ability.noability=No ability called '%1$s' is available
commands.ability.granted=The '%1$s' ability has been granted to you
commands.ability.revoked=The '%1$s' ability has been revoked from you
commands.ability.success=Ability has been updated
commands.achievement.alreadyHave=Player %1$s already has achievement %2$s
commands.achievement.description=Gives or removes an achievement from a player.
commands.achievement.dontHave=Player %1$s doesn't have achievement %2$s
commands.achievement.give.success.all=Successfully given all achievements to %1$s
commands.achievement.give.success.one=Successfully given %1$s the stat %2$s
commands.achievement.statTooLow=Player %1$s does not have the stat %2$s
commands.achievement.take.success.all=Successfully taken all achievements from %1$s
commands.achievement.take.success.one=Successfully taken the stat %1$s from %2$s
commands.achievement.unknownAchievement=Unknown achievement or statistic '%1$s'
commands.agent.attack.success=Agent attack successful
commands.agent.attack.failed=Agent failed to attack
commands.agent.collect.success=Agent collect successful
commands.agent.collect.failed=Agent failed to collect
commands.agent.createagent.success=Created Agent
commands.agent.createagent.failed=Unable to create Agent
commands.agent.destroy.success=Agent destroyed a block
commands.agent.destroy.failed=Agent destroy failed
commands.agent.detect.success=Agent detect successful
commands.agent.detect.failed=Agent failed to detect
commands.agent.detectredstone.success=Agent detectredstone successful
commands.agent.detectredstone.failed=Agent failed to detectredstone
commands.agent.drop.success=Agent drop successful
commands.agent.drop.failed=Agent failed to drop
commands.agent.dropall.success=Agent dropall successful
commands.agent.dropall.failed=Agent failed to dropall
commands.agent.getitemcount.success=Agent getitemcount successful
commands.agent.getitemcount.failed=Agent failed to getitemcount
commands.agent.getitemspace.success=Agent getitemspace successful
commands.agent.getitemspace.failed=Agent failed to getitemspace
commands.agent.getitemdetail.success=Agent getitemdetail successful
commands.agent.getitemdetail.failed=Agent failed to getitemdetail
commands.agent.getposition.success=Agent getposition successful
commands.agent.getposition.failed=Agent getposition failed
commands.agent.inspect.success=Agent inspect successful
commands.agent.inspect.failed=Agent failed to inspect
commands.agent.inspectdata.success=Agent inspect data successful
commands.agent.inspectdata.failed=Agent failed to inspect data
commands.agent.move.success=Agent move successful
commands.agent.move.failed=Unable to move Agent
commands.agent.outofrange=Cannot issue command, Agent is out of range
commands.agent.place.success=Agent place successful
commands.agent.place.failed=Agent failed to place
commands.agent.setitem.success=Agent set item successful
commands.agent.setitem.failed=Agent failed to set item
commands.agent.turn.success=Agent turn successful
commands.agent.turn.failed=Unable to turn Agent
commands.agent.till.success=Agent till successful
commands.agent.till.failed=Agent failed to till
commands.agent.tpagent.description=Teleport your Agent.
commands.agent.tpagent.success=Agent teleported
commands.agent.tpagent.failed=Agent failed to teleport
commands.agent.transfer.success=Agent transfer successful
commands.agent.transfer.failed=Agent failed to transfer
commands.always.day=Day-Night cycle %1$s
commands.always.day.locked=Day-Night cycle locked
commands.always.day.unlocked=Day-Night cycle unlocked
commands.ban.description=Adds player to banlist.
commands.autocomplete.a=all players
commands.autocomplete.c=my Agent
commands.autocomplete.e=all entities
commands.autocomplete.p=closest player
commands.autocomplete.r=random player
commands.autocomplete.s=yourself
commands.autocomplete.v=all Agents
commands.ban.failed=Could not ban player %1$s
commands.ban.success=Banned player %1$s
commands.banip.description=Adds IP address to banlist.
commands.banip.invalid=You have entered an invalid IP address or a player that is not online
commands.banip.success=Banned IP address %1$s
commands.banip.success.players=Banned IP address %1$s belonging to %2$s
commands.banlist.ips=There are %1$d total banned IP addresses:
commands.banlist.players=There are %1$d total banned players:
commands.blockdata.description=Modifies the data tag of a block.
commands.blockdata.placeFailed=You cannot place blocks here
commands.blockdata.destroyFailed=You cannot dig here
commands.blockdata.failed=The data tag did not change: %1$s
commands.blockdata.notValid=The target block is not a data holder block
commands.blockdata.outOfWorld=Cannot change block outside of the world
commands.blockdata.success=Block data updated to: %1$s
commands.blockdata.tagError=Data tag parsing failed: %1$s
commands.change-setting.description=Changes a setting on the dedicated server while it's running.
commands.change-setting.success=%1$s has been changed
commands.chunkinfo.compiled=Chunk is compiled.
commands.chunkinfo.data=First 64 vertices are: %1$s
commands.chunkinfo.empty=Chunk is empty.
commands.chunkinfo.hasLayers=Chunk has layers: %1$s
commands.chunkinfo.hasNoRenderableLayers=Chunk has no renderable layers.
commands.chunkinfo.isEmpty=Chunk has empty layers: %1$s
commands.chunkinfo.location=Chunk location: (%1$d, %2$d, %3$d)
commands.chunkinfo.noChunk=No chunk found at chunk position %1$d, %2$d, %3$d
commands.chunkinfo.notCompiled=Chunk is not compiled.
commands.chunkinfo.notEmpty=Chunk is not empty.
commands.chunkinfo.vertices=%1$s layer's buffer contains %2$d vertices
commands.classroommode.description=Attempt to launch and connect to Classroom Mode.
commands.classroommode.success=Attempting to launch Classroom Mode...
commands.clear.description=Clears items from player inventory.
commands.clear.failure=Could not clear the inventory of %1$s
commands.clear.failure.no.items=Could not clear the inventory of %1$s, no items to remove
commands.clear.success=Cleared the inventory of %1$s, removing %2$d items
commands.clear.tagError=Data tag parsing failed: %1$s
commands.clear.testing=%1$s has %2$d items that match the criteria
commands.clearfixedinv.description=Removes all Fixed Inventory Slots.
commands.clearfixedinv.success=Cleared the Fixed Inventory
commands.clone.description=Clones blocks from one region to another.
commands.clone.failed=No blocks cloned
commands.clone.filtered.error=Filtered usage requires a filter block to be specified
commands.clone.noOverlap=Source and destination can not overlap
commands.clone.outOfWorld=Cannot access blocks outside of the world
commands.clone.success=%1$d blocks cloned
commands.clone.tooManyBlocks=Too many blocks in the specified area (%1$d > %2$d)
commands.closechat.description=Closes the chat window of the local player if it is open.
commands.closechat.success=Chat closed
commands.closechat.failure=Chat was not open
commands.closewebsocket.description=Closes websocket connection if there is one.
commands.code.description=Launches Code Builder.
commands.code.success=Launched Code Builder.
commands.compare.failed=Source and destination are not identical
commands.compare.outOfWorld=Cannot access blocks outside of the world
commands.compare.success=%1$d blocks compared
commands.compare.tooManyBlocks=Too many blocks in the specified area (%1$d > %2$d)
commands.corruptworld.description=Corrupts the world loaded on the server.
commands.corruptworld.success=Successfully corrupted the world.
commands.daylock.description=Locks and unlocks the day-night cycle.
commands.debug.description=Starts or stops a debugging session.
commands.debug.notStarted=Can't stop profiling when we haven't started yet!
commands.debug.start=Started debug profiling
commands.debug.stop=Stopped debug profiling after %.2f seconds (%1$d ticks)
commands.defaultgamemode.description=Sets the default game mode.
commands.defaultgamemode.success=The world's default game mode is now %1$s
commands.deop.description=Revokes operator status from a player.
commands.deop.failed=Could not de-op (permission level too high): %s
commands.deop.success=De-opped: %s
commands.deop.message=You have been de-opped
commands.difficulty.description=Sets the difficulty level.
commands.difficulty.usage=/difficulty <new difficulty>
commands.difficulty.success=Set game difficulty to %1$s
commands.downfall.success=Toggled downfall
commands.effect.description=Add or remove status effects.
commands.effect.failure.notActive=Couldn't take %1$s from %2$s as they do not have the effect
commands.effect.failure.notActive.all=Couldn't take any effects from %1$s as they do not have any
commands.effect.failure.notAMob=%1$s cannot have effects
commands.effect.notFound=There is no such mob effect with ID %s
commands.effect.success=Gave %1$s * %2$d to %3$s for %4$d seconds
commands.effect.success.removed=Took %1$s from %2$s
commands.effect.success.removed.all=Took all effects from %1$s
commands.enchant.cantCombine=%1$s can't be combined with %2$s
commands.enchant.invalidLevel=%1$s does not support level %2$d
commands.enchant.cantEnchant=The selected enchantment can't be added to the target item: %1$s
commands.enchant.description=Adds an enchantment to a player's selected item.
commands.enchant.noItem=The target doesn't hold an item: %1$s
commands.enchant.notFound=There is no such enchantment with ID %1$d
commands.enchant.success=Enchanting succeeded for %1$s
commands.entitydata.description=Modifies the data tag of an entity.
commands.entitydata.failed=The data tag did not change: %1$s
commands.entitydata.noPlayers=%1$s is a player and cannot be changed
commands.entitydata.success=Entity data updated to: %1$s
commands.entitydata.tagError=Data tag parsing failed: %1$s
commands.execute.allInvocationsFailed=All invocations failed: '%1$s'
commands.execute.failed=Failed to execute '%1$s' as %2$s
commands.execute.description=Executes a command on behalf of one or more entities.
commands.fill.description=Fills all or parts of a region with a specific block.
commands.fill.failed=No blocks filled
commands.fill.outOfWorld=Cannot place blocks outside of the world
commands.fill.success=%1$d blocks filled
commands.fill.tagError=Data tag parsing failed: %1$s
commands.fill.tooManyBlocks=Too many blocks in the specified area (%1$d > %2$d)
commands.fill.replace.auxvalue.invalid=Invalid replace data value for block %1$s
commands.function.description=Runs commands found in the corresponding function file.
commands.function.functionNameNotFound=Function %1$s not found.
commands.function.invalidCharacters=Function with title '%s' is invalid, character '%s' is not allowed in function names.
commands.function.noEngineVersionSpecified=Function %s could not be run. You must specify a min_engine_version in the behavior pack's manifest.json.
commands.function.success=Successfully executed %1$d function entries.
commands.gamemode.description=Sets a player's game mode.
commands.gamemode.success.other=Set %2$s's game mode to %1$s
commands.gamemode.success.self=Set own game mode to %1$s
commands.gamemode.fail.invalid=Game mode '%1$s' is invalid
commands.gamerule.description=Sets or queries a game rule value.
commands.gamerule.type.invalid=Invalid type used for game rule '%1$s'
commands.gamerule.type.nocheatsenabled=Game rule '%1$s' can only be used if cheats are enabled in this world.
commands.gamerule.nopermission=Only server owners can change '%1$s'
commands.gamerule.norule=No game rule called '%1$s' is available
commands.gamerule.success=Game rule %1$s has been updated to %2$s
commands.generic.async.initiated='%1$s' command started (async step %2$d)
commands.generic.boolean.invalid='%1$s' is not true or false
commands.generic.chunk.notFound=Specified chunk not found
commands.generic.componentError=Component list parsing failed
commands.generic.dimension.notFound=Specified dimension not found
commands.generic.disabled=Cheats aren't enabled in this level.
commands.generic.disabled.templateLocked=Settings are currently locked. Unlock Template World Options in the Game Settings menu to change them.
commands.generic.double.tooBig=The number you have entered (%.2f) is too big, it must be at most %.2f
commands.generic.double.tooSmall=The number you have entered (%.2f) is too small, it must be at least %.2f
commands.generic.duplicateType=Duplicate type arguments
commands.generic.duplicateSelectorArgument=Duplicate %s selector arguments
commands.generic.encryption.badkey=Bad public key given. Expected 120 byte key after PEM formatting.
commands.generic.encryption.badsalt=Bad salt given. Expected a 16 bytes before base 64 encoding.
commands.generic.encryption.required=Encrypted session required
commands.generic.entity.invalidType=Entity type '%1$s' is invalid
commands.generic.entity.invalidUuid=The entity UUID provided is in an invalid format
commands.generic.entity.notFound=That entity cannot be found
commands.generic.exception=An unknown error occurred while attempting to perform this command
commands.generic.invalidAgentType=Type argument applied to Agent-only selector
commands.generic.invalidcontext=Invalid context provided for given command type
commands.generic.invalidDevice=The command you entered, %s, is not supported on this device
commands.generic.invalidPlayerType=Type argument applied to player-only selector
commands.generic.invalidType=Unknown type argument
commands.generic.levelError=Max level has to be larger than min level
commands.generic.malformed.body=Body is missing or malformed
commands.generic.malformed.type=Invalid request type
commands.generic.notimplemented=Not implemented
commands.generic.num.invalid='%1$s' is not a valid number
commands.generic.num.tooBig=The number you have entered (%1$d) is too big, it must be at most %2$d
commands.generic.num.tooSmall=The number you have entered (%1$d) is too small, it must be at least %2$d
commands.generic.parameter.invalid='%1$s' is not a valid parameter
commands.generic.permission.selector=<insufficient permissions for selector expansion>
commands.generic.player.notFound=That player cannot be found
commands.generic.protocol.mismatch=Provided protocol version doesn't match Minecraft's protocol version
commands.generic.radiusError=Minimum selector radius must be smaller than maximum
commands.generic.radiusNegative=Radius cannot be negative
commands.generic.rotationError=Rotation out of range
commands.generic.running=The command is already running
commands.generic.step.failed=Command step failed
commands.generic.syntax=Syntax error: Unexpected "%2$s": at "%1$s>>%2$s<<%3$s"
commands.generic.noTargetMatch=No targets matched selector
commands.generic.targetNotPlayer=Selector must be player-type
commands.generic.tooManyNames=Too many target name arguments
commands.generic.tooManyTargets=Too many targets matched selector
commands.generic.too.many.requests=Too many commands have been requested, wait for one to be done
commands.generic.unknown=Unknown command: %s. Please check that the command exists and that you have permission to use it.
commands.generic.usage=Usage: %1$s
commands.generic.usage.noparam=Usage:
commands.generic.version.mismatch=The requested version doesn't exist for this command
commands.generic.version.missing=Command calls not from chat should specify the command's version
commands.getchunkdata.description=Gets pixels for a specific chunk.
commands.getchunkdata.success=Chunk data received
commands.getchunks.description=Gets list of chunks that are loaded.
commands.getchunks.success=Chunk list received
commands.getlocalplayername.description=Returns the local player name.
commands.getspawnpoint.description=Gets the spawn position of the specified player(s).
commands.gettopsolidblock.description=Gets the position of the top non-air block below the specified position
commands.gettopsolidblock.notfound=No solid blocks under specified position
commands.give.block.notFound=There is no such block with name %1$d
commands.give.description=Gives an item to a player.
commands.give.item.invalid=Invalid command syntax: no such %s exists with that data value
commands.give.item.notFound=There is no such item with name %1$d
commands.give.map.invalidData=Invalid map data provided
commands.give.map.featureNotFound=Could not make exploration map. Feature not found on this dimension
commands.give.success=Gave %1$s * %2$d to %3$s
commands.give.successRecipient=You have been given %1$s * %2$d
commands.give.tagError=Data tag parsing failed: %1$s
commands.help.description=Provides help/list of commands.
commands.help.footer=Tip: Use the <tab> key while typing a command to auto-complete the command or its arguments
commands.help.header=--- Showing help page %1$d of %2$d (/help <page>) ---
commands.help.command.aliases=%s (also %s):
commands.immutableworld.description=Sets the immutable state of the world.
commands.immutableworld.info=immutableworld = %s
commands.kick.description=Kicks a player from the server.
commands.kick.description.edu=Removes a player from the game.
commands.kick.not.found=Could not find player %1$s
commands.kick.not.yourself=You may not remove yourself from the game
commands.kick.success=Kicked %1$s from the game
commands.kick.success.reason=Kicked %1$s from the game: '%2$s'
commands.kick.success.reasonedu=Removed %1$s from the game: '%2$s'
commands.kick.no.permissions=You do not have permission to use the kick command.
commands.kill.successful.edu=Removed %1$s
commands.kill.successful=Killed %1$s
commands.kill.description.edu=Removes entities (players, mobs, etc.).
commands.kill.description=Kills entities (players, mobs, etc.).
commands.list.description=Lists players on the server.
commands.locate.description=Displays the coordinates for the closest structure of a given type.
commands.locate.fail.noplayer=The command can only be used by a valid player
commands.locate.fail.nostructurefound=No valid structure found in this dimension
commands.locate.success=The nearest %1$s is at block %2$s, (y?), %3$s
commands.togglecontentlog.toggle=Enables/Disables the content log command
commands.togglecontentlog.enabled=Content Log Enabled
commands.togglecontentlog.disabled=Content Log Disabled
commands.me.description=Displays a message about yourself.
commands.message.display.incoming=%1$s whispers to you: %2$s
commands.message.display.outgoing=You whisper to %1$s: %2$s
commands.message.sameTarget=You can't send a private message to yourself!
commands.mixer.description=Mixer Interactivity control
commands.mixer.error.unknown=An unknown Mixer error occurred.
commands.mixer.error.notoken=You need to be signed in with a Microsoft Account to enable Mixer interactivity.
commands.mixer.error.notsupported=The device you are using does not support Mixer interactivity.
commands.mixer.interactive.error=A Mixer error has occurred: %1$s
commands.mixer.scene.failed=No scene named %1$s exists. Make sure you have entered the scene name correctly.
commands.mixer.scene.success=Scene changed to: %1$s
commands.mixer.start.success=Mixer interactive starting: %1$s
commands.mixer.start.fail.invalidCode=Could not find project with ID "%1$s". Make sure the ID or share code is correct.
commands.mixer.stop.success=Mixer interactive stopped.
commands.mixer.stop.fail=No interactive session to stop.
commands.mixer.status.notinitialized=Interactivity is not initialized.
commands.mixer.status.enabled=Interactivity is enabled.
commands.mixer.status.initializing=Interactivity is initializing.
commands.mixer.status.pending=Interactivity is pending.
commands.mixer.status.disabled=Interactivity is disabled.
commands.mixer.activatedbutton=%1$s activated %2$s.

commands.mobevent.description=Controls what mob events are allowed to run.
commands.mobevent.eventsEnabledSetToTrue=Mob Events are now enabled. Individual events which are set to false will not run.
commands.mobevent.eventsEnabledSetToFalse=Mob Events are now disabled. Individual events will not run.
commands.mobevent.eventsEnabledIsTrue=Mob Events are enabled. Individual events which are set to false will not run.
commands.mobevent.eventsEnabledIsFalse=Mob Events are disabled. Individual events will not run.

## for the following new loc strings, the first parameter is a localized event name, and the second is the literal event that must be entered into the command.
commands.mobevent.eventSetToTrue=Mob Event %s (id: %s) set to true.
commands.mobevent.eventSetToTrueButEventsDisabled=Mob Event %s (id: %s) set to true, but Mob Events are disabled.
commands.mobevent.eventSetToFalse=Mob Event %s (id: %s) set to false.
commands.mobevent.eventIsTrue=Mob Event %s (id: %s) is set to true.
commands.mobevent.eventIsTrueButEventsDisabled=Mob Event %s (id: %s) is set to true, but Mob Events are disabled.
commands.mobevent.eventIsFalse=Mob Event %s (id: %s) is set to false.

commands.op.description=Grants operator status to a player.
commands.op.failed=Could not op (already op or higher): %s
commands.op.success=Opped: %s
commands.op.message=You have been opped
commands.origin.commandblock=CommandBlock
commands.origin.external=External
commands.origin.devconsole=DevConsole
commands.origin.script=Script Engine
commands.origin.server=Server
commands.origin.teacher=Teacher
commands.ops.description=Reloads and applies Op permissions.
commands.ops.reloaded=Ops reloaded from file.
commands.ops.set.success=Succeeded in setting operator level for player %s.
commands.permissions.description=Reloads and applies permissions.
command.permissions.list.fail.filenotfound=Failed to list permissions from file, file not found.
commands.permissions.reloaded=Permissions reloaded from file.
command.permissions.reload.fail.filenotfound=Failed to reload permissions from file, file not found.
commands.permissions.set.failed=Could not set permission level %s for player %s.
commands.permissions.set.success=Succeeded in setting permission level %s for player %s.
commands.permissions.save.failed=Could not save permission level %s for player %s.
commands.permissions.save.success=Succeeded in saving permission level %s for player %s.
commands.spawnParticleEmitter.description=Creates a particle emitter
commands.particle.description=Creates particles.
commands.particle.notFound=Unknown effect name (%1$s)
commands.particle.success=Playing effect %1$s for %2$d times
commands.players.list=There are %1$d/%2$d players online:
commands.players.list.names=%s
commands.playsound.description=Plays a sound.
commands.playsound.playerTooFar=Player %1$s is too far away to hear the sound
commands.playsound.success=Played sound '%1$s' to %2$s
commands.position.description=Toggles on/off coordinates for player.
commands.publish.failed=Unable to host local game
commands.publish.started=Local game hosted on port %1$s
commands.querytarget.description=Gets transform, name, and id information about the given target entity or entities.
commands.querytarget.success=Target data: %1$s
commands.reload.description=Reloads all function files from all behavior packs.
commands.reload.success=Existing function files have been reloaded. Restart Minecraft to reload NEW functions.
commands.replaceitem.description=Replaces items in inventories.
commands.replaceitem.failed=Could not replace %s slot %d with %d * %s
commands.replaceitem.noContainer=Block at %s is not a container
commands.replaceitem.badSlotNumber=Could not replace slot %d, must be a value between %d and %d.
commands.replaceitem.success=Replaced %s slot %d with %d * %s
commands.replaceitem.success.entity=Replaced %s slot %d of %s with %d * %s
commands.replaceitem.tagError=Data tag parsing failed: %1$s
commands.save.description=Control or check how the game saves data to disk.
commands.save.disabled=Turned off world auto-saving
commands.save.enabled=Turned on world auto-saving
commands.save.failed=Saving failed: %1$s
commands.save.start=Saving...
commands.save.success=Saved the world
commands.save-all.error=An error occurred when trying to pause the level storage.
commands.save-all.success=Data saved. Files are now ready to be copied.
commands.save-off.alreadyOff=Saving is already turned off.
commands.save-on.alreadyOn=Saving is already turned on.
commands.save-on.notDone=A previous save has not been completed.
commands.save-on.description=Enables automatic server saves.
commands.save-on.success=Changes to the level are resumed.
commands.save-state.description=Checks if a previous save-all has finished and lists the files involved.
commands.say.description=Sends a message in the chat to other players.

## Scoreboard is composed of several Objectives, which can be displayed to players in 3 different 'slots'	##
## An Objective holds the scores that each player has for the Objective. 									##
## Objectives can have different Criteria, which dictate how the scores are updated for this Objective		##
## Objectives have two names, one for internal use and the other for Displaying in the UI					##

## Going overboard on the context but Scoreboards do some weird stuff :D 									##

commands.scoreboard.description=Track and display scores for various objectives						##
commands.scoreboard.allMatchesFailed=All matches failed												##
commands.scoreboard.noMultiWildcard=Only one user wildcard allowed									##
commands.scoreboard.objectiveNotFound=No objective was found by the name '%1$s'						##1: Objective Name
commands.scoreboard.objectiveReadOnly=The objective '%1$s' is read-only and cannot be set			##1: Objective Name
commands.scoreboard.objectives.add.alreadyExists=An objective with the name '%1$s' already exists	##1: Objective Name
commands.scoreboard.objectives.add.displayTooLong=The display name '%1$s' is too long for an objective, it can be at most %2$d characters long	##1: Objective Display Name ##2: Name Length Limit
commands.scoreboard.objectives.add.success=Added new objective '%1$s' successfully																##1: Objective Name
commands.scoreboard.objectives.add.tooLong=The name '%1$s' is too long for an objective, it can be at most %2$d characters long					##1: Objective Name ##2: Name Length Limit
commands.scoreboard.objectives.add.wrongType=Invalid objective criteria type '%1$s'						##1: Criteria Name
commands.scoreboard.objectives.add.needName=An objective needs a name.									##
commands.scoreboard.objectives.description=Modify scoreboard objectives.								##
commands.scoreboard.objectives.list.count=Showing %1$d objective(s) on scoreboard:						##1: Number of Objectives
commands.scoreboard.objectives.list.empty=There are no objectives on the scoreboard						##
commands.scoreboard.objectives.list.entry=- %1$s: displays as '%2$s' and is type '%3$s'					##1: Objective Name ##2: Objective Display Name ##3: Critiera Name
commands.scoreboard.objectives.remove.success=Removed objective '%1$s' successfully						##1: Objective Name
commands.scoreboard.objectives.setdisplay.invalidSlot=No such display slot '%1$s'						##1: Display Slot
commands.scoreboard.objectives.setdisplay.successCleared=Cleared objective display slot '%1$s'			##1: Display Slot
commands.scoreboard.objectives.setdisplay.successSet=Set the display objective in slot '%1$s' to '%2$s'	##1: Display Slot ##2: Objective Name
commands.scoreboard.players.add.success=Added %1$d to [%2$s] for %3$s (now %4$d)						##1: Score Value ##2: Objective Name ##3: Player Name ##4: New Score Value
commands.scoreboard.players.add.multiple.success=Added %1$d to [%2$s] for %3$d entities					##1: Score Value ##2: Objective Name ##3: Player Count
commands.scoreboard.players.nameNotFound=A player name must be given.									##
commands.scoreboard.players.enable.noTrigger=Objective %1$s is not a trigger							##1: Objective Name
commands.scoreboard.players.enable.success=Enabled trigger %1$s for %2$s								##1: Trigger Name ##2: Objective Name
commands.scoreboard.players.list.count=Showing %1$d tracked players on the scoreboard:					##1: Number of Players
commands.scoreboard.players.list.empty=There are no tracked players on the scoreboard					##
commands.scoreboard.players.list.player.count=Showing %1$d tracked objective(s) for %2$s:				##1: Objective Count ##2: Player Name
commands.scoreboard.players.list.player.empty=Player %1$s has no scores recorded						##1: Player Name
commands.scoreboard.players.list.player.entry=- %2$s: %1$d (%3$s)										##1: Score Value ##2: Objective Display Name ##3: Objective Name
commands.scoreboard.players.operation.invalidOperation=Invalid operation %1$s							##
commands.scoreboard.players.operation.notFound=No %1$s score for %2$s found								##1: Objective Name ##2: Player Name
commands.scoreboard.players.operation.success=Updated [%1$s] for %2$d entities							##1: Objective Name ##2: Player Count
commands.scoreboard.players.offlinePlayerName=Player Offline
commands.scoreboard.players.random.invalidRange=Min %1$d is not less than max %2$d						##1: Min Range Value ##2: Max Range Value
commands.scoreboard.players.remove.success=Removed %1$d from [%2$s] for %3$s (now %4$d)					##1: Score Value ##2: Objective Name ##3: Player Name ##4: New Score Value
commands.scoreboard.players.remove.multiple.success=Removed %1$d from [%2$s] for %3$d entities			##1: Score Value ##2: Objective Name ##3: Player Count
commands.scoreboard.players.reset.success=Reset scores of player %1$s									##1: Player Name
commands.scoreboard.players.resetscore.success=Reset score %1$s of player %2$s							##1: Objective Name ##2: Player Name
commands.scoreboard.players.set.success=Set [%1$s] for %2$s to %3$d										##1: Objective Name ##2: Player Name ##3: Score Value
commands.scoreboard.players.set.multiple.success=Set [%1$s] for %2$d entities to %3$d					##1: Objective Name ##2: Player Count ##3: Score Value
commands.scoreboard.players.set.tagError=Could not parse dataTag, reason: %1$s							##
commands.scoreboard.players.set.tagMismatch=The dataTag does not match for %1$s							##
commands.scoreboard.players.score.notFound=No %1$s score for %2$s found									##1: Objective Name ##2: Player Name
commands.scoreboard.players.test.failed=Score %1$d is NOT in range %2$d to %3$d							##1: Value ##2: Min Range Value ##3: Max Range Value
commands.scoreboard.players.test.success=Score %1$d is in range %2$d to %3$d							##1: Value ##2: Min Range Value ##3: Max Range Value

#### Scoreboards can also handle teams, which are close to what an Objective does	####
#### with just a little extra functionality											####

commands.scoreboard.teamNotFound=No team was found by the name '%1$s'																##1: Team Name
commands.scoreboard.teams.add.alreadyExists=A team with the name '%1$s' already exists												##1: Team Name
commands.scoreboard.teams.add.displayTooLong=The display name '%1$s' is too long for a team, it can be at most %2$d characters long	##1: Team Display Name ##2: Name Length Limit
commands.scoreboard.teams.add.success=Added new team '%1$s' successfully															##1: Team Name
commands.scoreboard.teams.add.tooLong=The name '%1$s' is too long for a team, it can be at most %2$d characters long				##1: Team Name ##2: Name Length Limit
commands.scoreboard.teams.empty.alreadyEmpty=Team %1$s is already empty, cannot remove nonexistant players							##1: Team Name
commands.scoreboard.teams.empty.success=Removed all %1$d player(s) from team %2$s													##1: Number of Players ##2: Team Name
commands.scoreboard.teams.join.failure=Could not add %1$d player(s) to team %2$s: %3$s												##1: Number of Players ##2: Team Name ##3: List of Players
commands.scoreboard.teams.join.success=Added %1$d player(s) to team %2$s: %3$s														##1: Number of Players ##2: Team Name ##3: List of Players
commands.scoreboard.teams.leave.failure=Could not remove %1$d player(s) from their teams: %2$s										##1: Number of Players ##2: Team Name
commands.scoreboard.teams.leave.noTeam=You are not in a team																		##
commands.scoreboard.teams.leave.success=Removed %1$d player(s) from their teams: %2$s												##1: Number of Players ##2: List of Players
commands.scoreboard.teams.list.count=Showing %1$d teams on the scoreboard:															##1: Number of Teams
commands.scoreboard.teams.list.empty=There are no teams registered on the scoreboard												##
commands.scoreboard.teams.list.entry=- %1$s: '%2$s' has %3$d players																##1: Team Name ##2: Team Display Name ##3: Number of Players
commands.scoreboard.teams.list.player.count=Showing %1$d player(s) in team %2$s:													##1: Number of Players ##2 Team Name
commands.scoreboard.teams.list.player.empty=Team %1$s has no players																##1: Team Name
commands.scoreboard.teams.list.player.entry=- %2$s: %1$d (%3$s)																		##1: Player Name ##2: Score Value ##3: Team Display Name
commands.scoreboard.teams.option.noValue=Valid values for option %1$s are: %2$s														##1: Option Name ##2: List of Option Values
commands.scoreboard.teams.option.success=Set option %1$s for team %2$s to %3$s														##1: Option Name ##2: Team Name ##3: Option Value
commands.scoreboard.teams.remove.success=Removed team %1$s																			##1: Team Name
commands.seed.success=Seed: %1$s
commands.setblock.description=Changes a block to another block.
commands.setblock.failed=Unable to place block
commands.setblock.noChange=The block couldn't be placed
commands.setblock.notFound=There is no such block with ID/name %1$s
commands.setblock.outOfWorld=Cannot place block outside of the world
commands.setblock.success=Block placed
commands.setblock.tagError=Data tag parsing failed: %1$s
commands.setidletimeout.success=Successfully set the idle timeout to %1$d minutes.
commands.setfixedinvslots.description=Sets the number of fixed inventory slots for the server.
commands.setfixedinvslots.success=Number of Fixed Inventory Slots set to %1$d
commands.setfixedinvslot.description=Sets a fixed slot to a specified item.
commands.setfixedinvslot.success=Fixed Inventory Slot %1$d set to %2$s
commands.globalpause.description=Sets or gets the paused state of the game for all players.
commands.globalpause.success=Set or got pause state
commands.setmaxplayers.description=Sets the maximum number of players for this game session.
commands.setmaxplayers.success=Set max players to %1$d.
commands.setmaxplayers.success.upperbound=(Bound to maximum allowed connections)
commands.setmaxplayers.success.lowerbound=(Bound to current player count)
commands.setworldspawn.description=Sets the world spawn.
commands.setworldspawn.success=Set the world spawn point to (%1$d, %2$d, %3$d)
commands.setworldspawn.wrongDimension=The world spawn can not be set in this dimension
commands.spawnpoint.success.single=Set %1$s's spawn point to (%2$d, %3$d, %4$d)
commands.spawnpoint.description=Sets the spawn point for a player.
commands.spawnpoint.success.multiple.specific=Set spawn point for %1$s to (%2$d, %3$d, %4$d)
commands.spawnpoint.success.multiple.generic=Set spawn point for %1$s
commands.spawnpoint.wrongDimension=The spawn point cannot be set in this dimension
commands.spreadplayers.description=Teleports entities to random locations.
commands.spreadplayers.failure.players=Could not spread %1$s players around %2$s,%3$s (too many players for space - try using spread of at most %4$s)
commands.spreadplayers.failure.teams=Could not spread %1$s teams around %2$s,%3$s (too many players for space - try using spread of at most %4$s)
commands.spreadplayers.info.players=(Average distance between players is %1$s blocks apart after %2$s iterations)
commands.spreadplayers.info.teams=(Average distance between teams is %1$s blocks apart after %2$s iterations)
commands.spreadplayers.spreading.players=Spreading %1$s players %2$s blocks around %3$s,%4$s (min %5$s blocks apart)
commands.spreadplayers.spreading.teams=Spreading %1$s teams %2$s blocks around %3$s,%4$s (min %5$s blocks apart)
commands.spreadplayers.success.players=Successfully spread %1$s players around %2$s,%3$s
commands.spreadplayers.success.teams=Successfully spread %1$s teams around %2$s,%3$s
commands.stats.cleared=Cleared %1$s stats
commands.stats.failed=Invalid parameters
commands.stats.noCompatibleBlock=Block at %1$d, %2$d, %3$d can not track stats
commands.stats.success=Storing %1$s stats in %2$s on %3$s
commands.stop.description=Stops the server.
commands.stop.start=Stopping the server
commands.stopsound.description=Stops a sound.
commands.stopsound.success=Stopped sound %s for %s
commands.stopsound.success.all=Stopped all sounds for %s
commands.summon.description=Summons an entity.
commands.summon.failed=Unable to summon object
commands.summon.outOfWorld=Cannot summon the object out of the world
commands.summon.success=Object successfully summoned
commands.summon.tagError=Data tag parsing failed: %1$s
commands.tag.description=Manages tags stored in entities.
commands.tag.add.failed=Target either already has the tag or has too many tags
commands.tag.add.success.single=Added tag '%1$s' to %2$s														##1: Tag string   ##2: Entity/Player/Item name
commands.tag.add.success.multiple=Added tag '%1$s' to %2$d entities												##1: Tag string   ##2: Number of entities (numerals)
commands.tag.list.single.empty=%s has no tags																	##1: Entity/Player/Item name
commands.tag.list.single.success=%1$s has %2$d tags: %3$s														##1: Entity/Player/Item name   ##2: Number of tags   ##3: Comma separated list of tags
commands.tag.list.multiple.empty=There are no tags on the %d entities											##1: Number of entities matching the selector
commands.tag.list.multiple.success=The %1$d entities have %2$d total tags: %3$s									##1: Number of entities matching the selector   ##2: Number of tags   ##3: Comma separated list of tags
commands.tag.remove.failed=Target does not have this tag
commands.tag.remove.success.single=Removed tag '%1$s' from %2$s													##1: Tag string   ##2: Entity/Player/Item name
commands.tag.remove.success.multiple=Removed tag '%1$s' from %2$d entities										##1: Tag string   ##2: Number of selector matches
commands.tell.description=Sends a private message to one or more players.
commands.tellraw.description=Sends a JSON message to players.
commands.tellraw.jsonException=Invalid json: %1$s
commands.tellraw.jsonStringException=Invalid json string data.
commands.tellraw.error.noData=No data was provided.
commands.tellraw.error.notArray=Rawtext object must contain an array. Example: "rawtext":[{..}]
commands.tellraw.error.textNotString=text field in rawtext must contain a string. Example: "rawtext":[{"text": "text to display"}]
commands.tellraw.error.translateNotString=translate field in rawtext must contain a language key. Example: "rawtext":[{"translate": "gui.ok"}]
commands.tellraw.error.withNotArray=with field in rawtext must contain a array. Example: "rawtext":[{"translate": "chat.type.announcement", "with": [ "value1", "value2" ]}]
commands.tellraw.error.itemIsNotObject=Json value in rawtext array was not an object. Example: "rawtext": [{ "text" : "my text" }]
commands.educlientinfo.description=Get tenant ID and host status. Intended for CM.
commands.testfor.description=Counts entities (players, mobs, items, etc.) matching specified conditions.
commands.testfor.failure=%1$s did not match the required data structure
commands.testfor.success=Found %1$s
commands.testfor.tagError=Data tag parsing failed: %1$s
commands.testforblock.description=Tests whether a certain block is in a specific location.
commands.testforblock.failed.data=The block at %1$d,%2$d,%3$d did not match the expected block state.
commands.testforblock.failed.nbt=The block at %1$d,%2$d,%3$d did not have the required NBT keys.
commands.testforblock.failed.tile=The block at %1$d,%2$d,%3$d is %4$s (expected: %5$s).
commands.testforblock.failed.tileEntity=The block at %1$d,%2$d,%3$d is not a tile entity and cannot support tag matching.
commands.testforblock.outOfWorld=Cannot test for block outside of the world
commands.testforblock.success=Successfully found the block at %1$d,%2$d,%3$d.
commands.testforblocks.description=Tests whether the blocks in two regions match.
commands.tickingarea.description=Add, remove, or list ticking areas.
commands.tickingarea.inuse=%1$d/%2$d ticking areas in use.
commands.tickingarea.noneExist.currentDimension=No ticking areas exist in the current dimension.
commands.tickingarea-add-bounds.success=Added ticking area from %1$d to %2$d.
commands.tickingarea-add-circle.success=Added ticking area centered at %1$d with a radius of %2$d chunks.
commands.tickingarea-add.failure=Max number of ticking areas (%1$d) has already been reached. Cannot add more ticking areas.
commands.tickingarea-add.conflictingname=A ticking area with the name %1$s already exists.
commands.tickingarea-add.chunkfailure=Ticking area contains more than %1$d chunks, ticking area is too large and cannot be created.
commands.tickingarea-add.radiusfailure=Radius cannot be larger than %1$d, ticking area is too large and cannot be created.
commands.tickingarea-remove.success=Removed ticking area(s)
commands.tickingarea-remove.failure=No ticking areas containing the block position %1$d exist in the current dimension.
commands.tickingarea-remove.byname.failure=No ticking areas named %1$s exist in the current dimension.
commands.tickingarea-remove_all.success=Removed ticking area(s)
commands.tickingarea-remove_all.failure=No ticking areas exist in the current dimension.
commands.tickingarea-list.chunks=chunks ## Meaning a chunk of the world.
commands.tickingarea-list.circle.radius=Radius
commands.tickingarea-list.success.currentDimension=List of all ticking areas in current dimension
commands.tickingarea-list.success.allDimensions=List of all ticking areas in all dimensions
commands.tickingarea-list.failure.allDimensions=No ticking areas exist in any dimension.
commands.tickingarea-list.to=to	## Used in the context of "from position to other position"
commands.tickingarea-list.type.circle=Circle
commands.time.added=Added %1$d to the time
commands.time.description=Changes or queries the world's game time.
commands.time.disabled=Always Day is enabled in this level.
commands.time.query.day=Day is %d
commands.time.query.daytime=Daytime is %d
commands.time.query.gametime=Gametime is %d
commands.time.set=Set the time to %1$d
commands.time.stop=Time %1$s
commands.title.description=Controls screen titles.
commands.title.success=Title command successfully executed
commands.titleraw.description=Controls screen titles with JSON messages.
commands.title.success=Title command successfully executed
commands.toggledownfall.description=Toggles the weather.
commands.tp.description=Teleports entities (players, mobs, etc.).
commands.tp.notSameDimension=Unable to teleport because players are not in the same dimension
commands.tp.outOfWorld=Cannot teleport entities outside of the world
commands.tp.permission=You do not have permission to use this slash command.
commands.tp.safeTeleportFail=Unable to teleport %1$s to %2$s because the area wasn't clear of blocks.
commands.tp.far=Unable to teleport %1$s to the unloaded area at %2$s
commands.tp.success=Teleported %1$s to %2$s
commands.tp.successVictim=You have been teleported to %1$s
commands.tp.success.coordinates=Teleported %1$s to %2$s, %3$s, %4$s
commands.transferserver.description=Transfers a player to another server.
commands.transferserver.successful=Transferred player
commands.transferserver.invalid.port=Invalid port (0-65535)
commands.trigger.description=Sets a trigger to be activated.
commands.trigger.disabled=Trigger %1$s is not enabled
commands.trigger.invalidMode=Invalid trigger mode %1$s
commands.trigger.invalidObjective=Invalid trigger name %1$s
commands.trigger.invalidPlayer=Only players can use the /trigger command
commands.trigger.success=Trigger %1$s changed with %2$s %3$s
commands.unban.failed=Could not unban player %1$s
commands.unban.success=Unbanned player %1$s
commands.unbanip.invalid=You have entered an invalid IP address
commands.unbanip.success=Unbanned IP address %1$s
commands.videostream.description=Attempts to connect to the websocket server to send a video stream.
commands.videostreamaction.description=Perform a videostream related action.
commands.weather.clear=Changing to clear weather
commands.weather.description=Sets the weather.
commands.weather.disabled=Weather Cycle isn't enabled in this level.
commands.weather.query=Weather state is: %s
commands.weather.query.clear=clear
commands.weather.query.rain=rain
commands.weather.query.thunder=thunder
commands.weather.rain=Changing to rainy weather
commands.weather.thunder=Changing to rain and thunder
commands.whitelist.add.failed=Could not add %1$s to the whitelist
commands.whitelist.add.success=Added %1$s to the whitelist
commands.whitelist.description=Manages the server whitelist.
commands.whitelist.disabled=Turned off the whitelist
commands.whitelist.enabled=Turned on the whitelist
commands.whitelist.list=There are %1$d (out of %2$d seen) whitelisted players:
commands.whitelist.reloaded=Whitelist reloaded from file.
commands.whitelist.remove.failed=Could not remove %1$s from the whitelist
commands.whitelist.remove.success=Removed %1$s from the whitelist
commands.world_age.description=Changes or queries the world's age (time since creation).
commands.world_age.added=Added %1$d to the world's age
commands.world_age.query=World age is %d
commands.world_age.set=Set the world's age to %1$d
commands.worldborder.center.success=Set world border center to %1$s,%2$s
commands.worldborder.damage.amount.success=Set world border damage amount to %1$s per block (from %2$s per block)
commands.worldborder.damage.buffer.success=Set world border damage buffer to %1$s blocks (from %2$s blocks)
commands.worldborder.get.success=World border is currently %1$s blocks wide
commands.worldborder.set.success=Set world border to %1$s blocks wide (from %2$s blocks)
commands.worldborder.setSlowly.grow.success=Growing world border to %1$s blocks wide (up from %2$s blocks) over %3$s seconds
commands.worldborder.setSlowly.shrink.success=Shrinking world border to %1$s blocks wide (down from %2$s blocks) over %3$s seconds
commands.worldborder.warning.distance.success=Set world border warning to %1$s blocks away (from %2$s blocks)
commands.worldborder.warning.time.success=Set world border warning to %1$s seconds away (from %2$s seconds)
commands.worldbuilder.description=Toggle World Builder status of caller.
commands.worldbuilder.success=World Builder status updated to %1$s
commands.wsserver.description=Attempts to connect to the websocket server on the provided URL.
commands.wsserver.invalid.url=The provided server URL is invalid
commands.wsserver.request.existing=Another connection request is currently running
commands.wsserver.request.failed=Could not connect to server: %1$s
commands.wsserver.success=Connection established to server: %1$s
commands.xp.description=Adds or removes player experience.
commands.xp.failure.widthdrawXp=Cannot give player negative experience points
commands.xp.success=Gave %1$d experience to %2$s
commands.xp.success.levels=Gave %1$d levels to %2$s
commands.xp.success.negative.levels=Taken %1$d levels from %2$s

connect.authorizing=Logging in...
connect.connecting=Connecting to the server...
connect.failed=Failed to connect to the server

connect_gamepad.warning.controllerRequired=Game controller required
connect_gamepad.pressButtonToContinue=Please press the 'A' button on your controller to continue
gamepad_disconnect=Controller lost connection
gamepad_disconnect.reconnectController=Your controller has lost connection.  Please reconnect your controller to continue.

container.beacon=Beacon
container.brewing=Brewing Stand
container.chest=Chest
container.chestDouble=Large Chest
container.crafting=Crafting
container.creative=Item Selection
container.dispenser=Dispenser
container.dropper=Dropper
container.enchant=Enchant
container.enchant.clue=%s . . . ?
container.enchant.levelrequirement=Level Requirement: %d
container.enchant.lapis.many=%d Lapis Lazuli
container.enchant.lapis.one=1 Lapis Lazuli
container.enchant.level.many=%d Enchantment Levels
container.enchant.level.one=1 Enchantment Level
container.enderchest=Ender Chest
container.furnace=Furnace
container.hopper=Item Hopper
container.inventory=Inventory
container.isLocked=%s is locked!
container.loom=Loom
container.minecart=Minecart
container.repair=Repair & Name
container.repair.cost=XP Cost: %1$d
container.repair.expensive=Too Expensive!
container.repairAndDisenchant=Repair & Disenchant
container.stonecutter=Stonecutter
container.stonecutter_block=Stonecutter
container.shulkerbox=Shulker Box
container.shulkerboxContains=and %d more...
container.barrel=Barrel

controller.buttonTip.addItem=Add Item
controller.buttonTip.addItemStack=Add Item Stack
controller.buttonTip.adjustSlider=Left/right Adjust Slider
controller.buttonTip.back=Back
controller.buttonTip.backToAnvil=Back to Anvil
controller.buttonTip.backToBrewing=Back to Brewing
controller.buttonTip.backToEnchanting=Back to Enchanting
controller.buttonTip.backToFurnace=Back to Furnace
controller.buttonTip.cancel=Cancel
controller.buttonTip.clearSearch=Clear Search
controller.buttonTip.clearQuickSelect=Clear Quick Select
controller.buttonTip.craft=Craft
controller.buttonTip.craftAll=Craft All
controller.buttonTip.craftOne=Craft One
controller.buttonTip.craftStack=Craft Stack
controller.buttonTip.craftTake=Craft & Take
controller.buttonTip.drop=Drop
controller.buttonTip.edit=Edit
controller.buttonTip.enterMessage=Enter Message
controller.buttonTip.exit=Exit
controller.buttonTip.external=External
controller.buttonTip.level=Level
controller.buttonTip.new=New
controller.buttonTip.openRecipeBook=Open Recipe Book
controller.buttonTip.pane.inventory=Inventory
controller.buttonTip.pane.recipeBook=Recipe Book
controller.buttonTip.place.one=Place One
controller.buttonTip.place=Place
controller.buttonTip.quick.move=Quick Move
controller.buttonTip.recipes.showAll=Show All
controller.buttonTip.recipes.showCraftable=Show Craftable
controller.buttonTip.remove=Remove
controller.buttonTip.removeFuel=Remove Fuel
controller.buttonTip.removeFuelInput=Remove Fuel/Input
controller.buttonTip.removeInput=Remove Input
controller.buttonTip.removeMaterial=Remove Material
controller.buttonTip.renameItem=Rename Item
controller.buttonTip.returnToRecipe=Return To Recipe
controller.buttonTip.enterSearch=Search
controller.buttonTip.searchRecipe=Quick Search
controller.buttonTip.confirmSearch=Confirm
controller.buttonTip.select.enchant=Select Enchant
controller.buttonTip.select.item=Select Item
controller.buttonTip.select.slot=Select Slot
controller.buttonTip.select=Select
controller.buttonTip.selectRecipe=Select Recipe
controller.buttonTip.tab=Tab
controller.buttonTip.take.half=Take Half
controller.buttonTip.take.one=Take One
controller.buttonTip.take=Take
controller.buttonTip.takeItem=Take Item
controller.buttonTip.clearHotbar=Clear Hotbar

controllerLayoutScreen.actions=Actions
controllerLayoutScreen.activeBindingNameFormat=> %s <
controllerLayoutScreen.bindings=Bindings
controllerLayoutScreen.resetAllBindings=Default Settings
controllerLayoutScreen.button=Button
controllerLayoutScreen.cancel=Cancel
controllerLayoutScreen.confirmation.reset=Do you really want to reset the settings?
controllerLayoutScreen.confirmation.unassigned=Save with unassigned actions?
controllerLayoutScreen.confirmation.unsaved=Quit without saving?
controllerLayoutScreen.save=Save
controllerLayoutScreen.saveAndExit=Save & Exit
controllerLayoutScreen.trigger=Trigger
controllerLayoutScreen.unassigned=Unassigned
controllerLayoutScreen.toggleLivingroom=Toggle Perspective
controls.reset=Reset
controls.resetAll=Reset Keys
controls.title=Controls

## Edu Course strings
course.edu.begin=Begin
course.edu.byLessonsEndGeneric=By the end of this lesson, you will learn the following:
course.edu.byLessonsEndOrdered=By the end of Lesson %d, you will learn the following:
course.edu.collaborativeLesson=Collaborative Lesson
course.edu.collaborativeLessonAndQuiz=Collaborative Lesson and Quiz
course.edu.connectionFailed=Oops! We think you are not connected to the internet. Check your connection and try again!
course.edu.connectionProblem.courseList=There was a problem connecting to your course list. Check your internet connection and try again.
course.edu.connectionProblem.quiz=There was a problem connecting to your quiz. Please try again.
course.edu.continueButton=Continue
course.edu.courses=Courses
course.edu.createdBy=By %s
course.edu.estimatedLength=Estimated Length
## Example of a formatted string is "2m 30s" (first string param is the minutes units, second string is the seconds units)
course.edu.estimatedLength.formatted=%d%s %d%s
course.edu.estimatedLength.minutesUnit=m
course.edu.estimatedLength.secondsUnit=s
course.edu.goal=Education Goal: %s
course.edu.hostButton=Host
course.edu.instructions=Instructions
course.edu.join=Join
course.edu.launchFailed=Oops! This world failed to launch! Did you delete a world template recently?
course.edu.learnMoreButton=Learn More
course.edu.lessonComplete=Lesson Complete
course.edu.lessonIncludesQuiz=This lesson includes a quiz
course.edu.lessonIsLocked=This lesson is locked
course.edu.lessonObjectives=Lesson Objectives
## The line below (course.edu.notSupported) doesn't need to be translated for other locales and will eventually be removed.
course.edu.notSupported=Not Supported Yet
course.edu.noCoursesFound.title=No Courses Found
course.edu.noCoursesFound.body=It looks like you don't have any courses assigned.
course.edu.objective.bulletedString=- %s
course.edu.progress.new=New
course.edu.progress.inProgress=In Progress
course.edu.progress.completed=Completed
course.edu.refresh.courses=Refresh Courses
course.edu.refresh.lessons=Refresh Lessons
course.edu.restartButton=Restart
course.edu.restartConfirmation.title=Restart Lesson?
course.edu.restartConfirmation.body=Restarting will erase all your work and start the lesson over.%sAre you sure you want to restart?
course.edu.restartFailed.title=Oh no!
course.edu.restartFailed.body=We failed to find this lesson. We're attempting to re-download it.
course.edu.startButton=Start
course.edu.tasks=Tasks
course.edu.teacherHasNotGivenAcccess=The teacher has not given you access to this lesson.
## The title is prefixed with the order of the course, followed by a colon, followed by the content title.
## Example: "1: This is the title of a Course"
course.edu.titleWithOrderPrefix.formatted=%d: %s
course.edu.tryItNowButton=Try it now

## Edu Course Multiplayer strings
course.edu.enterIp=Enter the IP Address of the world you would like to join.
course.edu.ipAddress=IP Address
course.edu.ipPlaceholder=Please enter the IP or Server Address
course.edu.ipTooltipText=To find the IP Address, ask the host to pause the lesson. The IP Address can be seen on the right side of their screen.
course.edu.joinLesson=Join Lesson
course.edu.joinLessonIntro=Find classmates in the same lesson to join, or enter their IP address if they don't appear in the list.
course.edu.joinLessonIntroTeacher=Select the group you would like to join, or enter the IP address if it does not appear in the list.
course.edu.noLessonFoundJoinIp=Enter IP Address
course.edu.portTooltipText=The port number determines the specific server program to use. This will typically not need to be changed unless the server host specifies to do so.
course.edu.searching=Searching for worlds...

crafting.badCombination=No valid output for that item
crafting.cannotCreate=You don't have all ingredients
crafting.insufficientLevel=Your level is too low
crafting.noRecipesInventory=You need to collect blocks to craft!
crafting.noRecipesStonecutter=You need stone materials to craft!
crafting.noRecipesStonecutter_block=You need stone materials to craft!
crafting.noRecipesWorkbench=You need to collect blocks to craft!

createWorld.customize.custom.baseSize=Depth Base Size
createWorld.customize.custom.biomeDepthOffset=Biome Depth Offset
createWorld.customize.custom.biomeDepthWeight=Biome Depth Weight
createWorld.customize.custom.biomeScaleOffset=Biome Scale Offset
createWorld.customize.custom.biomeScaleWeight=Biome Scale Weight
createWorld.customize.custom.biomeSize=Biome Size
createWorld.customize.custom.center= Center Height
createWorld.customize.custom.confirm1=This will overwrite your current
createWorld.customize.custom.confirm2=settings and cannot be undone.
createWorld.customize.custom.confirmTitle=Warning!
createWorld.customize.custom.coordinateScale=Coordinate Scale
createWorld.customize.custom.count= Spawn Tries
createWorld.customize.custom.defaults=Defaults
createWorld.customize.custom.depthNoiseScaleExponent=Depth Noise Exponent
createWorld.customize.custom.depthNoiseScaleX=Depth Noise Scale X
createWorld.customize.custom.depthNoiseScaleZ=Depth Noise Scale Z
createWorld.customize.custom.dungeonChance=Dungeon Count
createWorld.customize.custom.fixedBiome=Biome
createWorld.customize.custom.heightScale=Height Scale
createWorld.customize.custom.lavaLakeChance=Lava Lake Rarity
createWorld.customize.custom.lowerLimitScale=Lower Limit Scale
createWorld.customize.custom.mainNoiseScaleX=Main Noise Scale X
createWorld.customize.custom.mainNoiseScaleY=Main Noise Scale Y
createWorld.customize.custom.mainNoiseScaleZ=Main Noise Scale Z
createWorld.customize.custom.maxHeight= Max. Height
createWorld.customize.custom.minHeight= Min. Height
createWorld.customize.custom.next=Next Page
createWorld.customize.custom.page0=Basic Settings
createWorld.customize.custom.page1=Ore Settings
createWorld.customize.custom.page2=Advanced Settings (Expert Users Only!)
createWorld.customize.custom.page3=Extra Advanced Settings (Expert Users Only!)
createWorld.customize.custom.preset.caveChaos=Caves of Chaos
createWorld.customize.custom.preset.caveDelight=Caver's Delight
createWorld.customize.custom.preset.drought=Drought
createWorld.customize.custom.preset.goodLuck=Good Luck
createWorld.customize.custom.preset.isleLand=Isle Land
createWorld.customize.custom.preset.mountains=Mountain Madness
createWorld.customize.custom.preset.waterWorld=Water World
createWorld.customize.custom.presets=Presets
createWorld.customize.custom.presets.title=Customize World Presets
createWorld.customize.custom.prev=Previous Page
createWorld.customize.custom.randomize=Randomize
createWorld.customize.custom.riverSize=River Size
createWorld.customize.custom.seaLevel=Sea Level
createWorld.customize.custom.size= Spawn Size
createWorld.customize.custom.spread= Spread Height
createWorld.customize.custom.stretchY=Height Stretch
createWorld.customize.custom.upperLimitScale=Upper Limit Scale
createWorld.customize.custom.useCaves=Caves
createWorld.customize.custom.useDungeons=Dungeons
createWorld.customize.custom.useLavaLakes=Lava Lakes
createWorld.customize.custom.useLavaOceans=Lava Oceans
createWorld.customize.custom.useMineShafts=Mineshafts
createWorld.customize.custom.useMonuments=Ocean Monuments
createWorld.customize.custom.useRavines=Ravines
createWorld.customize.custom.useStrongholds=Strongholds
createWorld.customize.custom.useTemples=Temples
createWorld.customize.custom.useOceanRuins=Ocean Ruins
createWorld.customize.custom.useVillages=Villages
createWorld.customize.custom.useWaterLakes=Water Lakes
createWorld.customize.custom.waterLakeChance=Water Lake Rarity
createWorld.customize.flat.addLayer=Add Layer
createWorld.customize.flat.editLayer=Edit Layer
createWorld.customize.flat.height=Height
createWorld.customize.flat.layer=%d
createWorld.customize.flat.layer.bottom=Bottom - %d
createWorld.customize.flat.layer.top=Top - %d
createWorld.customize.flat.removeLayer=Remove Layer
createWorld.customize.flat.tile=Layer Material
createWorld.customize.flat.title=Superflat Customization
createWorld.customize.presets=Presets
createWorld.customize.presets.list=Alternatively, here's some we made earlier!
createWorld.customize.presets.select=Use Preset
createWorld.customize.presets.share=Want to share your preset with someone? Use the below box!
createWorld.customize.presets.title=Select a Preset

createWorldUpsell.removeTrial=Do not show trial in worlds list
createWorldUpsell.title=Create New World or Realm?
createWorldUpsell.title.realm=Create New Realm
createWorldUpsell.button.realm=New Realm
createWorldUpsell.button.world=New World
createWorldUpsell.button.trialText=Start 30 day free trial
createWorldUpsell.price.realm=%1$s/Month
createWorldUpsell.price.coin.realm=%1$s Minecoins/Month
createWorldUpsell.price.world=Free
createWorldUpsell.realm.sellPoint1=Add infinite members and play online with up to §d%d§r friends
createWorldUpsell.realm.sellPoint2=Saved online: Play on any Minecraft device with Minecraft Marketplace
createWorldUpsell.realm.sellPoint3=Takes up zero storage space on your device
createWorldUpsell.realm.sellPoint4=Friends can play even when you are away
createWorldUpsell.signIn=Create Realm with Microsoft Account
createWorldUpsell.world.sellPoint1=Play online with up to 7 friends
createWorldUpsell.world.sellPoint2=Saved to your device: Play offline
createWorldUpsell.world.sellPoint3=Uses device storage


createWorldScreen.action.editLocal=Make Infinite!
createWorldScreen.action.local=Create World!
createWorldScreen.action.realms=Create World!
createWorldScreen.action.realmsReset=Reset Realm
createWorldScreen.advanced=Advanced
createWorldScreen.cheatSettings=Cheats
createWorldScreen.worldSettings=World Options
createWorldScreen.alwaysDay=Always Day
createWorldScreen.bonusChest=Bonus Chest
createWorldScreen.pvp=Friendly Fire
createWorldScreen.immediaterespawn=Immediate Respawn
createWorldScreen.respawnRadius=Respawn Radius
createWorldScreen.trustPlayers=Trust Players
createWorldScreen.fireSpreads=Fire Spreads
createWorldScreen.mobLoot=Mob Loot
createWorldScreen.tileDrops=Tile Drops
createWorldScreen.keepInventory=Keep Inventory
createWorldScreen.naturalregeneration=Natural Regeneration
createWorldScreen.tntexplodes=TNT Explodes
createWorldScreen.weathercycle=Weather Cycle
createWorldScreen.daylightcycle=Do Daylight Cycle
createWorldScreen.mobSpawn=Mob Spawning
createWorldScreen.mobgriefing=Mob Griefing
createWorldScreen.educationtoggle=Education Edition
createWorldScreen.entitiesdroploot=Entities Drop Loot
createWorldScreen.commandblocksenabled=Command Blocks Enabled
createWorldScreen.experimentalgameplay=Use Experimental Gameplay
createWorldScreen.worldPreferences=World Preferences
createWorldScreen.startWithMap=Starting Map
createWorldScreen.defaultName=My World
createWorldScreen.create=Create
createWorldScreen.createOnRealms=Create on Realms
createWorldScreen.delete=Delete
createWorldScreen.deleteWorld=Delete World
createWorldScreen.delete.desc=Delete your world forever.
createWorldScreen.delete.confirm=Delete world permanently?
createWorldScreen.deleteWarningFormat=Are you sure you want to delete "%s"?  This world will be lost forever! (A long time!)
createWorldScreen.editTitleFormat=Edit "%s"
createWorldScreen.game.settings=Game Settings
createWorldScreen.gameMode=Game Mode
createWorldScreen.gameMode.default=Default Game Mode
createWorldScreen.gameMode.personal=Personal Game Mode
createWorldScreen.gameMode.adventure=Adventure
createWorldScreen.gameMode.creative=Creative
createWorldScreen.gameMode.creative.desc=Unlimited resources. No damage. Flying. No achievements even if you switch to survival later.
createWorldScreen.gameMode.survival=Survival
createWorldScreen.gameMode.survival.desc=Limited resources, you'll need tools. You may get hurt. Watch out for Monsters.
createWorldScreen.gameMode.serverDefault=Default
createWorldScreen.header.delete=World Permanence
createWorldScreen.header.editLocal=Edit your World
createWorldScreen.header.local=Create a World
createWorldScreen.header.realms=Create a Realms Server
createWorldScreen.header.realmsReset=Reset your Realm?
createWorldScreen.levelName=Name
createWorldScreen.levelSeed=Seed
createWorldScreen.multiplayer.settings=Multiplayer Settings
createWorldScreen.progress.local=Creating new world...
createWorldScreen.progress.realms=Resetting Realm...
createWorldScreen.seed.desc=Enter a seed to generate the same terrain again. Leave blank for a random world.
createWorldScreen.showCoordinates=Show Coordinates
createWorldScreen.worldType=World Type
createWorldScreen.classroomsettings=Show Classroom Settings
createWorldScreen.perfectweather=Perfect Weather
createWorldScreen.allowmobs=Allow Mobs
createWorldScreen.allowdestructiveitems=Allow Destructive Items
createWorldScreen.playerdamage=Player Damage
createWorldScreen.immutableworld=Immutable World
createWorldScreen.pvpdamage=Player VS Player Damage
createWorldScreen.randomtickspeed=Random Tick Speed
createWorldScreen.randomtickspeed.reset=Reset Random Tick Speed
createWorldScreen.showCoordinates=Show Coordinates
createWorldScreen.serverSimDistance=Simulation Distance
createWorldScreen.serverSimDistanceFormat=%s chunks
createWorldScreen.check_internet_connection=You are not connected to the internet. Your friends will not be able to join your multiplayer worlds. Sign in to your Microsoft Account to play with your friends.
createWorldScreen.disconnectedXboxLive=You are not connected to Microsoft services. Your friends with Gamertags will not be able to join your multiplayer world. Sign in to your Microsoft Account to play with your friends.
createWorldScreen.clearPlayerData.button=Clear Player Data
createWorldScreen.clearPlayerData.title=Delete Player Data from World?
createWorldScreen.clearPlayerData.body1=Select "All Player Data" to delete absolutely all player data from the world. The next time any player enters the world, they will start with fresh, default data.
createWorldScreen.clearPlayerData.body2=Selecting "Keep Owning Player Data", will retain your current data (such as inventory items & location) and apply it the 'World Owner'. This is any player that loads the world locally or the owner of a realm it's on. 
createWorldScreen.clearPlayerData.all=All Player Data
createWorldScreen.clearPlayerData.allExceptLocal=Keep Owning Player Data
createWorldScreen.clearPlayerData.cancel=Cancel
createWorldScreen.clearPlayerData.progress=Clearing Player Data


## Cross Platform Toggle
crossPlatformToggle.startMenuScreen.title=Cross-Platform Play
crossPlatformToggle.startMenuScreen.enable=Enable Cross-Platform Play
crossPlatformToggle.startMenuScreen.enable.line1=Do you want to enable cross-platform play?
crossPlatformToggle.startMenuScreen.enable.line2=Cross-platform play allows you and friends on other platforms to connect and play together!
crossPlatformToggle.startMenuScreen.disable=Disable Cross-Platform Play
crossPlatformToggle.startMenuScreen.disable.line1=Do you want to disable cross-platform play?
crossPlatformToggle.startMenuScreen.disable.line2=You will no longer connect or play with friends on other platforms.
crossPlatformToggle.crossPlatformDisabled.friends=Enable cross-platform play in the main menu to be able to join Realms, LAN Games, and play with all your Minecraft friends.
crossPlatformToggle.crossPlatformDisabled.invite=Enable cross-platform play in the main menu to invite friends on other platforms.
crossPlatformToggle.crossPlatformDisabled.realms=Enable cross-platform play in the main menu to join Realms.
crossPlatformToggle.crossPlatformDisabled.servers=Enable cross-platform play in the main menu to add or join Servers.
crossPlatformToggle.crossPlatformDisabled.multiplayerSettings=Enable cross-platform play in the main menu to play with friends on other platforms.

realmsPendingInvitationsScreen.pendingInvitations=New Realms Memberships
realmsPendingInvitationsScreen.noInvites=You have no pending memberships.
realmsPendingInvitationsScreen.fetchingInvites=Fetching memberships...
realmsPendingInvitationsScreen.decline=Decline
realmsPendingInvitationsScreen.accepted=Accepted
realmsPendingInvitationsScreen.declined=Declined
realmsPendingInvitationsScreen.showFriendInvites=Only show memberships from Friends.

realmsInvitationScreen.addingPlayers=Adding Players...
realmsInvitationScreen.findFriends=Find friends
realmsInvitationScreen.continue=Continue
realmsInvitationScreen.done=Done, Let's Play!
realmsInvitationScreen.friends=Friends
realmsInvitationScreen.invitedFriends=Invited Friends
realmsInvitationScreen.members=Members
realmsInvitationScreen.unableToAddPlayers=Could Not Add Players
realmsInvitationScreen.loadingFriends=Loading Friends and Members...
realmsInvitationScreen.loadingProfiles=Loading Profiles (%s / %s)
realmsInvitationScreen.noInvites=No players could be found.
realmsInvitationScreen.removed=Removed
realmsInvitationScreen.savingChanges=Saving Changes...
realmsInvitationScreen.sendingInvitesFailed=Something went wrong.  We could not add the players you selected to your Realm.  Please try again later.
realmsInvitationScreen.title=Add Members
realmsInvitationScreen.undo=Undo
realmsInvitationScreen.unblock=Unblock
realmsInvitationScreen.unblocked=Unblocked
realmsInvitationScreen.blocked=Blocked
realmsInvitationScreen.myRealm=My Realm
realmsInvitationScreen.fetchingLink=Fetching new link...
realmsInvitationScreen.shareTitle=Realms Invitation
realmsInvitationScreen.shareText=Come join me in this realm!
realmsInvitationScreen.refreshDialogTitle=Disable Current Link?
realmsInvitationScreen.refreshDialogLabel=This will disable the current link and generate a new one.  Anyone who became a member already will still be able to join your Realm.
realmsInvitationScreen.createLinkDialogTitle=Generate Realm Link?
realmsInvitationScreen.createLinkDialogLabel=Anyone who opens this link will become a member of your Realm.  You can disable a link by generating a new one.
realmsInvitationScreen.shareDialogTitle=Download Backup Before Sharing?
realmsInvitationScreen.shareDialogLabel=Anyone with this link can become a member of your Realm.  Be careful who you share it with.  You may want to download a backup of your world first.  You can remove members from Realm settings.
realmsInvitationScreen.shareDialogShareButton=Share
realmsInvitationScreen.shareDialogCopyButton=Copy
realmsInvitationScreen.shareDialogNewLinkButton=Create new link
realmsInvitationScreen.closedRealmWarning=Your Realm is closed.  Members cannot join.
realmsInvitationScreen.generate=Generate
realmsInvitationScreen.disable=Disable
realmsInvitationScreen.backup=Download
realmsInvitationScreen.shareByLink=Share link
realmsInvitationScreen.shareLinkToRealm=Share link to Realm
realmsInvitationScreen.shareInfoMessage=Anyone with the link can join your Realm. Go to Game > Download World to download a backup of your world.
realmsInvitationScreen.refreshLinkInfoMessage=This will invalidate the old link and create a new one. Do you wish to continue?
realmsInvitationScreen.kick=Remove
realmsInvitationScreen.kickAndBlock=Block
realmsInvitationScreen.kickPopupTitle=Revoke Access?
realmsInvitationScreen.kickPopupText=Do you want to revoke access for this player? You can undo this at any time.
realmsInvitationScreen.copyToastMessage=Link to your realm copied to clipboard!
realmsInvitationScreen.playerFilterTitle=Filter players
realmsInvitationScreen.setPermissionFail=Failed to set permission for user %s.
realmsInvitationScreen.setDefaultPermissionFail=Failed to set default permissions.

realmJoining.progressTitle=Joining Realm...

realmsClearMembers.clearMembersTitle=Clear Members?
realmsClearMembers.clearMembersText=Do you want to clear the members list of this realm?
realmsClearMembers.clear=Clear Members

realmsSharingScreen.join=Join
realmsSharingScreen.joinTitle=Join realm?
realmsSharingScreen.joinMessage=Join realm %s?
realmsSharingScreen.blockedInviteTitle=Permission required
realmsSharingScreen.blockedInviteMessage=You will need the Realm owner's permission to join this Realm.
realmsSharingScreen.badInviteTitle=Invalid Realm Link
realmsSharingScreen.badInviteMessage=This invite link could not be found, the owner of the Realm may have changed the link. Ask them for an updated link.
realmsSharingScreen.regenerateLinkFailed=Failed to generate new invite link
realmsSharingScreen.cantJoinExpiredTitle=%s's Realm is expired
realmsSharingScreen.cantJoinExpiredMessage=%s has expired. Ask %s to renew it.
realmsSharingScreen.cantJoinClosedTitle=%s closed this Realm
realmsSharingScreen.cantJoinClosedMessage=%s is closed.  Ask %s to reopen it.
realmsSharingScreen.failedSignInModalTitle=Sign in to join this Realm
realmsSharingScreen.failedSignInModalMessage=You need to be signed in to a Microsoft Account to join a Realm. Please sign in and try again.
realmsSharingScreen.invalidPermissionTitle=Invalid permissions

realmsCreateScreen.title.create=Create a Realm Server
realmsCreateScreen.title.extend=Extend Your Realm
realmsCreateScreen.title.renew=Renew Your Realm
realmsCreateScreen.defaultRealm=%s's Realm
realmsCreateScreen.creatingRealm=Creating Realm...
realmsCreateScreen.nameHeader.name=Realm Name
realmsCreateScreen.nameHeader.rename=Rename Realm?
realmsCreateScreen.termsAndConditionsAgree=I agree
realmsCreateScreen.termsAndConditionsHeader=Terms and Conditions
realmsCreateScreen.viewTermsAndConditions=View Terms and Conditions
realmsCreateScreen.viewPrivacyPolicy=View Privacy Policy
realmsCreateScreen.durationHeader=Duration
realmsCreateScreen.durationShort=30 Days
realmsCreateScreen.durationLong=180 Days
realmsCreateScreen.sizeHeader=Tier
realmsCreateScreen.purchasePrefix.create=Create
realmsCreateScreen.purchasePrefix.extend=Extend
realmsCreateScreen.purchasePrefix.renew=Renew
realmsCreateScreen.subscription.trial=Create For Free!%s(%s/month after first 30 days)
realmsCreateScreen.subscription.buy=Create for %s per month
realmsCreateScreen.subscription.renew=Renew for %s per month
realmsCreateScreen.consumable.trial=Create for Free!
realmsCreateScreen.consumable.buy=Create for %s
realmsCreateScreen.consumable.renew=Renew for %s
realmsCreateScreen.consumable.extend=Extend for %s
realmsCreateScreen.errorRealmName=Your realm name cannot be blank.
realmsCreateScreen.errorRealmNameAndTOS=Your realm name cannot be blank, and you must accept the terms of service to create a realm.
realmsCreateScreen.errorTOS=Must accept terms and conditions to create a realm.
realmsCreateScreen.goBack=Go Back
realmsCreateScreen.createFailed.title=Something Went Wrong
realmsCreateScreen.purchaseFailed=Sorry, we were unable to complete your realm purchase. Maybe check your internet connection?
realmsCreateScreen.createFailed.content=Your purchase succeeded, but we couldn't create your Realm right now.  We will create your Realm as soon as possible.  Check back later on this device.
realmsCreateScreen.createFailed.profanity=The Realm name %s is not allowed.
realmsCreateScreen.createFailed.generic=Club name could not be verified. Please try again later.
realmsCreateScreen.maxRealms.title=Maximum Subscriptions
realmsCreateScreen.maxRealms.content=You have the maximum number of active subscriptions on this platform and cannot create a new one right now.
realmsCreateScreen.offerNotFound.title=Product not found
realmsCreateScreen.offerNotFound.content=Please try again later
realmsCreateScreen.offerNotAvailable.title=Product not available for purchase
realmsCreateScreen.offerNotAvailable.content=Please try again later
realmsCreateScreen.extendRealms.title=Realm Subscription Full
realmsCreateScreen.extendRealms.content=You have extended your Realms subscription as long as possible.  You cannot add this additional time right now.  Try again later.
realmsCreateScreen.usersTwo=2 Players
realmsCreateScreen.usersTen=10 Players
realmsCreateScreen.viewTOS=To view the terms and conditions for Minecraft Realms, please visit https://aka.ms/minecraftrealmsterms in any web browser.
realmsCreateScreen.viewPrivacyPolicyText=To view the privacy policy for Minecraft Realms, please visit https://aka.ms/mcprivacy in any web browser.
realmsCreateScreen.waitingPurchase=Purchase in progress! This shouldn't take long.
realmsCreateScreen.paymentFailed.title=Payment service
realmsCreateScreen.paymentFailed.body=Unable to connect to Realms and complete your purchase at this time. Please try again later.
realmsCreateScreen.incomplete=Incomplete Realm
realmsCreateScreen.incomplete.create.a=It looks like we didn't finish things last time.  Let's complete creating your realm now.
realmsCreateScreen.incomplete.create.b=It looks like you started creating a realm with a different Microsoft Account.  Do you want to complete creating that realm with this Microsoft Account as the owner?
realmsCreateScreen.incomplete.create.c=Someone started creating a Realm with the billing account on this device.  Would you like to complete creating that Realm with this Microsoft Account as the owner?
realmsCreateScreen.incomplete.renew.a=It looks like we didn't finish things last time.  Let's complete renewing your realm now.
realmsCreateScreen.incomplete.renew.b=It looks like you started renewing a realm with a different Microsoft Account.  Do you want to complete renewing that realm with this Microsoft Account as the owner?
realmsCreateScreen.incomplete.renew.c=Someone started renewing a realm with the billing account on this device.  Would you like to complete renewing that realm with this Microsoft Account as the owner?
realmsCreateScreen.incomplete.override.renew=You are attempting to create a new realm but a prior purchase was found for renewing a realm.  Would you like to apply that purchase now?
realmsCreateScreen.incomplete.override.create=You are attempting to renew a realm but a prior purchase was found for creating a realm.  Would you like to apply that purchase now?
realmsCreateScreen.incomplete.override.realm=You are attempting to renew a realm but a prior purchase was found for a different realm.  Would you like to apply that purchase now?
realmsCreateScreen.prepare.store.title=Purchase History Needed
realmsCreateScreen.prepare.store.body=We need you to sign in to the store to view your purchase history before we can continue.
realmsCreateScreen.prepare.store.button=Sign In to Store
realmsCreateScreen.nsoinfotext=To use Realms, you need to subscribe to Nintendo Switch Online.
realmsCreateScreen.playNowTitle=Play on Realm, Now?
realmsCreateScreen.playNowMessage1=You can invite friends to your Realm and start playing now.
realmsCreateScreen.playNowMessage2=Otherwise, you can find it anytime in the Worlds tab of the Play screen. Edit it to change game modes or even upload any world to your Realm.
realmsCreateScreen.playWithFriends=Play with Friends
realmsCreateScreen.playSolo=Play Solo
realmsCreateScreen.goBack=Back to Store
realmsCreateScreen.purchaseConfirmation.title=Purchase Realm?
realmsCreateScreen.purchaseConfirmation.create.message=Are you sure you want to create a Realm?
realmsCreateScreen.purchaseConfirmation.extend.message=Are you sure you want to extend a Realm?
realmsCreateScreen.purchaseConfirmation.renew.message=Are you sure you want to renew a Realm?
realmsCreateScreen.purchaseConfirmation.continue=Confirm Purchase
realmsCreateScreen.purchaseConfirmation.cancel=Cancel

realmsCreateScreen.title=Create a Realm
realmsCreateScreen.nameHeader=Realm Name
realmsCreateScreen.chooseWorld=Create Realm: Choose World

realmsConfigurationScreen.realmName=Realm Name
realmsConfigurationScreen.realmDescription=Realm Description
realmsConfigurationScreen.open=Open Realm
realmsConfigurationScreen.close=Close Realm
realmsConfigurationScreen.resetRealm=Resetting a Realm permanently erases the world and starts over.  Are you sure you want to do this?
realmsConfigurationScreen.confirmReset=Confirm Reset
realmsConfigurationScreen.resetRealmTryAgain=Failed to reset the Realm. Would you like to try again?
realmsConfigurationScreen.failedOpenCloseTitle=Failed open/close.
realmsConfigurationScreen.failedOpenClose=Failed to open/close the Realm. Would you like to try again?

realmsWorld.notAvailable=Your device does not support Minecraft Realms.
realmsWorld.ownerPay=The owner pays monthly. Their friends can join for free!
realmsWorld.realmsDescription=Realms are private Minecraft worlds that are always available for you and your friends.
realmsWorld.realmsDescription.paragraph2=You can play on them even when the owner of the Realm is away and from any device running Minecraft!
realmsWorld.realmsDescription.paragraph3=Explore your Realm today!
realmsWorld.realmsDescription.Beta.line1=Realms, the safe, simple way to share a Minecraft world with friends, is not available while using beta versions of Minecraft.
realmsWorld.realmsDescription.Beta.line2=If you'd like to stop using the beta and access Realms, click below for instructions.
realmsWorld.newRealm=New Realm
realmsWorld.connectLive=Sign In to Try for Free!
realmsWorld.owner=owner
realmsWorld.leaveBeta=Leave the Beta?
realmsWorld.newRealmTrial=Start Your Free 30 Day Trial%s(%s/mo after the first month)
realmsWorld.creatingWorld=Creating World

realmsPlus.popup.top_button_text=More Info
realmsPlus.popup.bottom_button_text=Renew Subscription
realmsPlus.popup.title=Realms Plus Subscription Expired
realmsPlus.popup.message=Your Realms Plus subscription has expired. To regain access to your Realm and the packs or skins you received from Realms Plus you need to renew your subscription. 

realmsPlusUpgradeNotice.title=Welcome to Realms Plus!
realmsPlusUpgradeNotice.body=Your Realms subscription has been upgraded to Realms Plus. You now have access to 50+ content packs from the marketplace at no additional cost. Up to 10 players can play at one time, and they get access to all the subscriber content in your Realm – for free!
realmsPlusUpgradeNotice.continue=Continue
realmsPlusUpgradeNotice.viewpacks=View Realms Plus Packs

network.onlinePlay.title=Play with Friends
network.onlinePlay.msg=You can invite friends to your worlds, view your friends' creations, and craft in each other's worlds.
network.onlinePlay.instruction=From the Play menu, you can join your friends' worlds or invite them to your worlds and Realms.
network.thirdparty.connect.splitscreen=To play split-screen multiplayer online, sign in to a %s account.
network.thirdparty.connect.benefit=Sign in with your %s to play online with friends and check out cool stuff in the store.
network.thirdparty.connect.store=You will need a %s to browse the store.
network.thirdparty.connect.achievements=You will need a %s to earn achievements.
network.thirdparty.findfriends.failed=We were unable to connect to Microsoft account services. Maybe check your internet connection?


networkWorld.add_friend=Add Friend
networkWorld.add_server=Add Server
networkWorld.add_label=Add
networkWorld.lan_label=LAN Games
networkWorld.servers_label=Servers
networkWorld.friends_label=Friends
networkWorld.joinable_friends_label=Joinable Friends
networkWorld.no_joinable_friends_label=Your friends are not playing Minecraft right now.
networkWorld.check_internet_connection=Oops! Maybe check your internet connection? We can't find your friends' worlds.
networkWorld.connect_to_xbl=Sign in with a Microsoft Account to play with friends.
networkWorld.memberOfTooManyRealms=You have reached the realms membership limit.
networkWorld.multiplayerPrivilegesBlocked=You cannot play online multiplayer because of how your Microsoft Account is set up. Review and change your privacy settings at aka.ms/accountsettings.
networkWorld.versionOutOfDate=You cannot play online multiplayer because you are not running the latest version. Please install the latest version to access online features.
networkWorld.more_servers=More Servers
networkWorld.thirdparty.connect=Connect to '%s'
networkWorld.join=Join
networkWorld.joinByCode=Join Realm
networkWorld.realmsHashPlaceholder=Invite Code...
networkWold.joinByCodeHelpText=If you have received a Realm Invite Link, enter the code to join.

## The following text is placed on in-game signs, which have a 4-line, 15-character-per-line limit.
## The intent of the message is to convey to the reader that the sign author's chat permissions are
## blocked.  This cannot exceed the 4-line, 15-character/line limit!
## Use dashes ("---") to fill in empty lines.
networkWorld.userSignTextBlockedLine0=---
networkWorld.userSignTextBlockedLine1=Author
networkWorld.userSignTextBlockedLine2=is blocked
networkWorld.userSignTextBlockedLine3=---
networkWorld.userSignTextMutedLine0=---
networkWorld.userSignTextMutedLine1=Author
networkWorld.userSignTextMutedLine2=is blocked
networkWorld.userSignTextMutedLine3=---

networkWorld.userBookTextBlocked=[Author is Blocked]
networkWorld.userBookTextMuted=[Author is Blocked]

localWorld.no_local_world_label=You have not created any worlds yet.
localWorld.no_local_worlds_present=Did your worlds disappear?  Try changing your storage location:

thirdPartyWorld.comingSoon=Coming Soon!
thirdPartyWorld.Featured=Featured Servers
thirdPartyWorld.featuredComingSoon=Coming Soon
thirdPartyWorld.notConnected=We could not connect to Servers right now. We will try again soon.
thirdPartyWorld.playNow=Play Now!
thirdPartyWorld.loadingServers=Retrieving server information, please wait...
thirdPartyWorld.loadingFeaturedServers=Fetching Servers...

realmsSettingsScreen.playerCountLabel=Size
realmsSettingsScreen.forceResourcePackFail=Failed to set force resource pack value
realmsSettingsScreen.failedUploadPack=Failed to upload pack %s.
realmsSettingsScreen.failedApply=Some of the selected content could not be applied.
realmsSettingsScreen.realmDurationLabel=Duration
realmsSettingsScreen.manageSubscriptionButton=Manage Subscription
realmsSettingsScreen.manageSubscriptionsButton=Manage Subscriptions
realmsSettingsScreen.renewRealmButton=Renew Realm
realmsSettingsScreen.extendRealmButton=Extend Realm
realmsSettingsScreen.deleteRealmButton=Delete Realm
realmsSettingsScreen.openRealmButton=Open Realm
realmsSettingsScreen.closeRealmButton=Close Realm
realmsSettingsScreen.playRealmButton=Play
realmsSettingsScreen.playDisabledRealmButton=Realm Closed
realmsSettingsScreen.playExpiredRealmButton=Realm Expired
realmsSettingsScreen.selectorSectionLabel=Edit "%s"
realmsSettingsScreen.selectorSectionLabelRealm=Realm
realmsSettingsScreen.worldSectionTitle=World
realmsSettingsScreen.gameSectionTitle=Game
realmsSettingsScreen.playersSectionTitle=Players
realmsSettingsScreen.membersSectionTitle=Members
realmsSettingsScreen.subscriptionSectionTitle=Subscription
realmsSettingsScreen.devOptionsSectionTitle=Dev Options
realmsSettingsScreen.backupSection=Backups
realmsSettingsScreen.backupInfo=You can restore the world on your Realm with a backup found in this list.
realmsSettingsScreen.backupReplaced=World has been replaced with the selected backup.
realmsSettingsScreen.backupReplacedFailed=Replacing the world with the selected backup failed.
realmsSettingsScreen.backupReplacedTimeout=Replacing the world with the selected backup timed out.
realmsSettingsScreen.backupVersion=Version: %s
realmsSettingsScreen.backupVersionUnknown=Version: Unknown
realmsSettingsScreen.deleteRealm=Delete Realm
realmsSettingsScreen.deleteRealmToast=Realm %s has been deleted.
realmsSettingsScreen.deleteRealmFailToast=Delete of Realm %s failed.
realmsSettingsScreen.deleteRealmModalText=Your Realm will be permanently deleted. You will not be able to access your world, your member list or the club.
realmsSettingsScreen.backupRestoreHeader=Replacing World
realmsSettingsScreen.backupModalText=By replacing your realm with this backup, you will erase all recent progress from your Realm. The Realm will restart and all active players will need to reconnect.
realmsSettingsScreen.replaceWithBackup=Replace Realm with Backup
realmsSettingsScreen.backupInProgress=Replacing realm world with backup
realmsSettingsScreen.realmNameLabel=Realm Name
realmsSettingsScreen.realmNameEditPlaceholder=Enter Realm Name
realmsSettingsScreen.realmDescriptionEditPlaceholder=Enter Realm Description
realmsSettingsScreen.replaceLoadingText=We're replacing your Realm with a backup.
realmsSettingsScreen.resetWorldButton=Reset World
realmsSettingsScreen.downloadWorldButton=Download World
realmsSettingsScreen.downloadWorldNameCopy=%s - Copy
realmsSettingsScreen.uploadWorldButton=Replace World
realmsSettingsScreen.difficultyOptionLabelFormat=%s %s
realmsSettingsScreen.gameModeOptionLabel=Game Mode
realmsSettingsScreen.survivalRadioButton=Survival
realmsSettingsScreen.creativeRadioButton=Creative
realmsSettingsScreen.resetButtonHelperLabel=Start over from scratch?
realmsSettingsScreen.maxPlayersLabelFormat=%d Friends
realmsSettingsScreen.durationLabelFormat=%d Days Remaining
realmsSettingsScreen.subscriptionLabelFormat=Renews every %d days
realmsSettingsScreen.fetchingWorldInfo=Fetching Realm Information...
realmsSettingsScreen.updatingWorldInfo=Updating Realm Information...
realmsSettingsScreen.resettingRealm=Resetting Realm...
realmsSettingsScreen.applyPacks=Applying packs...
realmsSettingsScreen.openingRealm=Opening Realm...
realmsSettingsScreen.closingRealm=Closing Realm...
realmsSettingsScreen.realmInfoError=Failed to retrieve realm information. Please try again later.
realmsSettingsScreen.realmJoinError=Failed attempting to join realm. Please try again later.
realmsSettingsScreen.realmResetError=Failed to reset the realm. Please try again later.
realmsSettingsScreen.realmOpenError=Failed to open realm.
realmsSettingsScreen.realmCloseError=Failed to close realm.
realmsSettingsScreen.reset=Reset
realmsSettingsScreen.resetRealm=This will permanently delete the world and create a new one.  Are you sure you want to do this?
realmsSettingsScreen.confirmReset=Delete & Reset?
realmsSettingsScreen.closeRealmTitle=Close Realm?
realmsSettingsScreen.closeRealmMessage=Closing the realm will shut it down. Anyone currently playing on the realm will be disconnected. Are you sure you want to do this?
realmsSettingsScreen.LoadingSubscription=Loading subscription...
realmsSettingsScreen.failedLoadingSubscription=Failed Loading subscription
realmsSettingsScreen.matchingPurchasePlatformFormat=You purchased your subscription in the %s
realmsSettingsScreen.nonMatchingPurchasePlatformFormat=You started your subscription in the %s. You can only extend your subscription there.
realmsSettingsScreen.nonMatchingPurchasePlatformGeneric=You can manage your Realm subscription in the platform store where you originally purchased it.
realmsSettingsScreen.consumableToSubscriptionTransitionInfo=You can't extend your Realm right now. We have now introduced subscriptions for Realms on Xbox and you can buy a new subscription as soon as your Realm has expired in %d days. But don't worry! We will give you 14 extra days for free and your Realm will be kept online, so you will have plenty of time to extend it by then.
realmsSettingsScreen.consumableToSubscriptionGracePeriodInfo=Your Realm has expired, but don't worry! We will keep it online for another %d days. You can now go ahead and extend it using the new auto-renewing subscription, so you don't have to worry about keeping your Realm up and running ever again.
realmsSettingsScreen.consumableToSubscriptionTransitionInfoExpired=Your Realm has expired. Go ahead and extend it using the new auto-renewing subscription, that way you don't have to worry about keeping your Realm up and running ever again.

realmsSettingsScreen.switchStoreDisplayName=Nintendo eShop
realmsSettingsScreen.iosStoreDisplayName=App Store
realmsSettingsScreen.googlePlayStoreDisplayName=Google Play store
realmsSettingsScreen.windowsStoreDisplayName=Windows Store
realmsSettingsScreen.xboxOneStoreDisplayName=Xbox Store
realmsSettingsScreen.amazonStoreDisplayName=Amazon Appstore
realmsSettingsScreen.oculusStoreDisplayName=Oculus Store
realmsSettingsScreen.berwickStoreDisplayName="PlayStation Store"
realmsSettingsScreen.unknownStoreDisplayName=unknown store
realmsSettingsScreen.extendingRealm=Extending Realm...
realmsSettingsScreen.offerError.title=Purchase Pending
realmsSettingsScreen.offerError.content=A purchase is already pending. You cannot make another purchase until it has been resolved. Please try again later.
realmsSettingsScreen.dev_branch_label=Which commit should realm run on?
realmsSettingsScreen.dev_current_version_label=Current Version:
realmsSettingsScreen.dev_matching_version_label=Commit matching client:
realmsSettingsScreen.dev_all_commits_label=All available commits:
realmsSettingsScreen.dev_no_matching_version_message=NO MATCH FOR '%s'
realmsSettingsScreen.dev_prev_page_label=Prev
realmsSettingsScreen.dev_next_page_label=Next
realmsSettingsScreen.uploadConfirmationMessage=This will remove your current world from your Realm and let you replace it with a new one.  Your Realm members won't have access to your current world anymore. Do you want to continue?
realmsSettingsScreen.uploadConfirmationTitle=Replace World?
realmsSettingsScreen.whitelist_invite=Invite
realmsSettingsScreen.searchBox=Search...
realmsSettingsScreen.unknownPackName=Unknown Name
realmsSettingsScreen.viewSubscriptionsButton=View Subscriptions

realmsSettingsScreen.extendConsumableButton=Extend Subscription
realmsSettingsScreen.extendConsumableButton.30=Extend by 30 Days
realmsSettingsScreen.extendConsumableButton.180=Extend by 180 Days

xblLogin.LoginMessageTitle=Microsoft Account
xblLogin.LoginMessage=Connecting…

xbl.savePurchase.description.line1=Do you want to play with whatever you buy from any Minecraft Store enabled system? Sign in with a FREE Microsoft account and sync your purchased Skins, Textures, Maps, and Mash-ups to use and play in Minecraft!
xbl.savePurchase.description.line2=If you lose your system or uninstall Minecraft, your purchases will still be synced and accessible on any system when you use your Microsoft account.
xbl.savePurchase.leaveOnDevice=Leave on Device
xbl.savePurchase.title=Save purchases to a Microsoft Account?
xbl.savePurchase.saveButton=Save to Microsoft Account
xbl.savePurchase.toast.title=Success!
xbl.savePurchase.toast.message=Purchases synced to Microsoft Account!
xbl.savePurchase.inProgress.message=Transferring purchases to Microsoft Account...

xbl.syncIAP.title=Sync purchases to your Microsoft Account?
xbl.syncIAP.description=Sign in to a Microsoft Account to sync your Skins, Texture Packs, Worlds, and Mash-ups. They will be there for you on any device where you play Minecraft.
xbl.syncIAP.syncNow=Sync Now...
xbl.syncIAP.syncLaterEllipsis=Sync Later...
xbl.syncIAP.syncLater=Sync Later
xbl.syncIAP.toast.title=Success!
xbl.syncIAP.toast.message=Purchases synced to your Microsoft Account!
xbl.syncIAP.inProgress.message=Syncing purchases to your Microsoft Account…
xbl.syncIAP.skipSignIn.prompt.description=This Microsoft Account will not be the primary account for this device if you do not sync this device's purchases to this account. You will not automatically sign in when you start Minecraft.

xbl.syncIAP.confirmSync.prompt.title=Sync to %s?
xbl.syncIAP.confirmSync.prompt.description.line1=Would you like to sync your app store purchases to %s?
xbl.syncIAP.confirmSync.prompt.description.line2=You can only sync packs purchased outside of the Minecraft Marketplace once. They will continue to be available to anyone who plays on this device.
xbl.syncIAP.confirmSync.prompt.yes=Yes, Sync My Purchases

xbl.transferEntitlements.error.title=Account Error
xbl.transferEntitlements.error.body.button=There is an error with your account. You will be unable to use the marketplace until we have resolved the issue. If this issue persists for several days please contact support using the help button.
xbl.transferEntitlements.error.body.nobutton=There is an error with your account. You will be unable to use the marketplace until we have resolved the issue. If this issue persists for several days please contact support.
xbl.transferEntitlements.error.buttontext=help.mojang.com
xbl.transferEntitlements.error.errortitle=Error List:
xbl.transferEntitlements.error.error1=Error Number
xbl.transferEntitlements.error.error2=ID Number

store.sign_in_warning.no_xbl_and_local_content=Important! You have purchased packs and/or Minecoins that are only available on this device and might be lost due to a device malfunction, loss, or uninstalling Minecraft! Sign in with a Microsoft Account (free) and transfer your pack entitlements and Minecoins to that account to protect and access your them on any device that you sign in on.
store.sign_in_warning.no_xbl_and_no_local_content.line1=Important! Purchased packs that are only available on this device can be lost due to device malfunction, loss or uninstalling Minecraft!
store.sign_in_warning.no_xbl_and_no_local_content.line2=Sign in with a Microsoft Account (free) and transfer your pack entitlements to that account to protect your purchased packs and Minecoins.
store.sign_in_warning.no_xbl_and_no_local_content.line3=In addition, you will be able to access them on any device you use to play Minecraft!
store.sign_in_warning.no_xbl_and_no_local_content.line4=Content purchased through a partner store will be available as long as your device is signed in to your partner store account.

xbl.skipSignIn.prompt.title=Temporary Account
xbl.skipSignIn.prompt.description=This Microsoft Account will not be the primary account for this device if you do not transfer this device's purchases to this account. You will not automatically sign in when you start Minecraft.
xbl.signOut.title=Microsoft Account Purchases!
xbl.signOut.message1=While you are signed out of your Microsoft Account you will not have access to the Minecoins and items you bought in the store.
xbl.signOut.message2=Are you sure you want to do this?

date.short_january=Jan
date.short_february=Feb
date.short_march=Mar
date.short_april=Apr
date.short_may=May
date.short_june=Jun
date.short_july=Jul
date.short_august=Aug
date.short_september=Sep
date.short_october=Oct
date.short_november=Nov
date.short_december=Dec
date.formatted=%s %d %d

dayOneExperience.carousel.title=Welcome to New Minecraft!
dayOneExperience.carousel.welcome_text=There's a lot of exciting new stuff in this new version of Minecraft! Check out the Marketplace, create a Realm, and play online with Cross-Platform friends!
dayOneExperience.carousel.marketplace=The Minecraft Marketplace is a place to find new skins, worlds, textures, and mash-up packs by various talented creators.
dayOneExperience.carousel.cross_platform=Cross-platform play lets you connect with players on different platforms. Sign in to your Microsoft Account to play with all your Minecraft friends!
dayOneExperience.carousel.realms=Realms are servers run by us, just for you and your friends. Keep your Minecraft world online and always accessible, even when you log-off.
dayOneExperience.carousel.villager=New Minecraft is the only place to receive future updates containing new features, mobs, items, blocks and much more!

dayOneExperience.tab.landing=What's New
dayOneExperience.tab.marketplace=Marketplace
dayOneExperience.tab.crossplatform=Cross-platform Play
dayOneExperience.tab.realms=Realms
dayOneExperience.tab.villager=More Content

dayOneExperience.finish=Finish
dayOneExperience.next=Next
dayOneExperience.no_internet=Connect to the internet to load your previous skin.
dayOneExperience.intro_title=Minecraft has Updated!
dayOneExperience.intro_body_1=There's a lot of great new content in this update.
dayOneExperience.intro_body_2=Would you like help importing your saves, settings, and content from the previous version of Minecraft?
dayOneExperience.intro_welcome_1=You've updated to the latest version of Minecraft!
dayOneExperience.intro_welcome_2=If you would like to return to the previous version of Minecraft, you can find it on the main menu
dayOneExperience.editions_launch_warning=This is not the latest version of Minecraft, are you sure you want to launch the previous version of Minecraft?
dayOneExperience.editions_title=Launch Previous Version?
dayOneExperience.settings_imported=We've imported your settings for you!
dayOneExperience.settings_skin_imported=We've imported your skin and settings for you!
dayOneExperience.skin_current=Current Skin
dayOneExperience.skin_default=Default Skin
dayOneExperience.skin_tip=You can change your skin by selecting the Profile button on the main menu.
dayOneExperience.skip_title=Skip Conversion?
dayOneExperience.skip_body=Old Worlds can be converted at any time from the Play Game - Worlds menu.
dayOneExperience.world_import_time=Tip: Conversion time will vary based on the size of the world.
dayOneExperience.world_picker_title=Select a World to Convert
dayOneExperience.world_picker_skip=Skip for Now

death.attack.anvil=%1$s was squashed by a falling anvil
death.attack.arrow=%1$s was shot by %2$s
death.attack.arrow.item=%1$s was shot by %2$s using %3$s
death.attack.cactus=%1$s was pricked to death
death.attack.cactus.player=%1$s walked into a cactus whilst trying to escape %2$s
death.attack.drown=%1$s drowned
death.attack.drown.player=%1$s drowned whilst trying to escape %2$s
death.attack.explosion=%1$s blew up
death.attack.explosion.player=%1$s was blown up by %2$s
death.attack.fall=%1$s hit the ground too hard
death.attack.fallingBlock=%1$s was squashed by a falling block
death.attack.fireball=%1$s was fireballed by %2$s
death.attack.fireball.item=%1$s was fireballed by %2$s using %3$s
death.attack.fireworks=%1$s went off with a bang
death.attack.flyIntoWall=%1$s experienced kinetic energy
death.attack.generic=%1$s died
death.attack.indirectMagic=%1$s was killed by %2$s using magic
death.attack.indirectMagic.item=%1$s was killed by %2$s using %3$s
death.attack.inFire=%1$s went up in flames
death.attack.inFire.player=%1$s walked into fire whilst fighting %2$s
death.attack.inWall=%1$s suffocated in a wall
death.attack.lava=%1$s tried to swim in lava
death.attack.lava.player=%1$s tried to swim in lava to escape %2$s
death.attack.lightningBolt=%1$s was struck by lightning
death.attack.magic=%1$s was killed by magic
death.attack.magma=%1$s discovered floor was lava
death.attack.magma.player=%1$s walked on danger zone due to %2$s
death.attack.mob=%1$s was slain by %2$s
death.attack.onFire=%1$s burned to death
death.attack.onFire.player=%1$s was burnt to a crisp whilst fighting %2$s
death.attack.outOfWorld=%1$s fell out of the world
death.attack.player=%1$s was slain by %2$s
death.attack.player.item=%1$s was slain by %2$s using %3$s
death.attack.starve=%1$s starved to death
death.attack.thorns=%1$s was killed trying to hurt %2$s
death.attack.thrown=%1$s was pummeled by %2$s
death.attack.thrown.item=%1$s was pummeled by %2$s using %3$s
death.attack.wither=%1$s withered away
death.fell.accident.generic=%1$s fell from a high place
death.fell.accident.ladder=%1$s fell off a ladder
death.fell.accident.vines=%1$s fell off some vines
death.fell.accident.water=%1$s fell out of the water
death.fell.assist=%1$s was doomed to fall by %2$s
death.fell.assist.item=%1$s was doomed to fall by %2$s using %3$s
death.fell.finish=%1$s fell too far and was finished by %2$s
death.fell.finish.item=%1$s fell too far and was finished by %2$s using %3$s
death.fell.killer=%1$s was doomed to fall

deathScreen.deleteWorld=Delete world
deathScreen.hardcoreInfo=You cannot respawn in hardcore mode!
deathScreen.leaveServer=Leave server
deathScreen.message=You died!
deathScreen.quit=Main menu
deathScreen.quit.confirm=Are you sure you want to quit?
deathScreen.quit.confirmToMainMenuWarning=Are you sure you want to exit the game to the main menu?
deathScreen.quit.confirmToMainMenuTitleWarning=Quit to Main Menu?
deathScreen.quit.secondaryClient=Save and Leave
deathScreen.quit.secondaryClient.confirmLeaveWarning=Are you sure you want to save and leave the game?
deathScreen.quit.secondaryClient.confirmLeaveTitleWarning=Save and Leave
deathScreen.respawn=Respawn
deathScreen.score=Score
deathScreen.title=You died!
deathScreen.title.hardcore=Game over!
deathScreen.titleScreen=Title screen

demo.day.1=This demo will last five game days, do your best!
demo.day.2=Day Two
demo.day.3=Day Three
demo.day.4=Day Four
demo.day.5=This is your last day!
demo.day.6=You have passed your fifth day, use F2 to save a screenshot of your creation
demo.day.warning=Your time is almost up!
demo.demoExpired=Demo time's up!
demo.help.buy=Purchase Now!
demo.help.fullWrapped=This demo will last 5 ingame days (about 1 hour and 40 minutes of real time). Check the achievements for hints! Have fun!
demo.help.inventory=Use %1$s to open your inventory
demo.help.jump=Jump by pressing %1$s
demo.help.later=Continue Playing!
demo.help.movement=Use %1$s, %2$s, %3$s, %4$s and the mouse to move around
demo.help.movementMouse=Look around using the mouse
demo.help.movementShort=Move by pressing %1$s, %2$s, %3$s, %4$s
demo.help.title=Minecraft Demo Mode
demo.remainingTime=Remaining time: %s
demo.reminder=The demo time has expired, buy the game to continue or start a new world!

difficulty.lock.question=Are you sure you want to lock the difficulty of this world? This will set this world to always be %1$s, and you will never be able to change that again.
difficulty.lock.title=Lock World Difficulty

dimension.dimensionName0=Overworld
dimension.dimensionName1=Nether
dimension.dimensionName2=The End

disconnect.closed=Connection closed
disconnect.downloadPack=Error while downloading packs from server. Please try again.
disconnect.disconnected=Disconnected by Server
disconnect.endOfStream=End of stream
disconnect.genericReason=%s
disconnect.kicked=Was kicked from the game
disconnect.removed=You were removed from the game
disconnect.loginFailed=Failed to login
disconnect.loginFailedInfo=Failed to login: %s
disconnect.loginFailedInfo.invalidSession=Invalid session (Try restarting your game)
disconnect.loginFailedInfo.serversUnavailable=The authentication servers are currently down for maintenance.
disconnect.lost=Connection Lost
disconnect.overflow=Buffer overflow
disconnect.quitting=Quitting
disconnect.spam=Kicked for spamming
disconnect.timeout=Timed out

disconnectionScreen.header.realms_hidden=Multiplayer Invitation
disconnectionScreen.body.realms_hidden=Unable to join this specific server.
disconnectionScreen.cantConnect=Unable to connect to world.
disconnectionScreen.cantConnectToRealm=Unable to connect to realm.
disconnectionScreen.cantConnectToRealms=Unable to connect to realms.
disconnectionScreen.crossPlatformDisabled=Enable cross-platform play in the main menu to connect with friends on other platforms. To play with other friends online without cross-play, the host of the game must also have cross-play disabled via the main menu.
disconnectionScreen.incompatibleRealm=Your client is incompatible or out of date. Update your client to connect to realms.
disconnectionScreen.realmsAlphaEndedTitle=Testing the Realms Alpha has ended.  Thank you for helping!
disconnectionScreen.realmsAlphaEndedMessage=Realms is not available right now.
disconnectionScreen.disconnected=Disconnected from Server
disconnectionScreen.editionMismatch=This level was saved from Minecraft: Education Edition. It cannot be loaded.
disconnectionScreen.editionMismatchEduToVanilla=The server is not running Minecraft: Education Edition. Failed to connect.
disconnectionScreen.editionMismatchVanillaToEdu=The server is running an incompatible edition of Minecraft. Failed to connect.
disconnectionScreen.editionVersionMismatch.body=The world you selected cannot be opened in this version of Minecraft: Education Edition.
disconnectionScreen.editionVersionMismatch.title=Unable to load world.
disconnectionScreen.futureVersion=A newer version of the game has saved this level. It cannot be loaded.
disconnectionScreen.internalError.cantConnect=We were unable to connect you.
disconnectionScreen.internalError.cantEdit=There was a problem finding this server.
disconnectionScreen.internalError.cantFindEdit=We were unable to connect you. Try adding a new server.
disconnectionScreen.internalError.cantFindLocal=There was a problem loading this world.
disconnectionScreen.internalError.cantFindRealm=There was a problem finding this Realm.
disconnectionScreen.internalError.cantFindServer=There was a problem finding this server.
disconnectionScreen.invalidName=Invalid name!
disconnectionScreen.invalidPlayer=This world's multiplayer setting is set to friends only. You must be friends with the host of this world to join.
disconnectionScreen.invalidIP=Invalid IP address!
disconnectionScreen.invalidSkin=Invalid or corrupt skin!
disconnectionScreen.lockedSkin.title=Platform Restricted Skin!
disconnectionScreen.lockedSkin=The Skin that you have equipped is from a content pack that is not allowed in cross-platform multiplayer games.
disconnectionScreen.multiplayerLockedSkin.title=Multiplayer Restricted Skin!
disconnectionScreen.multiplayerLockedSkin=The Skin that you have equipped is from a content pack that is not allowed in multiplayer games.
disconnectionScreen.loggedinOtherLocation=Logged in from other location
disconnectionScreen.multiplayerDisabled=The world has been set to single player mode.
disconnectionScreen.noReason=You have been disconnected
disconnectionScreen.noInternet=Please check your connection to the internet and try again.
disconnectionScreen.notAllowed=You're not invited to play on this server.
disconnectionScreen.notAuthenticated=You need to authenticate to Microsoft services.
disconnectionScreen.outdatedClient=Could not connect: Outdated client!
disconnectionScreen.outdatedServer=Could not connect: Outdated server!
disconnectionScreen.outdatedClientRealms=You need to update your game to continue playing on this Realm.
disconnectionScreen.outdatedClientRealms.title=Realms Has Updated
disconnectionScreen.outdatedServerRealms=Your game is up-to-date but this Realm is not.
disconnectionScreen.outdatedServerRealms.title=Realms Update Pending Within 48 Hours
disconnectionScreen.realmsServerUpdateIncoming=Your Realm will soon be updated. You will need an update too.
disconnectionScreen.realmsServerUpdateIncoming.title=Check For Game Update Now?
disconnectionScreen.realmsWorldUnassigned=Assign version in dev options.
disconnectionScreen.realmsWorldUnassigned.title=Realm is Unassigned!
disconnectionScreen.scriptNotSupported=The world you are trying to enter requires your device to run scripts.  Your device doesn't support scripting at this time, so you can't join from this device.
disconnectionScreen.serverFull=Wow this server is popular! Check back later to see if space opens up.
disconnectionScreen.serverFull.title=Server Full
disconnectionScreen.serverIdConflict=Cannot join world. The account you are signed in to is currently playing in this world on a different device.
disconnectionScreen.sessionNotFound=Unable to connect to world. The world is no longer available to join.
disconnectionScreen.timeout=Connection timed out.
disconnectionScreen.nowifi=You must be connected to Wifi to play with your friends.
disconnectionScreen.invalidTenant=Unable to connect to world. Your school does not have access to this server.
disconnectionScreen.resourcePack=Encountered a problem while downloading or applying resource pack.
disconnectionScreen.badPacket=Server sent broken packet.
disconnectionScreen.realmsDisabled=Realms are disabled.
disconnectionScreen.realmsDisabledBeta=Realms are disabled for the beta.
disconnectionScreen.incompatiblePack.memory=You are unable to join the world because your device doesn't have enough memory for the following packs: %s
disconnectionScreen.worldCorruption.message=Try reopening the world or restoring it from a previous backup
disconnectionScreen.worldCorruption=Disconnected due to world corruption

storageSpaceWarningScreen.frontend=You are almost out of data storage space! Minecraft has restricted access to this feature until you clear up additional storage space.
storageSpaceWarningScreen.lowduringgameplay=Your device is almost out of the space that Minecraft can use to save worlds and settings on this device.  Why not delete some old stuff you don't need so that you can keep saving new stuff?
storageSpaceWarningScreen.fullduringgameplay=You are out of data storage space and Minecraft is unable to save your progress! Minecraft will return you to the Main Menu to clear up storage space.

edu.play_screen.view_worlds=VIEW MY WORLDS
edu.play_screen.view_library=VIEW LIBRARY
edu.play_screen.create_new=CREATE NEW
edu.play_screen.join_world=JOIN WORLD
edu.play_screen.import=IMPORT
edu.play_screen.new=NEW
edu.play_screen.from_template=TEMPLATES

edu.worlds_screen.title=My Worlds
edu.worlds_screen.new_world=New World
edu.worlds_screen.new=NEW
edu.worlds_screen.templates=TEMPLATES
edu.worlds_screen.search_placeholder=Search
edu.worlds_screen.play=PLAY
edu.worlds_screen.host=HOST
edu.worlds_screen.settings=SETTINGS
edu.worlds_screen.courses_title=My Courses

edu.templates.title=My Templates
edu.templates.local_header=MY TEMPLATES: %1
edu.templates.featured_header=FEATURED TEMPLATES
edu.templates.view_more=View More Templates
edu.templates.search_placeholder=Search
edu.templates.created_by=By %1
edu.templates.quick_play=PLAY
edu.templates.host=HOST
edu.templates.create_new=CREATE NEW

edu.pause.multiplayer.disabled=Multiplayer is not available in this lesson.

effect.badOmen=Bad Omen
effect.villageHero=Hero of the Village

enchantment.arrowDamage=Power
enchantment.arrowFire=Flame
enchantment.arrowInfinite=Infinity
enchantment.arrowKnockback=Punch
enchantment.crossbowMultishot=Multishot
enchantment.crossbowPiercing=Piercing
enchantment.crossbowQuickCharge=Quick Charge
enchantment.curse.binding=Curse of Binding
enchantment.curse.vanishing=Curse of Vanishing
enchantment.damage.all=Sharpness
enchantment.damage.arthropods=Bane of Arthropods
enchantment.damage.undead=Smite
enchantment.digging=Efficiency
enchantment.durability=Unbreaking
enchantment.fire=Fire Aspect
enchantment.fishingSpeed=Lure
enchantment.frostwalker=Frost Walker
enchantment.knockback=Knockback
enchantment.level.1=I
enchantment.level.10=X
enchantment.level.2=II
enchantment.level.3=III
enchantment.level.4=IV
enchantment.level.5=V
enchantment.level.6=VI
enchantment.level.7=VII
enchantment.level.8=VIII
enchantment.level.9=IX
enchantment.lootBonus=Looting
enchantment.lootBonusDigger=Fortune
enchantment.lootBonusFishing=Luck of the Sea
enchantment.mending=Mending
enchantment.oxygen=Respiration
enchantment.protect.all=Protection
enchantment.protect.explosion=Blast Protection
enchantment.protect.fall=Feather Falling
enchantment.protect.fire=Fire Protection
enchantment.protect.projectile=Projectile Protection
enchantment.thorns=Thorns
enchantment.untouching=Silk Touch
enchantment.waterWalker=Depth Strider
enchantment.waterWorker=Aqua Affinity
enchantment.tridentChanneling=Channeling
enchantment.tridentLoyalty=Loyalty
enchantment.tridentRiptide=Riptide
enchantment.tridentImpaling=Impaling


entity.area_effect_cloud.name=Area Effect Cloud
entity.armor_stand.name=Armor Stand
entity.arrow.name=Arrow
entity.bat.name=Bat
entity.bee.name=Bee
entity.blaze.name=Blaze
entity.boat.name=Boat
entity.cat.name=Cat
entity.cave_spider.name=Cave Spider
entity.chicken.name=Chicken
entity.cow.name=Cow
entity.creeper.name=Creeper
entity.dolphin.name=Dolphin
entity.panda.name=Panda
entity.donkey.name=Donkey
entity.dragon_fireball.name=Dragon Fireball
entity.drowned.name=Drowned
entity.egg.name=Egg
entity.elder_guardian.name=Elder Guardian
entity.ender_crystal.name=Ender Crystal
entity.ender_dragon.name=Ender Dragon
entity.enderman.name=Enderman
entity.endermite.name=Endermite
entity.ender_pearl.name=Ender Pearl
entity.evocation_illager.name=Evoker
entity.evocation_fang.name=Evoker Fang
entity.eye_of_ender_signal.name=Eye of Ender
entity.falling_block.name=Falling Block
entity.fireball.name=Fireball
entity.fireworks_rocket.name=Firework Rocket
entity.fishing_hook.name=Fishing Hook
entity.fish.clownfish.name=Clownfish
entity.fox.name=Fox
entity.cod.name=Cod
entity.pufferfish.name=Pufferfish
entity.salmon.name=Salmon
entity.tropicalfish.name=Tropical Fish
entity.ghast.name=Ghast
entity.guardian.name=Guardian
entity.horse.name=Horse
entity.husk.name=Husk
entity.ravager.name=Ravager
entity.iron_golem.name=Iron Golem
entity.item.name=Item
entity.leash_knot.name=Leash Knot
entity.lightning_bolt.name=Lightning Bolt
entity.lingering_potion.name=Lingering Potion
entity.llama.name=Llama
entity.llama_spit.name=Llama Spit
entity.magma_cube.name=Magma Cube
entity.minecart.name=Minecart
entity.chest_minecart.name=Minecart with Chest
entity.command_block_minecart.name=Minecart with Command Block
entity.furnace_minecart.name=Minecart with Furnace
entity.hopper_minecart.name=Minecart with Hopper
entity.tnt_minecart.name=Minecart with TNT
entity.mule.name=Mule
entity.mooshroom.name=Mooshroom
entity.moving_block.name=Moving Block
entity.ocelot.name=Ocelot
entity.painting.name=Painting
entity.parrot.name=Parrot
entity.phantom.name=Phantom
entity.pig.name=Pig
entity.pillager.name=Pillager
entity.polar_bear.name=Polar Bear
entity.rabbit.name=Rabbit
entity.sheep.name=Sheep
entity.shulker.name=Shulker
entity.shulker_bullet.name=Shulker Bullet
entity.silverfish.name=Silverfish
entity.skeleton.name=Skeleton
entity.skeleton_horse.name=Skeleton Horse
entity.stray.name=Stray
entity.slime.name=Slime
entity.small_fireball.name=Small Fireball
entity.snowball.name=Snowball
entity.snow_golem.name=Snow Golem
entity.spider.name=Spider
entity.splash_potion.name=Potion
entity.squid.name=Squid
entity.tnt.name=Block of TNT
entity.thrown_trident.name=Trident
entity.tripod_camera.name=Tripod Camera
entity.turtle.name=Turtle
entity.unknown.name=Unknown
entity.vex.name=Vex
entity.villager.name=Villager
entity.villager.armor=Armorer
entity.villager.butcher=Butcher
entity.villager.cartographer=Cartographer
entity.villager.cleric=Cleric
entity.villager.farmer=Farmer
entity.villager.fisherman=Fisherman
entity.villager.fletcher=Fletcher
entity.villager.leather=Leatherworker
entity.villager.librarian=Librarian
entity.villager.name=Villager
entity.villager.shepherd=Shepherd
entity.villager.tool=Tool Smith
entity.villager.weapon=Weapon Smith
entity.villager.mason=Stone Mason
entity.villager.unskilled=Unskilled Villager
entity.villager_v2.name=Villager
entity.vindicator.name=Vindicator
entity.wandering_trader.name=Wandering Trader
entity.witch.name=Witch
entity.wither.name=Wither
entity.wither_skeleton.name=Wither Skeleton
entity.wither_skull.name=Wither Skull
entity.wither_skull_dangerous.name=Wither Skull
entity.wolf.name=Wolf
entity.xp_orb.name=Experience Orb
entity.xp_bottle.name=Bottle o' Enchanting
entity.zombie.name=Zombie
entity.zombie_horse.name=Zombie Horse
entity.zombie_pigman.name=Zombie Pigman
entity.zombie_villager.name=Zombie Villager
entity.zombie_villager_v2.name=Zombie Villager

exports.share.file=Share %s
exports.suspendWarning.client.content=Warning: If you continue, you will be disconnected from this multiplayer session.
exports.suspendWarning.host.content=Warning: If you continue, this will end the multiplayer session for all players.
exports.suspendWarning.title=Warning
exports.fileError.title=Unable to save your file
exports.fileError.body=The disk may be full or write-protected or the file may be in use. Please check to make sure the file is not open and try again.

addExternalServerScreen.addServer=Add Server
addExternalServerScreen.nameTextBoxLabel=Server Name
addExternalServerScreen.namePlaceholder=Please enter server name
addExternalServerScreen.ipTextBoxLabel=Server Address
addExternalServerScreen.portTextBoxLabel=Port
addExternalServerScreen.ipPlaceholder=Please enter IP or Address
addExternalServerScreen.alreadyAdded=This server has already been added
addExternalServerScreen.saveButtonLabel=Save
addExternalServerScreen.playButtonLabel=Play
addExternalServerScreen.removeButtonLabel=Remove
addExternalServerScreen.removeConfirmation=Are you sure you want to remove this server? 
addExternalServerScreen.addTitle=Add External Server
addExternalServerScreen.editTitle=Edit External Server

feature.endcity=End City
feature.fortress=Nether Fortress
feature.mansion=Woodland Mansion
feature.mineshaft=Mineshaft
feature.missingno=Unknown Feature
feature.monument=Ocean Monument
feature.stronghold=Stronghold
feature.temple=Temple
feature.village=Village
feature.shipwreck=Shipwreck
feature.buriedtreasure=Buried Treasure
feature.ruins=Ocean Ruins
feature.pillageroutpost=Pillager Outpost

feed.like=Like
feed.manage_feed=Manage feed
feed.comment=Comment
feed.reported_label=Reports
feed.ignore=Ignore
feed.nrreport=%d report
feed.nrreports=%d reports
feed.newPost=New post
feed.daysAgo=%dd ago
feed.hoursAgo=%dh ago
feed.minutesAgo=%dm ago
feed.now=Just now
feed.whatup=What are you up to?
feed.upload=Your screenshot is being uploaded...
feed.manage=Manage Feed
feed.share=Share
feed.addScreenshot=Add Screenshot
feed.comments=Comments
feed.edit=Edit
feed.screenshots=Screenshots
feed.return=Menu
feed.goToFeed=Feed
feed.cancel=Cancel
feed.previous=Previous
feed.next=Next
feed.delete=Delete
feed.deleted=Deleted item
feed.report_xbox=Report to Xbox Live Enforcement
feed.report_club=Report to Club
feed.clubdescription=Realm Description
feed.no_feed_item_content=Screenshots and discussions in your Realm will show up here. Get started by pressing "New post"
feed.no_reported_item_content=No items have been reported
feed.no_screenshot=You do not have any screenshots
feed.reportToastMessage=Item reported
feed.forbidden=Unauthorized to post to timeline
feed.failedToPost=Post unsuccessful
feed.failedToPostDescription=Screenshot invalid
feed.uppload_started=Screenshot is being uploaded
feed.uppload_success=Screenshot has been uploaded
feed.connected=Every Realm is connected to a Club with your Microsoft Account.

comment.like=%d like					## 1 like
comment.likes=%d likes					## 0 or 2+ likes
comment.comment=%d comment				## 1 comment
comment.comments=%d comments			## 0 or 2+ comments
comment.likes_and_comments=%1 and %2	## comment.like(s) will be inserted at %1 and comment.comment(s) at %2
comment.commentplaceholder=Comment...

furnaceScreen.fuel=Fuel
furnaceScreen.header=Furnace
furnaceScreen.input=Input
furnaceScreen.result=Result

gameArgument.featureUnsupported=This version of Minecraft doesn't support this feature

gameMode.adventure=Adventure Mode
gameMode.changed=Your game mode has been updated to %s
gameMode.creative=Creative Mode
gameMode.hardcore=Hardcore Mode!
gameMode.spectator=Spectator Mode
gameMode.survival=Survival Mode

gameTip.cameraMovement.mouse=Move :light_mouse_button: to look around.
gameTip.cameraMovement.touch=Touch and drag the screen to look around.
gameTip.cameraMovement.controller=Move :tip_right_stick: to look around.
gameTip.playerMovement.keyboard=Press :_input_key.forward:, :_input_key.left:, :_input_key.back:, or :_input_key.right: to move.
gameTip.playerMovement.touch=Touch :tip_touch_forward: :tip_touch_left: :tip_touch_back: :tip_touch_right: to move.
gameTip.playerMovement.controller=Move :tip_left_stick: to move around.
gameTip.jumpMovement.keyboard=Press :_input_key.jump: to jump.
gameTip.jumpMovement.touch=Tap :tip_touch_jump: to jump.
gameTip.jumpMovement.controller=Press :_input_key.jump: to jump.
gameTip.jumpForward.keyboard=Press :_input_key.forward: and :_input_key.jump: to jump forward.
gameTip.jumpForward.touch=Touch :tip_touch_forward: and :tip_touch_jump: to jump forward.
gameTip.jumpForward.controller=Push up on :tip_left_stick: and press :_input_key.jump: to jump forward.
gameTip.findTree=Find a tree to gather wood.
gameTip.breakWood.mouse=Hold :_input_key.attack: to break wood.
gameTip.breakWood.touch=Tap and hold to break wood.
gameTip.breakWood.controller=Hold down :_input_key.attack: to break wood.
gameTip.openInventory.keyboard=Press :_input_key.inventory: to open your inventory.
gameTip.openInventory.touch=Tap the :tip_touch_inventory: button to open your inventory.
gameTip.openInventory.controller=Press :_input_key.inventory: to open your inventory.
gameTip.selectWoodForMakePlank.keyboard=:light_mouse_left_button: to select the wood.
gameTip.selectWoodForMakePlank.touch=Tap to select the wood.
gameTip.selectWoodForMakePlank.controller=Use :tip_left_stick: and :_gamepad_face_button_down: to select the wood.
gameTip.selectGridForMakePlank.keyboard=:light_mouse_left_button: in the crafting grid.
gameTip.selectGridForMakePlank.touch=Tap in the crafting grid.
gameTip.selectGridForMakePlank.controller=Use :tip_left_stick: and :_gamepad_face_button_down: to place in the crafting grid.
gameTip.selectOutputSlot.keyboard=:light_mouse_left_button: the output slot to craft.
gameTip.selectOutputSlot.touch=Tap the output slot to craft.
gameTip.selectOutputSlot.controller=Press :_gamepad_face_button_down: on the output slot to craft.
gameTip.selectHotBar.keyboard=:light_mouse_left_button: an open slot to add to your inventory.
gameTip.selectHotBar.touch=Tap an open slot to add to your inventory.
gameTip.selectHotBar.controller=Press :_gamepad_face_button_down: on an open slot below to add it to your inventory.
gameTip.open.recipe.keyboard=:light_mouse_left_button: here to open the recipe book.
gameTip.open.recipe.touch=Tap here to open the search tab.
gameTip.open.recipe.controller=Press :tip_left_trigger: to open the recipe book.
gameTip.open.recipe.touchcontroller=Press :_input_key.jump: here to open the search tab.
gameTip.craftTable.keyboard=:light_mouse_left_button: here to select the crafting table recipe.
gameTip.craftTable.touch=Tap here to select the crafting table recipe.
gameTip.craftTable.controller=Use :tip_left_stick: to select the crafting table recipe.
gameTip.outputSlotCraftTable.controller=Press :_gamepad_face_button_down: to craft the crafting table.
gameTip.selectHotBar.craftTable=Put the crafting table in the hotbar to use it.
gameTip.closeInventory.keyboard=Press :_input_key.inventory: or :light_mouse_left_button: here to close the inventory.
gameTip.closeInventory.touch=Tap here to close the inventory.
gameTip.closeInventory.controller=Press :_gamepad_face_button_right: to close the inventory.
gameTip.needMoreMaterials=You need 2 Wood to make a Wooden Pickaxe.
gameTip.selectItemInHotBar.keyboard=Use the :light_mouse_middle_button: or number keys to select items.
gameTip.selectItemInHotBar.touch=Tap the hotbar to select items.
gameTip.selectItemInHotBar.controller=Use :_input_key.cycleItemLeft: and :_input_key.cycleItemRight: to select hotbar items.
gameTip.placeCraftTable.keyboard=:_input_key.use: the ground to place a crafting table.
gameTip.placeCraftTable.touch=Tap the ground to place a crafting table.
gameTip.placeCraftTable.controller=Press :_input_key.use: to place a crafting table.
gameTip.useCraftTable.keyboard=:_input_key.use: a crafting table to use it.
gameTip.useCraftTable.touch=Touch a crafting table to use it.
gameTip.useCraftTable.controller=Press :_input_key.use: on a crafting table to use it.
gameTip.needPlanksAndSticks=You need 3 planks and 2 sticks.
gametip.openSearchTab.keyboard=:light_mouse_left_button: here to open the search tab.
gametip.openSearchTab.touch=Tap here to open the search tab.
gametip.openSearchTab.controller=Press :_gamepad_face_button_down: here to open the search tab.
gameTip.selectWoodenPickaxe=Make a wooden pickaxe to mine stone and coal.
gameTip.outputSlot.wooden.pickaxe.keyboard=:light_mouse_left_button: the output slot to craft a wooden pickaxe.
gameTip.outputSlot.wooden.pickaxe.touch=Tap the output slot to craft a wooden pickaxe.
gameTip.outputSlot.wooden.pickaxe.controller=Press :_gamepad_face_button_down: to craft a wooden pickaxe.
gameTip.selectHotBar.wooden.pickaxe=Put the pickaxe in your hotbar to use it.
gameTip.hintsDone=You're ready to explore on your own now. Have fun!

generator.amplified=AMPLIFIED
generator.amplified.info=Notice: Just for fun, requires beefy computer
generator.customized=Customized
generator.debug_all_block_states=Debug Mode
generator.default=Default
generator.flat=Flat
generator.infinite=Infinite
generator.largeBiomes=Large Biomes
generator.nether=Nether
generator.old=Old

globalPauseScreen.message=The game has been paused
globalPauseScreen.quit=Quit

gui.accept=Accept
gui.achievements=Achievements
gui.all=All
gui.back=Back
gui.cancel=Cancel
gui.clear=Clear
gui.close=Close
gui.confirm=Confirm
gui.confirmAndPlay=Confirm and Play Now
gui.custom=Custom
gui.default=Default
gui.decline=Decline
gui.delete=Delete
gui.deleteWorldLong=Delete World
gui.done=Done
gui.down=Down
gui.edit=Edit
gui.error=Error
gui.exit=Exit
gui.exportWorld=Export
gui.exportWorldLong=Export World
gui.exportTemplate=Export Template
gui.templateMinVersion=Template Minimum Version
gui.templateLocPickerTitle=Browse for languages.json in localization folder
gui.templateLocPickerButton=Select Localization
gui.templateIconPickerTitle=Browse for a world icon
gui.templateIconPickerDesc=Minecraft Icon
gui.templateIconPickerButton=Pick world icon
gui.pickLoc.successMessage=Copied localization
gui.pickLoc.failedMessage=Error: File picked wasn't languages.json
gui.pickIcon.successMessage=Copied world icon
gui.pickIcon.failedMessage=Error: Invalid file format
gui.previous=Previous
gui.copyWorld=Copy World
gui.copyWorld.failedMessage=Failed to create a copy of your world.
gui.goBack=Go Back
gui.import=Import
gui.importWorld=Import
gui.minecraftWorld=Minecraft World
gui.minecraftBundle=Minecraft Bundle
gui.login=Login
gui.logout=Logout
gui.next=Next
gui.no=No
gui.none=None
gui.ok=OK
gui.store=Check Store
gui.continue=Continue
gui.pickWorld=Browse for a Minecraft World
gui.retry=Retry
gui.select=Select
gui.skip=Skip
gui.stats=Statistics
gui.tab=Tab
gui.toMenu=Back to title screen
gui.tryAgain=Try Again
gui.up=Up
gui.warning.exitGameWarning=Do you want to exit Minecraft?
gui.worldTemplate=Minecraft World Template
gui.yes=Yes
gui.feedbackYes=Yes, go to Feedback website
gui.resourcepacks=Resource Packs...
gui.submit=Submit
gui.playOffline=Play Offline
gui.signIn=Sign In
gui.genericNetworkError=Something went wrong. Try checking your internet connection.

gui.edu.exitGameWarningCaption=Minecraft: Education Edition	## Use Minecraft 教育版 in zh_CN translation
gui.edu.exitGameWarningMessage=Exit Minecraft: Education Edition?	## Use 离开 Minecraft 教育版? in zh_CN translation

updateScreen.update=Update
updateScreen.updateRequired=Update Required
updateScreen.title=Version Out of Date
updateScreen.body=Update to the latest version to access all the new features, play with friends online, or see what's new in the Marketplace.
updateScreen.commerceNotSupported.title=Can't Access Marketplace
updateScreen.commerceNotSupported.body=This version of Minecraft can no longer access the Minecraft Marketplace. Update to the latest version to restore Marketplace access.
updateScreen.packs.updateRequired=Please update to the latest version to download your pack purchased in the Minecraft Marketplace.
updateScreen.patchVersion=Version %s Patch Notes:

hostOption.codeBuilder=Code Builder
hostOption.setWorldSpawn=Set World Spawn
hostOption.teleport=Teleport
hostOption.teleport.who=Who
hostOption.teleport.where=Where
hostOption.time=Time
hostOption.time.day=Day
hostOption.time.midnight=Midnight
hostOption.time.noon=Noon
hostOption.time.night=Night
hostOption.time.sunrise=Sunrise
hostOption.time.sunset=Sunset
hostOption.weather=Weather
hostOption.weather.clear=Clear
hostOption.weather.rain=Rain
hostOption.weather.thunderstorm=Thunderstorm

howtoplay.next=Next
howtoplay.previous=Previous

howtoplay.category.basics=Basics
howtoplay.category.crafting=Crafting
howtoplay.category.education=Education
howtoplay.category.engineering=Engineering
howtoplay.category.enhancedPlay=Enhanced Play
howtoplay.category.firstDay=First Day
howtoplay.category.optionsAndCheats=Options & Cheats
howtoplay.category.play=Play
howtoplay.category.theEnd=The End

howtoplay.adventureMode=Adventure Mode
howtoplay.adventureMode.title=How to Play : Adventure Mode
howtoplay.adventureMode.text.1=Adventure Mode is a gamemode for world builders. In Adventure Mode, players must use special tools to place or break blocks.
howtoplay.adventureMode.text.2=To get these special tools, you must use /give with canplace or candestroy parameters while in creative mode.

howtoplay.anvil=Anvil
howtoplay.anvil.title=How to Play : Anvil
howtoplay.anvil.text.1=Experience Levels can be used to repair, enchant, or rename items with the Anvil.
howtoplay.anvil.header.1=Renaming and Applying Enchantments
howtoplay.anvil.text.2=All items can be renamed, although only items with durability can be repaired or enchanted with Books on the Anvil.
howtoplay.anvil.header.2=Repairing
howtoplay.anvil.text.3=An item can be repaired by placing it in one of the input slots on the left, along with either some raw materials of the item, like Iron Ingots for an Iron Sword, or combined with another item of the same type.
howtoplay.anvil.text.4=Combining items is more efficient when done with an Anvil.
howtoplay.anvil.text.5=There is a chance that the Anvil will be damaged with each use and after enough usage it will be destroyed.

howtoplay.armor=Armor
howtoplay.armor.title=How to Play : Armor
howtoplay.armor.text.1=By wearing Armor, you can protect yourself from incoming damage. Your Armor level is represented by :armor: on the HUD, the higher the level, the less damage you will take.
howtoplay.armor.header.1=Crafting
howtoplay.armor.text.2=Armor can be crafted from Leather, Iron, Gold, or Diamond. Chain Armor can be obtained through villager trading and loot from zombies and skeletons.
howtoplay.armor.text.3=Equip Armor by opening your inventory and moving pieces of Armor to the appropriate slot next to your player skin.
howtoplay.armor.text.4=Iron, Chain, and Gold Armor can be smelted into Iron or Gold Nuggets in a Furnace.
howtoplay.armor.header.2=Shield
howtoplay.armor.text.5.keyboard=The Shield will block incoming damage. The Shield can be equipped in the offhand slot as well as the hotbar. Press and hold :_input_key.sneak: to block with the Shield.
howtoplay.armor.text.5.gamepad=The Shield will block incoming damage. The Shield can be equipped in the offhand slot as well as the hotbar. Press the :_input_key.sneak: button or click :_gamepad_stick_right: to toggle block with the Shield.
howtoplay.armor.text.5.touch=The Shield will block incoming damage. The Shield can be equipped in the offhand slot as well as the hotbar. Tap :touch_sneak: to toggle block with the Shield.
howtoplay.armor.text.5.rift_controller=The Shield will block incoming damage. The Shield can be equipped in the offhand slot as well as the hotbar. Press the :_input_key.sneak: button or click :rift_X: to toggle block with the Shield.
howtoplay.armor.text.5.windowsmr_controller=The Shield will block incoming damage. The Shield can be equipped in the offhand slot as well as the hotbar. Press the :_input_key.sneak: button or click :windowsmr_right_stick: to toggle block with the Shield.
howtoplay.armor.text.6=The Shield can even block a Creeper explosion!
howtoplay.armor.header.3=Disguise
howtoplay.armor.text.7=You can also wear a Carved Pumpkin on your head. This won't give you any :armor: but will make it so Endermen won't get mad when you look at them.

howtoplay.armorStand=Armor Stand
howtoplay.armorStand.title=How to Play : Armor Stand
howtoplay.armorStand.text.1=An Armor Stand holds your armor while you aren't using it.
howtoplay.armorStand.header.1=Outfitting
howtoplay.armorStand.text.2.notTouch=Press :_input_key.use: on an Armor Stand while holding a piece of armor to place it on the Armor Stand.
howtoplay.armorStand.text.2.touch=Tap an Armor Stand while holding a piece of armor to place it on the Armor Stand.
howtoplay.armorStand.text.3.notTouch=Press :_input_key.use: on an Armor Stand with an open hand to remove something from the Armor Stand.
howtoplay.armorStand.text.3.touch=Tap an Armor Stand with an open hand to remove something from the Armor Stand.
howtoplay.armorStand.header.2=Posing
howtoplay.armorStand.text.4.notTouch=An Armor Stand can change its pose, sneak and press :_input_key.use: on an Armor Stand to change the pose.  
howtoplay.armorStand.text.4.touch=An Armor Stand can change its pose, sneak and tap an Armor Stand to change the pose.
howtoplay.armorStand.header.3=Dancing
howtoplay.armorStand.text.5=Armor Stands will react to a Redstone signal, try giving it different signal strengths and see what happens.

howtoplay.banners=Banners
howtoplay.banners.title=How to Play : Banners
howtoplay.banners.text.1=Banners are a cool way to decorate your house or castle with custom patterns and colors.
howtoplay.banners.text.2=After crafting a Banner, you can put it on a Crafting Table or Loom and add different dyes to create your own unique, cool design!
howtoplay.banners.header.1=Applying a Pattern
howtoplay.banners.text.3=There are many different patterns that you can create with your dye such as:
howtoplay.banners.text.4=  - Making a triangle will give you a chevron pattern.
howtoplay.banners.text.5=  - Making a cross gives you a cross pattern.
howtoplay.banners.text.6=  - Adding a Oxeye Daisy gives you a flower pattern.
howtoplay.banners.text.7=Experiment with different combinations to get even more patterns!
howtoplay.banners.header.2=Multiple Layers
howtoplay.banners.text.8=A Banner can also have multiple layers, allowing you to have more than one pattern on your Banner. If you don't like the last pattern you put on a Banner, you can wash the last layer off in a Cauldron.
howtoplay.banners.header.3=Making Copies
howtoplay.banners.text.9=If you want to copy a Banner, put it on the Crafting Table with a blank Banner.

howtoplay.beacons=Beacons
howtoplay.beacons.title=How to Play : Beacons
howtoplay.beacons.text.1=Active Beacons project a bright beam of light into the sky.
howtoplay.beacons.text.2=They are crafted with Glass, Obsidian, and Nether Stars (which is obtained by defeating the Wither).
howtoplay.beacons.header.1=Construction
howtoplay.beacons.text.3=Beacons must be placed so that they have an unobstructed view of the sky and must be placed on Pyramids of Iron, Gold, Emerald, or Diamond blocks.
howtoplay.beacons.text.4=The material that the Beacon is placed on has no effect on the power of the Beacon.
howtoplay.beacons.header.2=Use
howtoplay.beacons.text.5=In the Beacon menu you can select one primary power for your Beacon. The more tiers your pyramid has the more powers you can have.
howtoplay.beacons.text.6=To set the powers of your Beacon you must use an Emerald, Diamond, Gold Ingot, or Iron Ingot in the payment slot.
howtoplay.beacons.text.7=Once set, the powers will emanate from the Beacon indefinitely. Powers are granted to all nearby players.
howtoplay.beacons.text.8=To change the color of the beam, place a stained glass block in its path.

howtoplay.beds=Beds
howtoplay.beds.title=How to Play : Beds
howtoplay.beds.text.1=Sleeping in a Bed passes the night and sets your spawn point.
howtoplay.beds.text.2.notTouch=Press :_input_key.use: on a Bed to go to sleep. This can only be done at night.
howtoplay.beds.text.2.touch=Tap a Bed to go to sleep. This can only be done at night.
howtoplay.beds.text.3=If you are playing Multiplayer, everyone in the world must be in a Bed at the same time to pass the night.
howtoplay.beds.text.4=If your Bed is broken you will return to where you first spawned into the world.
howtoplay.beds.text.5=Be careful when using a Bed in the Nether, it's usually too hot to sleep.

howtoplay.blocks=Blocks
howtoplay.blocks.title=How to Play : Blocks
howtoplay.blocks.text.1=The world of Minecraft is made from millions of blocks. Each one is breakable, collectable, and placeable. There are almost no limits to what you can build!
howtoplay.blocks.text.2=Most blocks you will have to find in the world, but some can only be crafted such as combining other blocks on a crafting table like a Redstone Block or smelted in a furnace like Glass.
howtoplay.blocks.header.1=Falling Blocks
howtoplay.blocks.text.3=Most blocks are not affected by gravity and can float in mid air, but some like Sand and Gravel, will fall if there is no block underneath them.
howtoplay.blocks.header.2=Interesting Blocks
howtoplay.blocks.text.4=Here are some examples of the more interesting blocks in Minecraft:
howtoplay.blocks.text.5=Glass - This is a transparent block that is made by smelting Sand in a Furnace.
howtoplay.blocks.text.6=Concrete - This is made by dropping a Concrete Powder block into water.
howtoplay.blocks.text.7=Terracotta - This is found naturally in Mesa biomes or can be crafted and dyed from Clay that is found underwater. Terracotta can be made into Glazed Terracotta by smelting it in a Furnace.
howtoplay.blocks.text.8=Magma - This is found naturally in the Nether, it gives off a little light and will burn whoever steps on it.
howtoplay.blocks.text.9=Coarse Dirt - This is almost like a normal Dirt Block but Grass can't grow on it. It can be found in Savanna and Mega Taiga biomes.

howtoplay.bookAndQuill=Book & Quill
howtoplay.bookAndQuill.title=How to Play : Book & Quill
howtoplay.bookAndQuill.text.1=The Book & Quill lets you record your adventures. Tell your story by entering text into the pages. You can even change the title and the author! Sign the Book & Quill to finalize your work. The Book & Quill will be editable until it is signed. 

howtoplay.brewingStand=Brewing Stand
howtoplay.brewingStand.title=How to Play : Brewing Stand
howtoplay.brewingStand.text.1=Brewing potions requires a Brewing Stand. Every potion starts off with a bottle of water, which is made by filling a Glass Bottle with water from a Cauldron or water source.
howtoplay.brewingStand.header.1=Know your Brewing Stand
howtoplay.brewingStand.text.2=A Brewing Stand has three slots for creating different potions. One ingredient can be used over all three bottles, so always brew three potions at the same time to best use your resources.
howtoplay.brewingStand.header.2=Ingredients and Potions
howtoplay.brewingStand.text.3=Putting Netherwart in the top position of the Brewing Stand will give you an Awkward Potion. This doesn't have any effect by itself, but creates a base potion that can be used to create more advanced potions.
howtoplay.brewingStand.text.4=There are many potion effects to discover, experiment with different ingredients and combinations to become a true alchemist.

howtoplay.cauldron=Cauldron
howtoplay.cauldron.title=How to Play : Cauldron
howtoplay.cauldron.text.1=The Cauldron is a container that can hold buckets or bottles of liquid. 
howtoplay.cauldron.header.1=Filling
howtoplay.cauldron.text.2.notTouch=Press :_input_key.use: on a Cauldron while holding a Bucket of Water or a Potion to fill the Cauldron with its contents.
howtoplay.cauldron.text.2.touch=Tap a Cauldron while holding a Bucket of Water or a Potion to fill the Cauldron with its contents.
howtoplay.cauldron.text.3=A Potion will only partially fill a Cauldron so you will have to use multiple Potions to fill it up completely.
howtoplay.cauldron.header.2=Dipping Arrows
howtoplay.cauldron.text.4.notTouch=Press :_input_key.use: on a Cauldron with your Arrow in hand to create all kinds of different arrows!
howtoplay.cauldron.text.4.touch=Tap a Cauldron with your Arrow in hand to create all kinds of different arrows!
howtoplay.cauldron.header.3=Dyeing Armor
howtoplay.cauldron.text.5.notTouch=To dye Leather Armor or Leather Horse Armor, press :_input_key.use: on a Cauldron filled with water while holding the desired dye. This will change the color of the water inside of the Cauldron. Then dip your armor into the Cauldron by pressing :_input_key.use: with armor in your hand.
howtoplay.cauldron.text.5.touch=To dye Leather Armor or Leather Horse Armor, tap a Cauldron filled with water while holding the desired dye. This will change the color of the water inside of the Cauldron. Then dip your armor into the Cauldron by tapping the Cauldron with the armor in your hand.
howtoplay.cauldron.text.6=If you want to wash the dye off, dip the dyed item into a Cauldron of undyed Water.

howtoplay.chat=Chat
howtoplay.chat.title=How to Play : Chat
howtoplay.chat.text.1.keyboard=Press :_input_key.chat: to open Chat. Type your message and press ENTER.
howtoplay.chat.text.1.gamepad=Press :_input_key.chat: to open Chat. Type your message and press the send button.
howtoplay.chat.text.1.touch=Tap the chat button to open Chat. Type your message and tap the send button.

howtoplay.chests=Chests
howtoplay.chests.title=How to Play : Chests
howtoplay.chests.text.1.notTouch=Once you have crafted a Chest, you can place it in the world and then open it with :_input_key.use: to store items from your inventory.
howtoplay.chests.text.1.touch=Once you have crafted a Chest, you can place it in the world and then tap to open it and store items from your inventory.
howtoplay.chests.text.2=Items in the Chest will be stored there for you to put into your inventory later.
howtoplay.chests.text.3=Two Chests placed next to each other will be combined to form a Large Chest.

howtoplay.commandBlocks=Command Blocks
howtoplay.commandBlocks.title=How to Play : Command Blocks
howtoplay.commandBlocks.text.1=Command Blocks are a powerful tool for map creators. They store and execute commands upon receiving a Redstone signal.
howtoplay.commandBlocks.text.2=To get a command block you must use /give.
howtoplay.commandBlocks.text.3=When a Command Block has either executed or failed to execute its stored command, it sends a Redstone signal from the side where the arrow is pointing.
howtoplay.commandBlocks.text.4=You can set the name of the Command Block in the Hover Note field.
howtoplay.commandBlocks.text.5=Block Type - There are three types of Command Blocks, each with their own color and behavior:
howtoplay.commandBlocks.text.5.1=  - Impulse - Only executes the stored command when the block receives a redstone signal.
howtoplay.commandBlocks.text.5.2=  - Chain - Executes the stored command when the Command Block behind it has or has not executed its command. 
howtoplay.commandBlocks.text.5.3=  - Repeat - Once activated, this Command Block will continue executing its stored command as long as it exists.
howtoplay.commandBlocks.text.6=Condition - There are two conditions, each will affect the behavior of the Command Block:
howtoplay.commandBlocks.text.6.1=  - Unconditional - Executes the stored command even if the Command Block behind it failed to execute its command.
howtoplay.commandBlocks.text.6.2=  - Conditional - Only executes the stored command if the Command Block behind it succeeded in executing its command.
howtoplay.commandBlocks.text.7=Redstone -  There are two redstone settings, each will affect the behavior of the Command Block:
howtoplay.commandBlocks.text.7.1=  - Needs Redstone - Requires a redstone signal to activate.
howtoplay.commandBlocks.text.7.2=  - Always Active - Does not require a redstone signal to activate.
howtoplay.commandBlocks.text.8=A Command Block will also show you its previous output. This will help you figure out if a Command Block failed to execute its command and why.

howtoplay.commands=Commands
howtoplay.commands.title=How to Play : Commands
howtoplay.commands.text.1=Commands can be executed from Chat. Type / before you type the command.
howtoplay.commands.text.2=There are multitudes of commands that will allow you to do amazing things. There are many sources out there that will provide you with all of the info that you'll ever need.

howtoplay.conduits=Conduits
howtoplay.conduits.title=How to Play : Conduits
howtoplay.conduits.text.1=An active Conduit gives you powers when you are underwater.
howtoplay.conduits.text.2=A Conduit is crafted with Nautilus Shells and a Heart of the Sea. The Conduit draws power from Prismarine and Sea Lanterns.
howtoplay.conduits.header.1=Construction
howtoplay.conduits.text.3=Once activated, a Conduit will grant Conduit Power to anything nearby.
howtoplay.conduits.text.4=The more power it draws in, the further its abilities reach.

howtoplay.controls=Controls
howtoplay.controls.title=How to Play : Controls
howtoplay.controls.text.1=Minecraft is a game about placing blocks to build anything you can imagine. At night monsters come out, so make sure to build a shelter before that happens.
howtoplay.controls.header.1=Movement
howtoplay.controls.text.2.keyboard=Use the mouse to look around.
howtoplay.controls.text.2.gamepad=Use :_gamepad_stick_right: to look around.
howtoplay.controls.text.2.touch=Touch and drag along the screen to look around.
howtoplay.controls.text.2.rift_controller=Use :rift_right_stick: to look around.
howtoplay.controls.text.2.windowsmr_controller=Use :windowsmr_right_stick: to look around.
howtoplay.controls.text.3.keyboard=Use :_input_key.forward: :_input_key.left: :_input_key.back: :_input_key.right: to move around.
howtoplay.controls.text.3.gamepad=Use :_gamepad_stick_left: to move around.
howtoplay.controls.text.3.touch=Use :touch_forward::touch_left::touch_back::touch_right: to move around.
howtoplay.controls.text.3.rift_controller=Use :rift_left_stick: to move around.
howtoplay.controls.text.3.windowsmr_controller=Use :windowsmr_left_stick: to move around.
howtoplay.controls.text.4.keyboard=Press :_input_key.jump: to jump.
howtoplay.controls.text.4.gamepad=Press :_input_key.jump: to jump.
howtoplay.controls.text.4.touch=Press :touch_jump: to jump.
howtoplay.controls.text.4.rift_controller=Press :rift_A: to jump.
howtoplay.controls.text.4.windowsmr_controller=Press :windowsmr_right_touchpad: to jump.
howtoplay.controls.text.5.keyboard=Press :_input_key.forward: twice in rapid succession or press :_input_key.sprint: to sprint. While you hold :_input_key.forward:, you will continue to sprint until you have less than :shank::shank::shank:.
howtoplay.controls.text.5.gamepad=Push :_gamepad_stick_left: forward twice in rapid succession to sprint. While you hold :_gamepad_stick_left: forward, you will continue to sprint until you have less than :shank::shank::shank:.
howtoplay.controls.text.5.touch=Press :touch_forward: twice in rapid succession to sprint. While you hold :touch_forward:, you will continue to sprint until you have less than :shank::shank::shank:.
howtoplay.controls.text.5.rift_controller=Push :rift_left_stick: forward twice in rapid succession to sprint. While you hold :rift_left_stick: forward, you will continue to sprint until you have less than :shank::shank::shank:.
howtoplay.controls.text.5.windowsmr_controller=Push :windowsmr_left_stick: forward twice in rapid succession to sprint. While you hold :windowsmr_left_stick: forward, you will continue to sprint until you have less than :shank::shank::shank:.
howtoplay.controls.header.2=Breaking and Placing
howtoplay.controls.text.6.notTouch=Hold :_input_key.attack: to mine and chop using your hand or whatever you are holding. You may need to craft a tool to mine some blocks.
howtoplay.controls.text.6.touch=Touch and hold the screen on a block to mine and chop using your hand or whatever you are holding. You may need to craft a tool to mine some blocks.
howtoplay.controls.text.7.keyboard=If you are holding an item in your hand, use :_input_key.use: to use that item or press :_input_key.drop: to drop it.
howtoplay.controls.text.7.gamepad=If you are holding an item in your hand, use :_input_key.use: to use that item or press :_input_key.drop: to drop it.
howtoplay.controls.text.7.rift_controller=If you are holding an item in your hand, use :rift_right_grab: to use that item or press :rift_right_stick: to drop it.
howtoplay.controls.text.7.windowsmr_controller=If you are holding an item in your hand, use :windowsmr_left_trigger: to use that item or press :windowsmr_right_grab: to drop it.
howtoplay.controls.text.7.touch=If you are holding an item in your hand, touch the screen to use that item or touch and hold the item in your hotbar to drop it.
howtoplay.controls.header.3=Sneaking
howtoplay.controls.text.8.keyboard=Sneaking is incredibly useful in Minecraft, sneaking keeps you from falling off of a block and hides your nametag. To sneak, hold :_input_key.sneak:.
howtoplay.controls.text.8.gamepad=Sneaking is incredibly useful in Minecraft, sneaking keeps you from falling off of a block and hides your nametag. To toggle sneak, press :_input_key.sneak: or click :_gamepad_stick_right:.
howtoplay.controls.text.8.touch=Sneaking is incredibly useful in Minecraft, sneaking keeps you from falling off of a block and hides your nametag. To toggle sneak, tap :touch_sneak:.
howtoplay.controls.text.8.rift_controller=Sneaking is incredibly useful in Minecraft, sneaking keeps you from falling off of a block and hides your nametag. To toggle sneak, press :rift_X:.
howtoplay.controls.text.8.windowsmr_controller=Sneaking is incredibly useful in Minecraft, sneaking keeps you from falling off of a block and hides your nametag. To toggle sneak, press :windowsmr_right_stick:.
howtoplay.controls.text.swim.keyboard=Sprinting while in water will make you swim. Use the mouse to control the direction that you are swimming.
howtoplay.controls.text.swim.gamepad=Sprinting while in water will make you swim. Use the :_gamepad_stick_right: to control the direction that you are swimming.
howtoplay.controls.text.swim.touch=Sprinting while in water will make you swim. Touch and drag along the screen to control the direction that you are swimming.
howtoplay.controls.text.swim.rift_controller=Sprinting while in water will make you swim. Use the :rift_right_stick: to control the direction that you are swimming.
howtoplay.controls.text.swim.windowsmr_controller=Sprinting while in water will make you swim. Use the :windowsmr_right_stick: to control the direction that you are swimming.

howtoplay.worldBuilder=World Builder
howtoplay.worldBuilder.title=How to Play : World Builder
howtoplay.worldBuilder.text.1=To place or use certain Minecraft: Education Edition blocks or items, a player must possess a special ability called 'World Builder'.
howtoplay.worldBuilder.text.2=With cheats activated, the host can enable or disable the World Builder ability for any players in the world.
howtoplay.worldBuilder.text.3=To grant World Builder ability to all players in the world, run the following command:
howtoplay.worldBuilder.text.4=/ability @a worldbuilder true
howtoplay.worldBuilder.text.5=To quickly toggle your own World Builder ability, run either of the following commands:
howtoplay.worldBuilder.text.6=/worldbuilder
howtoplay.worldBuilder.text.7=/wb
howtoplay.worldBuilder.text.8=To query your World Builder ability, run the following command:
howtoplay.worldBuilder.text.9=/ability @p worldbuilder

howtoplay.permissionBlocks=Permission Blocks
howtoplay.permissionBlocks.title=How to Play : Permission Blocks
howtoplay.permissionBlocks.text.1=Minecraft: Education Edition features several special blocks that make it easier for teachers to create and employ lessons.  Only players with World Builder ability can place and destroy these blocks.  Only players without World Builder ability are subject to the restrictions imposed by these blocks.
howtoplay.permissionBlocks.title.1=Allow/Deny
howtoplay.permissionBlocks.text.2=Allow and Deny blocks control where players can build in a world.
howtoplay.permissionBlocks.text.3=Players can place or destroy blocks above Allow blocks.  Players cannot place or destroy blocks above Deny blocks.
howtoplay.permissionBlocks.text.4=Allow and Deny blocks do not affect the area below them.
howtoplay.permissionBlocks.text.5=An Allow block with a Deny block somewhere below it prevents players from building in the space between the two blocks.  Above the Allow block, players can place and destroy blocks.
howtoplay.permissionBlocks.title.2=Border
howtoplay.permissionBlocks.text.6=Border blocks provide a force field that extends infinitely upward and downward, regardless of the presence of other blocks.  Players cannot move through, over, or under a Border block.  Players cannot place or destroy blocks that are above or below a Border block.

howtoplay.chalkboard=Chalkboards
howtoplay.chalkboard.title=How to Play : Chalkboards
howtoplay.chalkboard.text.1=Chalkboards are used to display text in the world.  Chalkboards display more text than Signs, can be edited after they have been placed, and come in three sizes: Slate, Poster, and Board.  The lock toggle allows you to prevent non-World Builders from destroying or editing your chalkboards.  Press :_input_key.use: on an existing chalkboard to edit it.
howtoplay.chalkboard.text.1.touch=Chalkboards are used to display text in the world.  Chalkboards display more text than Signs, can be edited after they have been placed, and come in three sizes: Slate, Poster, and Board.  The lock toggle allows you to prevent non-World Builders from destroying or editing your chalkboards.  Tap an existing chalkboard to edit it.

howtoplay.chemistry=Chemistry
howtoplay.chemistry.title=How to Play : Chemistry
howtoplay.chemistry.text.1=Students can simulate real world chemistry by using the Chemistry Equipment blocks.  Build any of the 118 different elements by adjusting the sliders in the Element Constructor or view the composition of elements by placing them in the Element Constructor.  Combine elements in the Compound Creator to produce chemical compounds.  Put elements and compounds into the Lab Table to create Minecraft items.  Reduce blocks to their component elements with the Material Reducer.
howtoplay.chemistry.text.2=Download the Chemistry Journal from the Minecraft: Education Edition website for a comprehensive guide to chemistry in Minecraft.

howtoplay.npc=Non-Player Characters
howtoplay.npc.title=How to Play : Non-Player Characters
howtoplay.npc.text.1=NPCs are non-player characters that can provide additional lesson information, hints, or instructions.
howtoplay.npc.text.2=Only World Builders can place, delete, name, or edit NPCs.  NPCs can execute commands and link to websites.  Commands can be assigned to buttons in the NPC's dialog window; website links will always have a button.
howtoplay.npc.text.3=NPCs have a variety of skins to choose from and are immobile.

howtoplay.camera=Camera
howtoplay.camera.title=How to Play : Camera
howtoplay.camera.text.1=The Camera allows players to take pictures in the world.
howtoplay.camera.text.2=To take a picture from your point of view, press :_input_key.use:.
howtoplay.camera.text.2.touch=To take a picture from your point of view, tap and hold, then release.
howtoplay.camera.text.3=To take a selfie, place a camera and press :_input_key.use: on it.
howtoplay.camera.text.3.touch=To take a selfie, place a camera and tap the Take Picture button.
howtoplay.camera.text.4=Pictures can be viewed in the Portfolio or inserted into the Book & Quill.

howtoplay.portfolio=Portfolio
howtoplay.portfolio.title=How to Play : Portfolio
howtoplay.portfolio.text.1=Pictures you have taken appear in the Portfolio.  Press :_input_key.use: to view the Portfolio.  When viewing the Portfolio, you can add captions to pictures, delete pictures, and export pictures as a series of images.
howtoplay.portfolio.text.1.touch=Pictures you have taken appear in the Portfolio.  Tap and hold to view the Portfolio.  When viewing the Portfolio, you can add captions to pictures, delete pictures, and export pictures as a series of images.

howtoplay.classroomMode=Classroom Mode
howtoplay.classroomMode.title=How to Play : Classroom Mode
howtoplay.classroomMode.text.1=Classroom Mode is an external application that provides educators with features designed to facilitate interaction with students.  You can download Classroom Mode from the Minecraft: Education Edition website.
howtoplay.classroomMode.text.2=As the host of a world, use the command /classroommode to launch the Classroom Mode app and connect it to Minecraft: Education Edition.

howtoplay.codeBuilder=Code Builder
howtoplay.codeBuilder.title=How to Play : Code Builder
howtoplay.codeBuilder.text.1=Code Builder allows players to explore, create, and play in Minecraft by writing code using familiar learn-to-code platforms.
howtoplay.codeBuilder.text.2=Launch Code Builder by pressing :_input_key.codeBuilder: or by using the command /code.
howtoplay.codeBuilder.text.2.touch=Launch Code Builder by tapping :code_builder_button: or by using the command /code.

howtoplay.crafting=Crafting
howtoplay.crafting.title=How to Play : Crafting
howtoplay.crafting.text.1.keyboard=Press :_input_key.inventory: to access the Recipe Book and Crafting Grid.
howtoplay.crafting.text.1.gamepad=Press :_input_container.crafting: to access the Recipe Book and Crafting Grid.
howtoplay.crafting.text.1.touch=Tap the Inventory Button to access the Recipe Book and Crafting Grid.
howtoplay.crafting.text.1.rift_controller=Press :rift_B: to access the Recipe Book and Crafting Grid.
howtoplay.crafting.text.1.windowsmr_controller=Press :windowsmr_left_grab: to access the Recipe Book and Crafting Grid.
howtoplay.crafting.header.1=Crafting Grid
howtoplay.crafting.text.2=Arrange items from your Inventory or Hotbar in the crafting grid in the shape of an item recipe.
howtoplay.crafting.text.3=If the recipe is correct the item you are attempting to craft will appear in the Output Slot to the right of the Crafting Grid. Take it from the Output Slot and put it in your Inventory. You can now use the item.
howtoplay.crafting.text.4=There are many recipes and for some you will need a larger Crafting Grid than the 2x2 grid that you start with. 
howtoplay.crafting.text.5=Craft 4 Wooden Planks into a Crafting Table and place it in the world.
howtoplay.crafting.text.6.keyboard=Press :_input_key.use: on a Crafting Table in the world to gain access to a 3x3 Crafting Grid.
howtoplay.crafting.text.6.gamepad=Press :_input_key.use: on a Crafting Table in the world to gain access to a 3x3 Crafting Grid.
howtoplay.crafting.text.6.rift_controller=Press :rift_right_grab: on a Crafting Table in the world to gain access to a 3x3 Crafting Grid.
howtoplay.crafting.text.6.windowsmr_controller=Press :windowsmr_left_trigger: on a Crafting Table in the world to gain access to a 3x3 Crafting Grid.
howtoplay.crafting.text.6.touch=Tap on a Crafting Table in the world to gain access to a 3x3 Crafting Grid.
howtoplay.crafting.header.2=Recipe Book
howtoplay.crafting.text.7=On the left side of the screen you will see the Recipe Book, which is good if you don't know a recipe or want to quickly craft a recipe that you already know. You will also see the Crafting grid on the right side of the screen above your Inventory.
howtoplay.crafting.text.8.keyboard=Once you have found the recipe you want to craft, :_input_key.attack: the recipe to load it into the crafting grid, then take it from the Output Slot to the right of the Crafting Grid and put it in your Inventory. :_input_key.use: the recipe to auto-craft, this automatically crafts one set and puts it in your inventory. SHIFT + :_input_key.use: the recipe to auto-craft a whole stack.
howtoplay.crafting.text.8.gamepad=Once you have found the recipe you want to craft, press :_gamepad_face_button_down: on the recipe to auto-craft, this automatically crafts one set and puts it in your inventory. Press :_gamepad_face_button_up: on the recipe to auto-craft a whole stack.
howtoplay.crafting.text.8.touch=Once you have found the recipe you want to craft, tap the recipe to load it into the crafting grid, then tap the item in the output slot to craft it. Press and hold the item in the output slot to craft continuously.
howtoplay.crafting.text.9=When the :craftable_toggle_on: is on, the Recipe Book shows all of the recipes that you have ingredients for. When the :craftable_toggle_off: is off, the Recipe Book shows every recipe in the game, even if you can't craft it. Check the tabs or search if you are looking for something specific. In Creative Mode some recipes will be collapsed inside lists. Opening the list will expand it and show you all of the recipes inside. 

howtoplay.craftingTable=Crafting Table
howtoplay.craftingTable.title=How to Play : Crafting Table
howtoplay.craftingTable.text.1=The Crafting Table will let you craft more complicated recipes.
howtoplay.craftingTable.text.2.notTouch=Place it in the world, then press :_input_key.use: on the Crafting Table to use it.
howtoplay.craftingTable.text.2.touch=Place it in the world, then tap the Crafting Table to use it.

howtoplay.creativeMode=Creative Mode
howtoplay.creativeMode.title=How to Play : Creative Mode
howtoplay.creativeMode.text.1=Creative Mode is there so you can build the land of your dreams mob free!
howtoplay.creativeMode.header.1=Unlimited Building
howtoplay.creativeMode.text.2=The creative mode interface allows any item in the game to be moved into the player's inventory without the need for mining or crafting the item. The items in the player's inventory will not be removed when they are placed or used in the world, and this allows the player to focus on building rather than resource gathering.
howtoplay.creativeMode.text.3=If you create, load, or save a world in Creative Mode, that world will have achievements and leaderboard updates disabled, even if it is then loaded in Survival Mode.
howtoplay.creativeMode.header.2=Flying
howtoplay.creativeMode.text.4.keyboard=To fly when in Creative Mode, press :_input_key.jump: twice quickly. To exit flying, repeat the action. To fly faster, push :_input_key.forward: twice in rapid succession or press :_input_key.sprint: while flying. When in flying mode, you can hold down :_input_key.jump: to move up and :_input_key.sneak: to move down.
howtoplay.creativeMode.text.4.gamepad=To fly when in Creative Mode, press :_input_key.jump: twice quickly. To exit flying, repeat the action. To fly faster, push :_gamepad_stick_left: forward twice in rapid succession while flying. When in flying mode, you can hold down :_input_key.jump: to move up and :_input_key.sneak: to move down.
howtoplay.creativeMode.text.4.touch=To fly when in Creative Mode, Tap :_input_key.jump: twice quickly. To exit flying, repeat the action. To fly faster, tap :_input_key.forward: twice in rapid succession while flying. When in flying mode, you can press :touch_fly_up: to move up and :touch_fly_down: to move down.

howtoplay.difficulty=Difficulty
howtoplay.difficulty.title=How to Play : Difficulty
howtoplay.difficulty.text.1=Minecraft has varying levels of difficulty so you can tailor the game to your level.
howtoplay.difficulty.header.1=Peaceful
howtoplay.difficulty.text.2=Peaceful - In Peaceful no hostile mobs will spawn except for shulkers and the Ender Dragon, but they won't do any damage to you.
howtoplay.difficulty.header.2=Easy
howtoplay.difficulty.text.3=Easy - In Easy hostile mobs will do less damage, you won't be affected by certain status effects, and if your hunger level drops to zero you will not lose health.
howtoplay.difficulty.header.3=Normal
howtoplay.difficulty.text.4=Normal - In Normal hostile mobs will do normal damage, and if your hunger level drops to zero your health will begin to drop, but you won't die.
howtoplay.difficulty.header.4=Hard
howtoplay.difficulty.text.5=Hard - In Hard hostile mobs will do more damage, zombies will break through doors, and if your hunger level drops to zero, your health will drop and you could die.

howtoplay.dispensers=Dispensers
howtoplay.dispensers.title=How to Play : Dispensers
howtoplay.dispensers.text.1=Dispensers are used to shoot out items. You will need to power each Dispenser with a redstone signal (for example, with a lever) to trigger it.
howtoplay.dispensers.text.2.notTouch=To fill a Dispenser with items, press :_input_key.use: on the Dispenser, then move the items that you want to dispense from your inventory to the Dispenser.
howtoplay.dispensers.text.2.touch=To fill a Dispenser, tap the Dispenser, then move the items that you want to dispense from your inventory into the Dispenser.
howtoplay.dispensers.header.1=Usage
howtoplay.dispensers.text.3=Whenever you power a Dispenser, an item will shoot out.
howtoplay.dispensers.text.4=Dispensers can also be used to place items as well, such as Pumpkins on Iron Golems and equipment on Armor Stands.

howtoplay.droppers=Droppers
howtoplay.droppers.title=How to Play : Droppers
howtoplay.droppers.text.1.notTouch=When powered by Redstone, Droppers will drop a single random item contained within. Press :_input_key.use: on the Dropper to open it and then you can load it with items from your inventory.
howtoplay.droppers.text.1.touch=When powered by Redstone, Droppers will drop a single random item contained within. Tap the Dropper to open it and then you can load the it with items from your inventory.
howtoplay.droppers.header.1=Usage
howtoplay.droppers.text.2=If a Dropper is facing a Chest or another type of container, the item will be placed into that instead. Long chains of Droppers can be constructed to transport items over a distance.

howtoplay.dyes=Dyes
howtoplay.dyes.title=How to Play : Dyes
howtoplay.dyes.text.1=Dye is used to change the color of many things in Minecraft.
howtoplay.dyes.text.2=Some dye materials are harder to find than others. While most Dyes can be crafted from flowers like Red Dye from a Poppy, some Dyes are found or created in more obscure ways such as: 
howtoplay.dyes.text.3=-Mining Lapis Lazuli deep underground
howtoplay.dyes.text.4=-Collecting Ink Sacs underwater
howtoplay.dyes.text.5=-Harvesting Cocoa Beans in the Jungle
howtoplay.dyes.text.6=-Green Dye must be smelted from Cactus
howtoplay.dyes.text.7=-Bonemeal can help grow plants and trees or be crafted into White Dye
howtoplay.dyes.text.8=-Collect Ink Sacs from Squids for Black Dye

howtoplay.elytra=Elytra
howtoplay.elytra.title=How to Play : Elytra
howtoplay.elytra.text.1=Elytra are an item that can be equipped to allow gliding. Elytra are equipped in the chest armor slot.
howtoplay.elytra.text.2=Elytra cannot be crafted, but are found in Item Frames in rare End Ships, guarded by fearsome Shulkers.
howtoplay.elytra.header.1=Learn to Fly
howtoplay.elytra.text.3.notTouch=When equipped with Elytra, a player can press :_input_key.jump: while falling to start gliding. Note that you need to be quite high to really get the full effect!
howtoplay.elytra.text.3.touch=When equipped with Elytra, a player can tap :_input_key.jump: while falling to start gliding. Note that you need to be quite high to really get the full effect!
howtoplay.elytra.text.4=While gliding, you can steer by looking around. If you look downwards, you'll move faster - but you'll also hit the ground sooner. If you look upwards, you'll gain some height - but do it for too long and you'll stall, falling instead of rising!
howtoplay.elytra.text.5=As you fly, your Elytra will lose durability. You can repair them by combining them with some Phantom Membrane at an Anvil. If they break mid-flight you'll fall, so be careful!
howtoplay.elytra.header.2=Rocket Powered Flight
howtoplay.elytra.text.6=Firework Rockets that have no effects can be used to extend Elytra flight. You will be damaged if you use a Firework Rocket that has effects to extend flight.

howtoplay.enchantingTable=Enchanting Table
howtoplay.enchantingTable.title=How to Play : Enchanting Table
howtoplay.enchantingTable.text.1=The Experience Points collected by picking up Experience Orbs can be used to enchant Weapons, Armor, Tools, and Books.
howtoplay.enchantingTable.header.1=Enchanting
howtoplay.enchantingTable.text.2=When an enchantable item is placed in the slot below the book in the Enchantment Table, the three buttons to the right of the slot will display some enchantments with their Experience Level cost and Lapis Lazuli cost.
howtoplay.enchantingTable.text.3=The actual enchantment applied is randomly selected based on the cost displayed.
howtoplay.enchantingTable.header.2=Stronger Enchantments
howtoplay.enchantingTable.text.4=If the Enchantment Table is surrounded by Bookshelves (up to a maximum of 15), with a one block gap between the Bookshelf and the Enchantment Table, arcane glyphs will be seen going to the book on the Enchantment Table and the potency of the enchantments will be increased.
howtoplay.enchantingTable.header.3=Enchanting Books
howtoplay.enchantingTable.text.5=Enchanted Books are used at the Anvil to apply enchantments to items. This gives you more control over which enchantments you would like on your items.

howtoplay.endCities=End Cities
howtoplay.endCities.title=How to Play : End Cities
howtoplay.endCities.text.1=After killing the Ender Dragon, an End Gateway will appear. Throwing an Ender Pearl into the portal will teleport you to the outer islands.
howtoplay.endCities.text.2=This alien new landscape must hide something interesting, and maybe something to bring back with you.

howtoplay.enderChest=Ender Chest
howtoplay.enderChest.title=How to Play : Ender Chest
howtoplay.enderChest.text.1=All Ender Chests in a world are linked. Items placed into an Ender Chest are accessible in any other Ender Chest. However, the contents of the Ender Chests are specific for each player. This allows players to store items in any Ender Chest, and retrieve them from other Ender Chests in different positions in the world.

howtoplay.eyeOfEnder=Eye Of Ender
howtoplay.eyeOfEnder.title=How to Play : Eye Of Ender
howtoplay.eyeOfEnder.text.1=Eyes of Ender are mysteriously connected to the End and will behave strangely when used.
howtoplay.eyeOfEnder.text.2=Rumor has it that they can lead you to open another dimension.

howtoplay.farming=Farming
howtoplay.farming.title=How to Play : Farming
howtoplay.farming.text.1=There are a variety of different crops that you can farm in Minecraft. Farming, while challenging, can provide an infinite food source.
howtoplay.farming.header.1=Plan Ahead
howtoplay.farming.text.2=Crops require light, water, and farmland to grow. You need a hoe, a water source, and seeds to farm.
howtoplay.farming.text.3=The Blocks next to the water need to be converted to farmland, do this with the hoe. Water will saturate up to four block away.
howtoplay.farming.header.2=Planting and Harvesting
howtoplay.farming.text.4=Now you can plant your seeds. Make sure your crops are protected as they can easily die from being trampled or lack of water or light.
howtoplay.farming.text.5=Fences can help protect your crops from hungry rabbits or trampling toes!
howtoplay.farming.text.6=When the crops are fully grown they change in appearance and can be harvested.
howtoplay.farming.text.7=Some plants like pumpkins and melons grow on stalks. Be careful not to harvest the stalks!

howtoplay.fireworks=Fireworks
howtoplay.fireworks.title=How to Play : Fireworks
howtoplay.fireworks.text.1=Fireworks are a fun way to create your own unique celebration in Minecraft!
howtoplay.fireworks.text.2=Fireworks have two components, the Firework Rocket and the Firework Star.
howtoplay.fireworks.header.1=Firework Rocket
howtoplay.fireworks.text.3.notTouch=To use a Firework, press :_input_key.use: on a block to send the Firework shooting into the sky. 
howtoplay.fireworks.text.3.touch=To use a Firework, tap on a block to send the Firework shooting into the sky. 
howtoplay.fireworks.text.4=Fireworks have no effect other than shooting up into the air.
howtoplay.fireworks.header.2=Firework Stars
howtoplay.fireworks.text.5=Firework Stars can be added to during the crafting of the Firework Rocket to give it many different colors and effects.
howtoplay.fireworks.text.6=Firework Stars are made from gunpowder and various other items.
howtoplay.fireworks.text.6.1=  - Color - Adding dye of any color will make an explosion of that color.
howtoplay.fireworks.text.6.2=  - Twinkle - Adding Glowstone Dust will make the explosion twinkle.
howtoplay.fireworks.text.6.3=  - Creeper Shaped - Adding a Head will make a creeper pattern in the sky.
howtoplay.fireworks.text.6.4=  - Burst - Adding a feather will give the explosion a burst effect.
howtoplay.fireworks.text.6.5=  - Trail - Adding a Diamond will give the explosion trails.
howtoplay.fireworks.text.6.6=  - Star Shaped - Adding a Gold Nugget will make a star pattern in the sky.
howtoplay.fireworks.header.3=Pyrotechnics
howtoplay.fireworks.text.7=Fireworks can be fired from a dispenser. This will shoot the Firework in the direction that the dispenser is facing.

howtoplay.fishing=Fishing
howtoplay.fishing.title=How to Play : Fishing
howtoplay.fishing.text.1=Fishing must be done with a Fishing Rod and is an excellent way to catch some fish or other cool items from the sea!
howtoplay.fishing.header.1=Casting
howtoplay.fishing.text.2.notTouch=Aim at a block of water and press :_input_key.use: while holding a fishing rod to cast the lure into the water.
howtoplay.fishing.text.2.touch=Look at a block of water and tap the Fish button while holding a Fishing Rod to cast the lure into the water.
howtoplay.fishing.header.2=Fish On!
howtoplay.fishing.text.3=Wait until you start to see a trail of bubbles coming for the lure.
howtoplay.fishing.text.4.notTouch=When the bubbles touch the lure, it will bob up and down. Press :_input_key.use: to reel in the lure. Be careful not to let your fish get away!
howtoplay.fishing.text.4.touch=When the bubbles touch the lure, it will bob up and down. Tap the Fish button again to reel in the lure. Be careful not to let your fish get away!

howtoplay.furnace=Furnace
howtoplay.furnace.title=How to Play : Furnace
howtoplay.furnace.text.1=A Furnace allows you to cook or smelt items. For example, you can smelt Iron Ore into Iron Ingots.
howtoplay.furnace.text.2.notTouch=Place the Furnace in the world and press :_input_key.use: to use it.
howtoplay.furnace.text.2.touch=Place the Furnace in the world and tap to use it.
howtoplay.furnace.header.1=Fuel
howtoplay.furnace.text.3=You need to put some fuel into the bottom of the Furnace, and the item that you want smelted in the top. The Furnace will then fire up and start working. Lots of things can be used as fuel;  try experimenting with anything burnable!

howtoplay.gameSettings=Game Settings
howtoplay.gameSettings.title=How to Play : Game Settings
howtoplay.gameSettings.text.1=When creating a world, you have many options. Below are the various settings that can be used to fine tune your experience. Don't worry if you didn't set them when you created the world, they can be changed at any time.
howtoplay.gameSettings.text.2=-World Name: This is where you can name your world.
howtoplay.gameSettings.text.3=-Default Game Mode: This is the gamemode that all new players to the world will start with. You can have players start in either survival or creative mode. Selecting creative mode will turn cheats on.
howtoplay.gameSettings.text.4=-Difficulty: This setting affects how difficult the world will be. You can choose Peaceful, Easy, Normal, or Hard.
howtoplay.gameSettings.header.1=World Preferences 
howtoplay.gameSettings.text.5=-Starting Map: If this setting is turned on, every player will start with a map in their hotbar.
howtoplay.gameSettings.text.6=-Bonus Chest: If this setting is turned on, there will be a chest with some starting loot near your initial spawn location.
howtoplay.gameSettings.text.7=-World Type: This setting controls the way the world is created. Select Infinite, Flat, or Old from the drop down menu to change the world type.
howtoplay.gameSettings.text.7.1=  - Infinite worlds go on and on forever.
howtoplay.gameSettings.text.7.2=  - Flat worlds do not have any hills or trees, just an infinite flat area to fill with your creations.
howtoplay.gameSettings.text.7.3=  - Old worlds limited in size and height.
howtoplay.gameSettings.text.8=-Seed: This is a unique number for the world. By entering a seed when you make a new world, you can control how it is created. You can also use a word or phrase for the seed and the game will convert it to a number for you.
howtoplay.gameSettings.header.2=Cheats and Gamerules
howtoplay.gameSettings.text.9=-Activate Cheats: This setting allows players to use commands. When a world is created with this setting on, all achievements are disabled.
howtoplay.gameSettings.text.10=Below are gamerules, cheats must be turned on to use them:
howtoplay.gameSettings.text.11=-Always Day: Night will never fall when this setting is turned on.
howtoplay.gameSettings.text.12=-Do Daylight Cycle: The time will not advance when this setting is turned off.
howtoplay.gameSettings.text.13=-Fire Spreads: This setting prevents fire from spreading from block to block, destroying blocks, going out on its own or being put out by rain.
howtoplay.gameSettings.text.14=-TNT Explodes: This setting prevents TNT from being lit.
howtoplay.gameSettings.text.15=-Keep Inventory: This setting allows players to keep their inventory when they die.
howtoplay.gameSettings.text.16=-Mob Spawning: This prevents all mobs from spawning when this setting is off.
howtoplay.gameSettings.text.17=-Natural Regeneration: When this setting is turned on, players will regenerate their health naturally as long as their hunger bar is full.
howtoplay.gameSettings.text.18=-Mob Loot: Controls whether mobs will drop loot when they die.
howtoplay.gameSettings.text.19=-Mob Griefing: Prevents mobs from destroying the world.
howtoplay.gameSettings.text.20=-Tile Drops: When this setting is turned on, blocks will drop themselves when they break.
howtoplay.gameSettings.text.21=-Entities Drop Loot: Controls whether things like minecarts and armor stands will drop themselves when they are broken.
howtoplay.gameSettings.text.22=-Weather Cycle: The weather will not change when this setting is turned off.

howtoplay.hoppers=Hoppers
howtoplay.hoppers.title=How to Play : Hoppers
howtoplay.hoppers.text.1=Hoppers are used to automatically move items. Hoppers can be attached to some other items like containers such as Chests or Jukeboxes. 
howtoplay.hoppers.text.2=Hoppers will continuously remove items out of a container placed above them and move them into the container they are pointing to.
howtoplay.hoppers.header.1=Usage
howtoplay.hoppers.text.3=To make a Hopper point to a particular block, place the Hopper against that block while sneaking.
howtoplay.hoppers.text.4=If a Hopper is powered by Redstone, it will become inactive and stop moving items.

howtoplay.hostAndPlayerOptions=Host and Player Options
howtoplay.hostAndPlayerOptions.title=How to Play : Host and Player Options
howtoplay.hostAndPlayerOptions.text.1.notTouch=When playing with cheats on, host options will allow you to teleport yourself or friends, change the weather, set the time of day, and change the entry point or world spawn of your game. Once cheats have been enabled, you can find these options in the chat window by pressing the [/] button next to the chat bar.
howtoplay.hostAndPlayerOptions.text.1.touch=When playing with cheats on, host options will allow you to teleport yourself or friends, change the weather, set the time of day, and change the entry point or world spawn of your game. Once cheats have been enabled, you can find these options in the chat window by tapping the chat button.

howtoplay.HUD=HUD
howtoplay.HUD.title=How to Play : HUD
howtoplay.HUD.text.1=The HUD shows information about your status, your health, your remaining oxygen (when you are underwater), your hunger level, and your armor (if you are wearing any).
howtoplay.HUD.header.1=Food and Health
howtoplay.HUD.text.2=If you lose some health but have at least nine :shank:, your health will replenish. Eating food will replenish your :shank:.
howtoplay.HUD.text.3.keyboard=Press and hold :_input_key.use: while holding a piece of food to eat it.
howtoplay.HUD.text.3.gamepad=Press and hold :_input_key.use: while holding a piece of food to eat it.
howtoplay.HUD.text.3.rift_controller=Press and hold :rift_right_grab: while holding a piece of food to eat it.
howtoplay.HUD.text.3.windowsmr_controller=Press and hold :windowsmr_left_trigger: while holding a piece of food to eat it.
howtoplay.HUD.text.3.touch=Press and hold a piece of food to eat it.
howtoplay.HUD.text.4=You must watch your hunger level while adventuring, your hunger level is represented on your HUD with :shank:. The hunger level decreases as you explore. Certain activities will decrease hunger level at a faster rate, such as sprinting. You can replenish your hunger level by eating food. Some foods are more nourishing than others and will fill up more :shank:. When you get your hunger level up to maximum, the last food you ate applies a saturation value. Saturation is a hidden number that gives you an extra bonus to your hunger level.
howtoplay.HUD.header.2=Experience
howtoplay.HUD.text.5=The Experience Bar is also shown on the HUD. The number shows your current Experience Level, and the bar indicates how many Experience Points are required to increase your Experience Level.
howtoplay.HUD.text.6=Experience Points are gained by collecting Experience Orbs. You can get Experience Orbs from activities such as killing mobs, mining certain blocks, and more! Don't leave any Experience Orbs behind, you're going to need them. Move near Experience Orbs to collect them.
howtoplay.HUD.header.3=The Hotbar
howtoplay.HUD.text.7.keyboard=The hotbar shows the items that are available to use. Scroll the mouse wheel to change the item in your hand.
howtoplay.HUD.text.7.gamepad=The hotbar shows the items that are available to use. Use :_input_key.cycleItemLeft: and :_input_key.cycleItemRight: to change the item in your hand.
howtoplay.HUD.text.7.touch=The hotbar shows the items that are available to use. Tap items in your hotbar to change the item in your hand.
howtoplay.HUD.text.7.windowsmr_controller=The hotbar shows the items that are available to use. Press :windowsmr_left_touchpad_horizontal: to change the item in your hand.
howtoplay.HUD.text.7.rift_controller=The hotbar shows the items that are available to use. Press :rift_left_trigger: or :rift_left_grab: to change the item in your hand.

howtoplay.inventory=Inventory
howtoplay.inventory.title=How to Play : Inventory
howtoplay.inventory.text.1.keyboard=Press :_input_key.inventory: to view your inventory.
howtoplay.inventory.text.1.gamepad=Press :_input_key.inventory: to view your inventory.
howtoplay.inventory.text.1.rift_controller=Press :rift_B: to view your inventory.
howtoplay.inventory.text.1.windowsmr_controller=Press :windowsmr_left_grab: to view your inventory.
howtoplay.inventory.text.1.touch=Tap the Inventory tab to view your inventory.
howtoplay.inventory.text.2=This screen shows the items that are available for use in your hand, and all of the other items that you are carrying. Your armor is also shown here.
howtoplay.inventory.text.3.keyboard=Use :mouse_left_button: to pick up the item that is under the cursor. If there is more than one item here this will pick them all up, or you can use :mouse_right_button: to pick up just half of them.
howtoplay.inventory.text.3.gamepad=Use :_gamepad_face_button_down: to pick up the item that is under the cursor. If there is more than one item here this will pick them all up, or you can use :_gamepad_face_button_left: to pick up just half of them.
howtoplay.inventory.text.3.touch=Tap to select an item. If there is more than one item here this will select them all.
howtoplay.inventory.text.4.keyboard=You can place items down again by using :mouse_left_button:. With multiple items on the cursor, use :mouse_left_button: to place them all or :mouse_right_button: to place just one.
howtoplay.inventory.text.4.gamepad=You can place items down again by using :_gamepad_face_button_down:. With multiple items on the cursor, use :_gamepad_face_button_down: to place them all or :_gamepad_face_button_left: to place just one.
howtoplay.inventory.text.4.touch=Tap another place in the inventory to move the item.
howtoplay.inventory.text.5.keyboard=If the item the cursor is over is armor, you can quick move it to the correct armor slot by pressing SHIFT+:mouse_left_button:.
howtoplay.inventory.text.5.gamepad=If the item the cursor is over is armor, you can quick move it to the correct armor slot by pressing :_gamepad_face_button_up:.
howtoplay.inventory.text.6.keyboard=To drop an item, pick up the item and put it down outside of the inventory window.
howtoplay.inventory.text.6.gamepad=To drop an item, pick up the item and press :_gamepad_face_button_up:.
howtoplay.inventory.text.6.touch=To drop an item, tap the item and then tap outside of the inventory window.

howtoplay.jukebox=Jukebox
howtoplay.jukebox.title=How to Play : Jukebox
howtoplay.jukebox.text.1=The Jukebox will play Music Discs that you have found.
howtoplay.jukebox.header.1=Get the Party Started
howtoplay.jukebox.text.2.notTouch=Press :_input_key.use: on a Jukebox with a Music Disc in your hand to play it. Press :_input_key.use: the Jukebox again to eject the Music Disc.
howtoplay.jukebox.text.2.touch=Tap a Jukebox with a Music Disc in your hand to play it. Tap the Jukebox again to eject the Music Disc.
howtoplay.jukebox.header.2=Redstone
howtoplay.jukebox.text.3=When a Jukebox is playing a Music Disc, it will power Redstone Dust.
howtoplay.jukebox.text.4=Try different Music Discs for a different power level.
howtoplay.jukebox.text.5=See what happens when you let it play to the end. 
howtoplay.jukebox.text.6=A Hopper can put a Music Disc in for you, and take it out when it's done playing.
howtoplay.jukebox.header.3=Finding Music Discs
howtoplay.jukebox.text.7=There are a couple of Music Discs that can be found in Chests around the world.
howtoplay.jukebox.text.8=Others are more difficult to come by, Creepers secretly appreciate music.

howtoplay.loom=Loom
howtoplay.loom.title=How to Play : Loom
howtoplay.loom.text.1=The Loom is a fast and efficient way to apply patterns to Banners. The Loom only uses one Dye to apply a pattern. It can also use Loom patterns to apply special patterns.
howtoplay.loom.header.1=Weaving
howtoplay.loom.text.2=By placing a Banner into the Loom's first slot, and any color dye into the second slot, patterns can be added to the Banner. Once a pattern is selected, take it from the output slot on the right and move it to your inventory.
howtoplay.loom.text.3=A Banner can have multiple layers allowing you to experiment with many different banner possibilities. Up to 6 patterns can be applied to a Banner to create a unique design.
howtoplay.loom.header.2=Loom Patterns 
howtoplay.loom.text.4=Adding a Loom Pattern to the Loom's third slot will allow the crafting of special patterns like a creeper face or a flower pattern. You will not lose you Loom Patterns when you apply these patterns to your Banners.

howtoplay.mounts=Mounts
howtoplay.mounts.title=How to Play : Mounts
howtoplay.mounts.text.1=A player can ride any adult Horse, Donkey, or Mule. Only Horses can be armored.
howtoplay.mounts.text.2=Mules and Donkeys may be equipped with saddlebags (for transporting items) by attaching a Chest. These saddlebags can then be accessed whilst riding or sneaking.
howtoplay.mounts.header.1=Taming
howtoplay.mounts.text.3.notTouch=Horses, Donkeys, and Mules must be tamed before they can be used. A player can tame a horse by riding it and staying on while it tries to buck them off.  Press :_input_key.use: with an empty hand to attempt to tame Horses, Donkeys, and Mules.
howtoplay.mounts.text.3.touch=Horses, Donkeys, and Mules must be tamed before they can be used. A player can tame a horse by riding it and staying on while it tries to buck them off.  Tap the Mount button that appears when near a horse  to attempt to tame Horses, Donkeys, and Mules.
howtoplay.mounts.text.4=When Hearts appear around the horse, it has been tamed and will no longer attempt to throw the player off.
howtoplay.mounts.header.2=Riding Animals
howtoplay.mounts.text.5=To steer a horse, the player must equip it with a Saddle. 
howtoplay.mounts.text.6=Saddles can be bought from villagers, found inside Chests hidden in the world, or acquired by fishing. While riding a horse, open your inventory and put a saddle in the horse's saddle slot. 
howtoplay.mounts.text.7=Horses and Donkeys can be bred like other animals using Golden Apples or Golden Carrots.
howtoplay.mounts.text.8=Foals will grow into adult horses over time, although feeding them Wheat or Hay will speed this up.
howtoplay.mounts.header.3=Pack Animals
howtoplay.mounts.text.9=Llamas are another ridable mob which can be tamed and used to transport large shipments of items. 
howtoplay.mounts.text.10=Llamas can be tamed the same as horses. However, even when tamed, the player can't control their movements when riding them. 
howtoplay.mounts.text.11=A lead can be attached to one, and when this happens, surrounding llamas (both tamed and un-tamed) will follow, making a caravan. 
howtoplay.mounts.text.12=Llamas can be equipped with chests, giving it 3-15 slots of inventory space depending on the strength of the Llama. They can also be equipped with carpets. Each carpet color yields a different pattern.
howtoplay.mounts.header.4=Pigs
howtoplay.mounts.text.13=Pigs can be ridden as well, although they won't go where you want without a little incentive. 
howtoplay.mounts.text.14=Equip a pig with a Saddle and point a Carrot on a Stick in the direction you want to go.
howtoplay.mounts.header.5=Dismount
howtoplay.mounts.text.15.notTouch=Press :_input_key.sneak: to dismount.
howtoplay.mounts.text.15.touch=Tap :_input_key.sneak: to dismount.

howtoplay.multiplayer=Multiplayer
howtoplay.multiplayer.title=How to Play : Multiplayer
howtoplay.multiplayer.text.1=Minecraft is a multiplayer game by default.
howtoplay.multiplayer.header.1=Splitscreen
howtoplay.multiplayer.text.2.splitscreen=You can have local players join your game by connecting controllers and pressing any button on their controller at any point during the game.
howtoplay.multiplayer.header.2=Online Multiplayer
howtoplay.multiplayer.text.3=By default, all worlds start with multiplayer enabled. If you would like to make the world private, go to world settings and select the multiplayer option, then toggle multiplayer off.
howtoplay.multiplayer.text.3.norealms=By default, all worlds start with multiplayer enabled. If you would like to make the world private, go to world settings and select the multiplayer option, then toggle multiplayer off.
howtoplay.multiplayer.header.3=Joining an Online Game
howtoplay.multiplayer.text.4=To join a multiplayer game, go to the friends tab. All joinable Realms, Friends, and LAN Games will be shown here. Pick a game and select it to join.
howtoplay.multiplayer.text.4.norealms=To join a multiplayer game, go to the friends tab. All joinable Realms, Friends, and LAN Games will be shown here. Pick a game and select it to join.
howtoplay.multiplayer.text.5=When you start or join an online game, it will be visible to people in your friends tab. Once your friends join, the game will now be visible to friends of these friends.
howtoplay.multiplayer.text.6=Note: You must have an internet connection and be signed in to a Microsoft Account to play multiplayer.
howtoplay.multiplayer.header.4=Player Permissions
howtoplay.multiplayer.text.7=If you would like to manage the permissions of individual players, player permissions makes it easy. Open the pause menu and on the right, you will see a list of players currently in the world. Select their permission icon to adjust that player's permissions.

howtoplay.navigation=Navigation
howtoplay.navigation.title=How to Play : Navigation
howtoplay.navigation.text.1=Navigation is an important skill that you will learn in Minecraft as it's not always easy to find your way home.
howtoplay.navigation.text.2=First of all, there are some basic tools that you should know about.
howtoplay.navigation.header.1=Navigation Tools
howtoplay.navigation.text.3=A Compass will point in the direction of the first place you spawned into the world, an easy way to find where you started.
howtoplay.navigation.text.4=A Map lets you see the area you've explored. Put it in your offhand slot to use it like a mini-map. You can increase its size by adding more paper to the edges in a Crafting Table.
howtoplay.navigation.text.5=A Locator Map will show you where you are and what direction you are facing in relation to the Locator Map's origin. It will also show you the location of other players in the same world.
howtoplay.navigation.text.6=A Cartography Table can help you create, expand, copy and even name maps.
howtoplay.navigation.text.7=Place paper into the first slot to craft a Map. If you add a compass into the second slot you can craft a Locator Map. Adding Paper or a blank Map will let you expand or copy your Map.  
howtoplay.navigation.header.2=Navigation Techniques
howtoplay.navigation.text.8=Try to watch for landmarks as you explore. The more you play on a world, the more you will be able to remember where you are. Another good trick is to place Torches as you go so that you may find your way back home.
howtoplay.navigation.text.9=The sun can also tell you what direction you are facing. Remember: the sun rises in the east and sets in the west!
howtoplay.navigation.header.3=Explorer Maps
howtoplay.navigation.text.10=There are several places for you to explore. Find a village and look for a Cartographer. He will trade you Explorer Maps that will lead you to strange places very far away.

howtoplay.netherPortals=Nether Portal
howtoplay.netherPortals.title=How to Play : Nether Portal
howtoplay.netherPortals.text.1=A Nether Portal allows you to travel to a different dimension.
howtoplay.netherPortals.text.2=If constructed correctly the Nether Portal can be lit and activated.
howtoplay.netherPortals.text.3=Examples of portal construction are shown here:

howtoplay.nightfall=Nightfall
howtoplay.nightfall.title=How to Play : Nightfall
howtoplay.nightfall.text.1=In Minecraft, the sun rises and sets as you adventure in your world. As the sun sets, you must take precautions. There are many dangers at night!
howtoplay.nightfall.header.1=Take Shelter
howtoplay.nightfall.text.2=If you have not already, you will need a place to take shelter from the dark and what lurks within it. This can be as simple as a hole in the side of a hill or a small cabin in the forest.
howtoplay.nightfall.text.3=Be sure to block off all entrances, you may want to use a door so you can go in and out. If you have made a bed this is a good place to put it along with your Crafting Table and Furnace.
howtoplay.nightfall.text.4=Also be sure to light up the area with torches, it may save your life.

howtoplay.pets=Pets
howtoplay.pets.title=How to Play : Pets
howtoplay.pets.header.1=Befriending Pets
howtoplay.pets.text.1.notTouch=There are several mobs in Minecraft that you can tame and make your pet. Each can be tamed by giving them an item that they like. Once tamed you can have them stay by pressing :_input_key.use: while looking at them.
howtoplay.pets.text.1.touch=There are several mobs in Minecraft that you can tame and make your pet. Each can be tamed by giving them an item that they like. Once tamed you can have them stay by tapping the Sit Button.
howtoplay.pets.header.2=Wolves
howtoplay.pets.text.2=Wolves are a neutral mob that hunt in the colder climates. They will attack Rabbits, Skeletons, and Sheep but run away from Llamas. They won't attack you unless you provoke them. You can tame them by giving them Bones.
howtoplay.pets.header.3=Cats
howtoplay.pets.text.3=Cats are a neutral mob that can be found in villages. They will attack Rabbits and Baby Turtles. They will also keep Phantoms and Creepers away. They will try to run from you so taming them can be tough. They like fish so give them Raw Cod or Salmon to tame them.
howtoplay.pets.header.4=Parrots
howtoplay.pets.text.4=Parrots are a passive mob that live in the Jungle. They will imitate any other mob in the area. Seeds are their favorite, give them a few to tame them. Parrots will also sit on your shoulder... Yes, you can have more than one up there.

howtoplay.raids=Raids
howtoplay.raids.title=How to Play : Raids
howtoplay.raids.text.1=While exploring the world you may encounter a Pillager encampment or patrol. Pillagers are always looking for villages to raid. Taking their Banners will get their attention, and they may choose to attack a nearby village.  
howtoplay.raids.text.2=Be sure to defend your village, the attack will stop if you defeat all the raiders.
howtoplay.raids.header.1=I Need A Hero
howtoplay.raids.text.3=Villagers will most likely be very grateful that you have saved them from the raiders. Be sure to check back with them, you might get a good deal.  

howtoplay.ranching=Ranching
howtoplay.ranching.title=How to Play : Ranching
howtoplay.ranching.text.1=In Minecraft, you can have your own ranch filled with livestock! Ranching provides lots of resources such as wool, eggs, and raw meat.
howtoplay.ranching.header.1=Growing Your Herd
howtoplay.ranching.text.2=In order to keep your livestock growing, you have to make baby animals. Adult animals must enter Love Mode for this to happen. Feed the animals their preferred food to get them to enter Love Mode.
howtoplay.ranching.text.3=When two like animals are nearby and in Love Mode, they will kiss for a moment and then a baby animal will appear! The baby animal will stay with their parents until they grow into an adult. After having a baby, adult animals must wait 5 minutes before entering Love Mode.
howtoplay.ranching.header.2=What Do Mobs Eat?
howtoplay.ranching.text.4=Cows, mooshrooms, and sheep all eat Wheat. Pigs can eat Carrots or Beetroot. Chickens love Seeds of any kinds. Rabbits will eat Carrots, Golden Carrots, or Dandelions. Ocelots enjoy Raw Cod or Salmon. Wolves like to eat any kind of meat.
howtoplay.ranching.text.5=Animals will follow you when you hold their desired food item. This will help you lead them to your ranch or another animal.

howtoplay.realms=Realms
howtoplay.realms.title=How to Play : Realms
howtoplay.realms.text.1=Realms is an awesome place to host a multiplayer game that is always running. Even when you aren't playing, your Realm will be up and running for your friends to explore!
howtoplay.realms.header.1=Get Started
howtoplay.realms.text.2=To set up a Realm, select Create New from the Worlds tab. Then select the New Realm button, then the Create New World button. Here you can name your Realm, and set the number of players. Then you will be able to add friends to your Realm.
howtoplay.realms.header.2=Realm Settings
howtoplay.realms.text.3=Once you have a Realm created, select the Pencil button to change the Realm settings.
howtoplay.realms.text.4=Under the Game button, you can rename the Realm, set the difficulty and gamemode, turn on cheats, and download/upload the world.
howtoplay.realms.text.5=Under the Members button you can manage all of the friends you've invited to your Realm, add/remove members, and set their permissions.
howtoplay.realms.text.6=Under the Subscription button you can see when the Realm will expire, extend the time until expiration, and open/close the Realm.

howtoplay.redstone=Redstone
howtoplay.redstone.title=How to Play : Redstone
howtoplay.redstone.text.1=Redstone Dust comes from Redstone that transmits an electrical signal. When it has power it lights up and transmits its signal to other Redstone items that are next to it.
howtoplay.redstone.header.1=Redstone Power
howtoplay.redstone.text.2=In order for one of these items to be powered, it needs a power source such as a Lever, Button, or Pressure Plate. Experiment to find them all!
howtoplay.redstone.text.3=Place some Redstone Dust next to a power source and turn it on to power it.
howtoplay.redstone.text.4=There are many items that use a Redstone signal to do something, such as a Hopper or a Piston.
howtoplay.redstone.text.5=Redstone allows for endless possibilities! Try creating something useful for yourself like an automatic farm!

howtoplay.resourceAndBehaviorPacks=Resource And Behavior Packs
howtoplay.resourceAndBehaviorPacks.title=How to Play : Resource And Behavior Packs
howtoplay.resourceAndBehaviorPacks.text.1=You can change the way that Minecraft looks and behaves using Resource and Behavior Packs.
howtoplay.resourceAndBehaviorPacks.text.2=In the Settings Menu, choose a pack from the available packs list and select the plus sign next to it. Now your pack will apply its changes to the default Minecraft pack.

howtoplay.scaffolding=Scaffolding
howtoplay.scaffolding.title=How to Play : Scaffolding
howtoplay.scaffolding.header.1=Reach For The Sky
howtoplay.scaffolding.text.1.keyboard=Scaffolding can help you create huge structures safely! You can walk inside scaffolding and stand on it. If you have a column of scaffolding you can go upward by standing in the scaffolding and pressing :_input_key.jump:. If you want to travel downward you can press :_input_key.sneak:.
howtoplay.scaffolding.text.1.gamepad=Scaffolding can help you create huge structures safely! You can walk inside scaffolding and stand on it. If you have a column of scaffolding you can go upward by standing in the scaffolding and pressing the :_input_key.jump: button. If you want to travel downward you can press and hold the :_input_key.sneak: button.
howtoplay.scaffolding.text.1.touch=Scaffolding can help you create huge structures safely! You can walk inside scaffolding and stand on it. If you have a column of scaffolding you can go upward by standing in the scaffolding and pressing the :touch_jump: button. If you want to travel downward you can press the :touch_fly_down: button.
howtoplay.scaffolding.text.1.rift_controller=Scaffolding can help you create huge structures safely! You can walk inside scaffolding and stand on it. If you have a column of scaffolding you can go upward by standing in the scaffolding and pressing the :rift_A: button. If you want to travel downward you can press and hold the :rift_X: button.
howtoplay.scaffolding.text.1.windowsmr_controller=Scaffolding can help you create huge structures safely! You can walk inside scaffolding and stand on it. If you have a column of scaffolding you can go upward by standing in the scaffolding and pressing the :windowsmr_right_touchpad: button. If you want to travel downward you can press and hold the :windowsmr_right_stick: button.
howtoplay.scaffolding.header.2=Support Your Build
howtoplay.scaffolding.text.2=Scaffolding needs support. You can only place so many scaffolding horizontally without touching a different block. When you attempt to place too many scaffolding blocks without proper support, it will fall. Scaffolding can be placed like normal blocks, but it can also be placed on its own foundation to quickly build a tall tower.
howtoplay.scaffolding.header.3=Break It Down
howtoplay.scaffolding.text.3=When Scaffolding is broken it will also break every Scaffolding block that was being supported by it. This will let you quickly clean up your scaffolding by breaking the supporting blocks!

howtoplay.structureBlocks=Structure Blocks
howtoplay.structureBlocks.title=How to Play : Structure Blocks	##
howtoplay.structureBlocks.text.1=Structure Blocks give creators the ability to copy and save out sections of their world into structures. Saved structures can then be pasted back into the world.	##
howtoplay.structureBlocks.header.1=How to Obtain	##
howtoplay.structureBlocks.text.2= To get a Structure Block you must use the /give slash command. To use a Structure Block, you must have Operator permissions.	##
howtoplay.structureBlocks.header.2= Modes	##
howtoplay.structureBlocks.text.3=Structure Blocks have 3 modes that can be switched between with the Mode dropdown: Save, Load, and 3D Export.	##
howtoplay.structureBlocks.text.4=Save mode saves an area of the world as a structure. ##
howtoplay.structureBlocks.text.5=Load mode loads a structure into the world. Only structures saved to the world or included in a behavior pack that is applied to the world can be loaded. ##
howtoplay.structureBlocks.text.6=3D Export exports a 3D model file on supported platforms.  ##
howtoplay.structureBlocks.header.3=Preview Window	##
howtoplay.structureBlocks.text.7=On the right side of the Structure Block screen, a preview of the area you have selected or the structure to load will appear depending on the mode the block is in.	##
howtoplay.structureBlocks.text.8.keyboard=Press :_input_key.attack: and drag, to rotate the 3D preview. 
howtoplay.structureBlocks.text.8.gamepad=Use :_gamepad_stick_right: to rotate the 3D preview. 
howtoplay.structureBlocks.text.8.touch=Touch and drag on the preview to rotate the 3D preview. 
howtoplay.structureBlocks.header.4=Redstone Activation	##
howtoplay.structureBlocks.text.9=Redstone can be used to activate a Structure Block. This will cause it to either save a structure out or load a structure into the world, depending on the mode of the block and its settings.	##
howtoplay.structureBlocks.header.5=Integrity and Seed	##
howtoplay.structureBlocks.text.10=You can change how much of a structure is loaded in by changing the values for Integrity and Seed.	##
howtoplay.structureBlocks.text.11=Integrity - the percentage of blocks to load in from the structure, accepted values are 0 to 100. Blocks will be chosen randomly if a Seed is not specified. ##
howtoplay.structureBlocks.text.12=Seed - the value entered here will determine which blocks to remove when using Integrity. Leave this field blank if you want the blocks to be chosen randomly. ##
howtoplay.structureBlocks.header.6=Structure Void Blocks	##
howtoplay.structureBlocks.text.13=When a structure with Structure Void blocks is loaded into the world, any block that would have been removed where the structure void block is loading into, will instead stay in the world.	##
howtoplay.structureBlocks.text.14=Example: loading an empty shipwreck into the bottom of the ocean will normally copy air over the water blocks. If the shipwreck was filled with structure void, then the water will stay, and you will have a shipwreck filled with water.  ##

howtoplay.servers=Servers
howtoplay.servers.title=How to Play : Servers
howtoplay.servers.text=The Server Tab will show popular Minecraft servers.

howtoplay.shulkerBoxes=Shulker Boxes
howtoplay.shulkerBoxes.title=How to Play : Shulker Boxes
howtoplay.shulkerBoxes.text.1=Combine two Shulker Shells with a Chest to create a Shulker Box. Shulker Shells are dropped by Shulkers found in End Cities. Unlike other containers, Shulker Boxes keep their items when broken. Shulker Boxes will break and drop as an item if pushed by Pistons, and can be placed using Dispensers.

howtoplay.theEnd=The End
howtoplay.theEnd.title=How to Play : The End
howtoplay.theEnd.text.1=The End is another dimension which can be accessed through an active End Portal. The End Portal can be found in a Stronghold, which is deep underground in the Overworld.
howtoplay.theEnd.text.2=Every End Portal frame block is missing something mysterious to bring out the power within.
howtoplay.theEnd.header.1=The Other Side
howtoplay.theEnd.text.3=Once the portal is active, jump into it to go to The End.
howtoplay.theEnd.text.4=The End is home to the dreaded Ender Dragon. She is a fierce and powerful enemy and seems to draw power from strange crystals. Her breath and fireball attacks leave lingering acid across the battlefield. Bring some friends to ensure your victory!
howtoplay.theEnd.text.5=If once wasn't enough, add four Ender Crystals to the exit portal corners to reawaken the Ender Dragon.

howtoplay.theStore=The Marketplace
howtoplay.theStore.title=How to Play : The Marketplace
howtoplay.theStore.text.1=The Minecraft Marketplace is a place to find new skins, worlds, textures, and mash-up packs by various talented creators.
howtoplay.theStore.header.1=Explore Content
howtoplay.theStore.text.2=Try a new skin for a fresh new look!
howtoplay.theStore.text.3=Check out worlds to unlock new maps and challenges that you will want to play again and again!
howtoplay.theStore.text.4=Give your old game a new look with texture packs that can be layered over your current worlds and Realms!
howtoplay.theStore.text.4.norealms=Give your old game a new look with texture packs that can be layered over your current worlds!
howtoplay.theStore.text.5=Or try them all with mash-up packs which are curated collections of skins, worlds, and textures that, when combined, will transform your worlds and Realms!
howtoplay.theStore.text.5.norealms=Or try them all with mash-up packs which are curated collections of skins, worlds, and textures that, when combined, will transform your worlds!

howtoplay.tools=Tools
howtoplay.tools.title=How to Play : Tools
howtoplay.tools.text.1=There are many tools in Minecraft that will allow you mine blocks faster than your fist. Tools can be crafted from Wood, Stone, Iron, Gold, or Diamond.
howtoplay.tools.text.2=Using the right tool for the job will not only mine that block faster, but will also lose less durability. More complex tools must be created in order to mine rarer resources.
howtoplay.tools.header.1=Tools that Break Stuff
howtoplay.tools.text.3=The Pickaxe is good at breaking hard blocks like Cobblestone, Iron, or Furnaces.
howtoplay.tools.text.4=The Axe is good at breaking wooden blocks like Logs, Planks, or Crafting Tables.
howtoplay.tools.text.5.notTouch=The Shovel is good at breaking loose blocks like Dirt, Sand, and Gravel. Shovels can also create a path by pressing :_input_key.use: on grass blocks.
howtoplay.tools.text.5.touch=The Shovel is good at breaking loose blocks like Dirt, Sand, and Gravel. Shovels can also create a path by tapping on grass blocks.
howtoplay.tools.text.6.notTouch=The Hoe is required for tilling Dirt to plant crops. Press :_input_key.use: on Dirt or Grass blocks to turn them into soil.
howtoplay.tools.text.6.touch=The Hoe is required for tilling Dirt to plant crops. Tap Dirt or Grass blocks to turn them into soil.
howtoplay.tools.text.7=All of these tools can accept enchantments, either from the Enchanting Table or Anvil.
howtoplay.tools.header.2=Other Tools
howtoplay.tools.text.8=There are even more tools to aid you on your adventure:
howtoplay.tools.text.9=A Bucket will allow you to collect things like Water, Milk, Lava, and even Fish.
howtoplay.tools.text.10=Flint & Steel allows you to light fires... be careful in densely wooded areas!
howtoplay.tools.text.11=A Fishing Rod lets you catch fish and other loot from water blocks. It can also be used to pull other players and mobs. Fishing Rods can also be enchanted!
howtoplay.tools.text.12=Shears are very handy for collecting items like Wool, Leaves, and Cobwebs.

howtoplay.trading=Villager Trading
howtoplay.trading.title=How to Play : Villager Trading
howtoplay.trading.text.1=Trading can be done in villages. Each villager has a profession; they can be Farmers, Butchers, Blacksmiths, Cartographers, Librarians, or Priests. Some professions will trade for items that other professions cannot. Explore and trade with many villagers to find all the trades. 
howtoplay.trading.header.1=Goods for Emeralds
howtoplay.trading.text.2=You can find out what a villager is offering as trade by interacting with them. A villager may adjust the price of a trade whenever a player trades with it. A villager may run out of stock, temporarily disabling the trade if it is used too frequently. Trades usually involve buying or selling items for emeralds. 
howtoplay.trading.text.3=As you trade with villagers, they will get better at their profession and start to offer better and more valuable trades.  
howtoplay.trading.text.4=Take care of your villagers, they need to rest, work, and eat to thrive.

howtoplay.transportation=Transportation
howtoplay.transportation.title=How to Play : Transportation
howtoplay.transportation.text.1=There are many ways to get around other than walking or riding a mount or flying.
howtoplay.transportation.header.1=Minecarts
howtoplay.transportation.text.2=Minecarts are ridable carts that roll on Rails. You can make the Rails go wherever you want by placing them next to each other, place a Minecart on it and you are ready to ride.
howtoplay.transportation.text.3.keyboard=Press :_input_key.use: while looking at a Minecart to hop in and ride. Press :_input_key.forward: to roll forward. Press :_input_key.jump: or :_input_key.sneak: to get out. 
howtoplay.transportation.text.3.gamepad=Press :_input_key.use: while looking at a Minecart to hop in and ride. Push :_gamepad_stick_left: forward to roll forward. Press :_input_key.jump: or :_input_key.sneak: to get out.
howtoplay.transportation.text.3.touch=Tap the Ride button while near a Minecart to hop in and ride. Press :_input_key.forward: to roll forward. Tap :_input_key.jump: to get out. 
howtoplay.transportation.header.2=Boats
howtoplay.transportation.text.4=Boats let you travel across water, place one on a water block and you are ready to go.
howtoplay.transportation.text.5.keyboard=Press :_input_key.use: while looking at a Boat to board it. Press :_input_key.left: or :_input_key.right: to turn left or right. Press :_input_key.forward: to move forward. Press :_input_key.jump: or :_input_key.sneak: to get out.
howtoplay.transportation.text.5.gamepad=Press :_input_key.use: while looking at a Boat to board it. Push :_gamepad_stick_left: left or right to turn. Push :_gamepad_stick_left: forward to move forward. Press :_input_key.jump: or :_input_key.sneak: to get out.
howtoplay.transportation.text.5.touch=Tap the board button while near a Boat to board it. Press :_input_key.left: or :_input_key.right: to turn left or right. Press them both to move forward. Tap the leave boat button to get out.

howtoplay.weapons=Weapons
howtoplay.weapons.title=How to Play : Weapons
howtoplay.weapons.header.1=Sword
howtoplay.weapons.text.1=The Sword is the primary melee weapon in Minecraft. It can be made from Wood, Stone, Iron, Gold, or Diamond. Jump attacks do critical damage!
howtoplay.weapons.text.2.notTouch=Press :_input_key.attack: with a Sword in your hand to attack. 
howtoplay.weapons.text.2.touch=Tap mobs to attack them.
howtoplay.weapons.header.2=Bow
howtoplay.weapons.text.3.notTouch=The Bow is the primary ranged weapon in Minecraft. Press and hold :_input_key.use: to draw the bow back. Release :_input_key.use: to fire an arrow. The bow requires arrows to fire. 
howtoplay.weapons.text.3.touch=The Bow is the primary ranged weapon in Minecraft. Press and hold the Screen to draw the bow back. Release the press to fire an arrow. The bow requires arrows to fire.
howtoplay.weapons.text.4=Bows do more damage the further they are pulled back, make sure to pull it all the way back.
howtoplay.weapons.header.3=Crossbow
howtoplay.weapons.text.5.notTouch=The Crossbow is an alternate ranged weapon. Press and hold the :_input_key.use: to load the crossbow. Once the Crossbow is loaded, it will stay loaded until it is fired. Press :_input_key.use: to fire an arrow. The Crossbow requires arrows to load.
howtoplay.weapons.text.5.touch=The Crossbow is an alternate ranged weapon. Press and hold the screen to load the crossbow. Once the Crossbow is loaded, it will stay loaded until it is fired, even if you put it in your inventory. Press the hold the screen to fire an arrow. The Crossbow requires arrows to load.
howtoplay.weapons.header.4=Trident
howtoplay.weapons.text.6.notTouch=The Trident is a dual-purpose melee and ranged weapon. Press :_input_key.attack: to attack. Press and hold :_input_key.use: to draw the trident back. Release :_input_key.use: to throw the trident. The Trident is the best weapon underwater.
howtoplay.weapons.text.6.touch=The Trident is a dual-purpose melee and ranged weapon. Tap mobs to attack them. Press and hold the Screen to draw the trident back. Release the press to throw the trident. The Trident is the best weapon underwater.
howtoplay.weapons.header.5=Enchanted Weapons
howtoplay.weapons.text.7=The Sword, Bow, Crossbow, and Trident have specific enchantments that you can get from the Enchanting Table or Anvil that will improve damage, durability and even light mobs on fire!

howtoplay.whatsNew=What's New

immersive_reader.book_page_header=Page %1 of %2
immersive_reader.portfolio_page_header= Page %1
immersive_reader.error.webview_failure=There was a problem connecting to Immersive Reader.
immersive_reader.error.identity_failure=There was a problem connecting to Immersive Reader. Please restart Minecraft: Education Edition and try again.

level.launch.failed=Launch failed
level.export.started=Level export started...
level.import.started=Level import started...
level.export.success=Level export finished successfully
level.import.success=Level import finished successfully
level.export.failed=Level export failed
level.import.failed=Level import failed
level.import.failed.incompatibleEdition=Level import failed: Unsupported file format
invite.clear=Clear Selection
invite.send=Send %d Invites
invite.sendOne=Send 1 Invite
invite.title=Invite Friends to your Game
invite.error.message=Some of your invites may have failed to send.
invite.noFriends=You haven't added anyone to your friend's list!
invite.OnlineFriends=Online Friends
invite.OfflineFriends=Offline Friends
invite.SuggestedFriends=Suggested Friends
invite.error1=Something went wrong.  We couldn't load your friends list.
invite.error2=Something went wrong.  We couldn't load some of your friends details.
invite.realm.add_member=Add Realm Members
invite.notification.title=Game Invite
invite.notification.description=%s has invited you to play Minecraft
invite.confirmation.description=%s has invited you to play Minecraft
invite.pageCounter=%d/%d

inventory.binSlot=Destroy Item

item.air.name=Air
item.apple.name=Apple
item.golden_apple.name=Golden Apple
item.appleEnchanted.name=Enchanted Apple
item.armor_stand.name=Armor Stand
item.arrow.name=Arrow
item.tipped_arrow.name=Tipped Arrow
item.banner.black.name=Black Banner
item.banner.blue.name=Blue Banner
item.banner.border.black=Black Bordure
item.banner.border.blue=Blue Bordure
item.banner.border.brown=Brown Bordure
item.banner.border.cyan=Cyan Bordure
item.banner.border.gray=Gray Bordure
item.banner.border.green=Green Bordure
item.banner.border.lightBlue=Light Blue Bordure
item.banner.border.lime=Lime Bordure
item.banner.border.magenta=Magenta Bordure
item.banner.border.orange=Orange Bordure
item.banner.border.pink=Pink Bordure
item.banner.border.purple=Purple Bordure
item.banner.border.red=Red Bordure
item.banner.border.silver=Light Gray Bordure
item.banner.border.white=White Bordure
item.banner.border.yellow=Yellow Bordure
item.banner.bricks.black=Black Field Masoned
item.banner.bricks.blue=Blue Field Masoned
item.banner.bricks.brown=Brown Field Masoned
item.banner.bricks.cyan=Cyan Field Masoned
item.banner.bricks.gray=Gray Field Masoned
item.banner.bricks.green=Green Field Masoned
item.banner.bricks.lightBlue=Light Blue Field Masoned
item.banner.bricks.lime=Lime Field Masoned
item.banner.bricks.magenta=Magenta Field Masoned
item.banner.bricks.orange=Orange Field Masoned
item.banner.bricks.pink=Pink Field Masoned
item.banner.bricks.purple=Purple Field Masoned
item.banner.bricks.red=Red Field Masoned
item.banner.bricks.silver=Light Gray Field Masoned
item.banner.bricks.white=White Field Masoned
item.banner.bricks.yellow=Yellow Field Masoned
item.banner.brown.name=Brown Banner
item.banner.circle.black=Black Roundel
item.banner.circle.blue=Blue Roundel
item.banner.circle.brown=Brown Roundel
item.banner.circle.cyan=Cyan Roundel
item.banner.circle.gray=Gray Roundel
item.banner.circle.green=Green Roundel
item.banner.circle.lightBlue=Light Blue Roundel
item.banner.circle.lime=Lime Roundel
item.banner.circle.magenta=Magenta Roundel
item.banner.circle.orange=Orange Roundel
item.banner.circle.pink=Pink Roundel
item.banner.circle.purple=Purple Roundel
item.banner.circle.red=Red Roundel
item.banner.circle.silver=Light Gray Roundel
item.banner.circle.white=White Roundel
item.banner.circle.yellow=Yellow Roundel
item.banner.creeper.black=Black Creeper Charge
item.banner.creeper.blue=Blue Creeper Charge
item.banner.creeper.brown=Brown Creeper Charge
item.banner.creeper.cyan=Cyan Creeper Charge
item.banner.creeper.gray=Gray Creeper Charge
item.banner.creeper.green=Green Creeper Charge
item.banner.creeper.lightBlue=Light Blue Creeper Charge
item.banner.creeper.lime=Lime Creeper Charge
item.banner.creeper.magenta=Magenta Creeper Charge
item.banner.creeper.orange=Orange Creeper Charge
item.banner.creeper.pink=Pink Creeper Charge
item.banner.creeper.purple=Purple Creeper Charge
item.banner.creeper.red=Red Creeper Charge
item.banner.creeper.silver=Light Gray Creeper Charge
item.banner.creeper.white=White Creeper Charge
item.banner.creeper.yellow=Yellow Creeper Charge
item.banner.cross.black=Black Saltire
item.banner.cross.blue=Blue Saltire
item.banner.cross.brown=Brown Saltire
item.banner.cross.cyan=Cyan Saltire
item.banner.cross.gray=Gray Saltire
item.banner.cross.green=Green Saltire
item.banner.cross.lightBlue=Light Blue Saltire
item.banner.cross.lime=Lime Saltire
item.banner.cross.magenta=Magenta Saltire
item.banner.cross.orange=Orange Saltire
item.banner.cross.pink=Pink Saltire
item.banner.cross.purple=Purple Saltire
item.banner.cross.red=Red Saltire
item.banner.cross.silver=Light Gray Saltire
item.banner.cross.white=White Saltire
item.banner.cross.yellow=Yellow Saltire
item.banner.curly_border.black=Black Bordure Indented
item.banner.curly_border.blue=Blue Bordure Indented
item.banner.curly_border.brown=Brown Bordure Indented
item.banner.curly_border.cyan=Cyan Bordure Indented
item.banner.curly_border.gray=Gray Bordure Indented
item.banner.curly_border.green=Green Bordure Indented
item.banner.curly_border.lightBlue=Light Blue Bordure Indented
item.banner.curly_border.lime=Lime Bordure Indented
item.banner.curly_border.magenta=Magenta Bordure Indented
item.banner.curly_border.orange=Orange Bordure Indented
item.banner.curly_border.pink=Pink Bordure Indented
item.banner.curly_border.purple=Purple Bordure Indented
item.banner.curly_border.red=Red Bordure Indented
item.banner.curly_border.silver=Light Gray Bordure Indented
item.banner.curly_border.white=White Bordure Indented
item.banner.curly_border.yellow=Yellow Bordure Indented
item.banner.cyan.name=Cyan Banner
item.banner.diagonal_left.black=Black Per Bend Sinister
item.banner.diagonal_left.blue=Blue Per Bend Sinister
item.banner.diagonal_left.brown=Brown Per Bend Sinister
item.banner.diagonal_left.cyan=Cyan Per Bend Sinister
item.banner.diagonal_left.gray=Gray Per Bend Sinister
item.banner.diagonal_left.green=Green Per Bend Sinister
item.banner.diagonal_left.lightBlue=Light Blue Per Bend Sinister
item.banner.diagonal_left.lime=Lime Per Bend Sinister
item.banner.diagonal_left.magenta=Magenta Per Bend Sinister
item.banner.diagonal_left.orange=Orange Per Bend Sinister
item.banner.diagonal_left.pink=Pink Per Bend Sinister
item.banner.diagonal_left.purple=Purple Per Bend Sinister
item.banner.diagonal_left.red=Red Per Bend Sinister
item.banner.diagonal_left.silver=Light Gray Per Bend Sinister
item.banner.diagonal_left.white=White Per Bend Sinister
item.banner.diagonal_left.yellow=Yellow Per Bend Sinister
item.banner.diagonal_right.black=Black Per Bend
item.banner.diagonal_right.blue=Blue Per Bend
item.banner.diagonal_right.brown=Brown Per Bend
item.banner.diagonal_right.cyan=Cyan Per Bend
item.banner.diagonal_right.gray=Gray Per Bend
item.banner.diagonal_right.green=Green Per Bend
item.banner.diagonal_right.lightBlue=Light Blue Per Bend
item.banner.diagonal_right.lime=Lime Per Bend
item.banner.diagonal_right.magenta=Magenta Per Bend
item.banner.diagonal_right.orange=Orange Per Bend
item.banner.diagonal_right.pink=Pink Per Bend
item.banner.diagonal_right.purple=Purple Per Bend
item.banner.diagonal_right.red=Red Per Bend
item.banner.diagonal_right.silver=Light Gray Per Bend
item.banner.diagonal_right.white=White Per Bend
item.banner.diagonal_right.yellow=Yellow Per Bend
item.banner.diagonal_up_left.black=Black Per Bend Inverted
item.banner.diagonal_up_left.blue=Blue Per Bend Inverted
item.banner.diagonal_up_left.brown=Brown Per Bend Inverted
item.banner.diagonal_up_left.cyan=Cyan Per Bend Inverted
item.banner.diagonal_up_left.gray=Gray Per Bend Inverted
item.banner.diagonal_up_left.green=Green Per Bend Inverted
item.banner.diagonal_up_left.lightBlue=Light Blue Per Bend Inverted
item.banner.diagonal_up_left.lime=Lime Per Bend Inverted
item.banner.diagonal_up_left.magenta=Magenta Per Bend Inverted
item.banner.diagonal_up_left.orange=Orange Per Bend Inverted
item.banner.diagonal_up_left.pink=Pink Per Bend Inverted
item.banner.diagonal_up_left.purple=Purple Per Bend Inverted
item.banner.diagonal_up_left.red=Red Per Bend Inverted
item.banner.diagonal_up_left.silver=Light Gray Per Bend Inverted
item.banner.diagonal_up_left.white=White Per Bend Inverted
item.banner.diagonal_up_left.yellow=Yellow Per Bend Inverted
item.banner.diagonal_up_right.black=Black Per Bend Sinister Inverted
item.banner.diagonal_up_right.blue=Blue Per Bend Sinister Inverted
item.banner.diagonal_up_right.brown=Brown Per Bend Sinister Inverted
item.banner.diagonal_up_right.cyan=Cyan Per Bend Sinister Inverted
item.banner.diagonal_up_right.gray=Gray Per Bend Sinister Inverted
item.banner.diagonal_up_right.green=Green Per Bend Sinister Inverted
item.banner.diagonal_up_right.lightBlue=Light Blue Per Bend Sinister Inverted
item.banner.diagonal_up_right.lime=Lime Per Bend Sinister Inverted
item.banner.diagonal_up_right.magenta=Magenta Per Bend Sinister Inverted
item.banner.diagonal_up_right.orange=Orange Per Bend Sinister Inverted
item.banner.diagonal_up_right.pink=Pink Per Bend Sinister Inverted
item.banner.diagonal_up_right.purple=Purple Per Bend Sinister Inverted
item.banner.diagonal_up_right.red=Red Per Bend Sinister Inverted
item.banner.diagonal_up_right.silver=Light Gray Per Bend Sinister Inverted
item.banner.diagonal_up_right.white=White Per Bend Sinister Inverted
item.banner.diagonal_up_right.yellow=Yellow Per Bend Sinister Inverted
item.banner.flower.black=Black Flower Charge
item.banner.flower.blue=Blue Flower Charge
item.banner.flower.brown=Brown Flower Charge
item.banner.flower.cyan=Cyan Flower Charge
item.banner.flower.gray=Gray Flower Charge
item.banner.flower.green=Green Flower Charge
item.banner.flower.lightBlue=Light Blue Flower Charge
item.banner.flower.lime=Lime Flower Charge
item.banner.flower.magenta=Magenta Flower Charge
item.banner.flower.orange=Orange Flower Charge
item.banner.flower.pink=Pink Flower Charge
item.banner.flower.purple=Purple Flower Charge
item.banner.flower.red=Red Flower Charge
item.banner.flower.silver=Light Gray Flower Charge
item.banner.flower.white=White Flower Charge
item.banner.flower.yellow=Yellow Flower Charge
item.banner.gradient.black=Black Gradient
item.banner.gradient.blue=Blue Gradient
item.banner.gradient.brown=Brown Gradient
item.banner.gradient.cyan=Cyan Gradient
item.banner.gradient.gray=Gray Gradient
item.banner.gradient.green=Green Gradient
item.banner.gradient.lightBlue=Light Blue Gradient
item.banner.gradient.lime=Lime Gradient
item.banner.gradient.magenta=Magenta Gradient
item.banner.gradient.orange=Orange Gradient
item.banner.gradient.pink=Pink Gradient
item.banner.gradient.purple=Purple Gradient
item.banner.gradient.red=Red Gradient
item.banner.gradient.silver=Light Gray Gradient
item.banner.gradient.white=White Gradient
item.banner.gradient.yellow=Yellow Gradient
item.banner.gradient_up.black=Black Base Gradient
item.banner.gradient_up.blue=Blue Base Gradient
item.banner.gradient_up.brown=Brown Base Gradient
item.banner.gradient_up.cyan=Cyan Base Gradient
item.banner.gradient_up.gray=Gray Base Gradient
item.banner.gradient_up.green=Green Base Gradient
item.banner.gradient_up.lightBlue=Light Blue Base Gradient
item.banner.gradient_up.lime=Lime Base Gradient
item.banner.gradient_up.magenta=Magenta Base Gradient
item.banner.gradient_up.orange=Orange Base Gradient
item.banner.gradient_up.pink=Pink Base Gradient
item.banner.gradient_up.purple=Purple Base Gradient
item.banner.gradient_up.red=Red Base Gradient
item.banner.gradient_up.silver=Light Gray Base Gradient
item.banner.gradient_up.white=White Base Gradient
item.banner.gradient_up.yellow=Yellow Base Gradient
item.banner.gray.name=Gray Banner
item.banner.green.name=Green Banner
item.banner.half_horizontal.black=Black Per Fess
item.banner.half_horizontal.blue=Blue Per Fess
item.banner.half_horizontal.brown=Brown Per Fess
item.banner.half_horizontal.cyan=Cyan Per Fess
item.banner.half_horizontal.gray=Gray Per Fess
item.banner.half_horizontal.green=Green Per Fess
item.banner.half_horizontal.lightBlue=Light Blue Per Fess
item.banner.half_horizontal.lime=Lime Per Fess
item.banner.half_horizontal.magenta=Magenta Per Fess
item.banner.half_horizontal.orange=Orange Per Fess
item.banner.half_horizontal.pink=Pink Per Fess
item.banner.half_horizontal.purple=Purple Per Fess
item.banner.half_horizontal.red=Red Per Fess
item.banner.half_horizontal.silver=Light Gray Per Fess
item.banner.half_horizontal.white=White Per Fess
item.banner.half_horizontal.yellow=Yellow Per Fess
item.banner.half_horizontal_bottom.black=Black Per Fess Inverted
item.banner.half_horizontal_bottom.blue=Blue Per Fess Inverted
item.banner.half_horizontal_bottom.brown=Brown Per Fess Inverted
item.banner.half_horizontal_bottom.cyan=Cyan Per Fess Inverted
item.banner.half_horizontal_bottom.gray=Gray Per Fess Inverted
item.banner.half_horizontal_bottom.green=Green Per Fess Inverted
item.banner.half_horizontal_bottom.lightBlue=Light Blue Per Fess Inverted
item.banner.half_horizontal_bottom.lime=Lime Per Fess Inverted
item.banner.half_horizontal_bottom.magenta=Magenta Per Fess Inverted
item.banner.half_horizontal_bottom.orange=Orange Per Fess Inverted
item.banner.half_horizontal_bottom.pink=Pink Per Fess Inverted
item.banner.half_horizontal_bottom.purple=Purple Per Fess Inverted
item.banner.half_horizontal_bottom.red=Red Per Fess Inverted
item.banner.half_horizontal_bottom.silver=Light Gray Per Fess Inverted
item.banner.half_horizontal_bottom.white=White Per Fess Inverted
item.banner.half_horizontal_bottom.yellow=Yellow Per Fess Inverted
item.banner.half_vertical.black=Black Per Pale
item.banner.half_vertical.blue=Blue Per Pale
item.banner.half_vertical.brown=Brown Per Pale
item.banner.half_vertical.cyan=Cyan Per Pale
item.banner.half_vertical.gray=Gray Per Pale
item.banner.half_vertical.green=Green Per Pale
item.banner.half_vertical.lightBlue=Light Blue Per Pale
item.banner.half_vertical.lime=Lime Per Pale
item.banner.half_vertical.magenta=Magenta Per Pale
item.banner.half_vertical.orange=Orange Per Pale
item.banner.half_vertical.pink=Pink Per Pale
item.banner.half_vertical.purple=Purple Per Pale
item.banner.half_vertical.red=Red Per Pale
item.banner.half_vertical.silver=Light Gray Per Pale
item.banner.half_vertical.white=White Per Pale
item.banner.half_vertical.yellow=Yellow Per Pale
item.banner.half_vertical_right.black=Black Per Pale Inverted
item.banner.half_vertical_right.blue=Blue Per Pale Inverted
item.banner.half_vertical_right.brown=Brown Per Pale Inverted
item.banner.half_vertical_right.cyan=Cyan Per Pale Inverted
item.banner.half_vertical_right.gray=Gray Per Pale Inverted
item.banner.half_vertical_right.green=Green Per Pale Inverted
item.banner.half_vertical_right.lightBlue=Light Blue Per Pale Inverted
item.banner.half_vertical_right.lime=Lime Per Pale Inverted
item.banner.half_vertical_right.magenta=Magenta Per Pale Inverted
item.banner.half_vertical_right.orange=Orange Per Pale Inverted
item.banner.half_vertical_right.pink=Pink Per Pale Inverted
item.banner.half_vertical_right.purple=Purple Per Pale Inverted
item.banner.half_vertical_right.red=Red Per Pale Inverted
item.banner.half_vertical_right.silver=Light Gray Per Pale Inverted
item.banner.half_vertical_right.white=White Per Pale Inverted
item.banner.half_vertical_right.yellow=Yellow Per Pale Inverted
item.banner.illager_captain.name=Illager Banner
item.banner.lightBlue.name=Light Blue Banner
item.banner.lime.name=Lime Banner
item.banner.magenta.name=Magenta Banner
item.banner.mojang.black=Black Thing
item.banner.mojang.blue=Blue Thing
item.banner.mojang.brown=Brown Thing
item.banner.mojang.cyan=Cyan Thing
item.banner.mojang.gray=Gray Thing
item.banner.mojang.green=Green Thing
item.banner.mojang.lightBlue=Light Blue Thing
item.banner.mojang.lime=Lime Thing
item.banner.mojang.magenta=Magenta Thing
item.banner.mojang.orange=Orange Thing
item.banner.mojang.pink=Pink Thing
item.banner.mojang.purple=Purple Thing
item.banner.mojang.red=Red Thing
item.banner.mojang.silver=Light Gray Thing
item.banner.mojang.white=White Thing
item.banner.mojang.yellow=Yellow Thing
item.banner.orange.name=Orange Banner
item.banner.pink.name=Pink Banner
item.banner.purple.name=Purple Banner
item.banner.red.name=Red Banner
item.banner.rhombus.black=Black Lozenge
item.banner.rhombus.blue=Blue Lozenge
item.banner.rhombus.brown=Brown Lozenge
item.banner.rhombus.cyan=Cyan Lozenge
item.banner.rhombus.gray=Gray Lozenge
item.banner.rhombus.green=Green Lozenge
item.banner.rhombus.lightBlue=Light Blue Lozenge
item.banner.rhombus.lime=Lime Lozenge
item.banner.rhombus.magenta=Magenta Lozenge
item.banner.rhombus.orange=Orange Lozenge
item.banner.rhombus.pink=Pink Lozenge
item.banner.rhombus.purple=Purple Lozenge
item.banner.rhombus.red=Red Lozenge
item.banner.rhombus.silver=Light Gray Lozenge
item.banner.rhombus.white=White Lozenge
item.banner.rhombus.yellow=Yellow Lozenge
item.banner.silver.name=Light Gray Banner
item.banner.skull.black=Black Skull Charge
item.banner.skull.blue=Blue Skull Charge
item.banner.skull.brown=Brown Skull Charge
item.banner.skull.cyan=Cyan Skull Charge
item.banner.skull.gray=Gray Skull Charge
item.banner.skull.green=Green Skull Charge
item.banner.skull.lightBlue=Light Blue Skull Charge
item.banner.skull.lime=Lime Skull Charge
item.banner.skull.magenta=Magenta Skull Charge
item.banner.skull.orange=Orange Skull Charge
item.banner.skull.pink=Pink Skull Charge
item.banner.skull.purple=Purple Skull Charge
item.banner.skull.red=Red Skull Charge
item.banner.skull.silver=Light Gray Skull Charge
item.banner.skull.white=White Skull Charge
item.banner.skull.yellow=Yellow Skull Charge
item.banner.small_stripes.black=Black Paly
item.banner.small_stripes.blue=Blue Paly
item.banner.small_stripes.brown=Brown Paly
item.banner.small_stripes.cyan=Cyan Paly
item.banner.small_stripes.gray=Gray Paly
item.banner.small_stripes.green=Green Paly
item.banner.small_stripes.lightBlue=Light Blue Paly
item.banner.small_stripes.lime=Lime Paly
item.banner.small_stripes.magenta=Magenta Paly
item.banner.small_stripes.orange=Orange Paly
item.banner.small_stripes.pink=Pink Paly
item.banner.small_stripes.purple=Purple Paly
item.banner.small_stripes.red=Red Paly
item.banner.small_stripes.silver=Light Gray Paly
item.banner.small_stripes.white=White Paly
item.banner.small_stripes.yellow=Yellow Paly
item.banner.square_bottom_left.black=Black Base Dexter Canton
item.banner.square_bottom_left.blue=Blue Base Dexter Canton
item.banner.square_bottom_left.brown=Brown Base Dexter Canton
item.banner.square_bottom_left.cyan=Cyan Base Dexter Canton
item.banner.square_bottom_left.gray=Gray Base Dexter Canton
item.banner.square_bottom_left.green=Green Base Dexter Canton
item.banner.square_bottom_left.lightBlue=Light Blue Base Dexter Canton
item.banner.square_bottom_left.lime=Lime Base Dexter Canton
item.banner.square_bottom_left.magenta=Magenta Base Dexter Canton
item.banner.square_bottom_left.orange=Orange Base Dexter Canton
item.banner.square_bottom_left.pink=Pink Base Dexter Canton
item.banner.square_bottom_left.purple=Purple Base Dexter Canton
item.banner.square_bottom_left.red=Red Base Dexter Canton
item.banner.square_bottom_left.silver=Light Gray Base Dexter Canton
item.banner.square_bottom_left.white=White Base Dexter Canton
item.banner.square_bottom_left.yellow=Yellow Base Dexter Canton
item.banner.square_bottom_right.black=Black Base Sinister Canton
item.banner.square_bottom_right.blue=Blue Base Sinister Canton
item.banner.square_bottom_right.brown=Brown Base Sinister Canton
item.banner.square_bottom_right.cyan=Cyan Base Sinister Canton
item.banner.square_bottom_right.gray=Gray Base Sinister Canton
item.banner.square_bottom_right.green=Green Base Sinister Canton
item.banner.square_bottom_right.lightBlue=Light Blue Base Sinister Canton
item.banner.square_bottom_right.lime=Lime Base Sinister Canton
item.banner.square_bottom_right.magenta=Magenta Base Sinister Canton
item.banner.square_bottom_right.orange=Orange Base Sinister Canton
item.banner.square_bottom_right.pink=Pink Base Sinister Canton
item.banner.square_bottom_right.purple=Purple Base Sinister Canton
item.banner.square_bottom_right.red=Red Base Sinister Canton
item.banner.square_bottom_right.silver=Light Gray Base Sinister Canton
item.banner.square_bottom_right.white=White Base Sinister Canton
item.banner.square_bottom_right.yellow=Yellow Base Sinister Canton
item.banner.square_top_left.black=Black Chief Dexter Canton
item.banner.square_top_left.blue=Blue Chief Dexter Canton
item.banner.square_top_left.brown=Brown Chief Dexter Canton
item.banner.square_top_left.cyan=Cyan Chief Dexter Canton
item.banner.square_top_left.gray=Gray Chief Dexter Canton
item.banner.square_top_left.green=Green Chief Dexter Canton
item.banner.square_top_left.lightBlue=Light Blue Chief Dexter Canton
item.banner.square_top_left.lime=Lime Chief Dexter Canton
item.banner.square_top_left.magenta=Magenta Chief Dexter Canton
item.banner.square_top_left.orange=Orange Chief Dexter Canton
item.banner.square_top_left.pink=Pink Chief Dexter Canton
item.banner.square_top_left.purple=Purple Chief Dexter Canton
item.banner.square_top_left.red=Red Chief Dexter Canton
item.banner.square_top_left.silver=Light Gray Chief Dexter Canton
item.banner.square_top_left.white=White Chief Dexter Canton
item.banner.square_top_left.yellow=Yellow Chief Dexter Canton
item.banner.square_top_right.black=Black Chief Sinister Canton
item.banner.square_top_right.blue=Blue Chief Sinister Canton
item.banner.square_top_right.brown=Brown Chief Sinister Canton
item.banner.square_top_right.cyan=Cyan Chief Sinister Canton
item.banner.square_top_right.gray=Gray Chief Sinister Canton
item.banner.square_top_right.green=Green Chief Sinister Canton
item.banner.square_top_right.lightBlue=Light Blue Chief Sinister Canton
item.banner.square_top_right.lime=Lime Chief Sinister Canton
item.banner.square_top_right.magenta=Magenta Chief Sinister Canton
item.banner.square_top_right.orange=Orange Chief Sinister Canton
item.banner.square_top_right.pink=Pink Chief Sinister Canton
item.banner.square_top_right.purple=Purple Chief Sinister Canton
item.banner.square_top_right.red=Red Chief Sinister Canton
item.banner.square_top_right.silver=Light Gray Chief Sinister Canton
item.banner.square_top_right.white=White Chief Sinister Canton
item.banner.square_top_right.yellow=Yellow Chief Sinister Canton
item.banner.straight_cross.black=Black Cross
item.banner.straight_cross.blue=Blue Cross
item.banner.straight_cross.brown=Brown Cross
item.banner.straight_cross.cyan=Cyan Cross
item.banner.straight_cross.gray=Gray Cross
item.banner.straight_cross.green=Green Cross
item.banner.straight_cross.lightBlue=Light Blue Cross
item.banner.straight_cross.lime=Lime Cross
item.banner.straight_cross.magenta=Magenta Cross
item.banner.straight_cross.orange=Orange Cross
item.banner.straight_cross.pink=Pink Cross
item.banner.straight_cross.purple=Purple Cross
item.banner.straight_cross.red=Red Cross
item.banner.straight_cross.silver=Light Gray Cross
item.banner.straight_cross.white=White Cross
item.banner.straight_cross.yellow=Yellow Cross
item.banner.stripe_bottom.black=Black Base Fess
item.banner.stripe_bottom.blue=Blue Base Fess
item.banner.stripe_bottom.brown=Brown Base Fess
item.banner.stripe_bottom.cyan=Cyan Base Fess
item.banner.stripe_bottom.gray=Gray Base Fess
item.banner.stripe_bottom.green=Green Base Fess
item.banner.stripe_bottom.lightBlue=Light Blue Base Fess
item.banner.stripe_bottom.lime=Lime Base Fess
item.banner.stripe_bottom.magenta=Magenta Base Fess
item.banner.stripe_bottom.orange=Orange Base Fess
item.banner.stripe_bottom.pink=Pink Base Fess
item.banner.stripe_bottom.purple=Purple Base Fess
item.banner.stripe_bottom.red=Red Base Fess
item.banner.stripe_bottom.silver=Light Gray Base Fess
item.banner.stripe_bottom.white=White Base Fess
item.banner.stripe_bottom.yellow=Yellow Base Fess
item.banner.stripe_center.black=Black Pale
item.banner.stripe_center.blue=Blue Pale
item.banner.stripe_center.brown=Brown Pale
item.banner.stripe_center.cyan=Cyan Pale
item.banner.stripe_center.gray=Gray Pale
item.banner.stripe_center.green=Green Pale
item.banner.stripe_center.lightBlue=Light Blue Pale
item.banner.stripe_center.lime=Lime Pale
item.banner.stripe_center.magenta=Magenta Pale
item.banner.stripe_center.orange=Orange Pale
item.banner.stripe_center.pink=Pink Pale
item.banner.stripe_center.purple=Purple Pale
item.banner.stripe_center.red=Red Pale
item.banner.stripe_center.silver=Light Gray Pale
item.banner.stripe_center.white=White Pale
item.banner.stripe_center.yellow=Yellow Pale
item.banner.stripe_downleft.black=Black Bend Sinister
item.banner.stripe_downleft.blue=Blue Bend Sinister
item.banner.stripe_downleft.brown=Brown Bend Sinister
item.banner.stripe_downleft.cyan=Cyan Bend Sinister
item.banner.stripe_downleft.gray=Gray Bend Sinister
item.banner.stripe_downleft.green=Green Bend Sinister
item.banner.stripe_downleft.lightBlue=Light Blue Bend Sinister
item.banner.stripe_downleft.lime=Lime Bend Sinister
item.banner.stripe_downleft.magenta=Magenta Bend Sinister
item.banner.stripe_downleft.orange=Orange Bend Sinister
item.banner.stripe_downleft.pink=Pink Bend Sinister
item.banner.stripe_downleft.purple=Purple Bend Sinister
item.banner.stripe_downleft.red=Red Bend Sinister
item.banner.stripe_downleft.silver=Light Gray Bend Sinister
item.banner.stripe_downleft.white=White Bend Sinister
item.banner.stripe_downleft.yellow=Yellow Bend Sinister
item.banner.stripe_downright.black=Black Bend
item.banner.stripe_downright.blue=Blue Bend
item.banner.stripe_downright.brown=Brown Bend
item.banner.stripe_downright.cyan=Cyan Bend
item.banner.stripe_downright.gray=Gray Bend
item.banner.stripe_downright.green=Green Bend
item.banner.stripe_downright.lightBlue=Light Blue Bend
item.banner.stripe_downright.lime=Lime Bend
item.banner.stripe_downright.magenta=Magenta Bend
item.banner.stripe_downright.orange=Orange Bend
item.banner.stripe_downright.pink=Pink Bend
item.banner.stripe_downright.purple=Purple Bend
item.banner.stripe_downright.red=Red Bend
item.banner.stripe_downright.silver=Light Gray Bend
item.banner.stripe_downright.white=White Bend
item.banner.stripe_downright.yellow=Yellow Bend
item.banner.stripe_left.black=Black Pale Dexter
item.banner.stripe_left.blue=Blue Pale Dexter
item.banner.stripe_left.brown=Brown Pale Dexter
item.banner.stripe_left.cyan=Cyan Pale Dexter
item.banner.stripe_left.gray=Gray Pale Dexter
item.banner.stripe_left.green=Green Pale Dexter
item.banner.stripe_left.lightBlue=Light Blue Pale Dexter
item.banner.stripe_left.lime=Lime Pale Dexter
item.banner.stripe_left.magenta=Magenta Pale Dexter
item.banner.stripe_left.orange=Orange Pale Dexter
item.banner.stripe_left.pink=Pink Pale Dexter
item.banner.stripe_left.purple=Purple Pale Dexter
item.banner.stripe_left.red=Red Pale Dexter
item.banner.stripe_left.silver=Light Gray Pale Dexter
item.banner.stripe_left.white=White Pale Dexter
item.banner.stripe_left.yellow=Yellow Pale Dexter
item.banner.stripe_middle.black=Black Fess
item.banner.stripe_middle.blue=Blue Fess
item.banner.stripe_middle.brown=Brown Fess
item.banner.stripe_middle.cyan=Cyan Fess
item.banner.stripe_middle.gray=Gray Fess
item.banner.stripe_middle.green=Green Fess
item.banner.stripe_middle.lightBlue=Light Blue Fess
item.banner.stripe_middle.lime=Lime Fess
item.banner.stripe_middle.magenta=Magenta Fess
item.banner.stripe_middle.orange=Orange Fess
item.banner.stripe_middle.pink=Pink Fess
item.banner.stripe_middle.purple=Purple Fess
item.banner.stripe_middle.red=Red Fess
item.banner.stripe_middle.silver=Light Gray Fess
item.banner.stripe_middle.white=White Fess
item.banner.stripe_middle.yellow=Yellow Fess
item.banner.stripe_right.black=Black Pale Sinister
item.banner.stripe_right.blue=Blue Pale Sinister
item.banner.stripe_right.brown=Brown Pale Sinister
item.banner.stripe_right.cyan=Cyan Pale Sinister
item.banner.stripe_right.gray=Gray Pale Sinister
item.banner.stripe_right.green=Green Pale Sinister
item.banner.stripe_right.lightBlue=Light Blue Pale Sinister
item.banner.stripe_right.lime=Lime Pale Sinister
item.banner.stripe_right.magenta=Magenta Pale Sinister
item.banner.stripe_right.orange=Orange Pale Sinister
item.banner.stripe_right.pink=Pink Pale Sinister
item.banner.stripe_right.purple=Purple Pale Sinister
item.banner.stripe_right.red=Red Pale Sinister
item.banner.stripe_right.silver=Light Gray Pale Sinister
item.banner.stripe_right.white=White Pale Sinister
item.banner.stripe_right.yellow=Yellow Pale Sinister
item.banner.stripe_top.black=Black Chief Fess
item.banner.stripe_top.blue=Blue Chief Fess
item.banner.stripe_top.brown=Brown Chief Fess
item.banner.stripe_top.cyan=Cyan Chief Fess
item.banner.stripe_top.gray=Gray Chief Fess
item.banner.stripe_top.green=Green Chief Fess
item.banner.stripe_top.lightBlue=Light Blue Chief Fess
item.banner.stripe_top.lime=Lime Chief Fess
item.banner.stripe_top.magenta=Magenta Chief Fess
item.banner.stripe_top.orange=Orange Chief Fess
item.banner.stripe_top.pink=Pink Chief Fess
item.banner.stripe_top.purple=Purple Chief Fess
item.banner.stripe_top.red=Red Chief Fess
item.banner.stripe_top.silver=Light Gray Chief Fess
item.banner.stripe_top.white=White Chief Fess
item.banner.stripe_top.yellow=Yellow Chief Fess
item.banner.triangle_bottom.black=Black Chevron
item.banner.triangle_bottom.blue=Blue Chevron
item.banner.triangle_bottom.brown=Brown Chevron
item.banner.triangle_bottom.cyan=Cyan Chevron
item.banner.triangle_bottom.gray=Gray Chevron
item.banner.triangle_bottom.green=Green Chevron
item.banner.triangle_bottom.lightBlue=Light Blue Chevron
item.banner.triangle_bottom.lime=Lime Chevron
item.banner.triangle_bottom.magenta=Magenta Chevron
item.banner.triangle_bottom.orange=Orange Chevron
item.banner.triangle_bottom.pink=Pink Chevron
item.banner.triangle_bottom.purple=Purple Chevron
item.banner.triangle_bottom.red=Red Chevron
item.banner.triangle_bottom.silver=Light Gray Chevron
item.banner.triangle_bottom.white=White Chevron
item.banner.triangle_bottom.yellow=Yellow Chevron
item.banner.triangle_top.black=Black Inverted Chevron
item.banner.triangle_top.blue=Blue Inverted Chevron
item.banner.triangle_top.brown=Brown Inverted Chevron
item.banner.triangle_top.cyan=Cyan Inverted Chevron
item.banner.triangle_top.gray=Gray Inverted Chevron
item.banner.triangle_top.green=Green Inverted Chevron
item.banner.triangle_top.lightBlue=Light Blue Inverted Chevron
item.banner.triangle_top.lime=Lime Inverted Chevron
item.banner.triangle_top.magenta=Magenta Inverted Chevron
item.banner.triangle_top.orange=Orange Inverted Chevron
item.banner.triangle_top.pink=Pink Inverted Chevron
item.banner.triangle_top.purple=Purple Inverted Chevron
item.banner.triangle_top.red=Red Inverted Chevron
item.banner.triangle_top.silver=Light Gray Inverted Chevron
item.banner.triangle_top.white=White Inverted Chevron
item.banner.triangle_top.yellow=Yellow Inverted Chevron
item.banner.triangles_bottom.black=Black Base Indented
item.banner.triangles_bottom.blue=Blue Base Indented
item.banner.triangles_bottom.brown=Brown Base Indented
item.banner.triangles_bottom.cyan=Cyan Base Indented
item.banner.triangles_bottom.gray=Gray Base Indented
item.banner.triangles_bottom.green=Green Base Indented
item.banner.triangles_bottom.lightBlue=Light Blue Base Indented
item.banner.triangles_bottom.lime=Lime Base Indented
item.banner.triangles_bottom.magenta=Magenta Base Indented
item.banner.triangles_bottom.orange=Orange Base Indented
item.banner.triangles_bottom.pink=Pink Base Indented
item.banner.triangles_bottom.purple=Purple Base Indented
item.banner.triangles_bottom.red=Red Base Indented
item.banner.triangles_bottom.silver=Light Gray Base Indented
item.banner.triangles_bottom.white=White Base Indented
item.banner.triangles_bottom.yellow=Yellow Base Indented
item.banner.triangles_top.black=Black Chief Indented
item.banner.triangles_top.blue=Blue Chief Indented
item.banner.triangles_top.brown=Brown Chief Indented
item.banner.triangles_top.cyan=Cyan Chief Indented
item.banner.triangles_top.gray=Gray Chief Indented
item.banner.triangles_top.green=Green Chief Indented
item.banner.triangles_top.lightBlue=Light Blue Chief Indented
item.banner.triangles_top.lime=Lime Chief Indented
item.banner.triangles_top.magenta=Magenta Chief Indented
item.banner.triangles_top.orange=Orange Chief Indented
item.banner.triangles_top.pink=Pink Chief Indented
item.banner.triangles_top.purple=Purple Chief Indented
item.banner.triangles_top.red=Red Chief Indented
item.banner.triangles_top.silver=Light Gray Chief Indented
item.banner.triangles_top.white=White Chief Indented
item.banner.triangles_top.yellow=Yellow Chief Indented
item.banner.white.name=White Banner
item.banner.yellow.name=Yellow Banner
item.bed.black.name=Black Bed
item.bed.red.name=Red Bed
item.bed.green.name=Green Bed
item.bed.brown.name=Brown Bed
item.bed.blue.name=Blue Bed
item.bed.purple.name=Purple Bed
item.bed.cyan.name=Cyan Bed
item.bed.silver.name=Light Gray Bed
item.bed.gray.name=Gray Bed
item.bed.pink.name=Pink Bed
item.bed.lime.name=Lime Bed
item.bed.yellow.name=Yellow Bed
item.bed.lightBlue.name=Light Blue Bed
item.bed.magenta.name=Magenta Bed
item.bed.orange.name=Orange Bed
item.bed.white.name=White Bed
item.bell.name=Bell
item.steak.name=Steak
item.beef.name=Raw Beef
item.beetroot.name=Beetroot
item.beetroot_soup.name=Beetroot Soup
item.blaze_powder.name=Blaze Powder
item.blaze_rod.name=Blaze Rod
item.boat.oak.name=Oak Boat
item.boat.spruce.name=Spruce Boat
item.boat.birch.name=Birch Boat
item.boat.jungle.name=Jungle Boat
item.boat.acacia.name=Acacia Boat
item.boat.big_oak.name=Dark Oak Boat
item.bone.name=Bone
item.book.name=Book
item.chainmail_boots.name=Chain Boots
item.leather_boots.name=Leather Boots
item.diamond_boots.name=Diamond Boots
item.golden_boots.name=Golden Boots
item.iron_boots.name=Iron Boots
item.bow.name=Bow
item.bowl.name=Bowl
item.bread.name=Bread
item.brewing_stand.name=Brewing Stand
item.brick.name=Brick
item.bucket.name=Bucket
item.bucketLava.name=Lava Bucket
item.bucketWater.name=Water Bucket
item.bucketFish.name=Bucket of Cod
item.bucketSalmon.name=Bucket of Salmon
item.bucketTropical.name=Bucket of Tropical Fish
item.bucketPuffer.name=Bucket of Pufferfish
item.bucketCustomFish.name=Bucket of 
item.tropicalColorWhite.name=White
item.tropicalColorOrange.name=Orange
item.tropicalColorMagenta.name=Magenta
item.tropicalColorSky.name=Sky
item.tropicalColorYellow.name=Yellow
item.tropicalColorLime.name=Lime
item.tropicalColorRose.name=Rose
item.tropicalColorGray.name=Gray
item.tropicalColorSilver.name=Silver
item.tropicalColorTeal.name=Teal
item.tropicalColorPlum.name=Plum
item.tropicalColorBlue.name=Blue
item.tropicalColorBrown.name=Brown
item.tropicalColorGreen.name=Green
item.tropicalColorRed.name=Red
item.tropicalBodyKobSingle.name=%1$s Kob
item.tropicalBodySunstreakSingle.name=%1$s SunStreak
item.tropicalBodySnooperSingle.name=%1$s Snooper
item.tropicalBodyDasherSingle.name=%1$s Dasher
item.tropicalBodyBrinelySingle.name=%1$s Brinely
item.tropicalBodySpottySingle.name=%1$s Spotty
item.tropicalBodyFlopperSingle.name=%1$s Flopper
item.tropicalBodyStripeySingle.name=%1$s Stripey
item.tropicalBodyGlitterSingle.name=%1$s Glitter
item.tropicalBodyBlockfishSingle.name=%1$s Blockfish
item.tropicalBodyBettySingle.name=%1$s Betty
item.tropicalBodyClayfishSingle.name=%1$s Clayfish
item.tropicalBodyKobMulti.name=%1$s-%2$s Kob
item.tropicalBodySunstreakMulti.name=%1$s-%2$s SunStreak
item.tropicalBodySnooperMulti.name=%1$s-%2$s Snooper
item.tropicalBodyDasherMulti.name=%1$s-%2$s Dasher
item.tropicalBodyBrinelyMulti.name=%1$s-%2$s Brinely
item.tropicalBodySpottyMulti.name=%1$s-%2$s Spotty
item.tropicalBodyFlopperMulti.name=%1$s-%2$s Flopper
item.tropicalBodyStripeyMulti.name=%1$s-%2$s Stripey
item.tropicalBodyGlitterMulti.name=%1$s-%2$s Glitter
item.tropicalBodyBlockfishMulti.name=%1$s-%2$s Blockfish
item.tropicalBodyBettyMulti.name=%1$s-%2$s Betty
item.tropicalBodyClayfishMulti.name=%1$s-%2$s Clayfish
item.tropicalSchoolAnemone.name=Anemone
item.tropicalSchoolBlackTang.name=Black Tang
item.tropicalSchoolBlueDory.name=Blue Dory
item.tropicalSchoolButterflyFish.name=Butterfly Fish
item.tropicalSchoolCichlid.name=Chichlid
item.tropicalSchoolClownfish.name=Clownfish
item.tropicalSchoolCottonCandyBetta.name=Cotton Candy Betta
item.tropicalSchoolDottyback.name=Dottyback
item.tropicalSchoolEmperorRedSnapper.name=Emperor Red Snapper
item.tropicalSchoolGoatfish.name=Goatfish
item.tropicalSchoolMoorishIdol.name=Moorish Idol
item.tropicalSchoolOrnateButterfly.name=Ornate Butterfly
item.tropicalSchoolParrotfish.name=Parrotfish
item.tropicalSchoolQueenAngelFish.name=Queen Angel Fish
item.tropicalSchoolRedCichlid.name=Red Cichlid
item.tropicalSchoolRedLippedBlenny.name=Red Lipped Blenny
item.tropicalSchoolRedSnapper.name=Red Snapper
item.tropicalSchoolThreadfin.name=Threadfin
item.tropicalSchoolTomatoClown.name=Tomato Clown
item.tropicalSchoolTriggerfish.name=Triggerfish
item.tropicalSchoolYellowTang.name=Yellow Tang
item.tropicalSchoolYellowtailParrot.name=Yellowtail Parrot
item.cake.name=Cake
item.camera.name=Camera
item.canBreak=Can break:
item.canPlace=Can be placed on:
item.golden_carrot.name=Golden Carrot
item.carrotOnAStick.name=Carrot on a Stick
item.carrot.name=Carrot
item.cauldron.name=Cauldron
item.charcoal.name=Charcoal
item.chainmail_chestplate.name=Chain Chestplate
item.leather_chestplate.name=Leather Tunic
item.diamond_chestplate.name=Diamond Chestplate
item.golden_chestplate.name=Golden Chestplate
item.iron_chestplate.name=Iron Chestplate
item.chorus_fruit.name=Chorus Fruit
item.chorus_fruit_popped.name=Popped Chorus Fruit
item.cooked_beef.name=Cooked Beef
item.cooked_chicken.name=Cooked Chicken
item.cooked_porkchop.name=Cooked Porkchop
item.chicken.name=Raw Chicken
item.clay_ball.name=Clay
item.clock.name=Clock
item.coal.name=Coal
item.comparator.name=Redstone Comparator
item.compass.name=Compass
item.cookie.name=Cookie
item.crossbow.name=Crossbow
item.diamond.name=Diamond
item.repeater.name=Redstone Repeater
item.acacia_door.name=Acacia Door
item.birch_door.name=Birch Door
item.dark_oak_door.name=Dark Oak Door
item.iron_door.name=Iron Door
item.jungle_door.name=Jungle Door
item.wooden_door.name=Oak Door
item.spruce_door.name=Spruce Door
item.dragon_breath.name=Dragon's Breath
item.dyed=Dyed
item.dye.black.name=Ink Sac
item.dye.black_new.name=Black Dye
item.dye.blue.name=Lapis Lazuli
item.dye.blue_new.name=Blue Dye
item.dye.brown.name=Cocoa Beans
item.dye.brown_new.name=Brown Dye
item.dye.cyan.name=Cyan Dye
item.dye.gray.name=Gray Dye
item.dye.green.name=Green Dye
item.dye.lightBlue.name=Light Blue Dye
item.dye.lime.name=Lime Dye
item.dye.magenta.name=Magenta Dye
item.dye.orange.name=Orange Dye
item.dye.pink.name=Pink Dye
item.dye.purple.name=Purple Dye
item.dye.red.name=Red Dye
item.dye.silver.name=Light Gray Dye
item.dye.white.name=Bone Meal
item.dye.white_new.name=White Dye
item.dye.yellow.name=Yellow Dye
item.egg.name=Egg
item.elytra.name=Elytra
item.emerald.name=Emerald
item.emptyMap.name=Empty Map
item.emptyLocatorMap.name=Empty Locator Map
item.emptyPotion.name=Water Bottle
item.enchanted_book.name=Enchanted Book
item.end_crystal.name=End Crystal
tile.end_rod.name=End Rod
item.ender_eye.name=Eye of Ender
item.ender_pearl.name=Ender Pearl
item.experience_bottle.name=Bottle o' Enchanting
item.feather.name=Feather
item.fermented_spider_eye.name=Fermented Spider Eye
item.fireball.name=Fire Charge
item.fireworks.flight=Flight Duration:
item.fireworks.name=Firework Rocket
item.fireworksCharge.black=Black
item.fireworksCharge.blue=Blue
item.fireworksCharge.brown=Brown
item.fireworksCharge.customColor=Custom
item.fireworksCharge.cyan=Cyan
item.fireworksCharge.fadeTo=Fade to
item.fireworksCharge.flicker=Twinkle
item.fireworksCharge.gray=Gray
item.fireworksCharge.green=Green
item.fireworksCharge.lightBlue=Light Blue
item.fireworksCharge.lime=Lime
item.fireworksCharge.magenta=Magenta
item.fireworksCharge.name=Firework Star
item.fireworksCharge.orange=Orange
item.fireworksCharge.pink=Pink
item.fireworksCharge.purple=Purple
item.fireworksCharge.red=Red
item.fireworksCharge.silver=Light Gray
item.fireworksCharge.trail=Trail
item.fireworksCharge.type=Unknown Shape
item.fireworksCharge.type.0=Small Ball
item.fireworksCharge.type.1=Large Ball
item.fireworksCharge.type.2=Star-shaped
item.fireworksCharge.type.3=Creeper-shaped
item.fireworksCharge.type.4=Burst
item.fireworksCharge.white=White
item.fireworksCharge.yellow=Yellow
item.clownfish.name=Tropical Fish
item.cooked_fish.name=Cooked Cod
item.fish.name=Raw Cod
item.pufferfish.name=Pufferfish
item.cooked_salmon.name=Cooked Salmon
item.salmon.name=Raw Salmon
item.fishing_rod.name=Fishing Rod
item.flint.name=Flint
item.flint_and_steel.name=Flint and Steel
item.flower_pot.name=Flower Pot
item.frame.name=Item Frame
item.ghast_tear.name=Ghast Tear
item.glass_bottle.name=Glass Bottle
item.gold_nugget.name=Gold Nugget
item.iron_nugget.name=Iron Nugget
item.diamond_axe.name=Diamond Axe
item.golden_axe.name=Golden Axe
item.iron_axe.name=Iron Axe
item.stone_axe.name=Stone Axe
item.wooden_axe.name=Wooden Axe
item.chainmail_helmet.name=Chain Helmet
item.leather_helmet.name=Leather Cap
item.diamond_helmet.name=Diamond Helmet
item.golden_helmet.name=Golden Helmet
item.iron_helmet.name=Iron Helmet
item.diamond_hoe.name=Diamond Hoe
item.golden_hoe.name=Golden Hoe
item.iron_hoe.name=Iron Hoe
item.stone_hoe.name=Stone Hoe
item.wooden_hoe.name=Wooden Hoe
item.honey_bottle.name=Honey Bottle
item.honeycomb.name=Honeycomb
item.horsearmordiamond.name=Diamond Horse Armor
item.horsearmorgold.name=Gold Horse Armor
item.horsearmoriron.name=Iron Horse Armor
item.horsearmorleather.name=Leather Horse Armor
item.gold_ingot.name=Gold Ingot
item.iron_ingot.name=Iron Ingot
item.lead.name=Lead
item.leather.name=Leather
item.leaves.name=Leaves
item.chainmail_leggings.name=Chain Leggings
item.leather_leggings.name=Leather Pants
item.diamond_leggings.name=Diamond Leggings
item.golden_leggings.name=Golden Leggings
item.iron_leggings.name=Iron Leggings
item.nautilus_shell.name=Nautilus Shell
item.heart_of_the_sea.name=Heart of the Sea
item.magma_cream.name=Magma Cream
item.map.name=Map
item.map.exploration.mansion.name=Woodland Explorer Map
item.map.exploration.monument.name=Ocean Explorer Map
item.map.exploration.treasure.name=Treasure Map
item.melon.name=Melon
item.milk.name=Milk
item.minecart.name=Minecart
item.chest_minecart.name=Minecart with Chest
item.command_block_minecart.name=Minecart with Command Block
item.minecartFurnace.name=Minecart with Furnace
item.hopper_minecart.name=Minecart with Hopper
item.tnt_minecart.name=Minecart with TNT
item.spawn_egg.entity.agent.name=Spawn Agent
item.spawn_egg.entity.bee.name=Spawn Bee
item.spawn_egg.entity.cat.name=Spawn Cat
item.spawn_egg.entity.chicken.name=Spawn Chicken
item.spawn_egg.entity.cow.name=Spawn Cow
item.spawn_egg.entity.cod.name=Spawn Cod
item.spawn_egg.entity.pufferfish.name=Spawn Pufferfish
item.spawn_egg.entity.salmon.name=Spawn Salmon
item.spawn_egg.entity.tropicalfish.name=Spawn Tropical Fish
item.spawn_egg.entity.pig.name=Spawn Pig
item.spawn_egg.entity.sheep.name=Spawn Sheep
item.spawn_egg.entity.npc.name=Spawn NPC
item.spawn_egg.entity.wolf.name=Spawn Wolf
item.spawn_egg.entity.villager.name=Spawn Villager
item.spawn_egg.entity.villager_v2.name=Spawn Villager
item.spawn_egg.entity.vindicator.name=Spawn Vindicator
item.spawn_egg.entity.mooshroom.name=Spawn Mooshroom
item.spawn_egg.entity.squid.name=Spawn Squid
item.spawn_egg.entity.rabbit.name=Spawn Rabbit
item.spawn_egg.entity.bat.name=Spawn Bat
item.spawn_egg.entity.ravager.name=Spawn Ravager
item.spawn_egg.entity.iron_golem.name=Spawn Iron Golem
item.spawn_egg.entity.snow_golem.name=Spawn Snow Golem
item.spawn_egg.entity.ocelot.name=Spawn Ocelot
item.spawn_egg.entity.parrot.name=Spawn Parrot
item.spawn_egg.entity.horse.name=Spawn Horse
item.spawn_egg.entity.llama.name=Spawn Llama
item.spawn_egg.entity.polar_bear.name=Spawn Polar Bear
item.spawn_egg.entity.donkey.name=Spawn Donkey
item.spawn_egg.entity.mule.name=Spawn Mule
item.spawn_egg.entity.skeleton_horse.name=Spawn Skeleton Horse
item.spawn_egg.entity.zombie_horse.name=Spawn Zombie Horse
item.spawn_egg.entity.zombie.name=Spawn Zombie
item.spawn_egg.entity.drowned.name=Spawn Drowned
item.spawn_egg.entity.creeper.name=Spawn Creeper
item.spawn_egg.entity.skeleton.name=Spawn Skeleton
item.spawn_egg.entity.spider.name=Spawn Spider
item.spawn_egg.entity.zombie_pigman.name=Spawn Zombie Pigman
item.spawn_egg.entity.slime.name=Spawn Slime
item.spawn_egg.entity.enderman.name=Spawn Enderman
item.spawn_egg.entity.silverfish.name=Spawn Silverfish
item.spawn_egg.entity.cave_spider.name=Spawn Cave Spider
item.spawn_egg.entity.ghast.name=Spawn Ghast
item.spawn_egg.entity.magma_cube.name=Spawn Magma Cube
item.spawn_egg.entity.blaze.name=Spawn Blaze
item.spawn_egg.entity.zombie_villager.name=Spawn Zombie Villager
item.spawn_egg.entity.zombie_villager_v2.name=Spawn Zombie Villager
item.spawn_egg.entity.witch.name=Spawn Witch
item.spawn_egg.entity.stray.name=Spawn Stray
item.spawn_egg.entity.husk.name=Spawn Husk
item.spawn_egg.entity.wither_skeleton.name=Spawn Wither Skeleton
item.spawn_egg.entity.guardian.name=Spawn Guardian
item.spawn_egg.entity.elder_guardian.name=Spawn Elder Guardian
item.spawn_egg.entity.shulker.name=Spawn Shulker
item.spawn_egg.entity.endermite.name=Spawn Endermite
item.spawn_egg.entity.evocation_illager.name=Spawn Evoker
item.spawn_egg.entity.vex.name=Spawn Vex
item.spawn_egg.entity.turtle.name=Spawn Sea Turtle
item.spawn_egg.entity.dolphin.name=Spawn Dolphin
item.spawn_egg.entity.phantom.name=Spawn Phantom
item.spawn_egg.entity.panda.name=Spawn Panda
item.spawn_egg.entity.pillager.name=Spawn Pillager
item.spawn_egg.entity.fox.name=Spawn Fox
item.spawn_egg.entity.unknown.name=Spawn
item.spawn_egg.entity.wandering_trader.name=Spawn Wandering Trader
item.trident.name=Trident
item.mushroom_stew.name=Mushroom Stew
item.muttonCooked.name=Cooked Mutton
item.muttonRaw.name=Raw Mutton
item.name_tag.name=Name Tag
item.netherbrick.name=Nether Brick
item.quartz.name=Nether Quartz
item.nether_wart.name=Nether Wart
item.netherStar.name=Nether Star
item.painting.name=Painting
item.paper.name=Paper
item.diamond_pickaxe.name=Diamond Pickaxe
item.golden_pickaxe.name=Golden Pickaxe
item.iron_pickaxe.name=Iron Pickaxe
item.stone_pickaxe.name=Stone Pickaxe
item.wooden_pickaxe.name=Wooden Pickaxe
item.porkchop_cooked.name=Cooked Porkchop
item.porkchop.name=Raw Porkchop
item.portfolio.name=Portfolio
item.potato.name=Potato
item.baked_potato.name=Baked Potato
item.poisonous_potato.name=Poisonous Potato
item.potion.name=Potion
item.prismarine_crystals.name=Prismarine Crystals
item.prismarine_shard.name=Prismarine Shard
item.pumpkin_pie.name=Pumpkin Pie
item.cooked_rabbit.name=Cooked Rabbit
item.rabbit_foot.name=Rabbit's Foot
item.rabbit_hide.name=Rabbit Hide
item.rabbit.name=Raw Rabbit
item.rabbit_stew.name=Rabbit Stew
item.record_11.desc=C418 - 11
item.record_13.desc=C418 - 13
item.record_blocks.desc=C418 - blocks
item.record_cat.desc=C418 - cat
item.record_chirp.desc=C418 - chirp
item.record_far.desc=C418 - far
item.record_mall.desc=C418 - mall
item.record_mellohi.desc=C418 - mellohi
item.record.name=Music Disc
item.record_stal.desc=C418 - stal
item.record_strad.desc=C418 - strad
item.record_wait.desc=C418 - wait
item.record_ward.desc=C418 - ward
item.redstone.name=Redstone
item.reeds.name=Sugar Canes
item.kelp.name=Kelp
item.dried_kelp.name=Dried Kelp
item.rotten_flesh.name=Rotten Flesh
item.ruby.name=Ruby
item.saddle.name=Saddle
item.wheat_seeds.name=Seeds
item.beetroot_seeds.name=Beetroot Seeds
item.melon_seeds.name=Melon Seeds
item.pumpkin_seeds.name=Pumpkin Seeds
item.shears.name=Shears
item.diamond_shovel.name=Diamond Shovel
item.golden_shovel.name=Golden Shovel
item.iron_shovel.name=Iron Shovel
item.stone_shovel.name=Stone Shovel
item.wooden_shovel.name=Wooden Shovel
item.sign.name=Oak Sign
item.spruce_sign.name=Spruce Sign
item.birch_sign.name=Birch Sign
item.jungle_sign.name=Jungle Sign
item.acacia_sign.name=Acacia Sign
item.darkoak_sign.name=Dark Oak Sign
item.skull.char.name=Head
item.skull.creeper.name=Creeper Head
item.skull.dragon.name=Dragon Head
item.skull.player.name=%s's Head
item.skull.skeleton.name=Skeleton Skull
item.skull.wither.name=Wither Skeleton Skull
item.skull.zombie.name=Zombie Head
item.slime_ball.name=Slimeball
item.snowball.name=Snowball
item.speckled_melon.name=Glistering Melon
item.spider_eye.name=Spider Eye
item.stick.name=Stick
item.string.name=String
item.sugar.name=Sugar
item.gunpowder.name=Gunpowder
item.diamond_sword.name=Diamond Sword
item.golden_sword.name=Golden Sword
item.iron_sword.name=Iron Sword
item.stone_sword.name=Stone Sword
item.wooden_sword.name=Wooden Sword
item.unbreakable=Unbreakable
item.wheat.name=Wheat
item.writable_book.name=Book & Quill
item.written_book.name=Written Book
item.glowstone_dust.name=Glowstone Dust
item.shield.name=Shield
item.shulker_shell.name=Shulker Shell
item.totem.name=Totem of Undying
item.turtle_helmet.name=Turtle Shell
item.turtle_shell_piece.name=Scute
item.phantom_membrane.name=Phantom Membrane
item.sweet_berries.name=Sweet Berries
item.suspicious_stew.name=Suspicious Stew
item.banner_pattern.bricks=Field Masoned
item.banner_pattern.creeper=Creeper Charge
item.banner_pattern.flower=Flower Charge
item.banner_pattern.name=Banner Pattern
item.banner_pattern.skull=Skull Charge
item.banner_pattern.thing=Thing
item.banner_pattern.vines=Bordure Indented

itemGroup.search=Search Items
itemGroup.name.planks=Planks
itemGroup.name.walls=Walls
itemGroup.name.fence=Fences
itemGroup.name.fenceGate=Fence Gates
itemGroup.name.stairs=Stairs
itemGroup.name.door=Doors
itemGroup.name.glass=Glass
itemGroup.name.glassPane=Glass Panes
itemGroup.name.permission=Permission Blocks
itemGroup.name.slab=Slabs
itemGroup.name.stoneBrick=Decorative Stone
itemGroup.name.sandstone=Sandstone
itemGroup.name.wool=Wool
itemGroup.name.woolCarpet=Wool Carpet
itemGroup.name.concretePowder=Concrete Powder
itemGroup.name.concrete=Concrete
itemGroup.name.stainedClay=Terracotta
itemGroup.name.glazedTerracotta=Glazed Terracottas
itemGroup.name.dye=Dyes
itemGroup.name.ore=Ores
itemGroup.name.stone=Stone
itemGroup.name.log=Logs
itemGroup.name.leaves=Leaves
itemGroup.name.sapling=Saplings
itemGroup.name.seed=Seeds
itemGroup.name.crop=Crops
itemGroup.name.grass=Ground Cover
itemGroup.name.flower=Flowers
itemGroup.name.rawFood=Raw Food
itemGroup.name.cookedFood=Cooked Food
itemGroup.name.miscFood=Miscellaneous Foods
itemGroup.name.mushroom=Mushrooms
itemGroup.name.monsterStoneEgg=Infested Stone
itemGroup.name.mobEgg=Mob Eggs
itemGroup.name.helmet=Helmets
itemGroup.name.chestplate=Chestplates
itemGroup.name.leggings=Leggings
itemGroup.name.boots=Boots
itemGroup.name.horseArmor=Horse Armor
itemGroup.name.sword=Swords
itemGroup.name.axe=Axes
itemGroup.name.pickaxe=Pickaxes
itemGroup.name.shovel=Shovels
itemGroup.name.hoe=Hoes
itemGroup.name.arrow=Arrows
itemGroup.name.potion=Potions
itemGroup.name.splashPotion=Splash Potions
itemGroup.name.lingeringPotion=Lingering Potions
itemGroup.name.bed=Beds
itemGroup.name.chalkboard=Chalkboards
itemGroup.name.anvil=Anvils
itemGroup.name.chest=Chests
itemGroup.name.shulkerBox=Shulker Boxes
itemGroup.name.record=Records
itemGroup.name.skull=Mob Skulls
itemGroup.name.boat=Boats
itemGroup.name.rail=Rails
itemGroup.name.minecart=Minecarts
itemGroup.name.pressurePlate=Pressure Plates
itemGroup.name.trapdoor=Trapdoors
itemGroup.name.enchantedBook=Enchanted Books
itemGroup.name.banner=Banners
itemGroup.name.firework=Fireworks
itemGroup.name.fireworkStars=Firework Charges
itemGroup.name.coral=Coral Blocks
itemGroup.name.coral_decorations=Coral Decorations
itemGroup.name.buttons=Buttons
itemGroup.name.sign=Signs
itemGroup.name.wood=Woods
itemGroup.name.banner_pattern=Banner Patterns

##EDU Joincode
joincode.entry_popup.icon_button=%1 join code button		# Example: Apple join code button
joincode.entry_popup.icon_entry=%3 join code entry %1 of %2	# Example: Apple join code entry 2 of 5
joincode.entry_popup.title=Enter Join Code
joincode.connecting.title=Join World
joincode.connecting.lower_text=Searching for world...
joincode.error.title=Join Error
joincode.error.message.no_match=The join code you entered does not match any worlds that are currently available.
joincode.error.message.no_response=There was a problem connecting to the join code service. Please try again.
joincode.error.message.not_available=Join codes are not available right now. Ask your host for their IP Address to join their game.
joincode.error.message.service_error=Something went wrong. Please try again.
joincode.found.title=Join World
joincode.found.message=Is this the world you were trying to join?
joincode.found.host_name=Hosted By: %1
joincode.icon_text.null=Empty
joincode.icon_text.1=Book & Quill
joincode.icon_text.2=Balloon
joincode.icon_text.3=Rail
joincode.icon_text.4=Alex
joincode.icon_text.5=Cookie
joincode.icon_text.6=Fish
joincode.icon_text.7=Agent
joincode.icon_text.8=Cake
joincode.icon_text.9=Pickaxe
joincode.icon_text.10=Water Bucket
joincode.icon_text.11=Steve
joincode.icon_text.12=Apple
joincode.icon_text.13=Carrot
joincode.icon_text.14=Panda
joincode.icon_text.15=Sign
joincode.icon_text.16=Potion
joincode.icon_text.17=Map
joincode.icon_text.18=Llama
joincode.ip_entry.button_tts=More Options
joincode.ip_entry.address.tooltip=To find the IP Address, ask the host to pause their game. The IP Address and Port number can be found on the multiplayer tab of the pause screen.
joincode.ip_entry.port.tooltip=To find the Port number, ask the host to pause their game. The IP Address and Port number can be found on the multiplayer tab of the pause screen.
joincode.generate_new.button.text=Generate New Join Code
joincode.generate_new.tooltip.text=Generating a new code will not interrupt your multiplayer session.
joincode.button.share_link.text=Share a Link
joincode.generating_new=Loading...
joincode.tooltip.errortext=There was an error generating your join code.
joincode.tooltip.account.error=There was a problem verifying your account. Please restart Minecraft: Education Edition and try again.
joincode.tooltip.infotext=Give the join code to your classmate so they can join your world.
joincode.button.stop_hosting.text=STOP HOSTING
joincode.confirmation.stop_hosting.message=If you stop hosting the world, the multiplayer session will end for all players. Are you sure you want to do this?
joincode.button.start_hosting.text=START HOSTING
joincode.confirmation.start_hosting.message=If you start hosting, we will create a code for you to give to other players so they can join your world. Would you like to start hosting?
joincode.service.unreachable=The discovery service can not be reached
joincode.button.generate_code.text=Generate New Join Code
joincode.confirmation.generate_code.message=Making a new join code will not stop your multiplayer game. The new code will be used to invite additional players.
joincode.service.unavailable=Join codes are not available right now. Try refreshing or use your IP address instead.

##EDU Pause Menu
edu.pause.ipaddress=IP ADDRESS
edu.pause.port=PORT

##EDU Pause Menu
edu.pause.ipaddress=IP ADDRESS
edu.pause.port=PORT

key.attack=Attack/Destroy
key.back=Walk Backwards
key.categories.gameplay=Gameplay
key.categories.inventory=Inventory
key.categories.misc=Miscellaneous
key.categories.movement=Movement
key.categories.multiplayer=Multiplayer
key.categories.stream=Streaming
key.categories.ui=Game Interface
key.codeBuilder=Code Builder
key.chat=Open Chat
key.command=Open Command
key.cycleItemLeft=Cycle Item Left
key.cycleItemRight=Cycle Item Right
key.scoreboard=Show Scoreboard
key.drop=Drop Item
key.forward=Walk Forwards
key.fullscreen=Toggle Fullscreen
key.hotbar.1=Hotbar Slot 1
key.hotbar.2=Hotbar Slot 2
key.hotbar.3=Hotbar Slot 3
key.hotbar.4=Hotbar Slot 4
key.hotbar.5=Hotbar Slot 5
key.hotbar.6=Hotbar Slot 6
key.hotbar.7=Hotbar Slot 7
key.hotbar.8=Hotbar Slot 8
key.hotbar.9=Hotbar Slot 9
key.cyclefixedinventory=Cycle Fixed Inventory
key.immersivereader=Immersive Reader
key.interactwithtoast=Open Notification
key.inventory=Inventory
key.jump=Jump/Fly Up
key.left=Strafe Left
key.lookCenter=Look Center
key.lookDown=Look Down
key.lookDownLeft=Look Down Left
key.lookDownRight=Look Down Right
key.lookDownSlight=Look Down Slight
key.lookDownSmooth=Look Down Smooth
key.lookLeft=Look Left
key.lookLeftSmooth=Look Left Smooth
key.lookRight=Look Right
key.lookRightSmooth=Look Right Smooth
key.lookUp=Look Up
key.lookUpLeft=Look Up Left
key.lookUpRight=Look Up Right
key.lookUpSlight=Look Up Slight
key.lookUpSmooth=Look Up Smooth
key.menuTabLeft=Menu Tab Left
key.menuTabRight=Menu Tab Right
key.mouseButton=Button %1$s
key.pickItem=Pick Block
key.playerlist=List Players
key.right=Strafe Right
key.screenshot=Take Screenshot
key.smoothCamera=Toggle Cinematic Camera
key.sneak=Sneak/Fly Down
key.spectatorOutlines=Highlight Players (Spectators)
key.sprint=Sprint
key.streamCommercial=Show Stream Commercials
key.streamPauseUnpause=Pause/Unpause Stream
key.streamStartStop=Start/Stop Stream
key.streamToggleMic=Push To Talk/Mute
key.togglePerspective=Toggle Perspective
key.use=Use Item/Place Block
key.flyDownSlow=Fly Down Slow
key.flyUpSlow=Fly Up Slow
key.mobEffects=Mob Effects
key.moveBack=Move Back
key.moveForward=Move Forward
key.moveLeft=Move Left
key.moveRight=Move Right
key.pause=Pause
key.toggleLivingroom=Toggle Perspective

keyboard.keyName.backspace=BACK
keyboard.keyName.tab=TAB
keyboard.keyName.return=RETURN
keyboard.keyName.pause=PAUSE
keyboard.keyName.lshift=SHIFT
keyboard.keyName.control=CONTROL
keyboard.keyName.capsLock=CAPITAL
keyboard.keyName.escape=ESCAPE
keyboard.keyName.space=SPACE
keyboard.keyName.pgDown=PAGE DOWN
keyboard.keyName.pgUp=PAGE UP
keyboard.keyName.end=END
keyboard.keyName.home=HOME
keyboard.keyName.left=LEFT
keyboard.keyName.up=UP
keyboard.keyName.right=RIGHT
keyboard.keyName.down=DOWN
keyboard.keyName.insert=INSERT
keyboard.keyName.delete=DELETE
keyboard.keyName.0=0
keyboard.keyName.1=1
keyboard.keyName.2=2
keyboard.keyName.3=3
keyboard.keyName.4=4
keyboard.keyName.5=5
keyboard.keyName.6=6
keyboard.keyName.7=7
keyboard.keyName.8=8
keyboard.keyName.9=9
keyboard.keyName.a=A
keyboard.keyName.b=B
keyboard.keyName.c=C
keyboard.keyName.d=D
keyboard.keyName.e=E
keyboard.keyName.f=F
keyboard.keyName.g=G
keyboard.keyName.h=H
keyboard.keyName.i=I
keyboard.keyName.j=J
keyboard.keyName.k=K
keyboard.keyName.l=L
keyboard.keyName.m=M
keyboard.keyName.n=N
keyboard.keyName.o=O
keyboard.keyName.p=P
keyboard.keyName.q=Q
keyboard.keyName.r=R
keyboard.keyName.s=S
keyboard.keyName.t=T
keyboard.keyName.u=U
keyboard.keyName.v=V
keyboard.keyName.w=W
keyboard.keyName.x=X
keyboard.keyName.y=Y
keyboard.keyName.z=Z
keyboard.keyName.f1=F1
keyboard.keyName.f2=F2
keyboard.keyName.f3=F3
keyboard.keyName.f4=F4
keyboard.keyName.f5=F5
keyboard.keyName.f6=F6
keyboard.keyName.f7=F7
keyboard.keyName.f8=F8
keyboard.keyName.f9=F9
keyboard.keyName.f10=F10
keyboard.keyName.f11=F11
keyboard.keyName.f12=F12
keyboard.keyName.f13=F13
keyboard.keyName.numpad0=NUMPAD0
keyboard.keyName.numpad1=NUMPAD1
keyboard.keyName.numpad2=NUMPAD2
keyboard.keyName.numpad3=NUMPAD3
keyboard.keyName.numpad4=NUMPAD4
keyboard.keyName.numpad5=NUMPAD5
keyboard.keyName.numpad6=NUMPAD6
keyboard.keyName.numpad7=NUMPAD7
keyboard.keyName.numpad8=NUMPAD8
keyboard.keyName.numpad9=NUMPAD9
keyboard.keyName.scroll=SCROLL
keyboard.keyName.equals=EQUALS
keyboard.keyName.add=ADD
keyboard.keyName.minus=MINUS
keyboard.keyName.subtract=SUBTRACT
keyboard.keyName.multiply=MULTIPLY
keyboard.keyName.divide=DIVIDE
keyboard.keyName.decimal=DECIMAL
keyboard.keyName.grave=GRAVE
keyboard.keyName.numLock=NUMLOCK
keyboard.keyName.slash=SLASH
keyboard.keyName.semicolon=SEMICOLON
keyboard.keyName.apostrophe=APOSTROPHE
keyboard.keyName.comma=COMMA
keyboard.keyName.period=PERIOD
keyboard.keyName.backslash=BACKSLASH
keyboard.keyName.lbracket=LBRACKET
keyboard.keyName.rbracket=RBRACKET

keyboard.keyName.backspace.short=BACK
keyboard.keyName.tab.short=TAB
keyboard.keyName.return.short=RETURN
keyboard.keyName.pause.short=PAUSE
keyboard.keyName.lshift.short=SHIFT
keyboard.keyName.control.short=CTRL
keyboard.keyName.capsLock.short=CAPS
keyboard.keyName.escape.short=ESC
keyboard.keyName.space.short=SPACE
keyboard.keyName.pgDown.short=PgDn
keyboard.keyName.pgUp.short=PgUp
keyboard.keyName.end.short=END
keyboard.keyName.home.short=HOME
keyboard.keyName.left.short=LEFT
keyboard.keyName.up.short=UP
keyboard.keyName.right.short=RIGHT
keyboard.keyName.down.short=DOWN
keyboard.keyName.insert.short=INS
keyboard.keyName.delete.short=DEL
keyboard.keyName.numpad0.short=N0
keyboard.keyName.numpad1.short=N1
keyboard.keyName.numpad2.short=N2
keyboard.keyName.numpad3.short=N3
keyboard.keyName.numpad4.short=N4
keyboard.keyName.numpad5.short=N5
keyboard.keyName.numpad6.short=N6
keyboard.keyName.numpad7.short=N7
keyboard.keyName.numpad8.short=N8
keyboard.keyName.numpad9.short=N9
keyboard.keyName.scroll.short=SCROLL
keyboard.keyName.equals.short==
keyboard.keyName.add.short=N+
keyboard.keyName.minus.short=-
keyboard.keyName.subtract.short=N-
keyboard.keyName.multiply.short=N*
keyboard.keyName.divide.short=N/
keyboard.keyName.decimal.short=N.
keyboard.keyName.grave.short=`
keyboard.keyName.numLock.short=NUMLOCK
keyboard.keyName.slash.short=/
keyboard.keyName.semicolon.short=;
keyboard.keyName.apostrophe.short='
keyboard.keyName.comma.short=,
keyboard.keyName.period.short=.
keyboard.keyName.backslash.short=\
keyboard.keyName.lbracket.short=[
keyboard.keyName.rbracket.short=]

lanServer.otherPlayers=Settings for Other Players
lanServer.scanning=Scanning for games on your local network
lanServer.start=Start LAN World
lanServer.title=LAN World
lanServer.restart=This server has restarted!

library.prompt.inGameLink=This will save and quit your current game and any multiplayer connections, are you sure you want to quit?
library.fetchingItem=Connecting to library...
library.fetchingItem.failed=Sorry, we weren't able to find that item in the Library.
library.welcome.1=Welcome to the Minecraft: Education Edition Library!
library.welcome.2=The Library is a new feature we've created to make it easy for teachers and students to find and import educational content directly in the game. In this release, you'll find a list of some of our most popular worlds to import and use, along with a link to the full world library on our website.
library.welcome.3=We're working on adding support for more types of content and functionality in the coming months. Please let us know what you think by clicking the feedback button.
library.item.sharepopup.title=Share Link
library.item.sharepopup.body=Share a link to this world.
library.item.share.toast.title=Link copied to your clipboard.

licensed_content.goBack=Go Back
licensed_content.viewLicensedContent=To view licensed content, please visit https://minecraft.net/licensed-content/ in any web browser.

livingroom.hint.tap_touchpad_for_immersive=Tap the touchpad to toggle Perspective
livingroom.hint.tap_view_for_immersive=Press the F5 key to toggle Perspective
livingroom.hint.tap_view_for_immersive_gamepad=Press D-Pad Up to toggle Perspective
livingroom.hint.tap_view_for_immersive_oculustouch=Press Y to toggle Perspective
livingroom.hint.tap_view_for_immersive_windowsmr=Press Left Thumbstick to toggle Perspective

map.toolTip.displayMarkers=Display Markers
map.toolTip.scaling=Scaling at 1:%s
map.toolTip.level=Level %s/%s
map.toolTip.unkown=Unknown Map
map.toolTip.locked=Locked
map.position.agent=Agent Pos: %s, %s, %s
map.position=Position: %s, %s, %s

mcoServer.title=Minecraft Online World

menu.achievements=Achievements
menu.convertingLevel=Expanding world
menu.copyright=©Mojang AB
menu.disconnect=Disconnect
menu.educatorResources=Educator Resources
menu.editions=Editions
menu.beta=Beta!!!
menu.game=Game menu
menu.generatingLevel=Generating world
menu.generatingTerrain=Building terrain
menu.howToPlay=How to Play
menu.howToPlay.caps=HOW TO PLAY
menu.host=Host
menu.howToPlay.generalMessage=Minecraft encyclopedia for new and experienced players.
menu.howToPlay.access=Press :_gamepad_face_button_down: to open How to Play!
menu.howToPlay.access.noicon=Press [A] to open How to Play!
menu.loadingLevel=Loading world
menu.multiplayer=Multiplayer
menu.online=Minecraft Realms
menu.options=Options
menu.settings=Settings
menu.settings.caps=SETTINGS
menu.serverStore=%s Store
menu.serverGenericName=Server
menu.play=Play
menu.playdemo=Play Demo World
menu.playOnRealms=Play on Realm
menu.quickplay=Quick Play
menu.quit=Save & Quit
menu.quit.edu=SAVE & EXIT
menu.quiz=Take Quiz
menu.resetdemo=Reset Demo World
menu.resourcepacks=Resource Packs
menu.globalpacks=Global Resources
menu.storageManagement=Storage
menu.behaviors=Behavior Packs
menu.worldtemplates=World Templates
menu.quiz=Take Quiz
menu.respawning=Respawning
menu.returnToGame=Resume Game
menu.returnToMenu=Save and Quit to Title
menu.shareToLan=Open to LAN
menu.simulating=Simulating the world for a bit
menu.singleplayer=Singleplayer
menu.store=Marketplace
menu.skins=Skins
menu.start=Start
menu.switchingLevel=Switching worlds
menu.makingBackup=Making backup...
menu.saving=Saving...

merchant.deprecated=Trade something else to unlock!


mount.onboard=Press %1$s to dismount

multiplayer.connect=Connect
multiplayer.downloadingStats=Downloading statistics & achievements...
multiplayer.downloadingTerrain=Downloading terrain
multiplayer.info1=Minecraft Multiplayer is currently not finished, but there
multiplayer.info2=is some buggy early testing going on.
multiplayer.ipinfo=Enter the IP of a server to connect to it:
multiplayer.packErrors=At least one of your resource or behavior packs failed to load.
multiplayer.packErrors.realms=At least one of your resource or behavior packs failed to load.  Try downloading this world from your Realm settings to see more details on the error.
multiplayer.player.inventory.recovered=Inventory recovered and placed in chests near you.
multiplayer.player.inventory.failed=Inventory recovered.  Find a safe place, and we'll place a chest near you the next time you join the world.
multiplayer.player.joined=%s joined the game
multiplayer.player.joined.renamed=%s (formerly known as %s) joined the game
multiplayer.player.joined.realms=%s joined the Realm
multiplayer.player.joined.realms.renamed=%s (formerly known as %s) joined the Realm
multiplayer.player.left=%s left the game
multiplayer.player.left.realms=%s left the Realm
multiplayer.player.changeToPersona=%s edited the character appearance.
multiplayer.player.changeToSkin=%s has changed to %s skin.
multiplayer.stopSleeping=Leave Bed
multiplayer.texturePrompt.line1=This server recommends the use of a custom resource pack.
multiplayer.texturePrompt.line2=Would you like to download and install it automagically?
multiplayer.title=Play Multiplayer
multiplayer.inBedOpenChat=Open Chat
multiplayer.joincode.refreshed=The game's join code has been updated.

npcscreen.action.buttonmode=Button Mode
npcscreen.action.buttonname=Button Name
npcscreen.action.command.placeholder=Type Command Here...
npcscreen.action.command.title=Command
npcscreen.action.url.placeholder=Type URL Here...
npcscreen.action.url.title=URL
npcscreen.addcommand=Add Command
npcscreen.addtext=Type Dialog Here...
npcscreen.addurl=Add URL
npcscreen.advancedsettings=Advanced Settings
npcscreen.advancedtitle=Advanced NPC Settings
npcscreen.appearance=Appearance
npcscreen.basictitle=Non Player Character
npcscreen.dialog=Dialog
npcscreen.editdialog=Edit Dialog
npcscreen.help.command.a=Click this button to add a command in the NPC dialog box.
npcscreen.help.command.b=Multiple commands can be added at once.
npcscreen.help.url.a=Click this button to add a URL hyperlink in the NPC dialog box.
npcscreen.help.url.b=Link opens in the player's default browser.
npcscreen.learnmore=Learn More
npcscreen.name=Name
npcscreen.npc=NPC
npcscreen.requiresop=OP Required

offer.category.skinpack=Skin Packs
offer.category.resourcepack=Texture Packs
offer.category.mashup=Mash-up Packs
offer.category.worldtemplate=Worlds
offer.category.editorschoice=Editor's Choice
offer.category.allByCreator=All by %s

offer.navigationTab.skins=Skins
offer.navigationTab.textures=Textures
offer.navigationTab.worlds=Worlds
offer.navigationTab.mashups=Mash-ups

options.adjustBrightness=Adjust your brightness until you can only see two creeper faces.
options.brightness.notVisible=Not Visible
options.brightness.barelyVisible=Barely Visible
options.brightness.easilyVisible=Easily Visible

options.adUseSingleSignOn=Enable Single Sign On
options.advancedButton=Advanced Video Settings...
options.showAdvancedVideoSettings=Show Advanced Video Settings
options.advancedOpengl=Advanced OpenGL
options.advancedVideoTitle=Advanced Video Settings
options.anaglyph=3D Anaglyph
options.termsAndConditions=Terms and Conditions
options.attribution=Attribution
options.3DRendering=3D Rendering
options.animatetextures=Animated water
options.ao=Smooth Lighting
options.ao.max=Maximum
options.ao.min=Minimum
options.ao.off=OFF
options.autojump=Auto Jump
options.blockAlternatives=Alternate Blocks
options.buildid.format=Build: %1$s
options.broadcast=Broadcast
options.broadcast.mixerCreatePrompt=Want to broadcast your game? You need to have a compatible broadcasting app installed. We recommend Mixer Create as it allows you to broadcast with sub-second latency, co-stream with friends and lets your viewers interact in fun ways. Certain features like Chat and Viewer Count only work with Mixer Create.
options.broadcast.startPrompt=Ready to broadcast your game?
options.broadcast.mixerCreate.get=Get Mixer Create
options.broadcast.mixerCreate.launch=Launch Mixer Create
options.broadcast.start=Start Broadcast
options.broadcast.permissionError=You do not have permission to use this feature. Review and change your privacy settings at aka.ms/accountsettings.
options.broadcast.xblError.title=Sign in to Broadcast
options.broadcast.xblError=We need you to sign in before you can start broadcasting your game.
options.protocolversion.format=Protocol Version: %1%s
options.worldconversion.version=World Converter: %s
options.builddate.format=Build Date: %s
options.buttonSize=Button Size
options.category.addons=Add-Ons
options.category.audio=Sound
options.category.game=Game
options.category.graphics=Graphics
options.category.input=Controls
options.category.server=Server
options.change=Change
options.changeGamertag=Change Gamertag
options.chat.color=Colors
options.chat.height.focused=Focused Height
options.chat.height.unfocused=Unfocused Height
options.chat.links=Web Links
options.chat.links.prompt=Prompt on Links
options.chat.opacity=Opacity
options.chat.scale=Scale
options.chat.title=Chat Settings...
options.chat.visibility=Chat
options.chat.visibility.full=Shown
options.chat.visibility.hidden=Hidden
options.chat.visibility.system=Commands Only
options.chat.width=Width
options.codeBuilder=Code Builder
options.content_log_file=Enable Content Log File
options.content_log_gui=Enable Content Log GUI
options.controller=Controller
options.controllerLayout=Controller Layout
options.controllerSettings=Controller Settings
options.controls=Controls...
options.credits=Credits
options.crouch=Crouch
options.customizeTitle=Customize World Settings
options.destroyvibration=Destroy Block (vibrate)
options.debug=Debug
options.debugTitle=Developer Options
options.dev_game_tip=Gameplay Tips
options.dev_ad_show_debug_panel=Show EDU Sign In Debug Panel
options.dev_ad_token_refresh_threshold=EDU Sign In Token Refresh Threshold Seconds
options.dev_assertions_debug_break=Assertions break in the debugger
options.dev_assertions_show_dialog=Assertions show a modal dialog
options.dev_eduDemo=Edu Demo (Requires re-sign-in)
options.dev_enableDebugUI=Enable Debug UI
options.dev_createRealmWithoutPurchase=Create realm without purchase
options.dev_flushOrphanedRealmsPurchases=Flush orphaned Realms purchases
options.dev_enableMixerInteractive=Enable Mixer interactive commands
options.dev_storeOfferQueryRequiresXbl=Require XBL for Store Offers
options.dev_clearPurchaseInventory=Reset Coins & Entitlements
options.dev_renderBoundingBox=Render bounding box
options.dev_renderPaths=Render paths
options.dev_renderGoalState=Render goal state
options.dev_resetClientId=Reset client ID
options.dev_showChunkMap=Show chunk map
options.dev_showServerChunkMap=Chunk map shows server chunks
options.dev_disableRenderTerrain=Disable Terrain Drawing
options.dev_disableRenderEntities=Disable Entities Drawing
options.dev_disableRenderBlockEntities=Disable Block Entities Drawing
options.dev_disableRenderParticles=Disable Particles Drawing
options.dev_disableRenderSky=Disable Sky Drawing
options.dev_disableRenderWeather=Disable Weather Drawing
options.dev_disableRenderHud=Disable Hud Drawing
options.dev_disableRenderItemInHand=Disable Item-in-Hand Drawing
options.dev_serverInstanceThread=Server Instance Thread
options.dev_newCuller=Use new culler
options.dev_showBuildInfo=Show build info
options.dev_showDevConsoleButton=Show dev console button
options.dev_realmsPermissionsEnabledButton=Show realms permissions
options.dev_show_tcui_replacement=Show TCUI replacement
options.dev_enableProfiler=Enable profiler
options.dev_newParticleSystem=Enable New Particle System
options.dev_toggle_default_font_overrides=Enable Default Font Overrides
options.dev_enableDebugHudOverlay=Enable debug HUD
options.dev_enableDebugHudOverlay.off=Off
options.dev_enableDebugHudOverlay.basic=Basic
options.dev_enableDebugHudOverlay.renderchunks=Render chunks
options.dev_enableDebugHudOverlay.workerthreads=Worker threads
options.dev_enableDebugHudOverlay.debugtextures=Debug textures
options.dev_enableDebugHudOverlay.profiler=Profiler
options.dev_enableDebugHudOverlay.imagememory=Image Memory
options.dev_enableDebugHudOverlay.debugBlocks=Debug Blocks
options.dev_enableDebugHudOverlay.client_network=Client Network
options.dev_enableDebugHudOverlay.server_network=Server Network
options.dev_multithreadedRendering=Enable multithreaded rendering
options.dev_file_watcher=Enable File Watcher
options.dev_achievementsAlwaysEnabled=Achievements always enabled
options.dev_useLocalServer=Use local server
options.dev_useIPv6Only=Use IPv6 Only
options.dev_attachPosRenderLevel=Render Attach Positions
options.dev_render_attach_pos.none=Off
options.dev_render_attach_pos.head_pos=Head Position
options.dev_render_attach_pos.eyes_pos=Eyes Position
options.dev_render_attach_pos.breath_pos=Breathing Position
options.dev_render_attach_pos.body_pos=Body Position
options.dev_render_attach_pos.feet_pos=Feet Position
options.dev_render_attach_pos.all=All
options.dev_disable_client_blob_cache=Disable Client Blob Cache
options.dev_force_client_blob_cache=Force Client Blob Cache On For Local Games
options.dev_connectionQuality=Connection Quality
options.dev_connection_quality.no_limit=Unlimited
options.dev_connection_quality.phone_4g=4G
options.dev_connection_quality.phone_3g=3G
options.dev_connection_quality.slow=Slow
options.dev_connection_quality.very_slow=Very Slow
options.dev_use_fps_independent_turning=Use FPS-independent Turning
options.dev_use_fast_chunk_culling=Use Fast Chunk Culling
options.dev_useRetailXboxSandbox=Use Retail MSA Sandbox
options.dev_displayMarketplaceDocumentId=Show Marketplace Document Id
options.dev_addCoins=Add 500 Coins
options.dev_clearPurchaseInventory=Reset Coins & Entitlements
options.dev_realmsEnvironment=Realms Environment
options.dev_realms_environment.production=Production
options.dev_realms_environment.staging=Staging
options.dev_realms_environment.local=Local
options.dev_realms_environment.dev=Dev
options.dev_realmsEndpoint=Realms Endpoint
options.dev_realmsEndpointPayment=Realms Endpoint Payment
options.dev_realmsRelyingParty=Realms Relying Party
options.dev_realmsRelyingPartyPayment=Realms Relying Party Payment
options.dev_experimentalTreatment=Override Experimental Treatments
options.dev_sandboxRetail=Xforge Sandbox: Retail
options.dev_sandboxDev=Xforge Sandbox: Dev
options.dev_displayTreatmentsPanel=Display Treatments
options.dev_currentTreatmentsTitle=Current Treatments
options.dev_unusedTreatmentsTitle=Unused Treatments
options.dev_addTreatmentId=Add Treatment Id
options.dev_addLabel=Add
options.dev_resetTreatmentsToDefault=Reset To Default
options.dev_clearTreatments=Clear Treatments
options.dev_reset_day_one_experience=Reset Day One Experience
options.dev_useZippedInPackagePacks=Use Zipped In-package Packs
options.dev_importPacksAsZip=Import Packs as Zip
options.dev_folders_portSettingsFolder=Export Settings Folder
options.dev_useOverrideDate=Use Override Date
options.dev_displayOverrideDatetime=Display Datetime
options.dev_loadOverrideDate=Load Override Date At Launch
options.dev.timeZoneType=Override Editor Timezone Type
options.dev.timeZoneType.local=Edit in Local Time
options.dev.timeZoneType.utc=Edit in UTC
options.dev_overrideDateYear=Year
options.dev_overrideDateMonth=Month
options.dev_overrideDateDay=Day
options.dev_overrideDateHour=Hour
options.dev_overrideDateMinute=Minute
options.dev_overrideDayLength=Override Day Length In Minutes (Min: 1)
options.dev_overrideTimeScale=Scale Speed That Time Passes (Min: 1, Default: 1)
options.dev_updateOverrideDate=Update Override Date
options.dev_overrideVersionMajor=Major
options.dev_overrideVersionMinor=Minor
options.dev_overrideVersionPatch=Patch
options.dev_updateVersionOverride=Update Client Version Override
options.dev_resetOverrideDate=Reset Override Date
options.dev_clearStoreCache=Clear Marketplace Cache
options.dev_clearAllCache=Clear All Cache
options.dev_connection_quality=Network conditioner (simulate bad connections)
options.dev_connection_off=Off - In-memory connection enabled for local play
options.dev_connection_nolimit=Full network stack enabled - No limits
options.dev_connection_4g=4G - 15Mbps, 100ms latency, 1% packet loss
options.dev_connection_3g=3G - 1.5Mbps, 200ms latency, 2% packet loss
options.dev_connection_slow=Slow - 400Kbps, 300ms latency, 3% packet loss
options.dev_connection_veryslow=Very slow - 200Kbps, 400ms latency, 4% packet loss
options.dev_deleteAllPersonas=Delete All Personas
options.dev_deleteLegacyPersona=Delete Legacy Persona Slot
options.dev_identity_environment=New Identity and Online Infrastructure Environment (requires restart)
options.dev_identity_environment.dev=Development
options.dev_identity_environment.test=Test
options.dev_identity_environment.prod=Production
options.dev.windowsStore=Select Windows Store (Restart Required)
options.dev.windowsStore.auto=Automatic
options.dev.windowsStore.v6=Legacy Windows Store - V6
options.dev.windowsStore.v8=OneStore - V8
options.dev.stores=Currently Active Stores:
options.difficulty=Difficulty
options.difficulty.easy=Easy
options.difficulty.hard=Hard
options.difficulty.hardcore=Hardcore
options.difficulty.normal=Normal
options.difficulty.peaceful=Peaceful
options.dpadscale=D-Pad Size
options.enableChatTextToSpeech=Enable Text To Speech For Chat
options.enableAutoPlatformTextToSpeech=Enable Text To Speech with Device Settings
options.enableUITextToSpeech=Enable UI Screen Reader
options.enableOpenChatMessage=Enable Open Chat Message
options.entityShadows=Entity Shadows
options.editSettings=Edit Settings
options.fancyskies=Beautiful Skies
options.farWarning1=A 64 bit Java installation is recommended
options.farWarning2=for 'Far' render distance (you have 32 bit)
options.fboEnable=Enable FBOs
options.forceUnicodeFont=Force Unicode Font
options.fov=FOV
options.fov.toggle=FOV Can Be Altered By Gameplay
options.licensed_content=Licensed Content
options.livingRoomFOV=Living Room FOV
options.default.format=%s
options.percent.format=%s%%
options.fov.format=%s°
options.fov.max=Quake Pro
options.fov.min=Normal
options.hudOpacity=HUD Opacity
options.hudOpacity.max=Normal
options.hudOpacity.min=Hidden
options.framerateLimit=Max Framerate
options.framerateLimit.max=Unlimited
options.fullKeyboardGameplay=Full Keyboard Gameplay
options.fullKeyboardLayout=Full Keyboard Layout
options.fullscreen=Fullscreen
options.gamepadcursorsensitivity=Controller Cursor Sensitivity
options.gamertag=Gamertag:
options.gamma=Brightness
options.gamma.max=Bright
options.gamma.min=Moody
options.worldLightBrightness=World Light Brightness
options.goToFeedbackWebsite=Go to Feedback Website
options.graphics=Fancy Graphics
options.transparentleaves=Fancy Leaves
options.bubbleparticles=Fancy Bubbles
options.smooth_lighting=Smooth Lighting
options.graphics.fancy=Fancy
options.graphics.fast=Fast
options.renderingProfile=Graphics
options.renderingProfile.sad=Basic
options.renderingProfile.fancy=Fancy
options.renderingProfile.superfancy=Super Fancy
options.group.audio=Sound
options.group.feedback=Feedback
options.group.game=Game
options.group.graphics=Graphics
options.group.graphics.experimental=Experimental
options.group.input=Controls
options.group.multiplayer=Multiplayer
options.group.realms=Invited to Realms Alpha?
options.guiScale=D-pad size
options.guiScale.auto=Auto
options.guiScale.large=Large
options.guiScale.maximum=Maximum
options.guiScale.medium=Medium
options.guiScale.minimum=Minimum
options.guiScale.normal=Normal
options.guiScale.optionName=GUI Scale Modifier
options.guiScale.small=Small
options.hidden=Hidden
options.hidehud=Hide HUD
options.hidehand=Hide Hand
options.classic_box_selection=Outline Selection
options.vr_classic_box_selection=Outline Selection
options.hidegamepadcursor=Hide Controller Cursor
options.hidegui=Hide GUI
options.hideKeyboardTooltips=Hide Keyboard and Mouse Hints
options.hidetooltips=Hide Controller Hints
options.splitscreenHUDsize=Splitscreen HUD Size
options.ingamePlayerNames=In-game Player Names
options.splitscreenIngamePlayerNames=Splitscreen In-game Player Names
options.interfaceOpacity=HUD Opacity
options.splitscreenInterfaceOpacity=Splitscreen HUD Opacity
options.hidepaperdoll=Hide Paper Doll
options.showautosaveicon=Show Autosave Icon
options.hold=Hold
options.invertMouse=Invert Mouse
options.invertYAxis=Invert Y-Axis
options.keyboardLayout=Keyboard Layout
options.keyboardAndMouse=Keyboard & Mouse
options.keyboardAndMouseSettings=Keyboard and Mouse Settings
options.language=Language
options.languageGuiScaleCompatibility.title=Incompatible Language and GUI Scale
options.languageGuiScaleCompatibility.message.short=Our font for the language you have selected is not readable at a GUI scale that small.
options.languageGuiScaleCompatibility.message.long=Our font for the language you have selected is not readable at a GUI scale that small.  Do you want to increase your GUI scale?
options.languageGuiScaleCompatibility.ok=Increase GUI Scale
options.languageGuiScaleCompatibility.cancel=Go Back
options.languageWarning=Language translations may not be 100% accurate
options.lefthanded=Lefty
options.hotbarOnlyTouch=Touch only affects hotbar
options.manage=Manage
options.manageAccount=Manage Account
options.mipmapLevels=Mipmap Levels
options.modelPart.cape=Cape
options.modelPart.hat=Hat
options.modelPart.jacket=Jacket
options.modelPart.left_pants_leg=Left Pants Leg
options.modelPart.left_sleeve=Left Sleeve
options.modelPart.right_pants_leg=Right Pants Leg
options.modelPart.right_sleeve=Right Sleeve
options.multiplayer.title=Multiplayer Settings...
options.music=Music
options.name=Name
options.defaultName=Steve
options.off=OFF
options.on=ON
options.particles=Particles
options.particles.all=All
options.particles.decreased=Decreased
options.particles.minimal=Minimal
options.patchNotes=Patch Notes
options.performanceButton=Video Performance Settings...
options.performanceVideoTitle=Video Performance Settings
options.postButton=Post-Processing Settings...
options.postProcessEnable=Enable Post-Processing
options.postVideoTitle=Post-Processing Settings
options.profile=Profile
options.profileTitle=User Profile and Settings
options.accountError=Account Error
options.accountErrorButton=View Errors
options.qualityButton=Video Quality Settings...
options.qualityVideoTitle=Video Quality Settings
options.reducedDebugInfo=Reduced Debug Info
options.renderClouds=Clouds
options.renderDistance=Render Distance
options.renderDistanceFormat=%s chunks
options.resetSettings=Reset to Default
options.resetSettings.popUp=Do you really want to reset the settings?
options.maxFramerate=Max Framerate (Experimental)
options.maxFramerateFormat=%s FPS
options.particleRenderDistance=Particle Render Distance
options.perf_turtle=Performance Turtle
options.msaa=Anti-Aliasing
options.texelAA=Texel Anti-Aliasing
options.renderDistance.far=Far
options.renderDistance.normal=Normal
options.renderDistance.short=Short
options.renderDistance.tiny=Tiny
options.resourcepacks=Resource Packs
options.safeZone=Safe Area
options.safeZoneX=Horizontal Safe Area
options.safeZoneY=Vertical Safe Area
options.safeZone.title=Change Screen Safe Area
options.safeZone.description=Adjust the sliders until the four corners fit the edge of your screen.
options.saturation=Saturation
options.screenAnimations=Screen Animations
options.screenPositionX=Horizontal Screen Position
options.screenPositionY=Vertical Screen Position
options.sensitivity=Sensitivity
options.sensitivity.max=HYPERSPEED!!!
options.sensitivity.min=*yawn*
options.multiplayergame=Multiplayer Game
options.servervisible=Visible to LAN Players
options.ShowComfortSelectScreen=Show Comfort Select Screen
options.sliderLabelFormat=%s: %s
options.smoothRotationSpeed=Smooth Rotation Speed
options.xboxliveBroadcast.inviteOnly=Invite Only
options.xboxliveBroadcast.friendsOnly=Friends Only
options.xboxliveBroadcast.friendsOfFriends=Friends of Friends
options.xboxliveBroadcastSettings=Microsoft Account Settings
options.xboxlivevisible=Visible to Xbox Live Players
options.xboxLiveAccountSettings=Microsoft Account Settings
options.xboxLiveSignedIn=Signed In with Microsoft Account
options.xboxLiveSignedOut=Signed Out of Microsoft Account
options.xboxLive.privacyControl=Manage who can send you invites
options.skinCustomisation=Skin Customization...
options.skinCustomisation.title=Skin Customization
options.skin.change=Change Skin
options.snooper=Allow Snooper
options.snooper.desc=We want to collect information about your machine to help improve Minecraft by knowing what we can support and where the biggest problems are. All of this information is completely anonymous and viewable below. We promise we won't do anything bad with this data, but if you want to opt out then feel free to toggle it off!
options.snooper.title=Machine Specs Collection
options.snooper.view=Snooper Settings...
options.sound=Sound Volume
options.sounds=Audio
options.sounds.title=Audio Settings
options.accessibility=Accessibility
options.accessibility.title=Accessibility Settings
options.splitscreen=Split Screen
options.splitscreen.horizontal=Horizontal Split Screen
options.splitscreen.vertical=Vertical Split Screen
options.stickyMining=Sticky Mining
options.stream=Broadcast Settings...
options.stream.bytesPerPixel=Quality
options.stream.changes=You may need to restart your stream for these changes to take place.
options.stream.chat.enabled=Enable
options.stream.chat.enabled.always=Always
options.stream.chat.enabled.never=Never
options.stream.chat.enabled.streaming=Whilst Streaming
options.stream.chat.title=Twitch Chat Settings
options.stream.chat.userFilter=User Filter
options.stream.chat.userFilter.all=All Viewers
options.stream.chat.userFilter.mods=Moderators
options.stream.chat.userFilter.subs=Subscribers
options.stream.compression=Compression
options.stream.compression.high=High
options.stream.compression.low=Low
options.stream.compression.medium=Medium
options.stream.estimation=Estimated resolution: %dx%d
options.stream.fps=Framerate
options.stream.ingest.reset=Reset Preference
options.stream.ingest.title=Twitch Broadcast Servers
options.stream.ingestSelection=Broadcast Server List
options.stream.kbps=Bandwidth
options.stream.mic_toggle.mute=Mute
options.stream.mic_toggle.talk=Talk
options.stream.micToggleBehavior=Push To
options.stream.micVolumne=Mic Volume
options.stream.sendMetadata=Send Metadata
options.stream.systemVolume=System Volume
options.stream.title=Twitch Broadcast Settings
options.thirdperson=Camera Perspective
options.thirdperson.firstperson=First Person
options.thirdperson.thirdpersonback=Third Person Back
options.thirdperson.thirdpersonfront=Third Person Front
options.title=Options
options.toggle=Toggle
options.renderclouds=Render Clouds
options.toggleCrouch=Toggle Crouch
options.touch=Touch
options.touchSettings=Touch Settings
options.touchscreen=Touchscreen Mode
options.uiprofile=UI Profile
options.uiprofile.classic=Classic
options.uiprofile.pocket=Pocket
options.usetouchpad=Split Controls
options.viewSubscriptions=Subscriptions
options.viewSubscriptions.button.info=Info
options.viewSubscriptions.button.manage=Manage
options.viewSubscriptions.renew=Renews every 30 days
options.viewSubscriptions.daysRemaining=%d days remaining
options.viewSubscriptions.realmsPlus.header=Realms Plus Subscription
options.viewSubscriptions.realmsPlus.detail=Over 50+ pieces of content and your own Realm server
options.viewSubscriptions.realms.header=Additional Subscriptions
options.viewSubscriptions.realms.detail=Access to your own Realm server with up to %d concurrent players
options.viewSubscriptions.loadingSubscriptions=Loading your subscriptions..
options.viewSubscriptions.loadingSubscriptionsFailed=Failed loading subscriptions
options.viewSubscriptions.purchasedPlatformDiffers=This was bought in the %s you must use that device to manage it.
options.viewSubscriptions.noActiveSubscriptions=You Have No Active Subscriptions
options.viewSubscriptions.signIn=Sign in
options.viewSubscriptions.twoPlayers=Access to your own Realm server with up to 2 concurrent players
options.viewSubscriptions.buyAnAdditionalRealm=Buy An Additional Realm
options.viewSubscriptions.realmsPlusSubscriptionForRealm=Realms plus subscription for realm %s.
options.viewSubscriptions.additionalSubscriptionForRealm=Additional subscription for realm %s.
options.viewSubscriptions.personalRealmServer=Your own Realm server to add infinite members and play online with up to 2 friends
options.viewSubscriptions.twoPlayers=2 Player Realm Server
options.viewSubscriptions.startedInStore=It was started in store: %s
options.viewSubscriptions.boughtOnAnotherDevice=Bought on another device
options.swapJumpAndSneak=Swap Jump and Sneak
options.swapGamepadAB=A/B Button Swap
options.swapGamepadXY=X/Y Button Swap
options.usetouchscreen=Play with Touch
options.vbo=Use VBOs
options.video=Video
options.videoTitle=Video Settings
options.viewBobbing=View Bobbing
options.visible=Shown
options.vsync=Use VSync
options.vsync.off=No Vertical Sync
options.vsync.on=Vertical Sync
options.vsync.adaptive=Adaptive Vertical Sync
options.websocketEncryption=Require Encrypted Websockets
options.websocketEncryptionWarningLabel=Only disable this option if you are actively connecting to a known and safe application.
options.filelocation.title=File Storage Location
options.filelocation.external=External
options.filelocation.appdata=Application
options.atmosphericsEnable=Atmospherics
options.edgeHighlightEnable=Edge Highlight
options.bloomEnable=Bloom
options.terrainShadowsEnable=Terrain Shadows
options.superFancyWaterEnable=Super Fancy Water

options.autoUpdateEnabled=Auto Update Unlocked Packs
options.autoUpdateMode=Auto Update Unlocked Packs
options.autoUpdateMode.off=Off
options.autoUpdateMode.on.withWifiOnly=On with Wi-Fi Only
options.autoUpdateMode.on.withCellular=On with Wi-Fi or Cellular Data
options.allowCellularData=Use Cellular Data
options.cellularDataWarningLabel=Playing over cellular networks may incur additional charges with your carrier.
options.turnOffAchievements=Turn Off Achievements?
options.turnOffAchievements.message=Achievements are only available in worlds set to survival mode with cheats off.  If you continue, no one will earn achievements while playing in this world ever again even if you switch back before playing.
options.achievementsDisabled=Achievements cannot be earned in this world.
options.achievementsDisabled.onLoad=If you start playing with these settings, achievements will no longer be earnable in this world.
options.achievementsDisabled.notSignedIn=Achievements can be earned in this world, but you must sign in to a Microsoft Account to earn them.
options.turnOffCrossPlatformMultiplayer=Turn Off Cross-Platform Multiplayer?
options.turnOffCrossPlatformMultiplayer.message=The content that you are trying to use is not allowed in cross-platform multiplayer games.  If you continue you will not be able to play cross-platform multiplayer games.
options.conflictingPacks=Conflicting Packs
options.conflictingPacks.message.onStack=A pack already on the stack can't be applied with other packs. %s
options.conflictingPacks.message.offStack=The pack you are trying to apply can't be applied with other packs. %s
options.conflictingPacks.message.offStackWithBehavior=Continuing will remove all current packs and then add the pack you are trying to apply. This will remove all behavior packs from the world which can break the world and cause you to lose what you created.
options.conflictingPacks.continue=Continuing will remove all current packs and then add the pack you are trying to apply.
options.crossPlatformMultiplayerDisabled=Content active in this world cannot be used in cross-platform multiplayer.
options.multiplayerDisabled=Content active in this world cannot be used in multiplayer.
options.skinsCrossPlatformMultiplayerDisabled=The skin you're using cannot be used in cross-platform multiplayer.
options.skinsMultiplayerDisabled=The skin you're using cannot be used in multiplayer.
options.content.noRealms=Edit World?
options.content.noRealms.message=This world uses a resource pack or template that cannot be used in cross-platform multiplayer.
options.activateExperimentalGameplay=Activate Experimental Gameplay?
options.activateExperimentalGameplay.message=Enables beta features. Experimental gameplay may break your world. If you continue we will make a copy of your world starting with [EX] with this feature turned on.
options.activateExperimentalGameplayCreate.message=Enables beta features. Experimental gameplay may not be stable.
options.activateFancyBubbles=Activate Fancy Bubble Columns?
options.activateFancyBubblesCreate.message=Enables Fancy Bubble Columns. Fancy Bubble Columns may lower performance on some devices.
options.unlockTemplateWorldOptions=Discard the Creator's Settings?
options.unlockTemplateWorldOptions.message=The experience that the creator of this template wants you to have might be broken if you unlock these settings.  You might not be able to return to that intended experience if you continue.
options.unlockTemplateWorldOptions.initiate=Unlock Template World Options
options.unlockTemplateWorldOptions.ok=Unlock All Settings
options.unlockTemplateWorldOptions.cancel=Keep Creator Settings
options.unlockTemplateWorldOptions.warning=Template World Options are locked to the values set by this template's Creator. Unlock to change them.
options.unlockTemplateWorldOptions.packWarning=Unlock Template World Options from Game Settings to change the packs for this world.
options.unlockTemplateWorldOptions.permissionsWarning=Unlock Template World Options from Game Settings to be able to change permissions.
options.continue=Continue
options.edit=Edit
options.enableEducation=Enable Education Edition?
options.enableEducation.message=Enables Education Edition chemistry features. Education gameplay may break your world. If you continue we will make a copy of your world starting with [EDU].
options.enableEducationCreate.message=Enables Education Edition chemistry features. This cannot be disabled after your world is created. Please note these features are best experienced on desktop devices with moderate to high memory.
options.goBack=Go Back
options.managePrivacy=To manage privacy settings, please visit https://account.xbox.com/Settings in any web browser.
options.unlink_msa.button=Unlink Microsoft Account
options.unlink_msa.confirm.title=Unlink Microsoft Account?
options.unlink_msa.confirm.warning=WARNING: You will no longer be able to store progression or purchases made on your "PlayStation 4" System on your account %s after unlinking.
options.unlink_msa.confirm.checkbox1=I will no longer be able to access any in-game Store content when I play on other platforms.
options.unlink_msa.confirm.checkbox2=I will no longer be able to play cross-platform games with my friends on other platforms.
options.unlink_msa.confirm.checkbox3=I will no longer be able to access Realms, including Realms subscriptions that are currently active.
options.unlink_msa.confirm.checkbox4=I understand the above, and I would like to continue with unlinking.
options.unlink_msa.confirm.button=Unlink
options.unlink_msa.progress.title=Unlinking
options.unlink_msa.progress.body=Unlinking your accounts...
options.unlink_msa.success.title=Unlink Successful
options.unlink_msa.success.body=Your accounts have been unlinked.
options.unlink_msa.failure.title=Something Went Wrong
options.unlink_msa.failure.body=Your accounts were not able to be unlinked. Maybe check your internet connection?

patchNotes.continue=Continue
patchNotes.unlock=Unlock
patchNotes.error.noInternet.title=Disconnected from the Internet
patchNotes.error.noInternet.msg=Oops! Something went wrong.  Maybe check your internet connection?
patchNotes.error.notFound.title=Patch Notes %1
patchNotes.error.notFound.msg=We fixed a few bugs in this release.  We'll send you new patch notes when we have more to report.

pauseScreen.back=Back to Game
pauseScreen.currentWorld=Current World
pauseScreen.header=Game Menu
pauseScreen.options=Options
pauseScreen.quit=Save & Quit
pauseScreen.secondaryClientLeave=Save & Leave
pauseScreen.feed=Feed
pauseScreen.invite=Invite to Game
pauseScreen.ipAddress=IP: %1
pauseScreen.error.noIpAddress=No IP found
pauseScreen.error.noPort=No Port found
pauseScreen.title=Game Paused
pauseScreen.betaFeedback=Beta Feedback
pauseScreen.xboxLiveDisconnect=Oops! Your Microsoft Account was disconnected. To invite more players, sign in on the Main Menu and restart your world.
pauseScreen.joinCode.Label=JOIN CODE
pauseScreen.joinCode.Icon=%3 join code icon, %1 of %2	# Example: Apple join code icon, 2 of 5

feedbackScreen.button=Feedback
feedbackScreen.title=Have Feedback?
feedbackScreen.suggestion=Make a suggestion!
feedbackScreen.bug=Report a bug!
feedbackScreen.help=Need help?
feedbackScreen.body=Have an idea to improve Minecraft? Find a bug that you want fixed? Or encounter an issue you need help with?  Let us know!
feedbackScreen.consoleMessage=Visit this website on another device. %s

hudScreen.tooltip.basic.back=Back
hudScreen.tooltip.basic.flyDown=Fly Down
hudScreen.tooltip.basic.flyUp=Fly Up
hudScreen.tooltip.basic.forward=Forward
hudScreen.tooltip.basic.jump=Jump
hudScreen.tooltip.basic.left=Left
hudScreen.tooltip.basic.right=Right
hudScreen.tooltip.basic.sneak=Sneak
hudScreen.tooltip.basic.startFlying=Start Flying
hudScreen.tooltip.basic.stopFlying=Stop Flying
hudScreen.tooltip.basic.swimDown=Swim Down
hudScreen.tooltip.basic.swimUp=Swim Up

hudScreen.tooltip.crafting=Crafting
hudScreen.tooltip.inventory=Inventory
hudScreen.tooltip.dropItem=Drop
hudScreen.tooltip.eject=Eject
hudScreen.tooltip.potion=Drink
hudScreen.tooltip.milk=Drink
hudScreen.tooltip.draw=Draw
hudScreen.tooltip.release=Release
hudScreen.tooltip.throw=Throw
hudScreen.tooltip.open=Open
hudScreen.tooltip.use=Use
hudScreen.tooltip.sleep=Sleep
hudScreen.tooltip.empty=Empty
hudScreen.tooltip.hang=Hang
hudScreen.tooltip.ignite=Ignite
hudScreen.tooltip.place=Place
hudScreen.tooltip.mine=Mine
hudScreen.tooltip.attach=Attach
hudScreen.tooltip.till=Till
hudScreen.tooltip.dig=Dig Path
hudScreen.tooltip.hit=Hit
hudScreen.tooltip.sleep=Sleep
hudScreen.tooltip.eat=Eat
hudScreen.tooltip.rotate=Rotate
hudScreen.tooltip.plant=Plant
hudScreen.tooltip.dismount=Dismount
hudScreen.tooltip.collect=Collect
hudScreen.tooltip.peel=Peel Bark
hudScreen.tooltip.scaffoldingDescend=Hold to Descend
hudScreen.tooltip.pick=Pick
hudScreen.tooltip.placeBook=Place Book
hudScreen.tooltip.readBook=Read Book
hudScreen.tooltip.removeBook=Remove Book
hudScreen.tooltip.shear=Shear

playscreen.fileSize.MB=MB
playscreen.fileSize.GB=GB
playscreen.joinableRealms=Joinable Realms
playscreen.noFriendsRealms=You have not become a member of any Realms yet.
playscreen.header.local=Play
playscreen.header.realms=Realms
playscreen.lastPlayed.daysAgo=%1 days ago
playscreen.lastPlayed.longAgo=Long ago
playscreen.lastPlayed.today=Today
playscreen.lastPlayed.weeksAgo=%1 weeks ago
playscreen.lastPlayed.yesterday=Yesterday
playscreen.new=New
playscreen.remoteWorld=Remote world at:
playscreen.realmsTrialWorld=Try Realms Plus free for 30 days
playscreen.realmsCreateFirstWorld=Create your first realm
playscreen.checkingRealmsCompatibility=Checking Realms compatibility...
playscreen.fetchingRealms=Fetching Realms...
playscreen.confirmLeaveMessage=Are you sure you want to leave Realm %1$s?
playscreen.confirmLeaveTitle=Confirm Leave
playscreen.realmExpired=Expired
playscreen.realmFull=Full
playscreen.realmClientOutdated=All Realms have been updated. You need to update your game to continue playing Realms.
playscreen.realms=Realms
playscreen.realmsCompatibilityFailure=We could not connect to Realms right now. We will try again soon.
playscreen.worlds=Worlds
playscreen.dontSeeLegacyWorlds=Don't see your worlds?
playscreen.syncLegacyWorlds=Sync Old Worlds
playscreen.fetchingLegacyWorlds=Fetching Old Worlds...
playscreen.upgradeLegacyWorlds=Old Worlds
playscreen.noLegacyWorldsFound.title=No Worlds Found
playscreen.noLegacyWorldsFound.body=No worlds from other versions of Minecraft detected.
playscreen.failedToAutoSyncLegacyWorlds=Unable to fetch Old Worlds. Clear up additional storage space in order to manage Old Worlds.
playscreen.lockedSkin=The skin that you have equipped is from a content pack that is not allowed in cross-platform multiplayer. Continuing will disable cross-platform multiplayer for this world.
playscreen.multiplayerLockedSkin=The skin that you have equipped is from a content pack that is not allowed in multiplayer. Continuing will disable multiplayer for this world.
playscreen.worldsStorage=Storage
playscreen.delete.legacy.content=Are you sure you want to delete the selected old world? This world will be lost forever! (A long time!)
playscreen.delete.legacy.title=Delete %s permanently?
playscreen.delete.legacy.confirm=Delete
playscreen.delete.legacy.deleting=Deleting world...

permissions.ability.build=Build
permissions.ability.mine=Mine
permissions.ability.doorsandswitches=Use doors and switches
permissions.ability.opencontainers=Open containers
permissions.ability.attackplayers=Attack players
permissions.ability.attackmobs=Attack mobs
permissions.ability.op=Operator Commands
permissions.ability.invisible=Become invisible
permissions.ability.teleport=Use Teleport
permissions.NeedPermission=You Need Permission
permissions.AddFriends=You cannot add friends because of how your Microsoft Account is set up.  This can be changed in your privacy & online safety settings on aka.ms/accountsettings.
permissions.MultiplayerSessions=You cannot play on Realms because of how your Microsoft Account is set up.  This can be changed in your privacy & online safety settings on aka.ms/accountsettings.
permissions.Communications=You cannot chat with other players because of how your Microsoft Account is set up.  This can be changed in your privacy & online safety settings on aka.ms/accountsettings.
permissions.RealmsAddFriends=You will not be able to add new friends to play on your realm with you because of how your Xbox Live account is set up.  This can be changed in your privacy & online safety settings on Xbox.com.  Do you want to continue?
permissions.CloudSave=You cannot save your worlds to Xbox Live because of how your account is set up.  This can be changed in your privacy & online safety settings on Xbox.com.
permissions.MultiplayerSessionsOnConsole=Your Xbox settings on your Microsoft Account do not allow multiplayer games.  Check your "Privacy and Online Safety" settings on aka.ms/accountsettings to make sure you have Xbox Live Gold and that your settings allow multiplayer.
permissions.GoBack=Go Back
permissions.Continue=Continue
permissions.chatmute=Chat is currently disabled
permissions.deopingother.message=They will no longer have Operator level permissions by lowering their permission level.
permissions.description.visitors=Visitors can freely explore your world, but cannot interact with blocks, items, or entities. Trust Players Off.
permissions.description.members=Members are active players in your world who can break and create blocks, and attack mobs and other players.
permissions.description.operators=Operators are members who can set player permissions and use commands to have more control over your world.
permissions.level=Permission Level
permissions.level.custom=Custom...
permissions.level.member=Member
permissions.level.operator=Operator
permissions.level.visitor=Visitor
permissions.nocheats=Cheats Are Off
permissions.nocheats.message=Certain commands like teleport are only available with cheats ON.  Cheats can also be turned on through the pause menu under Game Settings.  If you choose to turn cheats on, achievements will be disabled in this world.
permissions.nocheats.message.noachievements=Certain commands like teleport are only available with cheats ON.  Cheats can also be turned on through the pause menu under Game Settings.
permissions.nocheats.turnon=Set OP with cheats ON
permissions.nocheats.turnoff=Set OP with cheats OFF
permissions.nocheats.cancel=Cancel
permissions.deopingself=Remove Operator Permissions?
permissions.deopingself.message=You will no longer have Operator level permissions by lowering your permission level.
permissions.title=Player Permissions
permissions.title.settings=Player permission when joining from invite
permissions.title.settings.edu=Permission level for students who join your world
permissions.toast.playerLeft=A player left the game.
permissions.toast.playerJoined=A player joined the game.

## edu permissions popup
permissions.button.kickplayer=Remove player from game
permissions.popup.title=Permission Options
permissions.dropdown.title=Permissions Level for %s
permissions.operator=Operator
permissions.member=Member
permissions.visitor=Visitor
permissions.removeplayer=Remove Player
permissions.removeplayer.reason=You have been removed from the session by the host.
permissions.removeplayer.message=Are you sure you want to remove this player from the world? When you remove this player a new join code will be generated so that they will not be able to rejoin.
permissions.removeplayer.title=Remove Player Confirmation

portfolioScreen.page=Page %s
portfolioScreen.export=Export Portfolio
portfolioScreen.caption=[ caption ]
portfolioScreen.nopics0=Currently there are no photos to display.
portfolioScreen.nopics1=Photos you take with the camera will show up here.

potion.absorption=Absorption
potion.absorption.postfix=Potion of Absorption
potion.blindness=Blindness
potion.blindness.postfix=Potion of Blindness
potion.conduitPower=Conduit Power
potion.confusion=Nausea
potion.confusion.postfix=Potion of Nausea
potion.damageBoost=Strength
potion.damageBoost.postfix=Potion of Strength
potion.digSlowDown=Mining Fatigue
potion.digSlowDown.postfix=Potion of Dullness
potion.digSpeed=Haste
potion.digSpeed.postfix=Potion of Haste
potion.effects.whenDrank=When Applied:
potion.empty=No Effects
potion.fireResistance=Fire Resistance
potion.fireResistance.postfix=Potion of Fire Resistance
potion.harm=Instant Damage
potion.harm.postfix=Potion of Harming
potion.heal=Instant Health
potion.heal.postfix=Potion of Healing
potion.healthBoost=Health Boost
potion.healthBoost.postfix=Potion of Health Boost
potion.hunger=Hunger
potion.hunger.postfix=Potion of Hunger
potion.invisibility=Invisibility
potion.invisibility.postfix=Potion of Invisibility
potion.jump=Jump Boost
potion.jump.postfix=Potion of Leaping
potion.levitation=Levitation
potion.levitation.postfix=Potion of Levitation
potion.moveSlowdown=Slowness
potion.moveSlowdown.postfix=Potion of Slowness
potion.slowFalling=Slow Falling
potion.slowFalling.postfix=Potion of Slow Falling
potion.moveSpeed=Speed
potion.moveSpeed.postfix=Potion of Swiftness
potion.nightVision=Night Vision
potion.nightVision.postfix=Potion of Night Vision
potion.poison=Poison
potion.poison.postfix=Potion of Poison
potion.potency.0=
potion.potency.1=II
potion.potency.2=III
potion.potency.3=IV
potion.potency.4=V
potion.potency.5=VI
potion.prefix.acrid=Acrid
potion.prefix.artless=Artless
potion.prefix.awkward=Awkward
potion.prefix.bland=Bland
potion.prefix.bulky=Bulky
potion.prefix.bungling=Bungling
potion.prefix.buttered=Buttered
potion.prefix.charming=Charming
potion.prefix.clear=Clear
potion.prefix.cordial=Cordial
potion.prefix.dashing=Dashing
potion.prefix.debonair=Debonair
potion.prefix.diffuse=Diffuse
potion.prefix.elegant=Elegant
potion.prefix.fancy=Fancy
potion.prefix.flat=Flat
potion.prefix.foul=Foul
potion.prefix.grenade=Splash
potion.prefix.gross=Gross
potion.prefix.harsh=Harsh
potion.prefix.linger=Lingering
potion.prefix.milky=Milky
potion.prefix.mundane=Mundane
potion.prefix.mundane.extended=Long Mundane
potion.prefix.odorless=Odorless
potion.prefix.potent=Potent
potion.prefix.rank=Rank
potion.prefix.refined=Refined
potion.prefix.smooth=Smooth
potion.prefix.sparkling=Sparkling
potion.prefix.stinky=Stinky
potion.prefix.suave=Suave
potion.prefix.thick=Thick
potion.prefix.thin=Thin
potion.prefix.uninteresting=Uninteresting
potion.regeneration=Regeneration
potion.regeneration.postfix=Potion of Regeneration
potion.resistance=Resistance
potion.resistance.postfix=Potion of Resistance
potion.saturation=Saturation
potion.saturation.postfix=Potion of Saturation
potion.turtleMaster=Slowness
potion.turtleMaster2=Resistance
potion.turtleMaster.postfix=Potion of the Turtle Master
potion.waterBreathing=Water Breathing
potion.waterBreathing.postfix=Potion of Water Breathing
potion.weakness=Weakness
potion.weakness.postfix=Potion of Weakness
potion.wither=Wither
potion.wither.postfix=Potion of Decay

profileScreen.header=Profile
profileScreen.manage_button_text=Edit Character
profileScreen.manage_button_create_text=Create Character

progressScreen.cantConnect=Unable to connect to the world.  Please check your connection to the internet and try again.
progressScreen.generating=Generating world
progressScreen.saving=Saving world
progressScreen.loading=Loading...
progressScreen.title.downloading=Downloading packs %1
progressScreen.title.applyingPacks=Loading resource packs
progressScreen.title.searchingForSession=Searching for Game Session...
progressScreen.title.waitingForStorageProvider=Syncing user data
progressScreen.title.connectingLocal=Starting World
progressScreen.title.connectingLAN=Connecting to multiplayer game
progressScreen.title.connectingExternal=Connecting to external server
progressScreen.title.connectingRealms=Connecting to Realm
progressScreen.title.copyingWorld=World Copy
progressScreen.message.copyingWorld=Copying World... %d%%
progressScreen.message.building=Building terrain
progressScreen.message.done=Done!
progressScreen.message.exporting=Export in Progress
progressScreen.message.exporting.warning=Please do not exit the game. Exiting might corrupt the export.
progressScreen.message.importing=Importing world
progressScreen.message.importingContent=Step 2 of 2 - Importing Content
progressScreen.message.updatingContent=Updating %1 of %2 Packs
progressScreen.message.locating=Locating server
progressScreen.message.waitingForRealms=This may take a few moments
progressScreen.message.waitingForStorageProvider=This may take a few moments...
progressScreen.message.storageProviderSyncError=Failed to sync user data.
progressScreen.message.waitingForStoreProducts=This may take a few moments
progressScreen.message.allDone=All Done!
progressScreen.message.letsGo=Let's Go!
progressScreen.message.failed=Failed
progressScreen.message.failedNoNetwork=Failed: No Network Connection
progressScreen.message.downloadingWorld=Downloading World
progressScreen.message.downloadingContent=Step 1 of 2 - Downloading Content
progressScreen.message.uploadingWorld=Uploading World
progressScreen.message.copyingPacks=Saving World Resource Packs
progressScreen.message.initiatingTemplate=Initiating World Template
progressScreen.message.fileSize=File Size
progressScreen.message.initializingUpload=Initializing Upload
progressScreen.message.initializingDownload=Initializing Download
progressScreen.message.resourceLoading=Loading Resources
progressScreen.message.leaveLevel=Your game is being saved. Please do not turn off your device.
progressScreen.message.genericMayTakeAMoment=This may take a few moments
progressScreen.message.unkownError=An error occured
progressScreen.message.forbiddenContent=You do not own one or more of the applied packs
progressScreen.dialog.title.resourcePack=Download World Resource Packs?
progressScreen.dialog.title.behaviorAndResourcePack=Download World Behavior & Resource Packs?
progressScreen.dialog.title.onlyBehavior=Download World Behavior Packs?
progressScreen.dialog.title.scriptConfirm=Enable Scripts?
progressScreen.dialog.title.storageProviderError=Failed To Sync World Data
progressScreen.dialog.title.storageProviderLongWait=Syncing World Data
progressScreen.dialog.message.onlyBehavior=This world has Behavior Packs applied to it that you must download to join. Would you like to download them and join?
progressScreen.dialog.message.behaviorAndOptionalResourcePack=This world has Behavior Packs applied to it that you must download to join and Resource Packs that you may optionally download before joining. What would you like to download before joining?
progressScreen.dialog.message.behaviorAndResourcePack=This world has Behavior and Resource Packs applied to it that you must download to join. Would you like to download them and join?
progressScreen.dialog.message.resourcePackOptional=This world has Resource Packs applied to it. Would you like to download them before you join?
progressScreen.dialog.message.resourcePackRequired=This world has Resource Packs applied to it that you must download to join. Would you like to download them and join?
progressScreen.dialog.message.scriptConfirm=The world you are about to enter contains scripts that run on your device. Would you like to join this world?
progressScreen.dialog.message.storageProviderError=Could not sync your world data.  Would you like to try again?
progressScreen.dialog.message.storageProviderUnusableError=The world is not fully synced or has been corrupted. Try playing the world on a console where it's in a good state and try migrating it again.
progressScreen.dialog.message.worldCorrupted=The world has been corrupted. Please report this at bugs.mojang.com
progressScreen.dialog.message.storageProviderLongWait=Syncing this world is taking a long time.  Would you like to continue waiting?
progressScreen.dialog.button.enter=Enter World
progressScreen.dialog.button.joinAndDownload=Download & Join - %1
progressScreen.dialog.button.joinAndDownload.everything=Download Everything & Join - %1
progressScreen.dialog.button.joinAndDownload.onlyBehaviorPacks=Only Download Behaviors & Join - %1
progressScreen.dialog.button.join=Join
progressScreen.dialog.button.leave=Leave
progressScreen.dialog.button.retry=Retry
progressScreen.dialog.button.wait=Wait

quiz.popup.ok=Take the Quiz
quiz.popup.text=The quiz will open in a new window. You may return to Minecraft after you have completed the quiz.
quiz.popup.title=Quiz

raid.name=Raid
raid.progress=Mobs remaining:
raid.expiry=A raid has expired

recipeBook.setting.full=Full
recipeBook.setting.discover=Discover
recipeBook.setting.off=Off

record.nowPlaying=Now playing: %s

resourcePack.available.title=Available Resource Packs
resourcePack.available.title.behaviorPacks=Available Behavior Packs
resourcePack.available.title.packs=My Packs
resourcePack.available.add=Activate
resourcePack.available.none.text=You have no resource packs
resourcePack.available.none.store=View Marketplace
resourcePack.message.noneFound.packs=There are no available packs on this device.
resourcePack.message.allInUse.packs=Your packs are currently in use.
resourcePack.message.error=A pack has errors that will make it not function
resourcePack.message.warning=A pack has warnings that might cause odd behavior
resourcePack.suggestedContent.title=Get More Packs
resourcePack.suggestedContent.button.viewMore=View More Packs
resourcePack.cached.title=Cached Packs
resourcePack.folderInfo=(Place resource pack files here)
resourcePack.openFolder=Open resource pack folder
resourcePack.selected.title=Selected Resource Packs
resourcePack.selected.title.behaviorPacks=Active Behavior Packs
resourcePack.selected.title.packs=Active
resourcePack.selected.remove=Deactivate
resourcePack.realmsPlus.title.packs=Realms Plus Packs
resourcePack.realmsPlus.expired=Expired
resourcePack.errors=Errors: 
resourcePack.error.ingame.packs=You cannot change Resource Packs while playing in a world.
resourcePack.error.ingame.behaviorPacks=You cannot change Behavior Packs while playing in a world.
resourcePack.error.enteringgame.title=Expired Packs on Realm
resourcePack.error.enteringgame.message=This Realm contains resource or behavior packs that have expired from Realms Plus. You will need to either deactivate these packs or purchase them from the marketplace in order to play on the Realm.
resourcePack.error.enteringgame.button=View Active Packs
resourcePack.title=Select Resource Packs
resourcePack.toast.atlasFallback.message=Low memory. Textures will have reduced level of detail.
resourcePack.toast.atlasFallback.title=Resource Pack Fallback
resourcePack.copyGlobal=Copy from Global
resourcePack.description=Resource packs are applied bottom to top. This means any asset that is in two packs will be overridden by the higher pack.
resourcePack.description.default.level=You can edit these in Settings.
resourcePack.description.default.behaviorPacks=The default gameplay of Minecraft.
resourcePack.description.bottom.global=Resource packs are applied bottom to top. This means any asset that is in two packs will be overridden by the higher pack. Packs in your worlds will apply on top of these global packs. These resources are just for you. No one else will see the resources you set here. Resource Packs in your worlds or worlds you join will apply on top of these global resources.
resourcePack.description.bottom.behaviorPacks=Behavior Packs are applied bottom to top. That means anything that is in two Behavior Packs will be overridden by the higher one.
resourcePack.description.bottom.level=Resource packs are applied bottom to top. This means any asset that is in two packs will be overridden by the higher pack. These packs in your world apply on top of your global pack(s).
resourcePack.description.store=View in Store
resourcePack.header.behavior=Active Behavior Packs apply to all players.
resourcePack.header.level=Require players to accept resource packs to join
resourcePack.crashRecovery.title=Global Resources Reset
resourcePack.crashRecovery.message=Resources failed to load previously.
resourcePack.warnings=Warnings: 
resourcePack.warning.title=This Is Dangerous!
resourcePack.warning.body=Adding or removing Behavior Packs after playing a world might break the world and cause you to lose what you created.
resourcePack.error.plugin_removal.title=Action not allowed.
resourcePack.error.plugin_removal.body=Removing Behavior Packs with a plugin might break the world and cause you to lose what you created.
resourcePack.requiredDependency.title=Required Dependency
resourcePack.requiredDependency.body=This pack is a required dependency of another pack that is currently applied.
resourcePack.missingDependency.title=Missing Dependencies
resourcePack.missingDependency.body=This pack is missing one or more dependencies.  Would you like to apply it anyway?
resourcePack.delete=You are about to delete %s forever. Are you sure?
resourcePack.delete.confirm=Delete pack?
resourcePack.deleteSelected=This pack is currently selected! You are about to delete %s forever. Are you sure?
resourcePack.deleteMultiple=You are going to delete the following packs forever:%sAre you sure?
resourcePack.editPack=Delete Packs...
resourcePack.editPackDone=Done
resourcePack.toast.unownedContent.title=%s Deactivated
resourcePack.toast.unownedContent.subtitle=This account does not own this resource pack.
resourcePack.incompatibleDependency.memory=This pack has a dependency (%s) that is incompatible with your device because your device doesn't have enough memory.  
resourcePack.subpackResolution=Resolution: %s
resourcePack.incompatibleMemory.resolution=This resolution is incompatible with your device because your device doesn't have enough memory.
resourcePack.incompatibleMemory.pack=This pack is incompatible with your device because it doesn't have enough memory.
resourcePack.packSettingsTitle=%s Settings
resourcePack.missingPackDescription=This pack is missing!
resourcePack.legacyPackName=Legacy Resource Pack

storageManager.contentType.world=World
storageManager.contentType.worldTemplate=World Template
storageManager.contentType.resourcePack=Resource Pack
storageManager.contentType.behaviorPack=Behavior Pack
storageManager.contentType.skinPack=Skin Pack
storageManager.contentType.invalid=Invalid
storageManager.contentType.cachedData=Cached Data

storageManager.contentType.worlds=Worlds
storageManager.contentType.worldTemplates=World Templates
storageManager.contentType.resourcePacks=Resource Packs
storageManager.contentType.behaviorPacks=Behavior Packs
storageManager.contentType.skinPacks=Skin Packs

storageManager.baseWorld=Base World

storageManager.mainSizeLabel=%s - 1 Item
storageManager.mainSizeLabelPlural=%s - %s Items
storageManager.delete.title=Delete %s permanently?
storageManager.title.item.single=item
storageManager.title.item.plural=items
storageManager.delete.content=Are you sure you want to delete the selected items? These items will be lost forever! (A long time!) %s%s%s
storageManager.delete.content.redownload=You can redownload content you've bought from the Marketplace.
storageManager.delete.content.worldtemplate=Deleting templates may cause worlds that are using them to no longer work as intended.
storageManager.delete.content.affectedWorlds=The following worlds may stop working as as intended:
storageManager.delete.confirm=Delete
storageManager.delete.cancel=Cancel
storageManager.delete.dependency=The following content is dependent on this %s and may not continue to work correctly if you delete "%s":%s Do you still want to delete "%s"? This %s will be lost forever! (A long time!)
storageManager.delete.premium=You are about to delete "%s". You will still own %s and if you wish to use %s again in the future, you can re-download it from the Marketplace.
storageManager.delete.premium.end=Are you sure you want to delete "%s"? 
storageManager.deleting.content=Deleting Content
storageManager.download.premium=%s is not currently installed.
storageManager.download.premium.end=Would you like to download %s ( %s )?
storageManager.dependency.titleText=Did you want to break these?
storageManager.dependency.warningText=Some other items need content you are deleting to work properly. Do you want to risk breaking these items?
storageManager.dependency.doneText=The other %s can be deleted without breaking other resources.
storageManager.dependency.noItems=There are no other items left to delete.
storageManager.dependency.breakItem=Required content you are deleting:
storageManager.dependency.removeDependencies=Keep the items this pack needs
storageManager.dependency.breakPack=Are you sure you want to break this pack?
storageManager.share.compress=Compressing the selected resources...
storageManager.share.totalPercent=100%
storageManager.dependency.continue=Continue
storageManager.dependency.continue.scroll=Scroll to Continue
storageManager.sortLargest=Size - Largest First
storageManager.sortDateRecent=Date Used - Recent First
storageManager.sortDateOldest=Date Used - Oldest First
storageManager.version=Version 
storageManager.multiselectDelete=Delete
storageManager.multiselectShare=Share
storageManager.multiselect=Multiselect
storageManager.miscellaneous=Miscellaneous

storageManager.groupType=Type
storageManager.groupPack=Add-on

storageManager.shareTitle=Export Resources
storageManager.mcpack=Minecraft Pack
storageManager.mcaddon=Minecraft Addon
storageManager.mcworld=Minecraft World



resourcepack.City=City Texture Pack
resourcepack.Plastic=Plastic Texture Pack
resourcepack.Natural=Natural Texture Pack
resourcepack.Fantasy=Fantasy Texture Pack
resourcepack.Cartoon=Cartoon Texture Pack
resourcepack.Candy=Candy Texture Pack
resourcepack.FestiveMashup2016=Festive Mash-up 2016
resourcepack.ChineseMythology=Chinese Mythology Mash-up

resourcePack.vanilla.name=Minecraft Texture Pack
resourcePack.vanilla.description=The default Minecraft graphics, now updated with stylish new textures!

resourcePack.city.name=City
resourcePack.city.description=Ideal for building structures.

resourcePack.plastic.name=Plastic
resourcePack.plastic.description=Simple, colorful, and vibrant.

resourcePack.natural.name=Natural
resourcePack.natural.description=Designed to give your worlds a more natural look.

resourcePack.fantasy.name=Fantasy
resourcePack.fantasy.description=Transport yourselves to a time when knights were heroes.

resourcePack.cartoon.name=Cartoon
resourcePack.cartoon.description=Googly eyes! Goofy grins! Turn your world into a toon with this pack.

resourcePack.candy.name=Candy
resourcePack.candy.description=Sugarcoat your world and turn every texture into a syrupy snack.

resourcePack.festivemashup2016.name=Festive Mash-up 2016
resourcePack.festivemashup2016.description=Create a winter wonderland with this snowy seasonal pack.

resourcePack.chinesemythology.name=Chinese Mythology Mash-up
resourcePack.chinesemythology.description=Textures inspired by the myths and legends of China.

resourcepack.Fallout=Fallout Mash-up
resourcePack.Fallout.name=Fallout Mash-up
resourcePack.Fallout.description=Welcome to the Wasteland!

resourcepack.MagicTheGathering.name=Magic: The Gathering

resourcePack.GreekMythology=Greek Mythology Mash-up
resourcePack.GreekMythology.name=Greek Mythology Mash-up
resourcePack.GreekMythology.description=It's mythical Greece at your fingertips. 

resourcePack.Skyrim.name=Skyrim Mash-up
resourcePack.Skyrim.description=Build your own worlds in the land of Skyrim.

resourcePack.MashupAdventureTime.name=Adventure Time
resourcePack.MashupAdventureTime.description=

resourcePack.skin.name=Skins
resourcePack.skin.description=Skin, Skin, Skin!!!

resourcePack.invalid.description=Couldn't parse pack successfully. Click to display errors.
resourcePack.loading.description=Pack is still being loaded.

review.item.post.rating.dropdown.label=Choose a star rating
review.item.post.rating.footer=You will be able to change your rating.
review.item.post.rating.submit.button=Rate this pack
review.item.post.rating.1star=Oh no! We're sad you didn't enjoy your pack at all! Send us some feedback at https://aka.ms/marketplacefeedback
review.item.post.rating.2star=Oh! That's no good. Send us some feedback at https://aka.ms/marketplacefeedback
review.item.post.rating.3star=There's always room for improvement, but we're glad you're still having fun.
review.item.post.rating.4star=Woohoo! We're glad you're having a good time!
review.item.post.rating.5star=We're glad you're having a great time!
review.item.post.rating.submit.toast.line1=Thanks for rating this pack!
review.item.post.rating.submit.toast.line2=It may take some time for us to show your rating.

roaming.status_brief.limited_usage=Limited usage
roaming.status_brief.no_restrictions=Usable cross-platform
roaming.status_hover.limited_usage=Only available on specific platforms.
roaming.status_hover.no_restrictions=Available on any Bedrock platform!

screenshot.failure=Couldn't save screenshot: %s
screenshot.success=Saved screenshot as %s
screenshot.title=Screenshot Captured!
screenshot.caption=Add a caption and share?
screenshot.post=Share

seedPicker.search=Search
seedPicker.title=Seed Picker

selectServer.add=Add server
selectServer.defaultName=Minecraft Server
selectServer.delete=Delete
selectServer.deleteButton=Delete
selectServer.deleteQuestion=Are you sure you want to remove this server?
selectServer.deleteWarning=will be lost forever! (A long time!)
selectServer.direct=Direct Connect
selectServer.edit=Edit
selectServer.empty=empty
selectServer.hiddenAddress=(Hidden)
selectServer.refresh=Refresh
selectServer.select=Join Server
selectServer.title=Select Server

selectWorld.allowCommands=Allow Cheats:
selectWorld.allowCommands.info=Commands like /gamemode, /xp
selectWorld.bonusItems=Bonus Chest:
selectWorld.trustPlayers=Trust Players:
selectWorld.cheats=Activate Cheats
selectWorld.conversion=Must be converted!
selectWorld.convertInProgress.title=World Conversion
selectWorld.convertInProgress.msg=Converting World... %d%%
selectWorld.convertInProgress.download_msg=Downloading Resource Packs... %d%%
selectWorld.convertInProgress.import_msg=Importing Resource Packs... %d%%
selectWorld.uploadInProgress.title=Sending Old World
selectWorld.uploadInProgress.msg=Sending Old World to Minecraft... %d%%
selectWorld.convertFailed.msg=Oops. Something went wrong.
selectWorld.create=Create New World
selectWorld.createNew=Create New
selectWorld.createTemplate=Create From Template
selectWorld.createDemo=Play New Demo World
selectWorld.customizeType=Customize
selectWorld.delete=Delete
selectWorld.deleteButton=Delete
selectWorld.deleteQuestion=Are you sure you want to delete this world?
selectWorld.deleteWarning=will be lost forever! (A long time!)
selectWorld.empty=empty
selectWorld.enterName=World Name
selectWorld.enterSeed=Seed for the World Generator
selectWorld.gameMode=Game Mode
selectWorld.gameMode.adventure=Adventure
selectWorld.gameMode.adventure.line1=Same as survival mode, but blocks can't
selectWorld.gameMode.adventure.line2=be added or removed
selectWorld.gameMode.creative=Creative
selectWorld.gameMode.creative.line1=Unlimited resources, free flying and
selectWorld.gameMode.creative.line2=destroy blocks instantly
selectWorld.gameMode.hardcore=Hardcore
selectWorld.gameMode.hardcore.line1=Same as survival mode, locked at hardest
selectWorld.gameMode.hardcore.line2=difficulty, and one life only
selectWorld.gameMode.spectator=Spectator
selectWorld.gameMode.spectator.line1=You can look but don't touch
selectWorld.gameMode.spectator.line2=
selectWorld.gameMode.survival=Survival
selectWorld.gameMode.survival.line1=Search for resources, crafting, gain
selectWorld.gameMode.survival.line2=levels, health and hunger
selectWorld.hardcoreMode=Hardcore:
selectWorld.hardcoreMode.info=World is deleted upon death
selectWorld.mapFeatures=Generate Structures:
selectWorld.mapFeatures.info=Villages, dungeons etc
selectWorld.mapType=World Type:
selectWorld.mapType.normal=Normal
selectWorld.moreWorldOptions=More World Options...
selectWorld.newWorld=New World
selectWorld.newWorld.copyOf=Copy of %s
selectWorld.newWorld.educationCopyOf=[EDU] %s
selectWorld.newWorld.experimentalCopyOf=[EX] %s
## NOTE: "Realms" is the name of a service and doesn't get localized.
selectWorld.realmsComingSoon=Realms Coming Soon!
selectWorld.realmsBeta=Realms Beta
selectWorld.learnMore=Learn More
selectWorld.recreate=Re-Create
selectWorld.rename=Rename
selectWorld.renameButton=Rename
selectWorld.renameTitle=Rename World
selectWorld.resultFolder=Will be saved in:
selectWorld.seedInfo=Leave blank for a random seed
selectWorld.select=Play Selected World
selectWorld.tab.worlds=Worlds
selectWorld.tab.realms=Realms
selectWorld.tab.friends=Friends
selectWorld.tab.classmates=Friends
selectWorld.tab.thirdParty=Servers
selectWorld.title=Select World
selectWorld.world=World

selectTemplate.templateStart=Where do you want to begin?
selectTemplate.generateRandom=Create New World
selectTemplate.createRealm=Create New Realm
selectTemplate.create=Create ...
selectTemplate.realm=New Realm
selectTemplate.realmInfo=You can upload worlds to your Realm after you create it!
selectTemplate.signIn=Create Realm with Microsoft Account
selectTemplate.unableToSignIn=Microsoft services are unavailable on this device
selectTemplate.world=New World
selectTemplate.help=?
selectTemplate.templateDescription=Template
selectTemplate.deleteTemplate=Delete World Templates...
selectTemplate.deleteTemplateDone=Done
selectTemplate.deleteMessage=You are about to delete %s forever. Are you sure?
selectTemplate.delete.confirm=Delete World Template?
selectTemplate.delete=Delete
selectTemplate.myTemplates=My World Templates
selectTemplate.realmsPlus=Featured Realms Plus Templates
selectTemplate.importedTemplates=Imported Templates
selectTemplate.download=Download
selectTemplate.noTemplates=There are no world templates on this device.
selectTemplate.suggestedContent.title=Featured Marketplace Templates
selectTemplate.suggestedContent.button=See More Templates
selectTemplate.createdBy=Created by %s
selectTemplate.inventory=My Marketplace Packs
selectTemplate.seeMore=See More


sign.edit=Edit sign message

skin.New=New

skin.Standard.Alex=Alex
skin.Standard.Steve=Steve
skin.Standard.Custom=Custom
skin.Standard.CustomSlim=Custom
skin.Standard.Dummy=Custom

skinpack.Education=Education Edition Skins

skins.browse=Browse
skins.buy.buyButton=Buy
skins.buy.cancelButton=Cancel
skins.buy.noConnection=We're unable to connect to the Marketplace. Maybe check your internet connection?
skins.information.ingame=Sorry, you can't change your skin in-game. Access Options from the main menu.
skins.information.invalidCustomSkin=That's not a Minecraft skin, silly.
skins.information.upsellWithoutStore=You must purchase the skin pack to use that skin, and we can't connect to the Marketplace.
skins.information.selectSkin=Choose the correct model type for your skin
skins.restore.button=Restore
skins.show.restorePurchaseButton=Restore
skins.skinpackHeader.packs=Skin packs
skins.skinpackHeader.standard=Standard
skins.title=Skins
skins.picker.title=Choose Skin
skins.picker.accept.button=Confirm
skins.picker.unlock.button=Unlock
skins.picker.custom.button=Choose New Skin
skins.picker.default=Default
skins.picker.recent=Recent
skins.picker.no.cross.platform=Platform Restricted Skin Pack
skins.picker.no.multiplayer=Multiplayer Restricted Skin Pack
skins.picker.expanded.back=Skins in '%s'
skins.store.upsell.buy.button=Unlock All %s Skins for %s:minecoin:
skins.store.upsell.equip.button=Equip this Skin
skins.store.upsell.info=You need to unlock this pack to equip the skin you selected.
skins.store.upsell.seePack=See Pack in the Marketplace
skins.store.upsell.unlockPack=Unlock this pack to equip the skin you selected.
skins.store.equipped=You have equipped the %s skin.

soundCategory.ambient=Ambient/Environment
soundCategory.block=Blocks
soundCategory.hostile=Hostile Creatures
soundCategory.master=Master Volume
soundCategory.music=Music
soundCategory.neutral=Friendly Creatures
soundCategory.player=Players
soundCategory.record=Jukebox/Noteblocks
soundCategory.weather=Weather

stat.animalsBred=Animals Bred
stat.armorCleaned=Armor Pieces Cleaned
stat.bannerCleaned=Banners Cleaned
stat.beaconInteraction=Interactions with Beacon
stat.blocksButton=Blocks
stat.boatOneCm=Distance by Boat
stat.breakItem=%1$s Depleted
stat.brewingstandInteraction=Interactions with Brewing Stand
stat.cakeSlicesEaten=Cake Slices Eaten
stat.cauldronFilled=Cauldrons Filled
stat.cauldronUsed=Water Taken from Cauldron
stat.chestOpened=Chests Opened
stat.climbOneCm=Distance Climbed
stat.crafted=Times Crafted
stat.craftItem=%1$s Crafted
stat.createWorld=Worlds created
stat.crouchOneCm=Distance Crouched
stat.damageDealt=Damage Dealt
stat.damageTaken=Damage Taken
stat.deaths=Number of Deaths
stat.depleted=Times Depleted
stat.dispenserInspected=Dispensers Searched
stat.diveOneCm=Distance Dove
stat.drop=Items Dropped
stat.dropperInspected=Droppers Searched
stat.enderchestOpened=Ender Chests Opened
stat.entityKilledBy=%s killed you %d time(s)
stat.entityKilledBy.none=You have never been killed by %s
stat.entityKills=You killed %d %s
stat.entityKills.none=You have never killed %s
stat.fallOneCm=Distance Fallen
stat.fishCaught=Fish Caught
stat.flowerPotted=Plants potted
stat.flyOneCm=Distance Flown
stat.furnaceInteraction=Interactions with Furnace
stat.generalButton=General
stat.hopperInspected=Hoppers Searched
stat.horseOneCm=Distance by Horse
stat.itemEnchanted=Items Enchanted
stat.itemsButton=Items
stat.joinMultiplayer=Multiplayer joins
stat.jump=Jumps
stat.junkFished=Junk Fished
stat.leaveGame=Games quit
stat.loadWorld=Saves loaded
stat.mineBlock=%1$s Mined
stat.minecartOneCm=Distance by Minecart
stat.mined=Times Mined
stat.mobKills=Mob Kills
stat.mobsButton=Mobs
stat.noteblockPlayed=Noteblocks played
stat.noteblockTuned=Noteblocks tuned
stat.pigOneCm=Distance by Pig
stat.playerKills=Player Kills
stat.playOneMinute=Minutes Played
stat.recordPlayed=Records Played
stat.sprintOneCm=Distance Sprinted
stat.startGame=Times played
stat.swimOneCm=Distance Swum
stat.talkedToVillager=Talked to Villagers
stat.timeSinceDeath=Since Last Death
stat.tradedWithVillager=Traded with Villagers
stat.trappedChestTriggered=Trapped Chests Triggered
stat.treasureFished=Treasure Fished
stat.used=Times Used
stat.useItem=%1$s Used
stat.walkOneCm=Distance Walked
stat.workbenchInteraction=Interactions with Crafting Table

stats.tooltip.type.achievement=Achievement
stats.tooltip.type.statistic=Statistic

start.beta.icon=Beta

store.title=Marketplace
store.allStores=All Stores
store.menu.home=Home
store.itemDetail.back=Details
store.coin.bonus=Bonus!

store.realmsPlus=Realms Plus
store.realmsPlus.content=Content
store.realmsPlus.faq=FAQ
store.realmsPlus.buyFor=BUY FOR %s
store.realmsPlus.buyNow=BUY NOW
store.realmsPlus.errorNoOffer=ERROR NO OFFER FOUND
store.realmsPlus.manageSubscription=MANAGE SUBSCRIPTION
store.realmsPlus.viewAllPacksTitle=A GREAT VALUE - PACKS WORTH OVER §g$150 (USD)!§r

store.realmsPlus.nowActive=Realms Plus is now active for this device. You now have access to 50+ content packs from the marketplace at no additional cost. Up to 10 players can play on your Realms at one time, and get access to all the subscriber content in your Realm - for free!

store.realmsPlus.buyNow.buttonText=BUY FOR %s
store.realmsPlus.buyNow.viewTerms=TERMS & CONDITIONS
store.realmsPlus.buyNow.viewPrivacyPolicy=PRIVACY POLICY

store.realmsPlus.buyNow.title=Start your Realms Plus Subscription
store.realmsPlus.startTrial.title=Start your FREE 30 day trial now!
store.realmsPlus.startTrial.description=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your first month will be free, and you'll be billed %s/month afterwards and can cancel at any time.
store.realmsPlus.noTrial.description=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your subscription will be available for 30 days at the price of %s:minecoin:.
store.realmsPlus.noTrial.description.iap=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your subscription will be available for 30 days at the price of %s.
store.realmsPlus.realmName.placeholder=%s's Realm		#The string passed in here is the gamertag of the player to indicate the name of the player's world.

store.realmsPlus.recurring.offerTrial=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your first month will be free, and you'll be billed %s/month afterwards and can cancel at any time.
store.realmsPlus.recurring.withoutTrial=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! You'll be billed %s/month and can cancel at any time.
store.realmsPlus.consumable.offerTrial=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your first 30 days will be free and you can renew for 30 days at the price of %s afterwards.
store.realmsPlus.consumable.withoutTrial=Your Realm and 50+ marketplace packs will be immediately available. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free! Your subscription will be available for 30 days at the price of %s.

store.realmsPlus.landing.header=Subscription Includes:

store.realmsPlus.landing.info1.line1=- Access to a catalog of 50+ marketplace packs
store.realmsPlus.landing.info1.line2=- Your own personal Realm server
store.realmsPlus.landing.info1.line3=- Friends play for free in your Realm

store.realmsPlus.landing.info2.line1=- Secure cloud storage
store.realmsPlus.landing.info2.line2=- Play on any device with Realms and the Minecraft Marketplace
store.realmsPlus.landing.info2.line3=- Support the Minecraft Community
store.realmsPlus.landing.info2.line4=- Free 1 month trial

store.realmsPlus.faq.header=FREQUENTLY ASKED QUESTIONS
store.realmsPlus.faq.question1=What is Realms Plus?
store.realmsPlus.faq.answer1=For one low monthly price, enjoy unlimited access to over fifty great Minecraft worlds, texture packs, skin packs, and mash-ups, a personal Realms server supporting up to 10 simultaneous players, and cloud storage for your worlds.
store.realmsPlus.faq.question2=Can I use my Realms Plus packs offline?
store.realmsPlus.faq.answer2=You don’t need to be online to enjoy Realms Plus. Packs included in the subscription can either be played online on your personal Realm or played on a local device. You just need to come online once every 30 days to validate that your subscription is active.
store.realmsPlus.faq.question3=Do my friends need to have Realms Plus to play on my Realm?
store.realmsPlus.faq.answer3=Your friends don’t need a Realms Plus subscription to join a Realm. You can invite any friend that has access to Realms to join your Realm for free.
store.realmsPlus.faq.question4=Can I host my Realms Plus worlds, texture packs, and mash-ups on my Realm?
store.realmsPlus.faq.answer4=Yes! You can upload any worlds, texture packs, and mash-ups from the Realms Plus catalog or your purchases from the Minecraft Marketplace to your Realm and invite your friends to join for free.
store.realmsPlus.faq.question5=How much does a Realms Plus subscription cost?
store.realmsPlus.faq.answer5=%s per month. That’s the same price as before Realms was magically transformed into Realms Plus!
store.realmsPlus.faq.question6=How often are new packs added to Realms Plus?
store.realmsPlus.faq.answer6=New packs are added to Realms Plus once every month.
store.realmsPlus.faq.question7=How long do I have access to Packs within Realms Plus?
store.realmsPlus.faq.answer7=Realms Plus members enjoy unlimited access to over 50 great Minecraft worlds, texture packs, skin packs, and mash-ups, until either the subscription expires, or a pack leaves the catalog.
store.realmsPlus.faq.question8=Where do these packs come from?
store.realmsPlus.faq.answer8=The different packs in Realms Plus are created by a combination of our great community of creators like yourself, and us here at Mojang.
store.realmsPlus.faq.question9=Which editions of Minecraft support Realms Plus?
store.realmsPlus.faq.answer9=Realms Plus is currently available for Minecraft on mobile, Windows 10, the Xbox One consoles as well as the Nintendo Switch.
store.realmsPlus.faq.question10=Can I use my Realms Plus on any Bedrock device?
store.realmsPlus.faq.answer10=Yes! If you purchase Realms Plus on any Realms enabled Bedrock device, you can access all the packs, personal servers, and storage from any Minecraft Bedrock version that has Realms available and you’re signed into your Minecraft account.
store.realmsPlus.faq.question11=How many world templates can I play with Realms Plus?
store.realmsPlus.faq.answer11=You can play and save all the packs included in Realms Plus on your local device. You can also host one of your world templates on a Realm for your friends to join for free! You can change which world you load on your Realm at any time.
store.realmsPlus.faq.question12=What happens to my Realms and worlds when my subscription to Realms Plus ends?
store.realmsPlus.faq.answer12=If your subscription ends, you and your friends will no longer be able to play online together on Realms, and you will no longer have access to the packs or cloud storage. You will be able to purchase and download any world templates saved to your Realm and cloud storage within 18 months of ending your subscription unless they are no longer offered by Minecraft. If you used Realms Plus worlds, texture packs, or mash-ups, you will need to purchase those packs in the store or, if those packs are still in Realms Plus, re-subscribe, to play those world templates.
store.realmsPlus.faq.question13=How many world templates can I play with Realms Plus?
store.realmsPlus.faq.answer13=You can play and save ALL the packs included in the Realms Plus on your local device.  As well, you can host one of your world templates on a Realm for your friends to play with you for free!  You can change which world you load on your Realm anytime.


store.realmsPlus.content.skinDescription=Change your look with skins!
store.realmsPlus.content.worldDescription=Explore popular content packs!
store.realmsPlus.content.textureDescription=New visuals for your worlds!
store.realmsPlus.content.mashupDescription=Try a bit of everything in mash-ups!

store.realmsPlus.content.popularPacks=Popular Packs in Realms Plus:
store.realmsPlus.content.viewAllPacks=VIEW ALL PACKS
store.realmsPlus.content.contentDescription=Tap into the source of amazing Minecraft content! With Realms Plus, you get instant access to 50+ marketplace items like mash-ups, worlds, skin packs and epic adventures – with new additions each month. Your content is stored on your own personal server, and made available on any platform where you enjoy both Realms and the Minecraft Marketplace. Up to 10 players can play at one time, and they get access to the worlds in your Realm for free!


store.realmsPlus.freeTrial=START 1 MONTH FREE TRIAL

store.inventory.button=Inventory
store.inventory.title=Inventory

store.free=Free
store.redeem=Get
store.incompatible=Incompatible
store.incompatibleOnDevice=Incompatible on Device
store.itemIsIncompatible=This item is not compatible with your device.
store.itemMayNotBeCompatible=This item may not be compatible with your device.
store.owned=Owned
store.owned.legacy=Owned (P)
store.unlocked=Unlocked
store.mypacks=%s's Packs

store.error=Marketplace Error

store.toast.downloadStarted=Download Started: %s
store.toast.downloadComplete=Download Complete: %s
store.toast.downloadResumed=Download Resumed: %s
store.toast.downloadFailed=Download Failed: %s
store.toast.downloadPaused=Download Paused: %s

store.popup.goBack=Go Back
store.popup.xblRequired.title=Sign in for Free
store.popup.xblRequired.button1=Sign In
store.popup.xblRequired.button2=Cancel
store.popup.xblRequired.message=You need to sign in before unlocking items in the store so that we can remember what you have already unlocked.
store.popup.download.title.KB=Download Content? - %s KB
store.popup.download.title.MB=Download Content? - %s MB
store.popup.download.msg=This will download the %s to your device.
store.popup.downloadCell.title.KB=Download Over Cellular? - %s KB
store.popup.downloadCell.title.MB=Download Over Cellular? - %s MB
store.popup.downloadCell.msg=You are not connected to Wi-Fi.  Do you want to download the %s to your device over your cellular network?
store.popup.download.button1=Download
store.popup.download.button2=Skip
store.popup.download.back=Go Back
store.popup.wifiWarn.title=Warning
store.popup.wifiWarn.msg=You are not connected to WiFi, download anyway?
store.popup.download.noInternet.title=Something Went Wrong
store.popup.download.noInternet.msg=Sorry, we were unable to download the %s. Maybe check your internet connection?
store.popup.download.noSpace.title.KB=Not Enough Space - %s KB
store.popup.download.noSpace.title.MB=Not Enough Space - %s MB
store.popup.download.noSpace.msg=You do not have enough space available on your device to download the %s.
store.popup.download.unOwned.title=Purchase Pack?
store.popup.download.unOwned.msg=Would you like to purchase the packs used by this world?
store.popup.download.unOwnedTemplate.title=Purchase World Template?
store.popup.download.unOwnedTemplate.msg=This world was created using a template that you have not unlocked. You must purchase the template to unlock this world.  Would you like to purchase the template used by this world?
store.popup.downloadWorldTemplate.noInternet.title=Need Internet for Template
store.popup.downloadWorldTemplate.noInternet.msg=We're unable to connect to the Marketplace to download the template used to create this world. That template contained an Add-On pack that you need to continue playing this world. Reconnect to the internet and download the template to continue.
store.popup.downloadWorldTemplate.noSpace.msg=You do not have enough space to download the template used to create this world.  That template contained an Add-On pack that you need to continue playing this world.  Clear up space on your device to continue.
store.popup.downloadWorldTemplate.title.KB=Download Missing Template? - %s KB
store.popup.downloadWorldTemplate.title.MB=Download Missing Template? - %s MB
store.popup.downloadWorldTemplate.msg=The template used to create this world is missing.  That template contained an Add-On pack that you need to continue playing this world.
store.popup.downloadWorldTemplate.msg.removePacks=You can go to this world's resource and behavior pack settings and remove those packs to continue, but the world may no longer be playable.
store.popup.downloadWorldTemplate.download=Download Template
store.popup.downloadWorldTemplateCell.download=Download Template Over Cellular
store.popup.update.title.KB=Update Content? - %s KB
store.popup.update.title.MB=Update Content? - %s MB
store.popup.update.msg=This will update the %s on your device.
store.popup.updateCell.title.KB=Update Over Cellular? - %s KB
store.popup.updateCell.title.MB=Update Over Cellular? - %s MB
store.popup.updateCell.msg=You are not connected to Wi-Fi.  Do you want to update the %s on your device over your cellular network?
store.popup.update.button1=Update
store.popup.update.button2=Skip
store.popup.update.back=Go Back
store.popup.purchaseInProgress.title=Purchase in progress
store.popup.purchaseInProgress.msg=This shouldn't take long.
store.popup.purchaseFailed.title=Something Went Wrong
store.popup.purchaseFailed.msg=Sorry, we were unable to complete your purchase. Maybe check your internet connection?
store.popup.purchaseFailedInsufficientFunds.title=You Need More Minecoins
store.popup.purchaseFailedInsufficientFunds.msg=You do not have enough Minecoins to unlock this item. You can buy Minecoins from your app store.
store.popup.purchaseFailedInsufficientFunds.buyButton=Get Minecoins
store.popup.purchasePriceMismatch.msg=Sorry, we were unable to complete your purchase.  Please try again later.
store.popup.trialUpgradeFail.title=Game Unlock Error
store.popup.trialUpgradeFail.msg=It looks like you are signed into a different Microsoft Account from the one that first downloaded the Minecraft Trial. Try again after signing in with that account.
store.popup.downloaded.unOwnedTemplate.title=Purchase World Template?
store.popup.downloaded.unOwnedTemplate.msg=You do not own this template, and must purchase it to unlock it.  Would you like to purchase the template?
store.purchase.success=You just bought: %s
store.purchase.bundle=Unlock %d of %d Packs?
store.purchase.bundle.confirm=Unlock now!
store.purchase.bundle.partiallyOwnedWarning=Just so you know, you already own the following packs: %s.  Would you like to unlock the rest of the packs in this bundle?
store.purchase.bundle.owned=Owned
store.purchase.bundle.unowned=You'll get
store.createdBy=By %s
store.seeMoreBy=See more by %s
store.seeMoreBy.multiCreatorBundle=See More by Bundle Creators
store.fetchingItem=Connecting to Marketplace...
 
store.timer.daysAbbreviated=%sd	## This is to let player know how many days are left in a sale. It needs to be abbreviated with no more than 3 characters
store.timer.hoursAbbreviated=%shr	## This is to let player know how many days are left in a sale. It needs to be abbreviated with no more than 3 characters
store.timer.minutesAbbreviated=%smin	## This is to let player know how many days are left in a sale. It needs to be abbreviated with no more than 3 characters
store.timer.secondsAbbreviated=%ssec	## This is to let player know how many days are left in a sale. It needs to be abbreviated with no more than 3 characters
store.timer.left=%s left

store.startMenu.sale=Sale!

store.progress.fetchingProducts=Getting products
store.progress.checkingDownload=Checking download

store.content.download=Download
store.content.update=Update
store.content.updating=Updating...
store.content.requireXbl.signedIn=Download for free with a Microsoft Account.
store.content.requireXbl.notSignedIn=Sign in to a Microsoft Account to unlock.

store.mashup.count.texturePack=1 Texture Pack
store.mashup.count.texturePacks=%s Texture Packs	#number of texture packs
store.mashup.count.world=1 World 
store.mashup.count.worlds=%s Worlds	#number of worlds
store.mashup.count.skin=1 Skin

store.mashup.bundle=Bundle
store.mashup.bundle.multipleCreators=Multiple Creators
store.mashup.continue=Continue
store.mashup.createWorld=Create this World!
store.mashup.leaveWorldToCreate=Must Exit World Before Creating
store.mashup.equipSkin=Equip
store.mashup.equipThisSkin=Equip the %s Skin!
store.mashup.readMore=Read More
store.mashup.readLess=Read Less
store.mashup.mashupPack=Mash-up Pack
store.mashup.skinPack=Skin Pack
store.mashup.texturePack=Texture Pack
store.mashup.ratings=Ratings
store.mashup.unlockAll=Unlock all %s Skins, Texture Pack, and World for %s:minecoin:
store.mashup.unlockMashup=Unlock Mash-up Pack for %s:minecoin:
store.mashup.world=World
store.mashup.missingContent=Unable to complete action. Content missing locally.
store.mashup.title.bundles=%s Packs Included in Bundle!
store.mashup.title.bundleUpsell=Get This & More in a Bundle!
store.mashup.title.resourcePack=Take a tour!
store.mashup.title.skinPack=%s: %s
store.mashup.title.worldView=Journey in a new world!
store.mashup.title.ratings=Community Ratings
store.mashup.title.recentlyViewed=Recently Viewed
store.mashup.title.relatedItems=Related Items
store.mashup.mashupContentsFull=%s Skins, Texture Pack, and World
store.mashup.mashupContentsSkinAndTexturePack=%s Skins and a Texture Pack
store.mashup.mashupContentsSkinAndWorld=%s Skins and a World
store.mashup.mashupContentsTexturePackAndWorld=Texture Pack and a World
store.mashup.mashupContentsSkinPack=%s Skins
store.mashup.purchase.generalContent=Purchase content for %s:minecoin:
store.mashup.last_updated=Last Updated: %s
store.mashup.up_to_date=Pack is up to date
store.mashup.out_of_date_tooltip=This pack may not have skins for items/blocks released after its latest update
store.mashup.description=DESCRIPTION

store.new.icon=New

store.offerDescription.title=Pack Description

store.ratings.ratingsCount=%s Ratings
store.ratings.ratingOutOfFive=%s out of 5 stars
store.ratings.rateContent=Rate this Pack
store.ratings.rateContentGeneral=Rate this Content
store.ratings.yourRating=Your Rating: 
store.ratings.stars= %s Stars
store.ratings.rate=Rate this pack!

store.ratings.signIn.title=Rate Items in the Marketplace
store.ratings.signIn.description1=Tell the creator and the community about your experience with this item!
store.ratings.signIn.description2=Before we can let you rate an item, you will need to sign in with a Microsoft Account.

store.purchase.success=You just bought: %s
store.purchase.success.realMoney1=You just unlocked: %s for %s 
store.purchase.success.realMoney2=+%s:minecoin:
store.purchase.signIn=Expand your game with tons of great content.
store.purchase.signInPart2=Sign in with a Microsoft Account to start using the Marketplace!
store.purchase.realmoney.disclaimer=*Buys %s Minecoins for %s and redeems this pack for %s Minecoins leaving %s left over.
store.purchase.notAvailable=Currently Not Available

store.bundleUpsell.contents.single=+1 More Pack
store.bundleUpsell.contents.plural=+%s More Packs

store.coins.currentCoins=You Have %s Minecoins
store.coins.purchase.confirmation=Get more coins to unlock this pack?
store.coins.purchase.coinsLeftToBuy=You need %s more coins to unlock this pack.
store.coins.purchase=Purchase Minecoins
store.coins.offer0=Stack of Minecoins!
store.coins.offer1=Pile of Minecoins!
store.coins.offer2=Mountain of Minecoins!
store.coins.header=Use Minecoins to unlock all kinds of cool stuff!
store.coins.value=Best Value!
store.coins.purchase.toast.withCoinCount=You just bought %s Minecoins!
store.coins.purchase.toast.unknownCoinCount=You just bought Minecoins!
store.coins.purchased.failed.title=Something Went Wrong
store.coins.purchased.failed.body=We were unable to connect to the app store. Maybe sign in to the app store or try checking your internet connection.
store.coins.tooManyCoins=Use some of your Minecoins and then you can buy more.
store.coins.incomplete.title=Incomplete Minecoin Purchase
store.coins.incomplete.fulfill.a=It looks like we didn't finish things last time. Let's complete your purchase now.
store.coins.incomplete.fulfill.b=It looks like you started a purchase with a different Microsoft Account. Do you want to complete the purchase with this account instead?
store.coins.incomplete.fulfill.c=Someone started a purchase with the billing account on this device. Would you like to complete the purchase with this account?
store.coins.fixAppReceipt.title=Something went wrong
store.coins.fixAppReceipt.body=There appears to be a problem with your App Receipt, please sign in to continue.
store.coins.fixAppReceipt.button=Sign In to Store

store.featured.realms.title=Try Realms Free
store.featured.realms.desc=Experience the best way to play Minecraft with your friends.

store.hyperlink.creator.notFound=Sorry, but the creator specified in the hyperlink was not found.
store.hyperlink.ingame=Sorry, hyperlinks are not allowed while in a world.  Please exit world and try again.

store.resource.try=Try it out!
store.resource.create=Manage your Resource Packs
store.resource.screenshots=Screenshots
store.resourcePack.activateTexturePack=Activate Texture Pack!
store.resourcePack.unlock=Unlock this texture pack for %s:minecoin:
store.bundle.unlock=Unlock this bundle for %s:minecoin:
store.3pserverItem.fetchingCancel=Cancel
store.3pserverItem.fetchingItem=%s has something fun to share with you.
store.3pserverItem.fetchingItemTitle=Just a moment
store.3pserverItem.unlock=Unlock for %s
store.3pserverItem.alreadyPurchased=Owned
store.world.unlock=Unlock this world for %s:minecoin:
store.restore.description=We're restoring your purchases!
store.restore.failed=Sorry, we were unable to restore your purchases. Maybe check your internet connection?
store.connection.failed.title=Something Went Wrong
store.connection.failed.body=We're unable to connect to the Marketplace. Maybe check your internet connection?
store.createdBy=By %s
store.featured.createdBy=Created by %s
store.downloading=Downloading...
store.downloading.title=Downloading: %s
store.updating=Updating...
store.updating.title=Updating: %s
store.fetchingItem=Connecting to Marketplace...
store.importing.title=Importing: %s
store.importing=Importing...
store.showMore=See All %d
store.inventory.no.xbl=My Packs

store.promo.today=Today Only!
store.promo.upsell=Don't Miss Today's Free Gift!
store.promo.comeBackTomorrow=Come back for Tomorrow's Free Gift!
store.promo.end=Happy New Year!
store.promo.comingSoon=12 days of Minecraft starts tomorrow!

store.promo.holiday.first=On the first day of Minecraft,
store.promo.holiday.second=On the second day of Minecraft,
store.promo.holiday.third=On the third day of Minecraft,
store.promo.holiday.fourth=On the fourth day of Minecraft,
store.promo.holiday.fifth=On the fifth day of Minecraft,
store.promo.holiday.sixth=On the sixth day of Minecraft,
store.promo.holiday.seventh=On the seventh day of Minecraft,
store.promo.holiday.eighth=On the eighth day of Minecraft,
store.promo.holiday.ninth=On the ninth day of Minecraft,
store.promo.holiday.tenth=On the tenth day of Minecraft,
store.promo.holiday.eleventh=On the eleventh day of Minecraft,
store.promo.holiday.twelfth=On the twelfth day of Minecraft,

store.promo.date.jan=Jan. %s
store.promo.date.feb=Feb. %s
store.promo.date.mar=Mar. %s
store.promo.date.apr=Apr. %s
store.promo.date.may=May %s
store.promo.date.jun=Jun. %s
store.promo.date.july=July %s
store.promo.date.aug=Aug. %s
store.promo.date.sep=Sep. %s
store.promo.date.oct=Oct. %s
store.promo.date.nov=Nov. %s
store.promo.date.dec=Dec. %s

store.myAccount=My Account
store.xbl.signin=Sign In
store.xbl.signinOrSignUp=Sign In or Sign Up For Free
store.xbl.disconnected=Disconnected
store.xbl.conversionFailedTitle=Something Went Wrong
store.xbl.conversionFailedMessage=Please try again later.

store.sales.allByCreator=On Sale Now!
store.sales.bundlesOnSale=Bundles on Sale Now!
store.sales.mashupsOnSale=Mash-ups on Sale Now!
store.sales.skinsOnSale=Skins on Sale Now!
store.sales.texturesOnSale=Texture Packs on Sale Now!
store.sales.worldsOnSale=Worlds on Sale Now!

store.showalloffers.list.title=%s Store 
store.showalloffers.list.back=Go Back

store.suggestedOffers.defaultTitle=Fun Ways to Play

store.minecoin.notavailable=Coin purchase is not available on this version of Minecraft.

store.itemDetail.back=Details

store.search.allResults=%d Results
store.search.dynamicPlaceHolderText=Try searching for %s!
store.search.error.searchStringAndFilters=Oops! There are no results for "%s" and the selected filters. Try broadening search...
store.search.error.searchStringAndNoFilters=Oops! There are no results for "%s". Try another phrase...
store.search.error.emptySearchStringAndFilters=Oops! No results were found with the selected filters. Try broadening search...
store.search.error.emptySearch=Oops! We can't search for nothing.
store.search.greyListedResults= -- Only showing content that will work on this device.
store.search.noConnectionErrorText=Oops! We think you are disconnected from the internet. Check your connection and try again!
store.search.placeHolderText=Enter Search Here...
store.search.pluralResults= Results
store.search.pluralPossibleResults= Possible Results
store.search.singularResult= Result
store.search.singularPossibleResult= Possible Result
store.search.title=Search
store.search.trendingRowTitle=Trending Search: %s
store.search.button=Search

store.search.filters.bundles=Bundles
store.search.filters.clear=Clear Filters
store.search.filters.reset=Reset Filters

store.search.filter.clear.creator=Clear Creator Filters
store.search.filter.creator=Creators
store.search.filter.creator.selectedCount=%d Creators

store.search.filter.offer_type=Offer Types
store.search.filter.offer_type.selectedCount=%d Offer Types
store.search.filter.offer_type.bundles=Bundles
store.search.filter.offer_type.realmsplus=Realms Plus

store.search.filter.clear.pack_type=Clear Pack Type Filters
store.search.filter.mashupPacks=Mash-ups
store.search.filter.andSelectedText=and %s
store.search.filter.pack_type=Pack Types
store.search.filter.pack_type.selectedCount=%d Pack Types
store.search.filter.minecoin=Minecoins
store.search.filter.minecoin.selectedCount=%d Minecoin Filters
store.search.filter.clear.minecoin=Clear Minecoin Filters
store.search.filter.rating=Ratings
store.search.filter.rating.selectedCount=%d Rating Filters
store.search.filter.clear.rating=Clear Rating Filters
store.search.filter.skinPacks=Skins
store.search.filter.texturePacks=Textures
store.search.filter.title=Filters
store.search.filter.worldTemplates=Worlds
store.search.filter.installed_state=Installed State
store.search.filter.installed=Installed
store.search.filter.notInstalled=Not Installed
store.search.filter.clear.installed=Reset Installed Filters

store.search.sort_menu.sortConstWord=Sort
store.search.sort_menu.Default=Relevance
store.search.sort_menu.NewestFirst=Newest First
store.search.sort_menu.OldestFirst=Oldest First
store.search.sort_menu.A_to_Z=A to Z
store.search.sort_menu.Z_to_A=Z to A
store.search.sort_menu.Installed=Installed
store.search.sort_menu.notInstalled=Not Installed
store.search.sort_menu.HighestPrice=:minecoin: High to Low
store.search.sort_menu.LowestPrice=:minecoin: Low to High
store.search.sort_menu.HighestRating=Rating: High to Low
store.search.sort_menu.LowestRating=Rating: Low to High
store.search.sort_menu.resultText=by %s 

store.shareDescription=Whoa! %s made such a cool pack. You've got to check this out.
store.copyToastMessage=Link copied to clipboard!

store.uploadContentToRealmsSuccess=Content successfully uploaded to the selected Realm.
store.uploadContentToRealmsFail.title=Error
store.uploadContentToRealmsProgressTitle=Applying content to Realm
store.uploadContentToRealmsProgressText=The selected content is being applied to your Realm.
store.uploadContentToRealmsFail.message=Failed to upload content to the selected Realm!
store.uploadContentToRealmsFail.forbidden.message=You do not own one or more of the applied content!
store.applyToRealm=Create on Realm
store.inRealmsPlus=In Realms Plus
store.uploadWorldTitle=Replace World?
store.uploadPackTitle=Replace Pack?
store.uploadWorldMessage=This will remove your current world from your Realm and let you replace it with a new one. Your Realm members won't have access to your current world anymore. Do you want to continue?
store.uploadPackMessage=This will remove all current applied resource and behavior packs from your Realm and replace it with the selected pack. Do you want to continue?

stream.confirm_start=Are you sure you want to start broadcasting?
stream.unavailable.account_not_bound=Before you can broadcast Minecraft through Twitch, you will need to link your Twitch account on mojang.com. Would you like to do that now?
stream.unavailable.account_not_bound.okay=Link Accounts
stream.unavailable.account_not_migrated=Before you can broadcast Minecraft through Twitch, you will need to migrate your Minecraft account to a Mojang account. Would you like to do that now?
stream.unavailable.account_not_migrated.okay=Migrate Account
stream.unavailable.failed_auth=Authentication to Twitch failed. Please go to mojang.com and rebind your Twitch account.
stream.unavailable.failed_auth.okay=Rebind Accounts
stream.unavailable.failed_auth_error=Unable to authenticate to Twitch. Please try again later.
stream.unavailable.initialization_failure=Unable to initialize the Twitch SDK.
stream.unavailable.initialization_failure.extra=(Reason: %s)
stream.unavailable.library_arch_mismatch=The custom java version used to launch Minecraft has a different architecture than the one used to run the launcher. Please make sure these are the same, either 32-bit or 64-bit for both.
stream.unavailable.library_failure=Unable to load the libraries needed for the integrated Twitch broadcasting service.
stream.unavailable.no_fbo=Your video card needs to support at least OpenGL version 3.0 or support Framebuffer Objects via an extension to use the integrated Twitch broadcasting.
stream.unavailable.no_fbo.arb=Framebuffer object support via ARB is: %s
stream.unavailable.no_fbo.blend=Separate blending support via EXT is: %s
stream.unavailable.no_fbo.ext=Framebuffer object support via EXT is: %s
stream.unavailable.no_fbo.version=You are currently using: %s
stream.unavailable.not_supported.mac=Unfortunately the integrated Twitch broadcasting on Mac requires a version of OSX newer than the one you are on. You must use 10.7 (Mac OS X Lion) or newer to be able to use this service. Would you like to visit apple.com to learn about upgrading?
stream.unavailable.not_supported.mac.okay=Upgrade
stream.unavailable.not_supported.other=Unfortunately the integrated Twitch broadcasting service requires Windows (Vista or newer) or Mac OS X (10.7/Lion or newer)
stream.unavailable.not_supported.windows=Unfortunately the integrated Twitch broadcasting requires a newer version of Windows than you are on. You must have at least Windows Vista or newer.
stream.unavailable.report_to_mojang=Report to Mojang
stream.unavailable.soundflower.chat=Soundflower is required to be able to stream on Mac. %s
stream.unavailable.soundflower.chat.link=Please click here to install it.
stream.unavailable.title=Twitch Broadcasting Unavailable
stream.unavailable.unknown=Unfortunately you cannot broadcast to Twitch at this time. And we don't know why :'(
stream.unavailable.unknown.chat=Could not start stream: %s
stream.user.mode.administrator=Twitch Administrator
stream.user.mode.banned=Banned
stream.user.mode.banned.other=Banned on %s's channel
stream.user.mode.banned.self=Banned on your channel
stream.user.mode.broadcaster=Broadcaster
stream.user.mode.broadcaster.other=Broadcaster
stream.user.mode.broadcaster.self=Broadcaster (You!)
stream.user.mode.moderator=Moderator
stream.user.mode.moderator.other=Moderator on %s's channel
stream.user.mode.moderator.self=Moderator on your channel
stream.user.mode.staff=Twitch Staff
stream.user.subscription.subscriber=Subscriber
stream.user.subscription.subscriber.other=Subscriber to %s's channel
stream.user.subscription.subscriber.self=Subscriber to your channel
stream.user.subscription.turbo=Twitch Turbo
stream.userinfo.ban=Ban
stream.userinfo.chatTooltip=Click to manage user
stream.userinfo.mod=Promote to Moderator
stream.userinfo.timeout=Timeout
stream.userinfo.unban=Unban
stream.userinfo.unmod=Demote from Moderator

profanity_filter.title=Profanity Filter
profanity_filter.msg=The text you entered contains words that others may find offensive and cannot be used. Please try again.

terms_and_conditions.goBack=Go Back
terms_and_conditions.viewTermsAndConditions=To view terms and conditions, please visit https://minecraft.net/terms in any web browser.

tile.acaciaFence.name=Acacia Fence
tile.acacia_fence_gate.name=Acacia Fence Gate
tile.activator_rail.name=Activator Rail
tile.allow.name=Allow
tile.air.name=Air
tile.deny.name=Deny
tile.border_block.name=Border
tile.anvil.intact.name=Anvil
tile.anvil.name=Anvil
tile.anvil.slightlyDamaged.name=Slightly Damaged Anvil
tile.anvil.veryDamaged.name=Very Damaged Anvil
tile.barrier.name=Barrier
tile.beacon.name=Beacon
tile.beacon.primary=Primary Power
tile.beacon.secondary=Secondary Power
tile.beacon.primary.pocket=Primary
tile.beacon.secondary.pocket=Secondary
tile.beehive.name=Beehive
tile.bee_nest.name=Bee Nest
tile.bed.name=Bed
tile.bed.noSleep=You can only sleep at night
tile.bed.notSafe=You may not rest now, there are monsters nearby
tile.bed.notValid=Your home bed was missing or obstructed
tile.bed.occupied=This bed is occupied
tile.bed.respawnSet=Respawn point set
tile.bed.tooFar=Bed is too far away
tile.bedrock.name=Bedrock
tile.bell.name=Bell
tile.camera.name=Camera
tile.conduit.name=Conduit
tile.invisibleBedrock.name=Invisible Bedrock
tile.beetroot.name=Beetroot
tile.birchFence.name=Birch Fence
tile.birch_fence_gate.name=Birch Fence Gate
tile.blast_furnace.name=Blast Furnace
tile.bone_block.name=Bone Block
tile.coal_block.name=Block of Coal
tile.diamond_block.name=Block of Diamond
tile.emerald_block.name=Block of Emerald
tile.gold_block.name=Block of Gold
tile.iron_block.name=Block of Iron
tile.lapis_block.name=Lapis Lazuli Block
tile.redstone_block.name=Block of Redstone
tile.bookshelf.name=Bookshelf
tile.brick_block.name=Brick Block
tile.brown_mushroom.name=Mushroom
tile.brown_mushroom_block.name=Mushroom
tile.wooden_button.name=Oak Button
tile.acacia_button.name=Acacia Button
tile.birch_button.name=Birch Button
tile.dark_oak_button.name=Dark Oak Button
tile.jungle_button.name=Jungle Button
tile.spruce_button.name=Spruce Button
tile.stone_button.name=Stone Button
tile.cactus.name=Cactus
tile.cake.name=Cake
tile.dried_kelp_block.name=Dried Kelp Block
tile.carrot.name=Carrots
tile.carved_pumpkin.name=Carved Pumpkin
tile.cauldron.name=Cauldron
tile.chalkboard.oneByOne.name=Slate
tile.chalkboard.twoByOne.name=Poster
tile.chalkboard.threeByTwo.name=Board
tile.chest.name=Chest
tile.ender_chest.name=Ender Chest
tile.jigsaw.name=Jigsaw Block
tile.honey_block.name=Honey Block
tile.honeycomb_block.name=Honeycomb Block
tile.trapped_chest.name=Trapped Chest
tile.shulkerBoxWhite.name=White Shulker Box
tile.shulkerBoxOrange.name=Orange Shulker Box
tile.shulkerBoxMagenta.name=Magenta Shulker Box
tile.shulkerBoxLightBlue.name=Light Blue Shulker Box
tile.shulkerBoxYellow.name=Yellow Shulker Box
tile.shulkerBoxLime.name=Lime Shulker Box
tile.shulkerBoxPink.name=Pink Shulker Box
tile.shulkerBoxGray.name=Gray Shulker Box
tile.shulkerBoxSilver.name=Light Gray Shulker Box
tile.shulkerBoxCyan.name=Cyan Shulker Box
tile.shulkerBoxPurple.name=Purple Shulker Box
tile.shulkerBoxBlue.name=Blue Shulker Box
tile.shulkerBoxBrown.name=Brown Shulker Box
tile.shulkerBoxGreen.name=Green Shulker Box
tile.shulkerBoxRed.name=Red Shulker Box
tile.shulkerBoxBlack.name=Black Shulker Box
tile.shulkerBox.name=Shulker Box
tile.chorus_flower.name=Chorus Flower
tile.chorus_plant.name=Chorus Plant
tile.stained_glass.white.name=White Stained Glass
tile.stained_glass.silver.name=Light Gray Stained Glass
tile.stained_glass.gray.name=Gray Stained Glass
tile.stained_glass.black.name=Black Stained Glass
tile.stained_glass.brown.name=Brown Stained Glass
tile.stained_glass.red.name=Red Stained Glass
tile.stained_glass.orange.name=Orange Stained Glass
tile.stained_glass.yellow.name=Yellow Stained Glass
tile.stained_glass.lime.name=Lime Stained Glass
tile.stained_glass.green.name=Green Stained Glass
tile.stained_glass.cyan.name=Cyan Stained Glass
tile.stained_glass.light_blue.name=Light Blue Stained Glass
tile.stained_glass.blue.name=Blue Stained Glass
tile.stained_glass.purple.name=Purple Stained Glass
tile.stained_glass.magenta.name=Magenta Stained Glass
tile.stained_glass.pink.name=Pink Stained Glass
tile.stained_glass_pane.white.name=White Stained Glass Pane
tile.stained_glass_pane.silver.name=Light Gray Stained Glass Pane
tile.stained_glass_pane.gray.name=Gray Stained Glass Pane
tile.stained_glass_pane.black.name=Black Stained Glass Pane
tile.stained_glass_pane.brown.name=Brown Stained Glass Pane
tile.stained_glass_pane.red.name=Red Stained Glass Pane
tile.stained_glass_pane.orange.name=Orange Stained Glass Pane
tile.stained_glass_pane.yellow.name=Yellow Stained Glass Pane
tile.stained_glass_pane.lime.name=Lime Stained Glass Pane
tile.stained_glass_pane.green.name=Green Stained Glass Pane
tile.stained_glass_pane.cyan.name=Cyan Stained Glass Pane
tile.stained_glass_pane.light_blue.name=Light Blue Stained Glass Pane
tile.stained_glass_pane.blue.name=Blue Stained Glass Pane
tile.stained_glass_pane.purple.name=Purple Stained Glass Pane
tile.stained_glass_pane.magenta.name=Magenta Stained Glass Pane
tile.stained_glass_pane.pink.name=Pink Stained Glass Pane
tile.clay.name=Clay Block
tile.hardened_clay.name=Terracotta
tile.stained_hardened_clay.black.name=Black Terracotta
tile.stained_hardened_clay.blue.name=Blue Terracotta
tile.stained_hardened_clay.brown.name=Brown Terracotta
tile.stained_hardened_clay.cyan.name=Cyan Terracotta
tile.stained_hardened_clay.gray.name=Gray Terracotta
tile.stained_hardened_clay.green.name=Green Terracotta
tile.stained_hardened_clay.lightBlue.name=Light Blue Terracotta
tile.stained_hardened_clay.lime.name=Lime Terracotta
tile.stained_hardened_clay.magenta.name=Magenta Terracotta
tile.stained_hardened_clay.name=Terracotta
tile.stained_hardened_clay.orange.name=Orange Terracotta
tile.stained_hardened_clay.pink.name=Pink Terracotta
tile.stained_hardened_clay.purple.name=Purple Terracotta
tile.stained_hardened_clay.red.name=Red Terracotta
tile.stained_hardened_clay.silver.name=Light Gray Terracotta
tile.stained_hardened_clay.white.name=White Terracotta
tile.stained_hardened_clay.yellow.name=Yellow Terracotta
tile.structure_block.name=Structure Block
tile.structure_void.name=Structure Void
tile.wool.black.name=Black Wool
tile.wool.blue.name=Blue Wool
tile.wool.brown.name=Brown Wool
tile.wool.cyan.name=Cyan Wool
tile.wool.gray.name=Gray Wool
tile.wool.green.name=Green Wool
tile.wool.lightBlue.name=Light Blue Wool
tile.wool.lime.name=Lime Wool
tile.wool.magenta.name=Magenta Wool
tile.wool.name=Wool
tile.wool.orange.name=Orange Wool
tile.wool.pink.name=Pink Wool
tile.wool.purple.name=Purple Wool
tile.wool.red.name=Red Wool
tile.wool.silver.name=Light Gray Wool
tile.wool.white.name=White Wool
tile.wool.yellow.name=Yellow Wool
tile.cobblestone_wall.mossy.name=Mossy Cobblestone Wall
tile.cobblestone_wall.normal.name=Cobblestone Wall 
tile.cobblestone_wall.granite.name=Granite Wall
tile.cobblestone_wall.diorite.name=Diorite Wall
tile.cobblestone_wall.andesite.name=Andesite Wall
tile.cobblestone_wall.sandstone.name=Sandstone Wall
tile.cobblestone_wall.brick.name=Brick Wall
tile.cobblestone_wall.stone_brick.name=Stone Brick Wall
tile.cobblestone_wall.mossy_stone_brick.name=Mossy Stone Brick Wall
tile.cobblestone_wall.nether_brick.name=Nether Brick Wall
tile.cobblestone_wall.end_brick.name=End Stone Brick Wall
tile.cobblestone_wall.prismarine.name=Prismarine Wall
tile.cobblestone_wall.red_sandstone.name=Red Sandstone Wall
tile.cobblestone_wall.red_nether_brick.name=Red Nether Brick Wall
tile.cocoa.name=Cocoa
tile.command_block.name=Command Block
tile.composter.name=Composter
tile.light_block.name=Light Block
tile.repeating_command_block.name=Repeating Command Block
tile.chain_command_block.name=Chain Command Block
tile.wheat.name=Crops
tile.darkOakFence.name=Dark Oak Fence
tile.dark_oak_fence_gate.name=Dark Oak Fence Gate
tile.daylight_detector.name=Daylight Sensor
tile.deadbush.name=Dead Bush
tile.detector_rail.name=Detector Rail
tile.dirt.coarse.name=Coarse Dirt
tile.dirt.default.name=Dirt
tile.dirt.name=Dirt
tile.podzol.name=Podzol
tile.purpur_block.default.name=Purpur Block
tile.purpur_block.lines.name=Purpur Pillar
tile.purpur_block.chiseled.name=Chiseled Purpur
tile.dispenser.name=Dispenser
tile.iron_door.name=Iron Door
tile.doorWood.name=Wooden Door
tile.double_plant.fern.name=Large Fern
tile.double_plant.grass.name=Double Tallgrass
tile.double_plant.name=Plant
tile.double_plant.paeonia.name=Peony
tile.double_plant.rose.name=Rose Bush
tile.double_plant.sunflower.name=Sunflower
tile.double_plant.syringa.name=Lilac
tile.dragon_egg.name=Dragon Egg
tile.dropper.name=Dropper
tile.enchanting_table.name=Enchantment Table
tile.enderChest.name=Ender Chest
tile.end_portal_frame.name=End Portal
tile.farmland.name=Farmland
tile.fletching_table.name=Fletching Table
tile.fence.name=Oak Fence
tile.fence_gate.name=Oak Fence Gate
tile.iron_bars.name=Iron Bars
tile.fire.name=Fire
tile.yellow_flower.dandelion.name=Dandelion
tile.yellow_flower.name=Flower
tile.red_flower.allium.name=Allium
tile.red_flower.blueOrchid.name=Blue Orchid
tile.red_flower.cornflower.name=Cornflower
tile.red_flower.houstonia.name=Azure Bluet
tile.red_flower.name=Flower
tile.red_flower.lilyOfTheValley.name=Lily of the Valley
tile.red_flower.oxeyeDaisy.name=Oxeye Daisy
tile.red_flower.poppy.name=Poppy
tile.red_flower.tulipOrange.name=Orange Tulip
tile.red_flower.tulipPink.name=Pink Tulip
tile.red_flower.tulipRed.name=Red Tulip
tile.red_flower.tulipWhite.name=White Tulip
tile.wither_rose.name=Wither Rose
tile.furnace.name=Furnace
tile.glass.name=Glass
tile.golden_rail.name=Powered Rail
tile.grass.name=Grass Block
tile.grass_path.name=Grass Path
tile.gravel.name=Gravel
tile.hay_block.name=Hay Bale
tile.netherrack.name=Netherrack
tile.soul_sand.name=Soul Sand
tile.hopper.name=Hopper
tile.ice.name=Ice
tile.packed_ice.name=Packed Ice
tile.blue_ice.name=Blue Ice
tile.iron_trapdoor.name=Iron Trapdoor
tile.jukebox.name=Jukebox
tile.jungleFence.name=Jungle Fence
tile.jungle_fence_gate.name=Jungle Fence Gate
tile.ladder.name=Ladder
tile.flowing_lava.name=Lava
tile.leaves.acacia.name=Acacia Leaves
tile.leaves2.acacia.name=Acacia Leaves
tile.leaves.big_oak.name=Dark Oak Leaves
tile.leaves2.big_oak.name=Dark Oak Leaves
tile.leaves.birch.name=Birch Leaves
tile.leaves.jungle.name=Jungle Leaves
tile.leaves.name=Leaves
tile.leaves.oak.name=Oak Leaves
tile.leaves.spruce.name=Spruce Leaves
tile.lever.name=Lever
tile.glowstone.name=Glowstone
tile.lit_pumpkin.name=Jack o'Lantern
tile.lockedchest.name=Locked chest
tile.log.acacia.name=Acacia Log
tile.log.big_oak.name=Dark Oak Log
tile.log.birch.name=Birch Log
tile.log.jungle.name=Jungle Log
tile.log.name=Log
tile.log.oak.name=Oak Log
tile.log.spruce.name=Spruce Log
tile.magma.name=Magma Block
tile.melon_block.name=Melon
tile.mob_spawner.name=Monster Spawner
tile.monster_egg.brick.name=Infested Stone Brick
tile.monster_egg.chiseledbrick.name=Infested Chiseled Stone Brick
tile.monster_egg.cobble.name=Infested Cobblestone
tile.monster_egg.crackedbrick.name=Infested Cracked Stone Brick
tile.monster_egg.mossybrick.name=Infested Mossy Stone Brick
tile.monster_egg.name=Infested Stone
tile.monster_egg.stone.name=Infested Stone
tile.mushroom.name=Mushroom
tile.noteblock.name=Note Block
tile.mycelium.name=Mycelium
tile.nether_brick.name=Nether Brick Block
tile.red_nether_brick.name=Red Nether Brick
tile.nether_brick_fence.name=Nether Brick Fence
tile.quartz_ore.name=Nether Quartz Ore
tile.netherreactor.active=Active!
tile.netherreactor.builtTooHigh=The nether reactor needs to be built lower down.
tile.netherreactor.builtTooLow=The nether reactor needs to be built higher up.
tile.netherreactor.name=Nether Reactor Core
tile.netherreactor.playersTooFar=All players need to be close to the reactor.
tile.netherreactor.wrongPattern=Not the correct pattern!
tile.nether_wart.name=Nether Wart
tile.nether_wart_block.name=Nether Wart Block
tile.unlit_redstone_torch.name=Redstone Torch
tile.redstone_torch.name=Redstone Torch
tile.obsidian.name=Obsidian
tile.coal_ore.name=Coal Ore
tile.diamond_ore.name=Diamond Ore
tile.emerald_ore.name=Emerald Ore
tile.gold_ore.name=Gold Ore
tile.iron_ore.name=Iron Ore
tile.lapis_ore.name=Lapis Lazuli Ore
tile.redstone_ore.name=Redstone Ore
tile.oreRuby.name=Ruby Ore
tile.observer.name=Observer
tile.piston.name=Piston
tile.sticky_piston.name=Sticky Piston
tile.portal.name=Portal
tile.potatoes.name=Potatoes
tile.stone_pressure_plate.name=Stone Pressure Plate
tile.wooden_pressure_plate.name=Oak Pressure Plate
tile.acacia_pressure_plate.name=Acacia Pressure Plate
tile.birch_pressure_plate.name=Birch Pressure Plate
tile.dark_oak_pressure_plate.name=Dark Oak Pressure Plate
tile.jungle_pressure_plate.name=Jungle Pressure Plate
tile.spruce_pressure_plate.name=Spruce Pressure Plate
tile.prismarine.bricks.name=Prismarine Bricks
tile.prismarine.dark.name=Dark Prismarine
tile.prismarine.rough.name=Prismarine
tile.pumpkin.name=Pumpkin
tile.pumpkin_stem.name=Pumpkin Stem
tile.quartz_block.chiseled.name=Chiseled Quartz Block
tile.quartz_block.default.name=Block of Quartz
tile.quartz_block.lines.name=Pillar Quartz Block
tile.quartz_block.smooth.name=Smooth Quartz Block
tile.quartz_block.name=Block of Quartz
tile.rail.name=Rail
tile.red_mushroom.name=Mushroom
tile.red_mushroom_block.name=Mushroom
tile.red_sandstone.chiseled.name=Chiseled Red Sandstone
tile.red_sandstone.default.name=Red Sandstone
tile.red_sandstone.name=Red Sandstone
tile.red_sandstone.smooth.name=Smooth Red Sandstone
tile.red_sandstone.cut.name=Cut Red Sandstone
tile.redstone_wire.name=Redstone Dust
tile.redstone_lamp.name=Redstone Lamp
tile.reeds.name=Sugar cane
tile.sand.default.name=Sand
tile.sand.name=Sand
tile.sand.red.name=Red Sand
tile.sandstone.chiseled.name=Chiseled Sandstone
tile.sandstone.default.name=Sandstone
tile.sandstone.name=Sandstone
tile.sandstone.smooth.name=Smooth Sandstone
tile.sandstone.cut.name=Cut Sandstone
tile.sapling.acacia.name=Acacia Sapling
tile.sapling.big_oak.name=Dark Oak Sapling
tile.sapling.birch.name=Birch Sapling
tile.sapling.jungle.name=Jungle Sapling
tile.sapling.oak.name=Oak Sapling
tile.sapling.spruce.name=Spruce Sapling
tile.seaLantern.name=Sea Lantern
tile.standing_sign.name=Sign
tile.spruce_standing_sign.name=Spruce Sign
tile.birch_standing_sign.name=Birch Sign
tile.jungle_standing_sign.name=Jungle Sign
tile.acacia_standing_sign.name=Acacia Sign
tile.darkoak_standing_sign.name=Dark Oak Sign
tile.slime.name=Slime Block
tile.snow.name=Snow
tile.sponge.dry.name=Sponge
tile.sponge.wet.name=Wet Sponge
tile.spruceFence.name=Spruce Fence
tile.spruce_fence_gate.name=Spruce Fence Gate
tile.brick_stairs.name=Brick Stairs
tile.nether_brick_stairs.name=Nether Brick Stairs
tile.quartz_stairs.name=Quartz Stairs
tile.smooth_quartz_stairs.name=Smooth Quartz Stairs
tile.red_sandstone_stairs.name=Red Sandstone Stairs
tile.sandstone_stairs.name=Sandstone Stairs
tile.stone_stairs.name=Cobblestone Stairs
tile.normal_stone_stairs.name=Stone Stairs
tile.stone_brick_stairs.name=Stone Brick Stairs
tile.oak_stairs.name=Oak Wood Stairs
tile.acacia_stairs.name=Acacia Wood Stairs
tile.birch_stairs.name=Birch Wood Stairs
tile.dark_oak_stairs.name=Dark Oak Wood Stairs
tile.jungle_stairs.name=Jungle Wood Stairs
tile.spruce_stairs.name=Spruce Wood Stairs
tile.purpur_stairs.name=Purpur Stairs
tile.prismarine_stairs.name=Prismarine Stairs
tile.dark_prismarine_stairs.name=Dark Prismarine Stairs
tile.prismarine_bricks_stairs.name=Prismarine Brick Stairs
tile.granite_stairs.name=Granite Stairs
tile.diorite_stairs.name=Diorite Stairs
tile.andesite_stairs.name=Andesite Stairs
tile.polished_granite_stairs.name=Polished Granite Stairs
tile.polished_diorite_stairs.name=Polished Diorite Stairs
tile.polished_andesite_stairs.name=Polished Andesite Stairs
tile.mossy_stone_brick_stairs.name=Mossy Stone Brick Stairs
tile.smooth_red_sandstone_stairs.name=Smooth Red Sandstone Stairs
tile.smooth_sandstone_stairs.name=Smooth Sandstone Stairs
tile.end_brick_stairs.name=End Stone Brick Stairs
tile.mossy_cobblestone_stairs.name=Mossy Cobblestone Stairs
tile.red_nether_brick_stairs.name=Red Nether Brick Stairs
tile.smooth_stone.name=Smooth Stone
tile.standing_banner.black.name=Black Banner
tile.standing_banner.blue.name=Blue Banner
tile.standing_banner.brown.name=Brown Banner
tile.standing_banner.cyan.name=Cyan Banner
tile.standing_banner.gray.name=Gray Banner
tile.standing_banner.green.name=Green Banner
tile.standing_banner.lightBlue.name=Light Blue Banner
tile.standing_banner.lime.name=Lime Banner
tile.standing_banner.magenta.name=Magenta Banner
tile.standing_banner.name=Banner
tile.standing_banner.orange.name=Orange Banner
tile.standing_banner.pink.name=Pink Banner
tile.standing_banner.purple.name=Purple Banner
tile.standing_banner.red.name=Red Banner
tile.standing_banner.silver.name=Light Gray Banner
tile.standing_banner.white.name=Banner
tile.standing_banner.yellow.name=Yellow Banner
tile.stone.andesite.name=Andesite
tile.stone.andesiteSmooth.name=Polished Andesite
tile.stone.diorite.name=Diorite
tile.stone.dioriteSmooth.name=Polished Diorite
tile.stone.granite.name=Granite
tile.stone.graniteSmooth.name=Polished Granite
tile.stone.stone.name=Stone
tile.cobblestone.name=Cobblestone
tile.stonebrick.chiseled.name=Chiseled Stone Bricks
tile.stonebrick.cracked.name=Cracked Stone Bricks
tile.stonebrick.default.name=Stone Bricks
tile.stonebrick.mossy.name=Mossy Stone Bricks
tile.stonebrick.name=Stone Bricks
tile.stonebrick.smooth.name=Smooth Stone Bricks
tile.stonecutter.name=Stonecutter
tile.stonecutter_block.name=Stonecutter
tile.mossy_cobblestone.name=Mossy Cobblestone
tile.double_stone_slab.brick.name=Bricks Slab
tile.double_stone_slab.cobble.name=Cobblestone Slab
tile.double_stone_slab.name=Stone Slab
tile.double_stone_slab.nether_brick.name=Nether Brick Slab
tile.double_stone_slab.quartz.name=Quartz Slab
tile.double_stone_slab.sand.name=Sandstone Slab
tile.double_stone_slab.smoothStoneBrick.name=Stone Bricks Slab
tile.double_stone_slab.stone.name=Stone Slab
tile.double_stone_slab.wood.name=Wooden Slab
tile.stone_slab.name=Stone Slab
tile.stone_slab.brick.name=Bricks Slab
tile.stone_slab.cobble.name=Cobblestone Slab
tile.stone_slab.stone.name=Smooth Stone Slab
tile.stone_slab.nether_brick.name=Nether Brick Slab
tile.stone_slab.quartz.name=Quartz Slab
tile.stone_slab.sand.name=Sandstone Slab
tile.stone_slab.smoothStoneBrick.name=Stone Bricks Slab
tile.stone_slab.wood.name=Wooden Slab
tile.double_stone_slab2.red_sandstone.name=Red Sandstone Slab
tile.stone_slab2.red_sandstone.name=Red Sandstone Slab
tile.stone_slab2.purpur.name=Purpur Slab
tile.stone_slab2.prismarine.rough.name=Prismarine Slab
tile.stone_slab2.prismarine.dark.name=Dark Prismarine Slab
tile.stone_slab2.prismarine.bricks.name=Prismarine Bricks Slab
tile.stone_slab2.mossy_cobblestone.name=Mossy Cobblestone Slab
tile.stone_slab2.red_nether_brick.name=Red Nether Brick Slab
tile.stone_slab2.sandstone.smooth.name=Smooth Sandstone Slab
tile.stone_slab3.end_brick.name=End Stone Brick Slab
tile.stone_slab3.red_sandstone.smooth.name=Smooth Red Sandstone Slab
tile.stone_slab3.andesite.smooth.name=Polished Andesite Slab
tile.stone_slab3.andesite.name=Andesite Slab
tile.stone_slab3.diorite.name=Diorite Slab
tile.stone_slab3.diorite.smooth.name=Polished Diorite Slab
tile.stone_slab3.granite.name=Granite Slab
tile.stone_slab3.granite.smooth.name=Polished Granite Slab
tile.stone_slab4.mossy_stone_brick.name=Mossy Stone Brick Slab
tile.stone_slab4.smooth_quartz.name=Smooth Quartz Slab
tile.stone_slab4.stone.name=Stone Slab
tile.stone_slab4.cut_sandstone.name=Cut Sandstone Slab
tile.stone_slab4.cut_red_sandstone.name=Cut Red Sandstone Slab
tile.coral_block.blue.name=Tube Coral Block
tile.coral_block.pink.name=Brain Coral Block
tile.coral_block.purple.name=Bubble Coral Block
tile.coral_block.red.name=Fire Coral Block
tile.coral_block.yellow.name=Horn Coral Block
tile.coral_block.blue_dead.name=Dead Tube Coral Block
tile.coral_block.pink_dead.name=Dead Brain Coral Block
tile.coral_block.purple_dead.name=Dead Bubble Coral Block
tile.coral_block.red_dead.name=Dead Fire Coral Block
tile.coral_block.yellow_dead.name=Dead Horn Coral Block
tile.tallgrass.fern.name=Fern
tile.tallgrass.grass.name=Grass
tile.tallgrass.name=Grass
tile.tallgrass.shrub.name=Shrub
tile.seagrass.seagrass.name=Seagrass
tile.sea_pickle.name=Sea Pickle
tile.turtle_egg.name=Sea Turtle Egg
tile.coral.blue.name=Tube Coral
tile.coral.pink.name=Brain Coral
tile.coral.purple.name=Bubble Coral
tile.coral.red.name=Fire Coral
tile.coral.yellow.name=Horn Coral
tile.coral.blue_dead.name=Dead Tube Coral
tile.coral.pink_dead.name=Dead Brain Coral
tile.coral.purple_dead.name=Dead Bubble Coral
tile.coral.red_dead.name=Dead Fire Coral
tile.coral.yellow_dead.name=Dead Horn Coral
tile.coral_fan.blue_fan.name=Tube Coral Fan
tile.coral_fan.pink_fan.name=Brain Coral Fan
tile.coral_fan.purple_fan.name=Bubble Coral Fan
tile.coral_fan.red_fan.name=Fire Coral Fan
tile.coral_fan.yellow_fan.name=Horn Coral Fan
tile.coral_fan_dead.blue_fan.name=Dead Tube Coral Fan
tile.coral_fan_dead.pink_fan.name=Dead Brain Coral Fan
tile.coral_fan_dead.purple_fan.name=Dead Bubble Coral Fan
tile.coral_fan_dead.red_fan.name=Dead Fire Coral Fan
tile.coral_fan_dead.yellow_fan.name=Dead Horn Coral Fan
tile.glass_pane.name=Glass Pane
tile.tnt.name=TNT
tile.snow_layer.name=Top Snow
tile.torch.name=Torch
tile.trapdoor.name=Oak Trapdoor
tile.acacia_trapdoor.name=Acacia Trapdoor
tile.birch_trapdoor.name=Birch Trapdoor
tile.dark_oak_trapdoor.name=Dark Oak Trapdoor
tile.jungle_trapdoor.name=Jungle Trapdoor
tile.spruce_trapdoor.name=Spruce Trapdoor
tile.tripWire.name=Tripwire
tile.tripwire_hook.name=Tripwire Hook
tile.vine.name=Vines
tile.flowing_water.name=Water
tile.water.name=Water
tile.waterlily.name=Lily Pad
tile.web.name=Cobweb
tile.heavy_weighted_pressure_plate.name=Weighted Pressure Plate (Heavy)
tile.light_weighted_pressure_plate.name=Weighted Pressure Plate (Light)
tile.end_stone.name=End Stone
tile.end_bricks.name=End Stone Bricks
tile.planks.acacia.name=Acacia Wood Planks
tile.planks.big_oak.name=Dark Oak Wood Planks
tile.planks.birch.name=Birch Wood Planks
tile.planks.jungle.name=Jungle Wood Planks
tile.planks.name=Wooden Planks
tile.planks.oak.name=Oak Wood Planks
tile.planks.spruce.name=Spruce Wood Planks
tile.wooden_slab.acacia.name=Acacia Wood Slab
tile.wooden_slab.big_oak.name=Dark Oak Wood Slab
tile.wooden_slab.birch.name=Birch Wood Slab
tile.wooden_slab.jungle.name=Jungle Wood Slab
tile.wooden_slab.name=Wood Slab
tile.wooden_slab.oak.name=Oak Wood Slab
tile.wooden_slab.spruce.name=Spruce Wood Slab
tile.carpet.black.name=Black Carpet
tile.carpet.blue.name=Blue Carpet
tile.carpet.brown.name=Brown Carpet
tile.carpet.cyan.name=Cyan Carpet
tile.carpet.gray.name=Gray Carpet
tile.carpet.green.name=Green Carpet
tile.carpet.lightBlue.name=Light Blue Carpet
tile.carpet.lime.name=Lime Carpet
tile.carpet.magenta.name=Magenta Carpet
tile.carpet.name=Carpet
tile.carpet.orange.name=Orange Carpet
tile.carpet.pink.name=Pink Carpet
tile.carpet.purple.name=Purple Carpet
tile.carpet.red.name=Red Carpet
tile.carpet.silver.name=Light Gray Carpet
tile.carpet.white.name=White Carpet
tile.carpet.yellow.name=Yellow Carpet
tile.crafting_table.name=Crafting Table
tile.dragon_egg.name=Dragon Egg

tile.glazedTerracotta.white.name=White Glazed Terracotta
tile.glazedTerracotta.orange.name=Orange Glazed Terracotta
tile.glazedTerracotta.magenta.name=Magenta Glazed Terracotta
tile.glazedTerracotta.light_blue.name=Light Blue Glazed Terracotta
tile.glazedTerracotta.yellow.name=Yellow Glazed Terracotta
tile.glazedTerracotta.lime.name=Lime Glazed Terracotta
tile.glazedTerracotta.pink.name=Pink Glazed Terracotta
tile.glazedTerracotta.gray.name=Gray Glazed Terracotta
tile.glazedTerracotta.silver.name=Light Gray Glazed Terracotta
tile.glazedTerracotta.cyan.name=Cyan Glazed Terracotta
tile.glazedTerracotta.purple.name=Purple Glazed Terracotta
tile.glazedTerracotta.blue.name=Blue Glazed Terracotta
tile.glazedTerracotta.brown.name=Brown Glazed Terracotta
tile.glazedTerracotta.green.name=Green Glazed Terracotta
tile.glazedTerracotta.red.name=Red Glazed Terracotta
tile.glazedTerracotta.black.name=Black Glazed Terracotta
tile.concrete.black.name=Black Concrete
tile.concrete.red.name=Red Concrete
tile.concrete.green.name=Green Concrete
tile.concrete.brown.name=Brown Concrete
tile.concrete.blue.name=Blue Concrete
tile.concrete.purple.name=Purple Concrete
tile.concrete.cyan.name=Cyan Concrete
tile.concrete.silver.name=Light Gray Concrete
tile.concrete.gray.name=Gray Concrete
tile.concrete.pink.name=Pink Concrete
tile.concrete.lime.name=Lime Concrete
tile.concrete.yellow.name=Yellow Concrete
tile.concrete.lightBlue.name=Light Blue Concrete
tile.concrete.magenta.name=Magenta Concrete
tile.concrete.orange.name=Orange Concrete
tile.concrete.white.name=White Concrete
tile.glazedTerracottaWhite.name=White Glazed Terracotta
tile.glazedTerracottaOrange.name=Orange Glazed Terracotta
tile.glazedTerracottaMagenta.name=Magenta Glazed Terracotta
tile.glazedTerracottaLightBlue.name=Light Blue Glazed Terracotta
tile.glazedTerracottaYellow.name=Yellow Glazed Terracotta
tile.glazedTerracottaLime.name=Lime Glazed Terracotta
tile.glazedTerracottaPink.name=Pink Glazed Terracotta
tile.glazedTerracottaGray.name=Gray Glazed Terracotta
tile.glazedTerracottaSilver.name=Light Gray Glazed Terracotta
tile.glazedTerracottaCyan.name=Cyan Glazed Terracotta
tile.glazedTerracottaPurple.name=Purple Glazed Terracotta
tile.glazedTerracottaBlue.name=Blue Glazed Terracotta
tile.glazedTerracottaBrown.name=Brown Glazed Terracotta
tile.glazedTerracottaGreen.name=Green Glazed Terracotta
tile.glazedTerracottaRed.name=Red Glazed Terracotta
tile.glazedTerracottaBlack.name=Black Glazed Terracotta
tile.concretePowder.black.name=Black Concrete Powder
tile.concretePowder.red.name=Red Concrete Powder
tile.concretePowder.green.name=Green Concrete Powder
tile.concretePowder.brown.name=Brown Concrete Powder
tile.concretePowder.blue.name=Blue Concrete Powder
tile.concretePowder.purple.name=Purple Concrete Powder
tile.concretePowder.cyan.name=Cyan Concrete Powder
tile.concretePowder.silver.name=Light Gray Concrete Powder
tile.concretePowder.gray.name=Gray Concrete Powder
tile.concretePowder.pink.name=Pink Concrete Powder
tile.concretePowder.lime.name=Lime Concrete Powder
tile.concretePowder.yellow.name=Yellow Concrete Powder
tile.concretePowder.lightBlue.name=Light Blue Concrete Powder
tile.concretePowder.magenta.name=Magenta Concrete Powder
tile.concretePowder.orange.name=Orange Concrete Powder
tile.concretePowder.white.name=White Concrete Powder

tile.stripped_spruce_log.name=Stripped Spruce Log
tile.stripped_dark_oak_log.name=Stripped Dark Oak Log
tile.stripped_birch_log.name=Stripped Birch Log
tile.stripped_jungle_log.name=Stripped Jungle Log
tile.stripped_oak_log.name=Stripped Oak Log
tile.stripped_acacia_log.name=Stripped Acacia Log

tile.bamboo.name=Bamboo
tile.scaffolding.name=Scaffolding
tile.grindstone.name=Grindstone
tile.cartography_table.name=Cartography Table
tile.lantern.name=Lantern
tile.smoker.name=Smoker
tile.smithing_table.name=Smithing Table
tile.barrel.name=Barrel
tile.campfire.name=Campfire
tile.loom.name=Loom
tile.lectern.name=Lectern
tile.sweet_berry_bush.name=Sweet Berry Bush

tile.wood.oak.name=Oak Wood
tile.wood.spruce.name=Spruce Wood
tile.wood.birch.name=Birch Wood
tile.wood.jungle.name=Jungle Wood
tile.wood.acacia.name=Acacia Wood
tile.wood.dark_oak.name=Dark Oak Wood
tile.wood.stripped.oak.name=Stripped Oak Wood
tile.wood.stripped.spruce.name=Stripped Spruce Wood
tile.wood.stripped.birch.name=Stripped Birch Wood
tile.wood.stripped.jungle.name=Stripped Jungle Wood
tile.wood.stripped.acacia.name=Stripped Acacia Wood
tile.wood.stripped.dark_oak.name=Stripped Dark Oak Wood

tipped_arrow.effect.empty=Tipped Arrow
tipped_arrow.effect.water=Tipped Arrow
tipped_arrow.effect.mundane=Tipped Arrow
tipped_arrow.effect.thick=Tipped Arrow
tipped_arrow.effect.awkward=Tipped Arrow
tipped_arrow.effect.nightVision=Arrow of Night Vision
tipped_arrow.effect.invisibility=Arrow of Invisibility
tipped_arrow.effect.jump=Arrow of Leaping
tipped_arrow.effect.fireResistance=Arrow of Fire Resistance
tipped_arrow.effect.moveSpeed=Arrow of Swiftness
tipped_arrow.effect.moveSlowdown=Arrow of Slowness
tipped_arrow.effect.water=Arrow of Splashing
tipped_arrow.effect.waterBreathing=Arrow of Water Breathing
tipped_arrow.effect.heal=Arrow of Healing
tipped_arrow.effect.harm=Arrow of Harming
tipped_arrow.effect.poison=Arrow of Poison
tipped_arrow.effect.regeneration=Arrow of Regeneration
tipped_arrow.effect.damageBoost=Arrow of Strength
tipped_arrow.effect.weakness=Arrow of Weakness
tipped_arrow.effect.levitation=Arrow of Levitation
tipped_arrow.effect.luck=Arrow of Luck
tipped_arrow.effect.wither=Arrow of Decay
tipped_arrow.effect.turtleMaster=Arrow of the Turtle Master
tipped_arrow.effect.slowFalling=Arrow of Slow Falling

structure_block.title=Structure Block
structure_block.structure_name=Structure Name
structure_block.mode=Mode:
structure_block.mode.save=Save
structure_block.mode.save.experimental=Save (Experimental)
structure_block.mode.load=Load
structure_block.mode.load.experimental=Load (Experimental)
structure_block.mode.corner=Corner
structure_block.mode.data=Data
structure_block.mode.export=3D Export
structure_block.mode.invalid=Invalid mode
structure_block.size=Size:
structure_block.data_label=Data:
structure_block.offset=Offset:
structure_block.invisible_blocks=Invisible Blocks:
structure_block.include_entities=Include Entities:
structure_block.show_bounding_box=Show Bounding Box:
structure_block.mirror=Mirror:
structure_block.rotation=Rotation
structure_block.integrity=Integrity:
structure_block.seed=Seed:
structure_block.off=Off
structure_block.show=Show
structure_block.include=Include
structure_block.detect=Detect
structure_block.export=Export
structure_block.export.disabled_message=Export is disabled on world templates from the Minecraft Marketplace.
structure_block.reset=Reset
structure_block.save=Save
structure_block.redstone_save_mode=Redstone Save Mode
structure_block.save_to_disk=Save To Disk
structure_block.save_to_memory=Save In Memory
structure_block.save.successful=Structure Saved!
structure_block.load=Load
structure_block.mirror.none=None
structure_block.mirror.left_right=Left Right
structure_block.mirror.front_back=Front Back
structure_block.rotation.none=No Rotation
structure_block.rotation.90=90
structure_block.rotation.180=180
structure_block.rotation.270=270
structure_block.progress.generating=Generating your model...
structure_block.extensionDescription=Structure File
structure_block.exportFileTitle=Export Structure
structure_block.exportFailedTitle=Export Failed
structure_block.exportFailedDescription=We failed to export the structure.
structure_block.exportProgressTitle=Exporting Your Structure
structure_block.experimental=The "Use Experimental Gameplay" setting must be toggled on in the World Options category to be able to use Structure Blocks.

3d_export.title=3D Export
3d_export.include_players=Include Players:
3d_export.remove_blocks=Remove Blocks:
3d_export.off=Off
3d_export.show=Show
3d_export.include=Include
3d_export.remove=Remove
3d_export.export=Export
3d_export.reset=Reset
3d_export.remixServiceDiscontinued=Note: Remix3D Upload service is no longer available, use Export to save locally instead.

title.oldgl1=Old graphics card detected; this may prevent you from
title.oldgl2=playing in the future as OpenGL 2.0 will be required.

## These are being used by TextObject tests. Changing them will break the test.
translation.test.args=%s %s
translation.test.complex=Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!
translation.test.escape=%%s %%%s %%%%s %%%%%s
translation.test.invalid=hi %
translation.test.invalid2=hi %  s
translation.test.none=Hello, world!
translation.test.world=world

## NOTE: Please do not localize the typeface names
typeface.mojangles=Mojangles
typeface.notoSans=Noto Sans

worldConversionErrorPrompt.title=World Conversion Failed
worldConversionErrorPrompt.message=Oops, something went wrong during world conversion. Try again later.
worldConversionErrorPrompt.message.upload=Something went wrong. We weren't able to convert your world. You can send us your world later by going to your old world and pressing Y. Or send us your world now so we can investigate further.
worldConversionErrorPrompt.sendWorld=Send World Now
worldConversionErrorPrompt.backToWorld=Back to Worlds

worldConversionErrorReport.title=Report This World
worldConversionErrorReport.button.reportAndSend=Report and Send World
worldConversionErrorReport.dropdown.default=What went wrong?
worldConversionErrorReport.dropdown.mobs=Mobs were misbehaving or missing
worldConversionErrorReport.dropdown.terrain=Terrain was broken or missing
worldConversionErrorReport.dropdown.itemsMissing=Lost my items, creations, and/or player stats
worldConversionErrorReport.dropdown.itemInteraction=Couldn't interact with items
worldConversionErrorReport.dropdown.worldConversion=World conversion failed
worldConversionErrorReport.terms.agreement=By hitting Agree, you are uploading your world to the Minecraft Team. The Microsoft Services Agreement and Privacy Statement apply.
worldConversionErrorReport.terms.agreementForBeta=By uploading your World you hereby authorize Microsoft to test, reproduce, modify, distribute and publicly display your World in support of transferring your World to Minecraft.
worldConversionErrorReport.terms.viewTerms=View Terms


worldConversionErrorReport.sending.title=Sending Old World
worldConversionErrorReport.sending.description=Sending Old World to Minecraft...

worldConversionErrorReport.sending.complete=Send Completed
worldConversionErrorReport.sending.completeMessage.part1=Thank you! We are hard at work fixing things and won't be able to get back to you about this specific world. Here's your receipt confirming that we received it:
worldConversionErrorReport.sending.completeMessage.part2=Receipt: %s

worldError.corrupted=is corrupted and can't be started.
worldError.invalidArguments=was created with invalid arguments.
worldError.IO=can't be opened.
worldError.notFound=can't be found.
worldError.notSupported=has an unsupported format.
worldError.unknown=has an unknown problem.
worldError.writeCorrupted=is corrupted and can't be saved.
worldError.worldRecovered=World Recovered
worldError.recoveredCorruptedWorld=Successfully recovered your corrupted world.
worldError.recoveredCorruptedWorldWarning=Some of your most recent changes may have been lost in recovery.
worldError.worldFailedRecovery=Recovery Failed
worldError.worldFailedRecoveryText=We detected a corrupted world and failed to recover it.

userData.recovered.title=Save Data Recovered
userData.recovered.text=Successfully recovered your corrupted user settings.
userData.recovered.warning=Some of your most recent changes may have been lost in recovery.
userData.unrecoverable.title=Save Data Recovery Failed
userdata.unrecoverable.text=We detected that your user settings have become corrupted but failed to recover them.

trial.pauseScreen.remainingTime=Remaining Trial Time: %s
trial.pauseScreen.buyGame=Unlock Full Game
trial.survival.welcome=Welcome to your Minecraft Trial!
trial.survival.remainingTime=%d Minutes Remaining

trial.noInvitesOrJoining=To play with friends please purchase the full game

trial.upsell.trialLabel=Minecraft Trial
trial.upsell.title=Let the trial begin!
trial.upsell.description=In this trial you are limited to 90 minutes of play time. Craft, build, explore, and have fun! Your world will be preserved if you choose to purchase the full version.
trial.upsell.description.updated=Explore, craft, build, and have fun! In this trial you will have 90 minutes of play time.  Unlock the full game to continue playing as long as you want!
trial.upsell.description.worldsDontTransferToFullGame.line1=Explore, craft, build, and have fun! In this trial you will have 90 minutes of play time. Unlock the full game to play an infinite amount of worlds for as long as you want!*
trial.upsell.description.worldsDontTransferToFullGame.line2=*(This world will not transfer to the full game)
trial.upsell.unlock=Unlock Full Game
trial.upsell.startTrial=Start Trial
trial.upsell.continueTrial=Continue Trial

ratingPopUp.title=Are you enjoying Minecraft?
feedbackPopup.title=Do you have any feedback for us?

## NOTE: The following five lines are all related and shown together. When localizing, you may add line breaks to fit the paragraph,
## as opposed to trying to literally translate each line.
trial.upsell.expiredDescription.line1=Time has expired. Please purchase to unlock the full game:
trial.upsell.expiredDescription.line2=-Explore limitless worlds
trial.upsell.expiredDescription.line3=-Play with your friends
trial.upsell.expiredDescription.line4=-Craft hundreds of items
trial.upsell.expiredDescription.line5=-Discover mysterious structures and realms
trial.upsell.expiredNewDescription=Do you want to continue exploring, crafting, and building?  Unlock the full game to play as long as you want!  This world will be saved for you to pick up where you left off but you can't modify it until you unlock the full game.
trial.upsell.expiredNewDescription.worldsDontTransferToFullGame.line1=Do you want to continue exploring, crafting, and building? Unlock the full game to play an infinite amount of worlds for as long as you want!*
trial.upsell.expiredTitle=Time's Up!
trial.upsell.expiredViewWorld=View World

## tabbedUpsell content
trial.tabbed_upsell.title=Unlock Full Game
trial.tabbed_upsell.button=Buy Minecraft!
trial.tabbed_upsell.buttonAlternate=Unlock Minecraft Full Game
trial.tabbed_upsell.minecraft.title=MINECRAFT 
trial.tabbed_upsell.minecraft.description=Endlessly explore your own unique worlds and construct anything you can imagine - from the simplest of homes to the grandest of castles! Play Creative mode to build wonders with unlimited resources. Or mine in Survival mode, crafting weapons and armor to fend off dangerous mobs and survive the nights. Plus, play with friends online! Unlock the full game today!
trial.tabbed_upsell.xbl.title=Microsoft Account
trial.tabbed_upsell.xbl.description=With the full Minecraft experience you get Creative mode, Achievements, and more, plus with a Microsoft Account you can meet others in the Minecraft community! Join your friends' worlds, make new friends on Servers, and sync your Marketplace purchases to everywhere you play Minecraft.
trial.tabbed_upsell.xbl.description.line2=*(in versions with Marketplace)
trial.tabbed_upsell.achievements.title=ACHIEVEMENTS
trial.tabbed_upsell.achievements.description=In addition to the full Minecraft experience with Creative mode, Multiplayer, and more, you'll earn Achievements and a Gamerscore to mark your Minecraft progress and show your Microsoft Account friends your accomplishments. Get the full game to start earning them!
trial.tabbed_upsell.multiplayer.title=MULTIPLAYER
trial.tabbed_upsell.multiplayer.description=The full Minecraft experience lets you enjoy Creative Mode, Achievements and Multiplayer with your friends across Minecraft devices! Play with friends on console, PC and mobile to create, explore and survive together. Unlock the full Minecraft experience today!
trial.tabbed_upsell.server.title=SERVERS
trial.tabbed_upsell.server.description=Servers offer some of the most creative ways to enjoy Minecraft! Play great mini-games, meet the Minecraft community and make new friends. Unlock the full Minecraft experience to try Servers today!
trial.tabbed_upsell.store.title=STORE
trial.tabbed_upsell.store.description=Get access to the Minecraft Marketplace, our in-game store. Switch up your look with new skins, change your world, or explore incredible new maps from the best community creators! Get the full game today!
trial.tabbed_upsell.creative.title=CREATIVE
trial.tabbed_upsell.creative.description=Buy Minecraft and get Creative Mode! Access the entire inventory - the only limit to what you can build is your imagination! Fly around your world, make your dream home out of anything and everything, or just blow up a mountain with unlimited TNT! 
trial.tabbed_upsell.addon.title=ADD-ONS
trial.tabbed_upsell.addon.description=Want to make wolves act like bunnies? Turn all the trees into cotton candy? You can alter everything in the full Minecraft experience with add-ons! Plus get access to Multiplayer, Creative mode, Achievements and more! Buy Minecraft now for full access!
trial.tabbed_upsell.seeds.title=SEEDS
trial.tabbed_upsell.seeds.description=Unlock the full Minecraft experience and get access to Seeds! Seeds help you discover new biomes - survive on ice plains, ascend extreme hills, or trek across deserts. Start with unique templates or explore random new worlds! Access Seeds, Creative Mode, Multiplayer and more by buying Minecraft today!

trial.tabbed_upsell.navleft=Navigate Tab Left
trial.tabbed_upsell.navRight=Navigate Tab Right

## Trial Collection content:
trial.starter.collection.title=Minecraft Starter Collection
trial.master.collection.title=Minecraft Master Collection
trial.starter.collection.button=Buy Starter Collection
trial.master.collection.button=Buy Master Collection
trial.starter.offer=Get Minecraft, 700 Minecoins, 2 skin packs, 1 texture pack and 1 mash-up
trial.master.offer=Get Minecraft, 1000 Minecoins, 4 skin packs, 2 texture packs, 3 maps, and 1 mash-up
trial.collection.1.title=1000 Minecoins
trial.collection.1.description=Buy skins, textures and worlds in the Marketplace!
trial.collection.2.title=700 Minecoins
trial.collection.2.description=Buy skins, textures and worlds in the Marketplace!
trial.collection.3.title=Wildlife: Savanna
trial.collection.3.description=Go on safari in a rugged off-road vehicle that seats 4!
trial.collection.4.title=Adventurer's Dream
trial.collection.4.description=Explore Noxcrew's magical world full of ancient castles!
trial.collection.5.title=PureBDcraft Pack
trial.collection.5.description=Transform your Minecraft world into a comic!
trial.collection.6.title=Relics of Privateers
trial.collection.6.description=Vast tropical wilderness on a whirlwind hunt for treasure!
trial.collection.7.title=Greek Mythology Mash-up
trial.collection.7.description=Explore an ancient world, texture pack and skins!
trial.collection.8.title=Winter Mini-Games Festival
trial.collection.8.description=Playgrounds filled with games and challenges!
trial.collection.9.title=Pastel Skin Pack
trial.collection.9.description=Get ready for an explosion of rainbow pastel happiness!
trial.collection.10.title=Plastic Texture Pack
trial.collection.10.description=Make your worlds simple, colorful and vibrant!
trial.collection.11.title=Skin Pack 1
trial.collection.11.description=Customize your character with this selection of skins!
trial.collection.12.title=Villains Skin Pack
trial.collection.12.description=Dress as a Cake Maniac, Frankencrafter, or Worse!
trial.includes=Includes

## Trial Game content:
trial.survival.mode.title=SURVIVAL
trial.creative.mode.title=CREATIVE
trial.survival.mode.1.intro_description=The original Minecraft experience: collect resources to craft tools and build structures, find and farm food to manage your hunger, battle dangerous mobs, and explore a vast world.
trial.survival.mode.2.intro_description=Start by collecting some wood and dirt for a makeshift home to help you survive the first night! From there it's all up to you.
trial.creative.mode.1.intro_description=Build anything you can imagine in Creative Mode. Take no damage, fly through the skies, and enjoy instant access to every block in the game! This mode lets you focus on crafting and gives you everything you need to build something beautiful!
trial.creative.mode.2.intro_description=You'll also have access to Inspiration Island, a tutorial world and introduction to Creative mode that'll teach you how to be a master builder in no time! You can find more great worlds like this in the Minecraft Marketplace.
trial.tour.marketplace=TOUR THE MARKETPLACE
trial.world.title=Which Trial World?

## Edu Tutorial strings
tutorial.edu.menuTitle=Learn to Play
tutorial.edu.title=Tutorials

utility.zipFile=Zip File
utility.pdfFile=PDF File
verification.nolicense.title=License Error
verification.nolicense.description=We're having trouble verifying that you own Minecraft on this device. Make sure you have downloaded and installed Minecraft from the store. Or simply reconnect to the internet and try again.

## Autosave Info strings
autosave.title=Autosave
autosave.info.general=When you see this icon, we are saving your game. Do not turn off your device while this icon is on screen.
autosave.info.nx=When you see this icon, we are saving your game. Do not turn off your Nintendo Switch while this icon is on screen.
autosave.info.xbox=When you see this icon, we are saving your game. Do not turn off your Xbox while this icon is on screen.
autosave.info.desktop=When you see this icon, we are saving your game. Do not turn off your computer while this icon is on screen.

## Xbox Live Strings
xbox.signin.error=Please sign in with your Microsoft Account to play with friends, earn gamerscore and achievements.
xbox.signin.error.pocket.line1=Thanks for testing Realms! The features are not final and you might run into bugs.  Send us feedback! We're listening.
xbox.signin.error.pocket.line2=
xbox.signin.error.pocket.line3=During this test, everyone will need to sign in with a Microsoft Account so they can be sent invites and added to Realms.
xbox.connection.error=Disconnected from Microsoft Account
xbox.signin=Sign In
xbox.signin.enterCode=And enter this code:
xbox.signin.letsPlay=Let's Play!
xbox.signin.message=Sign in with your Free Microsoft Account to experience the full world of Minecraft.
xbox.signin.url=https://aka.ms/remoteconnect
xbox.signin.useDifferentAccount=Sign in with a different Microsoft Account
xbox.signin.website=Visit this website on another device:
xbox.signin.welcome=Welcome!
xbox.signin.newaccount.welcome=Welcome!
xbox.signin.newaccount.info=Your friends and their friends can see if you're online. your game clips, and your recent apps and games. You can change these on Xbox.com.
xbox.signinFirst=Sign In First
xbox.signinFree=Sign In For Free
xbox.signout=Sign Out
xbox.signinquestion=Sign In?
xbox.notnow=Not Now
xbox.signingin=Signing in with your Microsoft Account...
xbox.signingin.offline=Signing in
xbox.firstsignin.line1=Welcome to Minecraft! Use a Microsoft Account to start connecting with the Minecraft community, where PCs, consoles, phones, and tablets can all play together. And the best part?
xbox.firstsignin.line2=It's FREE!
xbox.firstsignin.line3=Don't have an account? Create one now to earn achievements and join Realms and Servers!
xbox.firstsignin.line3.norealmsOrServers=Don't have an account? Create one now to earn achievements and play with your cross-platform friends!
xbox.failedsignin.line1=We tried to sign you in to your Microsoft Account, but something went wrong.
xbox.failedsignin.line2=If you play without signing in you won't be able to see your friends list, earn achievements, join Realms, or play online on Xbox Live.
xbox.dev_wrongSandboxSigninFailed.title=Something Went Wrong
xbox.dev_wrongSandboxSigninFailed.line1=Failed to sign in to %s sandbox. Please change sandboxes and restart Minecraft or sign in with a different account. Sandbox will be %s on restart. 
xbox.dev_wrongSandboxSigninFailed.button.dev=Change To Dev Sandbox
xbox.dev_wrongSandboxSigninFailed.button.retail=Change To Retail Sandbox
xbox.dev_wrongSandboxSigninFailed.button.clearXbl=Clear Account Sign in Data
xbox.achievementssignin.line1=Achievements and Gamerscore mark your progress through Minecraft.
xbox.achievementssignin.line2=Sign in with a Microsoft Account to start earning them!
xbox.addfriendsignin.line1=Find your friends by their Gamertag to see when they are online.
xbox.addfriendsignin.line2=Sign in with a Microsoft Account to start adding friends!
xbox.invitesignin.line1=Invite your friends by their Gamertag to see their worlds when they are online.
xbox.invitesignin.line2=Sign in with a Microsoft Account to start inviting friends!
xbox.thirdpartysignin.line1=Awesome mini-games and new players are waiting for you! But first, '%s' would like to know who you are.
xbox.thirdpartysignin.line2=You need to sign in with a Microsoft Account to connect!
xbox.signInLong=Sign in for Free!
xbox.signOutLong=Sign out of your Microsoft Account
xbox.thirdpartysignin.line1=Awesome mini-games and new players are waiting for you! But first, '%s' would like to know who you are.
xbox.thirdpartysignin.line2=You need to sign in to Xbox Live to connect!
xbox.externalServer.title=Play on a Server
xbox.externalServer.line1=Servers offer some of the most creative ways to play Minecraft! We just need you to sign in so that we can make sure you have permission to play online with new people.

xbox.genericsignin.line1=Your Minecraft account lets you play online multiplayer with friends on PCs and mobile devices. And the best part? 
xbox.genericsignin.line2=It's FREE!
xbox.genericsignin.line3=You can use it anywhere you play Minecraft.

xbox.disconectionscreen.notSignedIn=Sign in with your Microsoft Account to play with friends.
xbox.disconectionscreen.multiplayerNotAllowed=This Microsoft Account does not have permission to join multiplayer games.
xbox.disconectionscreen.accessDenied=You must be friends with someone in this game in order to join.

xbox.friendfinder.enterGamertag=Enter Gamertag
xbox.friendfinder.findFriends=Find Friends by Gamertag
xbox.friendfinder.searchingForGamertag=Searching
xbox.friendfinder.gamertagNotFound=Gamertag Not Found

xbox.profile.addFriend=Add Friend
xbox.profile.addFriend.success=Success! %s was added to your friend list.
xbox.profile.block=Block
xbox.profile.currentlyPlaying=Currently playing %s
xbox.profile.favorite=Favorite
xbox.profile.favoriteHelperText=Favorite friends go first on your friends list. You'll see when they go online or start broadcasting.
xbox.profile.friend=Friend
xbox.profile.friendHelperText=Follow %s. After you both are friends, you can invite them to your game.
xbox.profile.mute=Mute
xbox.profile.realName=Manage your privacy settings for sharing your real name in the Xbox app.
xbox.profile.removeFriend=Unfriend
xbox.profile.report=Report

xbox.report.gamertag=Report %s
xbox.report.optionalText=Enter Text Here (optional)
xbox.report.toast=You have reported %s
xbox.report.toastError=We were unable to send your report. Try again soon.

xbox.report.bioOrLocation=Bio or location
xbox.report.cheating=Cheating
xbox.report.nameOrGamertag=Player Name or Gamertag
xbox.report.quittingEarly=Quitting early
xbox.report.unsportingBehavior=Unsporting behavior
xbox.report.voiceCommunication=Voice communication
xbox.report.whyReport=Why are you reporting %s?

authserver.notavailable=Something went wrong.  We can't verify that the server you are trying to connect to is one that we trust.  We recommend that you try again later.
authserver.authfailed=This is bad.  This server failed our verification test.  Someone we don't trust might be pretending to be a trusted server.

platform.model.unknown=UNKNOWN

## resource pack validation errors
packdiscoveryerror.manifest_pack_error=Unable to open pack.
packdiscoveryerror.incomplete_pack=Incomplete pack.
packdiscoveryerror.unsupported_file_format=Not a valid zip archive.
packdiscoveryerror.missing_manifest=Unable to find manifest in pack.
packdiscoveryerror.manifest_parse_error=Unable to parse pack manifest with stack: %s
packdiscoveryerror.required_manifest_property_missing=Missing '%s' element in pack manifest.
packdiscoveryerror.required_manifest_property_wrong_type=Required '%s' element is the wrong type in pack manifest.
packdiscoveryerror.required_manifest_property_empty=Required '%s' element is empty in pack manifest.
packdiscoveryerror.required_manifest_property_invalid_value=Provided '%s' element has an invalid value in pack manifest.
packdiscoveryerror.malformed_uuid=Provided '%s' element is not a valid UUID in pack manifest .
packdiscoveryerror.malformed_version=Provided '%s' element is not SemVer (semver.org) compliant in pack manifest.
packdiscoveryerror.missing_modules=Missing '%s' element in pack manifest; Defaulting to resource pack.
packdiscoveryerror.missing_dependency=Missing dependency with ID '%s' and version '%s'.
packdiscoveryerror.unsupported_format_version=Provided format version '%s' is not supported.
packdiscoveryerror.duplicate_uuid=Provided UUID '%s' element already exists in pack manifest.
packdiscoveryerror.multiple_modules=Multiple conflicting modules detected in pack manifest.
packdiscoveryerror.invalid_capability_value=Pack capability '%s' is not supported.
packdiscoveryerror.unsupported_format_patch=Revision component provided in version '%s' will be ignored and treated as 0.
packdiscoveryerror.ignored_property=The property '%s' is not used for this type of content. The field will be ignored.
packdiscoveryerror.version_too_high=The property '%s' has a version of '%s' which is too high.  The highest value we accept is '%s'.
packdiscoveryerror.version_too_low=The property '%s' has a version of '%s' which is too low.  The lowest value we accept is '%s'.
packdiscoveryerror.format_version_1_engine_version_cap=The property '%s` must have a value set less than `%s`.  To use a higher version, you need to use format version 2.

## pack upgrading
packupgradewarning.invalidpacktype=Provided '%s' element has an invalid value in pack manifest; Defaulting to resource pack.
packupgradewarning.required_manifest_property_missing=Missing '%s' element in pack manifest; Defaulting to '%s'.
packupgradewarning.required_manifest_property_empty=Required '%s' element is empty in pack manifest; Defaulting to '%s'.
packupgradewarning.malformed_uuid=Provided '%s' element is not a valid UUID in pack manifest; Defaulting to '%s'.
packupgradewarning.malformed_version=Provided '%s' element is not SemVer (semver.org) compliant in pack manifest; Defaulting to '%s'.
packupgradewarning.manifest_upgraded=This pack manifest has been upgraded to a new version.
packupgradewarning.duplicate_uuid=Provided UUID '%s' element already exists in pack manifest; Defaulting to '%s'.
packupgradewarning.multiple_modules=Multiple conflicting modules detected in pack manifest; Defaulting to '%s'.

## pack strings
pack.authors.label=Author: %s
pack.authors.none=Unknown

## manifest pack validation
manifestvalidation.title=Pack Info
manifestvalidation.error=Error
manifestvalidation.info=Info
manifestvalidation.noerror=No errors were found
manifestvalidation.warning=Warning
manifestvalidation.delete=Delete
manifestvalidation.delete.confirm_title=Delete Pack?
manifestvalidation.delete.confirm_body=You are about to delete this resource/behavior pack forever. Are you sure?
manifestvalidation.delete.confirm_delete=Delete
manifestvalidation.delete.confirm_goBack=Go Back
manifestvalidation.packid=Pack ID:
manifestvalidation.packversion=Pack Version:
manifestvalidation.filelocation=File Location:
manifestvalidation.errorlist=Error List
manifestvalidation.file=File:
manifestvalidation.issue=Issue:
manifestvalidation.unkown.packtype=Unknown Pack Type
manifestvalidation.unkown.packtitle=Unknown Pack Name
manifestvalidation.unkown.packdescription=Unknown Pack Description
manifestvalidation.unkown.packid=Unknown Pack ID
manifestvalidation.unkown.packversion=Unknown Pack Version
manifestvalidation.noname=Missing Name

## UI pack warnings and errors
uiPackError.invalidChildNames=%s: Child controls must have unique names: %sIn the future this will be an error and this file won't load.
uiPackError.parseError=%s: Unable to parse UI JSON file with stack: %s
uiPackError.missingControl=%s: Can't find the control: %s
uiPackError.missingControlTarget=%s (%s): Can't find the control '%s' during operation '%s'
uiPackError.missingArrayName=%s (%s): Missing either the field 'array_name' or 'control_name'. 
uiPackError.missingCondition=%s (%s): Missing condition on operation '%s'.
uiPackError.missingValue=%s (%s): Missing the value for the operation '%s'.
uiPackError.missingOperation=%s (%s): Missing operation.
uiPackError.invalidOperationName=%s (%s): Invalid operation '%s'.

jsonValidationError.typeError=%s: Invalid type for property. Expected %s got %s
jsonValidationError.requiredPropertyError=%s: Could not find required property '%s'
jsonValidationError.invalidValueError=%s: Did not find valid value for property. Expected %s

serverUI.errorTitle=Error creating form.
serverUI.errorDescription=Recieved invalid form json. Error:%s

## Content importing
content.import.failed=Failed to import '%s'
content.import.failed.subtitle=See output log for more details
content.import.failed.subtitle_duplicate=Duplicate pack detected
content.import.failed.subtitle_malformed_zip=Not a valid zip archive
content.import.failed.subtitle_premiumcontent=Content in this world is not supported by Minecraft: Education Edition.
content.import.failed.title_premiumcontent=Content Not Supported
content.import.succeeded=Successfully imported '%s'
content.import.succeeded_with_warnings=Successfully imported '%s' with warnings
content.import.succeeded_with_warnings.subtitle=Click here for more info
content.import.started=Import started...

## World Templates
worldTemplate.festivemashup2016.name=Festive Mash-up 2016
worldTemplate.redstonemansion.name=Redstone Mansion
worldTemplate.chinesemythology.name=Chinese Mythology Mash-up
worldTemplate.GreekMythology.name=Greek Mythology Mash-up
worldTemplate.Skyrim.name=Skyrim Mash-up

eduTemplateWorld.theAgentTrials.name=The Agent Trials
eduTemplateWorld.tutorialWorld.name=Tutorial World
eduTemplateWorld.mushroomIsland.name=Mushroom Island
eduTemplateWorld.mooshroomIsland.name=Mushroom Island
eduTemplateWorld.starterTown.name=Starter Town
eduTemplateWorld.tutorialVolumeII.name=Tutorial Volume II
eduTemplateWorld.blocksOfGrass.name=Blocks of Grass

## World Conversion Feedback
worldConversion.feedback.done.title=World Submitted
worldConversion.feedback.failed.title=World Submission Failed
worldConversion.feedback.done.msg=Thank you for sending us a copy of your world and helping us find bugs! We are hard at work fixing things and won't be able to get back to you about this specific world. Here's your receipt confirming that we received it: %s
worldConversion.feedback.failed.msg=Oops! We weren't able to receive your world. Try checking your internet connection or sending it again later.
worldConversion.feedback.prompt.title=Submit World
worldConversion.feedback.prompt.msg=Did your old world not look or work the way it should have? Send us your world and help us find the bugs!

## World Conversion Complete Popup
worldConversionComplete.title=Conversion Complete
worldConversionComplete.load_prompt=Would you like to play your world now?

## the following strings are still in-use in 15.* and should not be removed until we are done servicing the 15.* releases
addServer.add=Done
externalServerScreen.addServer=Add Server
addServer.enterIp=Server Address
addServer.enterName=Server Name
addServer.hideAddress=Hide Address
addServer.resourcePack=Server Resource Packs
addServer.resourcePack.disabled=Disabled
addServer.resourcePack.enabled=Enabled
addServer.resourcePack.prompt=Prompt
addServer.title=Edit Server Info
addServer.alreadyAdded=This server has already been added
externalServerScreen.addServer=Add Server
externalServerScreen.header=Add External Server
externalServerScreen.label=Add server by IP/Address.
externalServerScreen.serverAddress=IP/Address
externalServerScreen.serverAddressInput=Server IP or Address
externalServerScreen.serverName=Name
externalServerScreen.serverNameInput=Server Name
externalServerScreen.serverPort=Port
externalServerScreen.serverPortInput=Server Port
survey.feedbackButton=Send Us Your Feedback
survey.label=Select all that apply:
survey.line1=Took too long to load
survey.line2=Got stuck on an error message
survey.line3=Asked too many questions
survey.line4=I didn't know it was free
survey.line5=I thought I needed a Xbox console
survey.line6=Not interested
survey.title=Feedback: Why didn't you sign in?
## end of 15.* release branch strings

tips.edu.1=You can hold down Shift to take a close up picture using the Camera.
tips.edu.2=Unlike Signs, you can edit Slates, Posters, and Boards over and over again.
tips.edu.3=Use Ctrl+B to turn on text-to-speech capabilities for in-game chat.
tips.edu.4=You can export your Minecraft world to share it with others.
tips.edu.5=To have your peers connect to your world, pause your game and share your join code.
tips.edu.6=Remember that to use slash commands, you need to enable cheats in your world.
tips.edu.7=Done with your creation? Have others tour your world as visitors to protect your work.
tips.edu.8=To use code builder, press C on your keyboard or touch the Agent icon at the top of your screen!
tips.edu.9=Use the Library to find new game worlds to build in.
tips.edu.10=Looking for a specific biome? Try the Library.
tips.edu.11=NPC stands for Non-Player Character.
tips.edu.12=In Survival Mode, players must find their own resources.
tips.edu.13=In Creative Mode, players can freely choose to build from any of the blocks in Minecraft.
tips.edu.14=Your code building robot helper is called the Agent.
tips.edu.15=Redstone can be used in Minecraft to create circuits and simple machines.
tips.edu.16=You can copy-and-paste from a text editor to a slate, poster, or board.
tips.edu.17=You can toggle Keyboard and Mouse Hints on or off in the Settings menu under Controls.
tips.edu.18=You can toggle Auto Jump on or off in the Settings menu under Controls.
tips.edu.19=While in Creative Mode, quickly press jump twice to fly.
tips.edu.20=Choose a Default Game Mode when creating world to specify the type of gameplay you want.
tips.edu.21=Use the command "/setworldspawn" to have new players begin playing where you are standing.
tips.edu.22=Choose a default player permission when creating your world to specify the permissions you want your classmates to have when they join.
tips.edu.23=Press I on your keyboard or touch the Immersive Reader icon to read or translate in-game text.

tips.game.1=Use seagrass to attract and breed sea turtles.
tips.game.2=Protect baby turtles from hostile mobs!
tips.game.3=Baby turtles will drop scutes when they grow - these can be crafted into turtle shells.
tips.game.4=Use a bed to set your respawn point during the day.
tips.game.5=Brew some water breathing potions for underwater exploration!
tips.game.6=Dolphins can lead you to shipwrecks and underwater ruins, feed them cod and follow their trail!
tips.game.7=Use shears to carve a pumpkin.
tips.game.8=Use phantom membranes to repair your elytra.
tips.game.9=There are 3587 types of tropical fish!
tips.game.10=Sea pickles can be smelted into lime green dye.
tips.game.11=Use haybales to breed Llamas!
tips.game.12=Compasses always point to the world spawn - craft one to find your way!
tips.game.13=Trade with villagers to obtain food, tools, and even treasure maps!
tips.game.14=Did you know you can hold a map in your off-hand?
tips.game.15=Leads can be used on boats.
tips.game.16=Sneak or wear frost walker boots to walk safely on magma blocks.
tips.game.17=You can gather cob webs using shears.
tips.game.18=Need diamonds? Try mining on Y coordinate 12!
tips.game.19=Before you mine diamonds, redstone or gold, make sure you're using an iron or diamond pickaxe or the ore won't drop.
tips.game.20=Need coal for torches or fuel? Try smelting wood logs in a furnace to make charcoal!
tips.game.21=Gold is more abundant in Mesa biomes!
tips.game.22=Elytra can be used to glide, find them in End City ships!
tips.game.23=Test out some new features by turning on the Experimental Gameplay option!
tips.game.24=Tridents are dropped by the Drowned, and can be held or thrown.
tips.game.25=Check out mixer.com/minecraft to watch the latest Minecraft livestreams.
tips.game.26=You can smelt 20 items in a furnace by using a block of dried kelp as fuel.
tips.game.27=Monsters that are killed by skeletons arrows have a chance of dropping music discs.
tips.game.28=People like YOU make Marketplace content!
tips.game.29=Did you know that several community creators make a living off the content in the Marketplace?
tips.game.30=You can find skins to customize your experience in-game, or on select platforms use your own!
tips.game.31=Texture packs change the way that blocks, items, and even the menus look in game. Try them out!
tips.game.32=Check out the Marketplace to find new worlds and adventures.
tips.game.33=Apply to become a Marketplace Creator!
tips.game.34=Mash-up packs come with a world to explore, skins, textures, and even new music.
tips.game.35=Did you know there are over 500 community-created Marketplace packs with more added every week?
tips.game.36=Join the discussion at discord.gg/Minecraft
tips.game.37=Emerald ores are the rarest ores in Minecraft! They can only be found in extreme hill biomes!
tips.game.38=Taming a skeleton horse doesn't require a saddle.
tips.game.39=Ocelots show creepers who's boss!
tips.game.40=Diorite, you either love it or hate it.
tips.game.41=Phantoms are dangerous mobs that appear during the night. Make sure to sleep regularly!
tips.game.42=Zombies turn into drowned if they sink underwater.
tips.game.43=MINECON takes place every year! Look out for the next one!
tips.game.44=Using fireworks boosts elytra speed in mid air. Just... make sure they don't explode, otherwise it'll hurt BADLY!
tips.game.45=Gold is most common in mesa biomes.
tips.game.46=Do people actually read these?
tips.game.47=Infinite wonders, Endless possibilities.
tips.game.48=DON'T LEAVE TREES FLOATING
tips.game.49=Sprint in water to swim!
tips.game.50=Riptide enchantment will propel you through the air in the rain.
tips.game.51=MINECON Earth is a worldwide interactive live-streaming event. Grab your items and join the party!
tips.game.52=Be nice to animals!
tips.game.53=The Beacon is a powerful item that can only be crafted with a nether star from the Wither!
tips.game.54=Don't kill dolphins, you monster!
tips.game.55=Did you know that nether stars can't be destroyed by explosions?
tips.game.56=When digging straight up, place a torch at your feet to break any sand or gravel that could potentially fall down on you.


splitscreen.joinPrompt=Join as Player %s
splitscreen.unavailable.toastHeader=Split Screen Is Unavailable
splitscreen.unavailable.toastMessage=A second player can't join right now.
splitscreen.unavailable.toastBodyInfo=Tap for more information.
splitscreen.joyconError.toastHeader=Can't Connect Joy-Con!
splitscreen.joyconError.toastMessage=You have too many controllers connected while in Local Network Mode to join this game.

usermanagement.changeUser=Change User

lateJoinScreen.waitingForUserReady=Getting things ready

hbui.Account.switchAccountButton=Switch account
hbui.Account.signOutButton=Sign out
hbui.Account.readPatchNotesButton=Read patch notes
hbui.Account.leaveFeedbackButton=Leave feedback
hbui.Achievements.header=Achievements
hbui.Achievements.labelHoursPlayed=Hours Played
hbui.Achievements.labelAchievements=Achievements
hbui.Achievements.labelGamerScore=Gamer Score
hbui.Achievements.labelWorldsPlayed=Worlds Played
hbui.Help.title=Help
hbui.Help.howToPlayButton=How to play
hbui.Help.faqButton=FAQ
hbui.Help.patchNotesButton=Patch notes
hbui.Help.closeButton=Close
hbui.MainMenu.signInLabel=Sign in
hbui.MainMenu.helpLabel=Help
hbui.MainMenu.invitesLabel=New invite
hbui.MainMenu.friendsLabel=Online
hbui.MainMenu.newWorld=Play
hbui.MainMenu.store=Marketplace
hbui.MainMenu.profile=Skins
hbui.MainMenu.achievements=Achievements
hbui.MainMenu.settings=Settings
hbui.MainMenu.recentWorldHeader=Recently played
hbui.MainMenu.marketPlaceHeader=Featured in Marketplace
hbui.MainMenu.realmsHeader=Realms
hbui.MainMenu.viewAllHeader=View all
hbui.MainMenu.heroVignette=Latest played
hbui.MainMenu.heroResumeButton=Resume
hbui.MainMenu.heroPlayButton=Resume world
hbui.MainMenu.survival=Survival
hbui.MainMenu.creative=Creative
hbui.MainMenu.miniGame=Minigame
hbui.Settings.trueLabel=On
hbui.Settings.falseLabel=Off
hbui.Settings.sensitivity=Sensitivity
hbui.Settings.invertYAxis=Invert Y Axis
hbui.Settings.autojump=Auto Jump
hbui.Settings.fullKeyboardGameplay=Full Keyboard Access
hbui.Settings.customKeyboardLayout=Custom Keyboard Layout

trade.doesNotWant=Trader does not want this
trade.excalamation=!
trade.expProgress=Experience: %d/%d
trade.expMax=Experience: MAX
trade.levelPrefix=Level %d
trade.levelPrefixAndName=Level %d - %s
trade.levelPrefixAndName.max=Max Level - %s
trade.level.1=Novice
trade.level.2=Apprentice
trade.level.3=Journeyman
trade.level.4=Expert
trade.level.5=Master
trade.mysteriousText=dab
trade.notEnough=Not enough %s
trade.question=?
trade.trade=Trade

map.rename=Rename Map
map.basicMap=Basic Map
map.locatorMap=Map that Shows Players
map.extendAndClear=Zoom Out & Clear
map.clone=Copy Map
map.name=Map Name

contentlog.history.title=Content Log History
contentlog.history.copyToClipboard=Copy to Clipboard
contentlog.logLocationSectionName=Content Log Location:
contentlog.settingsSectionName=Content Log Settings
contentlog.clear_files=Delete Old Content Logs
contentlog.delete.title=Delete Old Log Files?
contentlog.delete.body1=Select "Delete Now" to delete all previous log files.
contentlog.delete.body2=Note: The log file for your currently active session will remain.
contentlog.delete.delete=Delete Now
contentlog.delete.cancel=Cancel
contentlog.delete.progress=Deleting Content Logs

client.version.generic=Update %s
client.version.1.0=Ender Update (%s)
client.version.1.1=Discovery Update (%s)
client.version.1.2=Better Together Update (%s)
client.version.1.4=Update Aquatic (%s)
client.version.1.5=Update Aquatic (%s)
client.version.1.11=Village & Pillage (%s)

dr.banner.sale=Sale!
dr.banner.limitedTimeOffer=Limited Time Offer!
dressingRoom.skin_color_picker_title=Skin Color
dr.right_side.category_title=%s
dr.rarity.optional=Rarity
dr.rarity.optional.common=Common
dr.rarity.optional.epic=Epic
dr.rarity.optional.legendary=Legendary
dr.rarity.optional.rare=Rare
dr.rarity.optional.uncommon=Uncommon
key.emote=Emote
dr.loading=Loading...
dr.classic_skins.addition_skin_count=+%s
dr.classic_skins.choose_custom_skin=Choose New Skin
dr.classic_skins.custom_skin_title=Import Skin
dr.classic_skins.custom_skin_description=Import an image file from your device to use as your skin.  This will not sync between devices or games.
dr.classic_skins.custom_skin_section_title=Import
dr.classic_skins.invalidCustomSkin=That's not a Minecraft skin, silly.
dr.classic_skins.owned=Owned
dr.classic_skins.purchasable=Purchasable
dr.classic_skins.right_side.author_name=by %s
dr.classic_skins.right_side.featured_title=Featured & Recommended Skin Packs
dr.classic_skins.right_side.limited_usage=Limited usage
dr.classic_skins.right_side.limited_usage.tooltip=This skin can be used here, but may not roam.
dr.classic_skins.right_side.free_usage=Usable everywhere
dr.classic_skins.right_side.free_usage.tooltip=This skin will roam wherever you've logged in.
dr.classic_skins.right_side.purchasing_disclaimer=*Buys %s Minecoins for %s and redeems this pack for %s Minecoins leaving %s left over.
dr.classic_skins.right_side.skin_count.plural=%s Skins
dr.classic_skins.right_side.skin_count.singular=%s Skin
dr.classic_skins.right_side.texture_count.plural=%s Texture Packs
dr.classic_skins.right_side.texture_count.singular=%s Texture Pack
dr.classic_skins.right_side.world_count.plural=%s Worlds
dr.classic_skins.right_side.world_count.singular=%s World
dr.classic_skins.see_pack_in_store=See Pack in Store
dr.classic_skins.select_skin=Choose the correct model type for your skin
dr.classic_skins.select_skin.title=Skin Type
dr.classic_skins.upsellWithoutStore=You must purchase the skin pack to use that skin, and we can't connect to the Marketplace.
dr.header.capes=Capes
dr.zooming=Zooming
dr.header.classic_skins=Classic Skins
dr.header.customization=Character Creator
dr.header.featured=Featured Items
dr.header.previewAppearance=Preview Appearance
dr.arm_size_label_text=Arm Size
dr.body_size_label_text=Height
dr.mouth_color=Mouth
dr.iris_color=Iris
dr.sclera_color=Sclera
dr.eyebrows_color=Eyebrows
dr.hair_color=Hair
dr.facial_hair_color=Facial Hair
dr.skin_color=Base
dr.left_arm=Left Arm
dr.right_arm=Right Arm
dr.left_leg=Left Leg
dr.right_leg=Right Leg
dr.none_button_text=None
dr.default_piece_button_text=Default
dr.categories.body=Body
dr.categories.style=Style
dr.categories.base=Base
dr.categories.eyes=Eyes
dr.categories.mouth=Mouth
dr.categories.hair=Hair
dr.categories.facial_hair=Facial Hair
dr.categories.arms=Arms
dr.categories.legs=Legs
dr.categories.size=Size
dr.categories.top=Top
dr.categories.bottom=Bottom
dr.categories.outerwear=Outerwear
dr.categories.headwear=Headwear
dr.categories.gloves=Gloves
dr.categories.footwear=Footwear
dr.categories.back_item=Back Item
dr.categories.face_item=Face Item
dr.categories.capes=Capes
dr.notification.to_skins=Switching to Classic Skins
dr.notification.to_persona=Switching to Character Creator
dr.notification.persona_save=The Character has been saved
dr.notification.persona_create=The Character has been created
dr.notification.persona_delete=The Character has been deleted
dr.notification.item_selection_failed=Failed to select item
dr.notification.character_failed=Failed to load character %s
dr.modal.persona_delete_confirm=The current character will be deleted

dr.default.alex.bottom=Alex's Pants
dr.default.alex.hair=Alex's Hair
dr.default.alex.mouth=Alex's Mouth
dr.default.alex.shoes=Alex's Shoes
dr.default.alex.skin=Alex
dr.default.alex.top=Alex's Shirt
dr.default.eyes=Standard Stare
dr.default.steve.bottom=Steve's Pants
dr.default.steve.hair=Steve's Hair
dr.default.steve.mouth=Steve's Mouth
dr.default.steve.shoes=Steve's Shoes
dr.default.steve.skin=Steve
dr.default.steve.top=Steve's Shirt
