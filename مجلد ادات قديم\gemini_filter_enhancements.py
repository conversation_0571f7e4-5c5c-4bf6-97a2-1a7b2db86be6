# -*- coding: utf-8 -*-
"""
تحسينات إضافية لفلتر الصور الذكي باستخدام Gemini AI
"""

import re
import time
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import hashlib

class GeminiFilterEnhancements:
    """تحسينات إضافية لفلتر الصور"""

    def __init__(self):
        self.cache = {}  # تخزين مؤقت للنتائج
        self.image_analysis_cache = {}  # تخزين مؤقت لتحليل الصور
        
    def pre_filter_images(self, image_urls: List[str], mod_name: str) -> List[str]:
        """فلترة أولية للصور قبل إرسالها إلى Gemini"""
        print("🔍 بدء الفلترة الأولية للصور...")
        
        filtered_urls = []
        
        for url in image_urls:
            if self._is_likely_mod_image(url, mod_name):
                filtered_urls.append(url)
                print(f"✅ صورة محتملة: {url[:50]}...")
            else:
                print(f"❌ صورة مرفوضة: {url[:50]}...")
        
        print(f"📊 الفلترة الأولية: {len(filtered_urls)} من أصل {len(image_urls)}")
        return filtered_urls

    def _is_likely_mod_image(self, url: str, mod_name: str) -> bool:
        """تحديد ما إذا كانت الصورة محتملة لتكون صورة مود"""
        url_lower = url.lower()
        
        # رفض الصور الواضحة غير المرغوبة
        unwanted_patterns = [
            'gravatar.com',
            'avatar',
            'profile',
            'user',
            'shield.png',
            'logo',
            'favicon',
            'icon',
            'banner',
            'advertisement',
            'social',
            'share',
            'widget',
            'placeholder',
            'loading',
            'spinner',
            'empty.png',
            '_nuxt',
            'data:image'
        ]
        
        if any(pattern in url_lower for pattern in unwanted_patterns):
            return False
        
        # قبول الصور من مصادر موثوقة
        trusted_patterns = [
            'media.forgecdn.net/attachments',
            'mcpedl.com/wp-content/uploads',
            'api.mcpedl.com/storage',
            'r2.mcpedl.com/content'
        ]
        
        if any(pattern in url_lower for pattern in trusted_patterns):
            return True
        
        # فحص إضافي بناءً على اسم المود
        if mod_name:
            mod_keywords = self._extract_mod_keywords(mod_name)
            if any(keyword in url_lower for keyword in mod_keywords):
                return True
        
        # قبول الصور العامة من mcpedl.com (ما عدا المستخدمين)
        if 'mcpedl.com' in url_lower and '/users/' not in url_lower:
            return True
        
        return False

    def _extract_mod_keywords(self, mod_name: str) -> List[str]:
        """استخراج كلمات مفتاحية من اسم المود"""
        if not mod_name:
            return []
        
        # تنظيف اسم المود
        clean_name = re.sub(r'[^\w\s]', ' ', mod_name.lower())
        words = clean_name.split()
        
        # إزالة الكلمات الشائعة
        common_words = ['mod', 'addon', 'pack', 'texture', 'resource', 'behavior', 'v', 'version']
        keywords = [word for word in words if word not in common_words and len(word) > 2]
        
        return keywords

    def enhance_prompt_with_context(self, base_prompt: str, mod_data: Dict, page_content: str = "") -> str:
        """تحسين prompt بإضافة سياق إضافي"""
        enhanced_prompt = base_prompt
        
        # إضافة معلومات إضافية عن المود
        if mod_data.get('category'):
            enhanced_prompt += f"\n**فئة المود:** {mod_data['category']}"
        
        if mod_data.get('version'):
            enhanced_prompt += f"\n**الإصدار:** {mod_data['version']}"
        
        # إضافة كلمات مفتاحية من محتوى الصفحة
        if page_content:
            keywords = self._extract_content_keywords(page_content)
            if keywords:
                enhanced_prompt += f"\n**كلمات مفتاحية من الصفحة:** {', '.join(keywords[:10])}"
        
        # إضافة تعليمات محددة بناءً على نوع المود
        category = mod_data.get('category', '').lower()
        if 'dragon' in mod_data.get('name', '').lower():
            enhanced_prompt += "\n**ملاحظة خاصة:** ابحث عن صور التنانين، الطيران، والمخلوقات الجديدة"
        elif 'texture' in category or 'pack' in category:
            enhanced_prompt += "\n**ملاحظة خاصة:** ابحث عن صور الكتل، التكسشرز، والمواد المحسنة"
        elif 'shader' in category:
            enhanced_prompt += "\n**ملاحظة خاصة:** ابحث عن صور الإضاءة، الظلال، والتأثيرات البصرية"
        
        return enhanced_prompt

    def _extract_content_keywords(self, content: str) -> List[str]:
        """استخراج كلمات مفتاحية من محتوى الصفحة"""
        if not content:
            return []
        
        # كلمات مفتاحية متعلقة بـ Minecraft
        minecraft_keywords = [
            'dragon', 'mob', 'block', 'item', 'texture', 'shader', 'biome',
            'dimension', 'weapon', 'armor', 'tool', 'food', 'potion',
            'enchantment', 'recipe', 'crafting', 'building', 'structure',
            'village', 'dungeon', 'boss', 'pet', 'mount', 'vehicle'
        ]
        
        content_lower = content.lower()
        found_keywords = []
        
        for keyword in minecraft_keywords:
            if keyword in content_lower:
                found_keywords.append(keyword)
        
        return found_keywords

    def post_process_results(self, selected_images: List[str], original_images: List[str]) -> List[str]:
        """معالجة إضافية للنتائج"""
        if not selected_images:
            print("⚠️ لم يختر Gemini أي صور، سيتم تطبيق فلترة احتياطية")
            return self._fallback_selection(original_images)
        
        # ترتيب الصور حسب الأولوية
        prioritized_images = self._prioritize_images(selected_images)
        
        # إزالة الصور المكررة
        unique_images = self._remove_duplicates(prioritized_images)
        
        return unique_images

    def _fallback_selection(self, images: List[str]) -> List[str]:
        """اختيار احتياطي في حالة عدم اختيار Gemini لأي صور"""
        fallback_images = []
        
        # أولوية للصور من forgecdn
        for img in images:
            if 'media.forgecdn.net/attachments' in img.lower():
                fallback_images.append(img)
        
        # إذا لم نجد صور forgecdn، أخذ صور من مصادر موثوقة أخرى
        if not fallback_images:
            trusted_patterns = [
                'mcpedl.com/wp-content/uploads',
                'api.mcpedl.com/storage',
                'r2.mcpedl.com/content'
            ]
            
            for img in images:
                if any(pattern in img.lower() for pattern in trusted_patterns):
                    fallback_images.append(img)
                    if len(fallback_images) >= 5:  # حد أقصى 5 صور
                        break
        
        # إذا لم نجد أي صور موثوقة، أخذ أول 3 صور
        if not fallback_images:
            fallback_images = images[:3]
        
        print(f"🔄 الاختيار الاحتياطي: {len(fallback_images)} صور")
        return fallback_images

    def _prioritize_images(self, images: List[str]) -> List[str]:
        """ترتيب الصور حسب الأولوية"""
        def get_priority(img_url: str) -> int:
            url_lower = img_url.lower()
            
            # أولوية عالية للصور من forgecdn
            if 'media.forgecdn.net/attachments' in url_lower:
                return 1
            
            # أولوية متوسطة للصور من wp-content
            if 'mcpedl.com/wp-content/uploads' in url_lower:
                return 2
            
            # أولوية منخفضة للصور الأخرى
            return 3
        
        return sorted(images, key=get_priority)

    def _remove_duplicates(self, images: List[str]) -> List[str]:
        """إزالة الصور المكررة"""
        seen = set()
        unique_images = []
        
        for img in images:
            # إنشاء hash للصورة بناءً على الرابط
            img_hash = hashlib.md5(img.encode()).hexdigest()
            
            if img_hash not in seen:
                seen.add(img_hash)
                unique_images.append(img)
        
        return unique_images

    def analyze_image_quality(self, image_url: str) -> Dict[str, any]:
        """تحليل جودة الصورة (بدون تحميل فعلي)"""
        analysis = {
            'likely_quality': 'medium',
            'estimated_relevance': 0.5,
            'source_trust': 'medium'
        }
        
        url_lower = image_url.lower()
        
        # تحليل مصدر الصورة
        if 'media.forgecdn.net/attachments' in url_lower:
            analysis['source_trust'] = 'high'
            analysis['estimated_relevance'] = 0.9
        elif 'mcpedl.com/wp-content' in url_lower:
            analysis['source_trust'] = 'high'
            analysis['estimated_relevance'] = 0.8
        elif 'gravatar' in url_lower or 'avatar' in url_lower:
            analysis['source_trust'] = 'low'
            analysis['estimated_relevance'] = 0.1
        
        # تحليل اسم الملف
        filename = urlparse(image_url).path.split('/')[-1].lower()
        
        # مؤشرات جودة عالية
        quality_indicators = ['screenshot', 'preview', 'showcase', 'feature']
        if any(indicator in filename for indicator in quality_indicators):
            analysis['likely_quality'] = 'high'
            analysis['estimated_relevance'] += 0.2
        
        # مؤشرات جودة منخفضة
        low_quality_indicators = ['thumb', 'icon', 'small', 'mini']
        if any(indicator in filename for indicator in low_quality_indicators):
            analysis['likely_quality'] = 'low'
            analysis['estimated_relevance'] -= 0.3
        
        # تحديد الحد الأدنى والأقصى
        analysis['estimated_relevance'] = max(0.0, min(1.0, analysis['estimated_relevance']))
        
        return analysis

    def create_detailed_report(self, original_images: List[str], filtered_images: List[str], 
                             processing_time: float) -> Dict[str, any]:
        """إنشاء تقرير مفصل عن عملية الفلترة"""
        report = {
            'summary': {
                'total_original': len(original_images),
                'total_filtered': len(filtered_images),
                'filter_ratio': len(filtered_images) / len(original_images) if original_images else 0,
                'processing_time_seconds': processing_time
            },
            'image_analysis': [],
            'recommendations': []
        }
        
        # تحليل كل صورة مفلترة
        for img in filtered_images:
            analysis = self.analyze_image_quality(img)
            analysis['url'] = img
            report['image_analysis'].append(analysis)
        
        # إضافة توصيات
        if len(filtered_images) == 0:
            report['recommendations'].append("لم يتم اختيار أي صور - تحقق من جودة الصور الأصلية")
        elif len(filtered_images) < 3:
            report['recommendations'].append("عدد قليل من الصور - قد تحتاج لمراجعة معايير الفلترة")
        elif len(filtered_images) > 10:
            report['recommendations'].append("عدد كبير من الصور - قد تحتاج لمعايير فلترة أكثر صرامة")
        
        # تحليل مصادر الصور
        sources = {}
        for img in filtered_images:
            domain = urlparse(img).netloc
            sources[domain] = sources.get(domain, 0) + 1
        
        report['source_distribution'] = sources
        
        return report

def test_enhancements():
    """اختبار التحسينات"""
    print("🧪 اختبار تحسينات فلتر الصور")
    print("=" * 50)
    
    enhancements = GeminiFilterEnhancements()
    
    # صور تجريبية
    test_images = [
        "https://media.forgecdn.net/attachments/123/456/dragon_screenshot.png",
        "https://mcpedl.com/wp-content/uploads/2023/01/mod_preview.jpg",
        "https://gravatar.com/avatar/user123.png",
        "https://mcpedl.com/img/shield.png",
        "https://r2.mcpedl.com/content/dragon_feature.png"
    ]
    
    # اختبار الفلترة الأولية
    print("🔍 اختبار الفلترة الأولية...")
    filtered = enhancements.pre_filter_images(test_images, "Dragon Mounts")
    print(f"✅ نتيجة الفلترة الأولية: {len(filtered)} صور")
    
    # اختبار تحليل جودة الصور
    print("\n📊 اختبار تحليل جودة الصور...")
    for img in filtered:
        analysis = enhancements.analyze_image_quality(img)
        print(f"   {img[:50]}... - جودة: {analysis['likely_quality']}, صلة: {analysis['estimated_relevance']:.2f}")
    
    # اختبار إنشاء التقرير
    print("\n📋 اختبار إنشاء التقرير...")
    report = enhancements.create_detailed_report(test_images, filtered, 2.5)
    print(f"   إجمالي الصور الأصلية: {report['summary']['total_original']}")
    print(f"   إجمالي الصور المفلترة: {report['summary']['total_filtered']}")
    print(f"   نسبة الفلترة: {report['summary']['filter_ratio']:.2f}")
    
    print("\n✅ انتهى اختبار التحسينات")

if __name__ == "__main__":
    test_enhancements()
