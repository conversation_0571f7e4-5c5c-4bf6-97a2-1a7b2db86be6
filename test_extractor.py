# -*- coding: utf-8 -*-
"""
ملف اختبار مستخرج الصور المحسن
يختبر الوظائف الأساسية ويتأكد من عمل الأداة بشكل صحيح

المطور: MiniMax Agent
التاريخ: 2025-06-22
"""

import sys
import os
from mcpedl_image_extractor_v2 import extract_mcpedl_images, get_mod_images_only

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("🧪 اختبار الوظائف الأساسية...")
    
    # روابط اختبار معروفة
    test_urls = [
        "https://mcpedl.com/glow-em-all-shader/",
        "https://mcpedl.com/simple-guns-fws/",
        "https://mcpedl.com/azify-revive-shader/"
    ]
    
    results = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{'='*60}")
        print(f"🔍 اختبار {i}: {url}")
        print('='*60)
        
        try:
            # اختبار الاستخراج الكامل
            result = extract_mcpedl_images(url, use_selenium=True, headless=True)
            
            if result.get('success', False):
                print(f"✅ نجح الاستخراج:")
                print(f"   اسم المود: {result['mod_name']}")
                print(f"   عدد الصور: {len(result['main_mod_images'])}")
                print(f"   طريقة الاستخراج: {result['extraction_method']}")
                
                # عرض أول 3 صور
                if result['main_mod_images']:
                    print(f"   أول 3 صور:")
                    for j, img in enumerate(result['main_mod_images'][:3], 1):
                        print(f"     {j}. {img[:60]}...")
                
                results.append({
                    'url': url,
                    'success': True,
                    'images_count': len(result['main_mod_images']),
                    'mod_name': result['mod_name']
                })
            else:
                print(f"❌ فشل الاستخراج: {result.get('error', 'خطأ غير معروف')}")
                results.append({
                    'url': url,
                    'success': False,
                    'error': result.get('error', 'خطأ غير معروف')
                })
        
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            results.append({
                'url': url,
                'success': False,
                'error': str(e)
            })
    
    return results

def test_simple_function():
    """اختبار الدالة المبسطة"""
    print("\n🧪 اختبار الدالة المبسطة...")
    
    test_url = "https://mcpedl.com/glow-em-all-shader/"
    
    try:
        images = get_mod_images_only(test_url)
        
        if images:
            print(f"✅ الدالة المبسطة تعمل - تم استخراج {len(images)} صورة")
            return True
        else:
            print("❌ الدالة المبسطة لم تستخرج أي صور")
            return False
    
    except Exception as e:
        print(f"❌ خطأ في الدالة المبسطة: {str(e)}")
        return False

def print_test_summary(results):
    """طباعة ملخص الاختبارات"""
    print(f"\n{'='*80}")
    print("📊 ملخص نتائج الاختبارات")
    print('='*80)
    
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"🎯 معدل النجاح: {successful}/{total} ({successful/total*100:.1f}%)")
    
    print(f"\n✅ الاختبارات الناجحة:")
    for result in results:
        if result['success']:
            print(f"   • {result['mod_name']} - {result['images_count']} صورة")
    
    if successful < total:
        print(f"\n❌ الاختبارات الفاشلة:")
        for result in results:
            if not result['success']:
                print(f"   • {result['url']} - {result['error']}")
    
    # تقييم الأداء
    if successful == total:
        print(f"\n🎉 ممتاز! جميع الاختبارات نجحت - الأداة جاهزة للاستخدام")
    elif successful >= total * 0.7:
        print(f"\n👍 جيد! معظم الاختبارات نجحت - الأداة تعمل بشكل مقبول")
    else:
        print(f"\n⚠️ تحتاج لتحسين! فشل عدد كبير من الاختبارات")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار مستخرج الصور المحسن...")
    print("⏳ هذا قد يستغرق بضع دقائق...")
    
    try:
        # اختبار الوظائف الأساسية
        results = test_basic_functionality()
        
        # اختبار الدالة المبسطة
        simple_success = test_simple_function()
        
        # طباعة الملخص
        print_test_summary(results)
        
        # نصائح إضافية
        print(f"\n💡 نصائح:")
        print(f"   • للحصول على أفضل دقة، استخدم Selenium (الخيار الافتراضي)")
        print(f"   • إذا كان Selenium بطيء، يمكنك استخدام CloudScraper")
        print(f"   • تأكد من اتصال الإنترنت المستقر")
        
        print(f"\n✅ اكتمل الاختبار!")
        
        return len([r for r in results if r['success']]) > 0
    
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
