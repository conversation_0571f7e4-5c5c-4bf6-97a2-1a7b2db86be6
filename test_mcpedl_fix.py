# -*- coding: utf-8 -*-
"""
اختبار محسن لحل مشاكل MCPEDL scraper
"""

import sys
import os
import time
import traceback

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🌐 اختبار الاتصال الأساسي...")
    
    try:
        import requests
        
        # اختبار الاتصال بـ Google أولاً
        response = requests.get("https://www.google.com", timeout=10)
        if response.status_code == 200:
            print("✅ الاتصال بالإنترنت يعمل")
        else:
            print("❌ مشكلة في الاتصال بالإنترنت")
            return False
            
        # اختبار الاتصال بـ MCPEDL
        response = requests.get("https://mcpedl.com", timeout=15)
        print(f"رمز الاستجابة من MCPEDL: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ يمكن الوصول إلى MCPEDL")
            return True
        else:
            print("⚠️ مشكلة في الوصول إلى MCPEDL")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_cloudscraper():
    """اختبار cloudscraper"""
    print("\n☁️ اختبار cloudscraper...")
    
    try:
        import cloudscraper
        
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            }
        )
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        response = scraper.get(test_url, timeout=30)
        print(f"رمز الاستجابة: {response.status_code}")
        print(f"حجم المحتوى: {len(response.text)} حرف")
        
        if response.status_code == 200 and len(response.text) > 5000:
            print("✅ cloudscraper يعمل بشكل صحيح")
            
            # حفظ المحتوى للفحص
            with open('test_cloudscraper_output.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("💾 تم حفظ المحتوى في test_cloudscraper_output.html")
            
            # فحص سريع للمحتوى
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"📝 العنوان: {title.get_text()[:100]}...")
                return True
            else:
                print("⚠️ لا يوجد عنوان في الصفحة")
                return False
        else:
            print("❌ cloudscraper لا يعمل بشكل صحيح")
            return False
            
    except ImportError:
        print("❌ cloudscraper غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في cloudscraper: {e}")
        return False

def test_selenium():
    """اختبار Selenium"""
    print("\n🤖 اختبار Selenium...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, WebDriverException
        
        # إعداد Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        driver = None
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            
            test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
            print(f"اختبار الرابط: {test_url}")
            
            driver.get(test_url)
            
            # انتظار تحميل العنوان
            try:
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "title"))
                )
                print("✅ تم تحميل العنوان")
            except TimeoutException:
                print("⚠️ انتهت مهلة انتظار العنوان")
            
            # الحصول على HTML
            html_content = driver.page_source
            print(f"حجم المحتوى: {len(html_content)} حرف")
            
            if len(html_content) > 5000:
                print("✅ Selenium يعمل بشكل صحيح")
                
                # حفظ المحتوى للفحص
                with open('test_selenium_output.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("💾 تم حفظ المحتوى في test_selenium_output.html")
                
                return True
            else:
                print("❌ محتوى قليل من Selenium")
                return False
                
        finally:
            if driver:
                try:
                    driver.quit()
                    print("تم إغلاق المتصفح")
                except:
                    pass
                    
    except ImportError:
        print("❌ Selenium غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في Selenium: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_mcpedl_scraper():
    """اختبار MCPEDL scraper"""
    print("\n🔧 اختبار MCPEDL scraper...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار استخراج البيانات من: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            print("✅ تم استخراج البيانات بنجاح!")
            print(f"اسم المود: {result.get('name', 'غير متوفر')}")
            print(f"الفئة: {result.get('category', 'غير متوفر')}")
            print(f"عدد الصور: {len(result.get('image_urls', []))}")
            print(f"طول الوصف: {len(result.get('description', ''))} حرف")
            return True
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في MCPEDL scraper: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار شامل لحل مشاكل MCPEDL scraper")
    print("=" * 60)
    
    results = {}
    
    # اختبار الاتصال الأساسي
    results['basic_connection'] = test_basic_connection()
    
    # اختبار cloudscraper
    results['cloudscraper'] = test_cloudscraper()
    
    # اختبار Selenium
    results['selenium'] = test_selenium()
    
    # اختبار MCPEDL scraper
    results['mcpedl_scraper'] = test_mcpedl_scraper()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    # توصيات
    print("\n💡 التوصيات:")
    if not results['basic_connection']:
        print("- تحقق من الاتصال بالإنترنت")
    if not results['cloudscraper']:
        print("- تثبيت cloudscraper: pip install cloudscraper")
    if not results['selenium']:
        print("- تثبيت selenium: pip install selenium")
        print("- تأكد من تثبيت Chrome browser")
    if not results['mcpedl_scraper']:
        print("- تحقق من ملفات MCPEDL scraper")
    
    if all(results.values()):
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n⚠️ بعض الاختبارات فشلت - راجع التوصيات أعلاه")

if __name__ == "__main__":
    main()
