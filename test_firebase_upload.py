# -*- coding: utf-8 -*-
"""
اختبار رفع الملفات إلى Firebase Storage
"""

import os
import json
import tempfile
from datetime import datetime

def create_test_files():
    """إنشاء ملفات اختبار مؤقتة"""
    test_files = {}
    
    # إنشاء ملف نصي للاختبار
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write("هذا ملف اختبار لرفع Firebase Storage\n")
        f.write(f"تم إنشاؤه في: {datetime.now()}\n")
        f.write("محتوى تجريبي للاختبار")
        test_files['text'] = f.name
    
    # إنشاء ملف JSON للاختبار
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "message": "اختبار رفع ملف JSON"
        }
        json.dump(test_data, f, ensure_ascii=False, indent=2)
        test_files['json'] = f.name
    
    return test_files

def test_firebase_storage_upload():
    """اختبار رفع الملفات إلى Firebase Storage"""
    print("📦 اختبار رفع الملفات إلى Firebase Storage...")
    
    try:
        import firebase_admin
        from firebase_admin import credentials, storage
        
        # التحقق من ملف الخدمة
        service_account_file = "firebase-service-account.json"
        if not os.path.exists(service_account_file):
            print(f"❌ ملف خدمة Firebase غير موجود: {service_account_file}")
            return False
        
        # قراءة معلومات المشروع
        with open(service_account_file, 'r') as f:
            service_account_info = json.load(f)
            project_id = service_account_info.get('project_id')
        
        # التحقق من وجود تطبيق مُهيأ
        try:
            app = firebase_admin.get_app()
        except ValueError:
            # تهيئة تطبيق جديد
            cred = credentials.Certificate(service_account_file)
            app = firebase_admin.initialize_app(cred, {
                'storageBucket': f'{project_id}.firebasestorage.app'
            })
        
        # الحصول على bucket
        bucket = storage.bucket()
        print(f"✅ متصل بـ Firebase Storage: {bucket.name}")
        
        # إنشاء ملفات اختبار
        test_files = create_test_files()
        uploaded_files = []
        
        try:
            # رفع الملفات
            for file_type, file_path in test_files.items():
                print(f"\n🔄 رفع ملف {file_type}...")
                
                # تحديد مسار الملف في Storage
                storage_path = f"test_uploads/{file_type}_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_type}"
                
                # رفع الملف
                blob = bucket.blob(storage_path)
                blob.upload_from_filename(file_path)
                
                # جعل الملف قابل للقراءة عامة (اختياري)
                blob.make_public()
                
                print(f"✅ تم رفع {file_type}: {storage_path}")
                print(f"🔗 رابط الملف: {blob.public_url}")
                
                uploaded_files.append({
                    'type': file_type,
                    'storage_path': storage_path,
                    'public_url': blob.public_url,
                    'size': blob.size
                })
            
            # عرض ملخص الرفع
            print(f"\n📊 ملخص الرفع:")
            print(f"✅ تم رفع {len(uploaded_files)} ملف بنجاح")
            
            for file_info in uploaded_files:
                print(f"  📁 {file_info['type']}: {file_info['storage_path']}")
                print(f"     📏 الحجم: {file_info.get('size', 'غير معروف')} بايت")
            
            # اختبار تحميل الملفات
            print(f"\n🔄 اختبار تحميل الملفات...")
            for file_info in uploaded_files:
                blob = bucket.blob(file_info['storage_path'])
                if blob.exists():
                    print(f"✅ الملف موجود: {file_info['storage_path']}")
                else:
                    print(f"❌ الملف غير موجود: {file_info['storage_path']}")
            
            # تنظيف الملفات المرفوعة (اختياري)
            print(f"\n🧹 تنظيف ملفات الاختبار...")
            for file_info in uploaded_files:
                blob = bucket.blob(file_info['storage_path'])
                blob.delete()
                print(f"🗑️ تم حذف: {file_info['storage_path']}")
            
            return True
            
        finally:
            # تنظيف الملفات المؤقتة
            for file_path in test_files.values():
                try:
                    os.unlink(file_path)
                except:
                    pass
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رفع Firebase Storage: {e}")
        return False

def test_firebase_storage_operations():
    """اختبار عمليات Firebase Storage المختلفة"""
    print("\n🔧 اختبار عمليات Firebase Storage...")
    
    try:
        import firebase_admin
        from firebase_admin import storage
        
        bucket = storage.bucket()
        
        # إدراج الملفات الموجودة
        print("📋 إدراج الملفات الموجودة...")
        blobs = list(bucket.list_blobs(max_results=10))
        print(f"📁 عدد الملفات: {len(blobs)}")
        
        for i, blob in enumerate(blobs[:5], 1):
            print(f"  {i}. {blob.name} ({blob.size} بايت)")
        
        # اختبار إنشاء مجلدات
        print(f"\n📁 اختبار إنشاء مجلدات...")
        folders = ['images', 'mods', 'temp']
        
        for folder in folders:
            # إنشاء ملف فارغ لتمثيل المجلد
            folder_blob = bucket.blob(f"{folder}/.keep")
            folder_blob.upload_from_string("")
            print(f"✅ تم إنشاء مجلد: {folder}/")
        
        # تنظيف ملفات .keep
        for folder in folders:
            try:
                folder_blob = bucket.blob(f"{folder}/.keep")
                folder_blob.delete()
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات Firebase Storage: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لرفع الملفات - Firebase Storage")
    print("=" * 60)
    
    results = {}
    
    # اختبار رفع الملفات
    results['upload'] = test_firebase_storage_upload()
    
    # اختبار العمليات
    results['operations'] = test_firebase_storage_operations()
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, status in results.items():
        status_icon = "✅" if status else "❌"
        test_names = {
            'upload': 'رفع الملفات',
            'operations': 'العمليات'
        }
        print(f"{status_icon} {test_names.get(test_name, test_name)}: {'نجح' if status else 'فشل'}")
    
    print(f"\n📈 معدل النجاح: {passed_tests}/{total_tests} ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع اختبارات رفع الملفات نجحت!")
        print("✅ Firebase Storage جاهز للاستخدام في التطبيق")
    else:
        print("\n⚠️ بعض اختبارات رفع الملفات فشلت")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
