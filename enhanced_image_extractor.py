# -*- coding: utf-8 -*-
"""
مستخرج الصور المحسن لمودات MCPEDL - إصدار محسن
يحل مشكلة استخراج الصور الخاطئة ويركز على الصورة الرئيسية للمود

الميزات المحسنة:
✅ استخراج دقيق للصورة الرئيسية فقط
✅ تجنب صور المودات الأخرى المقترحة 
✅ فلترة ذكية للصور غير المرغوبة
✅ دعم الصور المخفية في الصفحة
✅ تحسين أداء الاستخراج

المطور: MiniMax Agent
تاريخ التحديث: 2025-06-23
"""

import time
import json
import re
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup

# محاولة استيراد Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# محاولة استيراد cloudscraper
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False


class EnhancedImageExtractor:
    """
    مستخرج محسن للصور الرئيسية لمودات MCPEDL
    يركز على استخراج الصورة الرئيسية فقط وتجنب الصور المقترحة
    """
    
    def __init__(self, prefer_selenium=True, headless=True, timeout=20):
        """
        تهيئة المستخرج المحسن
        
        Args:
            prefer_selenium: تفضيل استخدام Selenium للدقة
            headless: تشغيل المتصفح في الخلفية
            timeout: مهلة انتظار تحميل المحتوى
        """
        self.prefer_selenium = prefer_selenium and SELENIUM_AVAILABLE
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self.session = None
        
        # قائمة بكلمات مفتاحية لتجنب الصور غير المرغوبة
        self.unwanted_keywords = [
            'related', 'suggested', 'recommendation', 'more', 'other',
            'أخرى', 'مقترح', 'ذات صلة', 'مشابه', 'مقترحة',
            'popular', 'trending', 'featured', 'similar',
            'logo', 'avatar', 'profile', 'banner',
            'ad', 'advertisement', 'sponsor'
        ]
        
        # محاولة الحصول على أدوات الاستخراج
        self._setup_extractor()
    
    def _setup_extractor(self):
        """إعداد أداة الاستخراج المناسبة"""
        if self.prefer_selenium:
            try:
                self._setup_selenium()
                print("✅ تم إعداد Selenium WebDriver للاستخراج المحسن")
                return
            except Exception as e:
                print(f"⚠️ فشل إعداد Selenium: {e}")
        
        # إعداد cloudscraper كبديل
        if CLOUDSCRAPER_AVAILABLE:
            try:
                self._setup_cloudscraper()
                print("✅ تم إعداد cloudscraper للاستخراج")
                return
            except Exception as e:
                print(f"⚠️ فشل إعداد cloudscraper: {e}")
        
        # استخدام requests كبديل أخير
        self._setup_requests()
        print("✅ تم إعداد requests (وظائف محدودة)")
    
    def _setup_selenium(self):
        """إعداد Selenium WebDriver مع تحسينات للأداء"""
        if not SELENIUM_AVAILABLE:
            raise RuntimeError("Selenium غير متاح")
        
        options = Options()
        if self.headless:
            options.add_argument('--headless')
        
        # تحسينات الأداء
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')  # لتسريع التحميل
        options.add_argument('--disable-javascript')  # إذا لم تكن مطلوبة
        options.add_argument('--window-size=1920,1080')
        
        # إعدادات إضافية لتجنب كشف البوت
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.set_page_load_timeout(self.timeout)
    
    def _setup_cloudscraper(self):
        """إعداد cloudscraper"""
        self.session = cloudscraper.create_scraper()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def _setup_requests(self):
        """إعداد requests عادي"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def extract_main_mod_image(self, url: str) -> Dict[str, Any]:
        """
        استخراج الصورة الرئيسية للمود فقط مع تجنب الصور المقترحة
        
        Args:
            url: رابط صفحة المود في MCPEDL
            
        Returns:
            dict: نتيجة الاستخراج مع الصورة الرئيسية
        """
        print(f"🔍 بدء استخراج الصورة الرئيسية من: {url}")
        
        try:
            # الحصول على محتوى الصفحة
            page_content = self._get_page_content(url)
            if not page_content:
                return {
                    'success': False,
                    'error': 'فشل في تحميل محتوى الصفحة',
                    'main_image': None
                }
            
            # تحليل المحتوى باستخدام BeautifulSoup
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # استخراج الصورة الرئيسية باستخدام عدة طرق
            main_image_url = self._find_main_image(soup, url)
            
            if main_image_url:
                print(f"✅ تم العثور على الصورة الرئيسية: {main_image_url}")
                return {
                    'success': True,
                    'main_image': main_image_url,
                    'error': None
                }
            else:
                print("❌ لم يتم العثور على صورة رئيسية")
                return {
                    'success': False,
                    'error': 'لم يتم العثور على صورة رئيسية',
                    'main_image': None
                }
                
        except Exception as e:
            print(f"❌ خطأ في استخراج الصورة: {e}")
            return {
                'success': False,
                'error': str(e),
                'main_image': None
            }
        
        finally:
            self._cleanup()
    
    def _get_page_content(self, url: str) -> Optional[str]:
        """
        الحصول على محتوى الصفحة باستخدام الطريقة المتاحة
        """
        if self.driver:
            return self._get_content_selenium(url)
        elif self.session:
            return self._get_content_requests(url)
        else:
            raise RuntimeError("لا توجد أدوات HTTP متاحة")
    
    def _get_content_selenium(self, url: str) -> Optional[str]:
        """الحصول على المحتوى باستخدام Selenium"""
        try:
            self.driver.get(url)
            
            # انتظار تحميل المحتوى
            WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "img"))
            )
            
            # انتظار إضافي للمحتوى الديناميكي
            time.sleep(3)
            
            return self.driver.page_source
            
        except TimeoutException:
            print("⚠️ انتهت مهلة انتظار تحميل الصفحة")
            return self.driver.page_source if self.driver else None
        except Exception as e:
            print(f"❌ خطأ في Selenium: {e}")
            return None
    
    def _get_content_requests(self, url: str) -> Optional[str]:
        """الحصول على المحتوى باستخدام requests/cloudscraper"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"❌ خطأ في requests: {e}")
            return None
    
    def _find_main_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """
        البحث عن الصورة الرئيسية للمود باستخدام عدة استراتيجيات
        """
        print("🔍 البحث عن الصورة الرئيسية...")
        
        # الاستراتيجية 1: البحث عن meta og:image
        main_image = self._find_og_image(soup, base_url)
        if main_image:
            return main_image
        
        # الاستراتيجية 2: البحث في المحتوى الرئيسي
        main_image = self._find_content_main_image(soup, base_url)
        if main_image:
            return main_image
        
        # الاستراتيجية 3: البحث في صور العرض الرئيسية
        main_image = self._find_gallery_main_image(soup, base_url)
        if main_image:
            return main_image
        
        # الاستراتيجية 4: البحث العام مع فلترة
        main_image = self._find_filtered_main_image(soup, base_url)
        if main_image:
            return main_image
        
        print("⚠️ لم يتم العثور على صورة رئيسية بأي من الطرق")
        return None
    
    def _find_og_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """البحث عن صورة Open Graph"""
        og_image = soup.find('meta', {'property': 'og:image'})
        if og_image and og_image.get('content'):
            image_url = og_image['content']
            if self._is_valid_image_url(image_url):
                return urljoin(base_url, image_url)
        return None
    
    def _find_content_main_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """البحث في المحتوى الرئيسي للمود"""
        
        # البحث في العنصر الرئيسي للمحتوى
        main_content_selectors = [
            '.mod-content img',
            '.post-content img', 
            '.entry-content img',
            '.main-content img',
            'article img',
            '.mod-details img'
        ]
        
        for selector in main_content_selectors:
            images = soup.select(selector)
            for img in images:
                if self._is_main_mod_image(img, soup):
                    src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                    if src and self._is_valid_image_url(src):
                        return urljoin(base_url, src)
        
        return None
    
    def _find_gallery_main_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """البحث في معرض الصور للصورة الرئيسية"""
        
        # البحث في معرض الصور
        gallery_selectors = [
            '.gallery img:first-child',
            '.image-gallery img:first-child',
            '.mod-gallery img:first-child',
            '.slider img:first-child',
            '.carousel img:first-child'
        ]
        
        for selector in gallery_selectors:
            img = soup.select_one(selector)
            if img and self._is_main_mod_image(img, soup):
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src and self._is_valid_image_url(src):
                    return urljoin(base_url, src)
        
        return None
    
    def _find_filtered_main_image(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """البحث العام مع فلترة الصور غير المرغوبة"""
        
        all_images = soup.find_all('img')
        
        # ترتيب الصور حسب الأولوية
        prioritized_images = []
        
        for img in all_images:
            if not self._is_main_mod_image(img, soup):
                continue
                
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if not src or not self._is_valid_image_url(src):
                continue
            
            # حساب نقاط الأولوية
            priority = self._calculate_image_priority(img, soup)
            if priority > 0:
                prioritized_images.append((priority, urljoin(base_url, src)))
        
        # إرجاع الصورة ذات الأولوية الأعلى
        if prioritized_images:
            prioritized_images.sort(reverse=True, key=lambda x: x[0])
            return prioritized_images[0][1]
        
        return None
    
    def _is_main_mod_image(self, img, soup: BeautifulSoup) -> bool:
        """
        فحص ما إذا كانت الصورة هي الصورة الرئيسية للمود وليس صورة مقترحة
        """
        
        # فحص السياق المحيط بالصورة
        parent = img.parent
        for _ in range(3):  # فحص 3 مستويات للأعلى
            if parent:
                parent_text = parent.get_text(strip=True).lower()
                parent_class = ' '.join(parent.get('class', [])).lower()
                parent_id = parent.get('id', '').lower()
                
                # إذا كان السياق يحتوي على كلمات مقترحة، تجاهل الصورة
                for keyword in self.unwanted_keywords:
                    if keyword in parent_text or keyword in parent_class or keyword in parent_id:
                        return False
                
                parent = parent.parent
            else:
                break
        
        # فحص src وalt للصورة نفسها
        src = img.get('src', '').lower()
        alt = img.get('alt', '').lower()
        img_class = ' '.join(img.get('class', [])).lower()
        
        for keyword in self.unwanted_keywords:
            if keyword in src or keyword in alt or keyword in img_class:
                return False
        
        return True
    
    def _calculate_image_priority(self, img, soup: BeautifulSoup) -> int:
        """
        حساب أولوية الصورة بناءً على موقعها وخصائصها
        """
        priority = 0
        
        # الأولوية حسب الموقع في الصفحة
        img_html = str(img)
        page_html = str(soup)
        position = page_html.find(img_html)
        if position != -1:
            # كلما كانت الصورة أقرب لبداية الصفحة، كلما زادت الأولوية
            priority += max(0, 100 - (position // 1000))
        
        # الأولوية حسب الحجم (width/height)
        width = img.get('width')
        height = img.get('height')
        if width and height:
            try:
                w = int(width)
                h = int(height)
                if w >= 200 and h >= 200:  # صور كبيرة نسبياً
                    priority += 50
                elif w >= 100 and h >= 100:
                    priority += 20
            except ValueError:
                pass
        
        # الأولوية حسب الكلاسات والمعرفات
        img_class = ' '.join(img.get('class', [])).lower()
        img_id = img.get('id', '').lower()
        
        positive_keywords = ['main', 'primary', 'hero', 'featured', 'showcase', 'preview']
        for keyword in positive_keywords:
            if keyword in img_class or keyword in img_id:
                priority += 30
        
        # الأولوية حسب السياق
        parent = img.parent
        if parent:
            parent_class = ' '.join(parent.get('class', [])).lower()
            if any(keyword in parent_class for keyword in ['content', 'main', 'article']):
                priority += 20
        
        return priority
    
    def _is_valid_image_url(self, url: str) -> bool:
        """فحص صحة رابط الصورة"""
        if not url:
            return False
        
        # تجاهل data URLs
        if url.startswith('data:'):
            return False
        
        # تجاهل الصور الصغيرة جداً أو الأيقونات
        if any(keyword in url.lower() for keyword in ['icon', 'logo', 'avatar', 'thumb']):
            return False
        
        # تأكد من أن الرابط ينتهي بامتداد صورة
        valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()
        
        # إزالة المعاملات من الرابط للفحص
        url_clean = url_lower.split('?')[0].split('#')[0]
        
        if any(url_clean.endswith(ext) for ext in valid_extensions):
            return True
        
        # في بعض الحالات قد لا يكون الامتداد واضحاً
        # نقبل الرابط إذا كان يحتوي على كلمات مفتاحية للصور
        if any(keyword in url_lower for keyword in ['image', 'img', 'picture', 'photo']):
            return True
        
        return False
    
    def _cleanup(self):
        """تنظيف الموارد"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None


def extract_main_mod_image_simple(url: str, use_selenium: bool = True, timeout: int = 20) -> Optional[str]:
    """
    دالة مبسطة لاستخراج الصورة الرئيسية للمود
    
    Args:
        url: رابط صفحة المود
        use_selenium: استخدام Selenium للدقة
        timeout: مهلة الانتظار
        
    Returns:
        str: رابط الصورة الرئيسية أو None
    """
    extractor = EnhancedImageExtractor(prefer_selenium=use_selenium, timeout=timeout)
    
    try:
        result = extractor.extract_main_mod_image(url)
        
        if result.get('success', False):
            return result.get('main_image')
        else:
            print(f"❌ فشل الاستخراج: {result.get('error', 'خطأ غير معروف')}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {e}")
        return None


def extract_mod_images_enhanced(url: str, max_images: int = 5) -> List[str]:
    """
    استخراج صور المود مع إعطاء أولوية للصورة الرئيسية
    
    Args:
        url: رابط صفحة المود
        max_images: العدد الأقصى للصور
        
    Returns:
        list: قائمة بروابط الصور مرتبة حسب الأولوية
    """
    main_image = extract_main_mod_image_simple(url)
    
    if main_image:
        # إذا وجدت الصورة الرئيسية، ضعها في المقدمة
        images = [main_image]
        
        # يمكن إضافة المزيد من الصور إذا كان مطلوباً
        if max_images > 1:
            # هنا يمكن إضافة استخراج صور إضافية
            pass
        
        return images[:max_images]
    else:
        return []


# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار الاستخراج
    test_url = "https://mcpedl.com/example-mod/"
    
    print("🧪 اختبار استخراج الصورة الرئيسية...")
    main_image = extract_main_mod_image_simple(test_url)
    
    if main_image:
        print(f"✅ الصورة الرئيسية: {main_image}")
    else:
        print("❌ لم يتم العثور على صورة رئيسية")
