# -*- coding: utf-8 -*-
"""
تشغيل سريع لاختبار الإصلاحات
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("بدء الاختبار السريع للإصلاحات...")
    print("=" * 50)
    
    # 1. تشغيل الإصلاحات أولاً
    print("1. تشغيل الإصلاحات...")
    try:
        from fix_remaining_issues import main as fix_main
        fix_success = fix_main()
        if not fix_success:
            print("⚠️ بعض الإصلاحات فشلت، لكن سنتابع الاختبار...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الإصلاحات: {e}")
    
    print("\n" + "=" * 50)
    
    # 2. تشغيل الاختبارات
    print("2. تشغيل الاختبارات...")
    try:
        from test_all_fixes import main as test_main
        test_success = test_main()
        
        if test_success:
            print("\nجميع الاختبارات نجحت!")
            print("الأداة جاهزة للاستخدام")
        else:
            print("\nبعض الاختبارات فشلت")
            print("راجع النتائج أعلاه لمعرفة المشاكل")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
    
    print("\n" + "=" * 50)
    print("3. ملخص الحالة:")
    
    # فحص الملفات المهمة
    important_files = [
        "enhanced_integration.py",
        "image_processor.py", 
        "firebase_config.py",
        "api_keys.json",
        "firebase_config.json"
    ]
    
    for file in important_files:
        if os.path.exists(file):
            print(f"[OK] {file} موجود")
        else:
            print(f"[MISSING] {file} مفقود")
    
    print("\nللمتابعة:")
    print("1. تأكد من إعداد Firebase (راجع INSTALLATION_GUIDE_FIXED.md)")
    print("2. أضف مفاتيح Gemini API إلى api_keys.json")
    print("3. شغل mod_processor_broken_final.py")
    
if __name__ == "__main__":
    main()
