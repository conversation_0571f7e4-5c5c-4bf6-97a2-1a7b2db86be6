#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار المستخرج المحسن لـ MCPEDL مع التحسينات الجديدة
"""

import sys
import os
import json

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_features():
    """اختبار المميزات المحسنة الجديدة"""
    print("🧪 اختبار المميزات المحسنة لمستخرج MCPEDL")
    print("=" * 60)

    try:
        # استيراد المستخرج المحسن
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        print("✅ تم استيراد المستخرج المحسن بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد المستخرج: {e}")
        return False

    # إنشاء مثيل من المستخرج
    try:
        extractor = MCPEDLExtractorFixed()
        print("✅ تم إنشاء مثيل من المستخرج")
    except Exception as e:
        print(f"❌ فشل في إنشاء المستخرج: {e}")
        return False

    # اختبار HTML تجريبي مع فيديوهات YouTube وروابط تواصل
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Dragon Mounts Addon | MCPEDL</title>
        <meta property="og:image" content="https://media.forgecdn.net/attachments/123/456/dragon_preview.jpg">
    </head>
    <body>
        <h1 class="post-page__title">Dragon Mounts Addon v1.3.25</h1>
        
        <div class="creator-name">
            <a href="/user/dragonmaster123">DragonMaster123</a>
        </div>
        
        <div class="post-page__content">
            <p>This amazing addon adds rideable dragons to your Minecraft world!</p>
            
            <!-- فيديو YouTube مضمن -->
            <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" width="560" height="315"></iframe>
            
            <img src="https://media.forgecdn.net/attachments/123/456/dragon_screenshot1.jpg" alt="Dragon Screenshot">
            <img src="https://media.forgecdn.net/attachments/123/457/dragon_screenshot2.png" alt="Dragon Screenshot 2">
            
            <p>Features:</p>
            <ul>
                <li>Rideable dragons</li>
                <li>Multiple dragon types</li>
                <li>Dragon breeding system</li>
            </ul>
            
            <p>Follow me on social media:</p>
            <a href="https://discord.gg/dragonmods">Join our Discord</a>
            <a href="https://youtube.com/channel/UC123456789">My YouTube Channel</a>
            <a href="https://twitter.com/dragonmaster123">Twitter</a>
            
            <!-- صور مودات مقترحة (يجب رفضها) -->
            <div class="related-posts">
                <img src="https://media.forgecdn.net/attachments/999/888/other_mod.jpg" alt="Other Mod">
            </div>
        </div>
    </body>
    </html>
    """

    print("\n🔍 اختبار استخراج البيانات من HTML تجريبي...")
    
    try:
        # استخراج البيانات
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        mod_data = extractor.extract_mod_data(test_html, test_url, generate_ai_descriptions=False)
        
        if mod_data:
            print("\n✅ تم استخراج البيانات بنجاح!")
            print("\n📋 البيانات المستخرجة:")
            
            # عرض البيانات الأساسية
            print(f"📝 اسم المود: {mod_data.get('name', 'غير محدد')}")
            print(f"👤 المطور: {mod_data.get('creator_name', 'غير محدد')}")
            print(f"🔗 ملف المطور: {mod_data.get('creator_profile_url', 'غير محدد')}")
            print(f"📂 الفئة: {mod_data.get('category', 'غير محدد')}")
            
            # عرض الصور
            images = mod_data.get('image_urls', [])
            print(f"\n🖼️ الصور المستخرجة ({len(images)}):")
            for i, img in enumerate(images, 1):
                print(f"  [{i}] {img}")
            
            # عرض فيديوهات YouTube الجديدة
            videos = mod_data.get('video_urls', [])
            print(f"\n🎥 فيديوهات YouTube ({len(videos)}):")
            for i, video in enumerate(videos, 1):
                print(f"  [{i}] {video}")
            
            # عرض روابط التواصل الاجتماعي
            social_links = mod_data.get('creator_social_channels', [])
            print(f"\n🌐 روابط التواصل الاجتماعي ({len(social_links)}):")
            for i, link in enumerate(social_links, 1):
                print(f"  [{i}] {link}")
            
            # تحليل جودة النتائج
            print("\n📊 تحليل جودة النتائج:")
            
            # فحص الصور
            if images:
                forgecdn_images = [img for img in images if 'forgecdn.net' in img]
                print(f"✅ صور forgecdn: {len(forgecdn_images)}/{len(images)}")
                
                suggested_images = [img for img in images if any(pattern in img.lower() for pattern in ['related', 'suggested', 'other_mod'])]
                if suggested_images:
                    print(f"⚠️ صور مودات مقترحة (يجب تجنبها): {len(suggested_images)}")
                else:
                    print("✅ لا توجد صور مودات مقترحة")
            else:
                print("⚠️ لم يتم استخراج أي صور")
            
            # فحص الفيديوهات
            if videos:
                youtube_videos = [v for v in videos if 'youtube.com' in v or 'youtu.be' in v]
                print(f"✅ فيديوهات YouTube: {len(youtube_videos)}/{len(videos)}")
            else:
                print("⚠️ لم يتم استخراج أي فيديوهات")
            
            # فحص روابط التواصل
            if social_links:
                platforms = set()
                for link in social_links:
                    if ':' in link:
                        platform = link.split(':')[0]
                        platforms.add(platform)
                print(f"✅ منصات التواصل: {', '.join(platforms)}")
            else:
                print("⚠️ لم يتم استخراج روابط تواصل")
            
            # فحص ملف المطور
            creator_profile = mod_data.get('creator_profile_url', '')
            if creator_profile and 'mcpedl.com' in creator_profile:
                print("✅ تم استخراج ملف المطور على MCPEDL")
            else:
                print("⚠️ لم يتم استخراج ملف المطور")
            
            return True
            
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        return False

def test_fallback_social_extraction():
    """اختبار الاستخراج الاحتياطي لروابط التواصل الاجتماعي"""
    print("\n🔄 اختبار الاستخراج الاحتياطي لروابط التواصل الاجتماعي")
    print("=" * 60)
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        from bs4 import BeautifulSoup
        
        extractor = MCPEDLExtractorFixed()
        
        # HTML تجريبي مع روابط تواصل متنوعة
        test_html = """
        <div class="content">
            <p>Contact the creator:</p>
            <p>Discord: discord.gg/testserver</p>
            <p>YouTube: https://youtube.com/channel/UC123456789</p>
            <p>Twitter: @testuser</p>
            <a href="https://github.com/testuser/project">GitHub Project</a>
            <a href="https://patreon.com/testcreator">Support on Patreon</a>
        </div>
        """
        
        soup = BeautifulSoup(test_html, 'html.parser')
        
        # اختبار الاستخراج الاحتياطي
        fallback_links = extractor.extract_social_links_fallback(soup, test_html)
        
        print(f"📊 تم استخراج {len(fallback_links)} رابط تواصل احتياطي:")
        for i, link in enumerate(fallback_links, 1):
            print(f"  [{i}] {link}")
        
        # التحقق من وجود المنصات المتوقعة
        expected_platforms = ['Discord', 'YouTube', 'GitHub', 'Patreon']
        found_platforms = set()
        
        for link in fallback_links:
            if ':' in link:
                platform = link.split(':')[0]
                found_platforms.add(platform)
        
        print(f"\n✅ المنصات المكتشفة: {', '.join(found_platforms)}")
        
        missing_platforms = set(expected_platforms) - found_platforms
        if missing_platforms:
            print(f"⚠️ منصات مفقودة: {', '.join(missing_platforms)}")
        else:
            print("🎉 تم اكتشاف جميع المنصات المتوقعة!")
        
        return len(fallback_links) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستخراج الاحتياطي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار شامل للمستخرج المحسن")
    print("=" * 70)
    
    success_count = 0
    total_tests = 2
    
    # اختبار المميزات المحسنة
    if test_enhanced_features():
        success_count += 1
        print("✅ اختبار المميزات المحسنة نجح")
    else:
        print("❌ اختبار المميزات المحسنة فشل")
    
    # اختبار الاستخراج الاحتياطي
    if test_fallback_social_extraction():
        success_count += 1
        print("✅ اختبار الاستخراج الاحتياطي نجح")
    else:
        print("❌ اختبار الاستخراج الاحتياطي فشل")
    
    # النتيجة النهائية
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {success_count}/{total_tests} اختبارات نجحت")
    
    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت! التحسينات تعمل بشكل صحيح")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - قد تحتاج لمراجعة التحسينات")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
