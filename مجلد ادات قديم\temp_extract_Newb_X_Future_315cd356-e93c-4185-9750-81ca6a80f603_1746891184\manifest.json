{"format_version": 2, "header": {"name": "§lAndroid§r Newb X Future", "description": "§cOnly works with Patched Minecraft\n§7- A newb shader variant with Complementary style!\nv16.3-android\ngithub repo: §3https://github.com/1Sekon/newb-x-future/\n", "uuid": "2b876f38-1181-4865-a860-45d47c2a2030", "version": [0, 16, 3], "min_engine_version": [1, 21, 20]}, "modules": [{"type": "resources", "uuid": "4b5c2fae-0bf0-3415-a7a4-857809a4dc68", "version": [0, 16, 3]}], "subpacks": [{"folder_name": "nfdm", "name": "§lDisable Fake Depth§r\n - Disable fake depth effect on textures when there are torch light nearby", "memory_tier": 1}, {"folder_name": "glossy_effects", "name": "§lGlossy Effects§r\n - Always reflection effect", "memory_tier": 1}, {"folder_name": "multilayer", "name": "§lMultilayer Clouds§r\n - Enable multilayer rounded clouds\n - Performance heavy!", "memory_tier": 1}, {"folder_name": "no_wpwave", "name": "§lNo Wave§r\n - Disable plants and water wave but keep the swinging lantern", "memory_tier": 1}, {"folder_name": "default", "name": "§lDefault§r\n - Nothing to describe about this, just enjoy :)", "memory_tier": 1}], "metadata": {"authors": ["1Sekon", "deven<PERSON>n"], "url": "https://github.com/1Sekon/newb-x-future/"}}