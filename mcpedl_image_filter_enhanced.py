# -*- coding: utf-8 -*-
"""
فلتر محسن للصور من MCPEDL لتجنب صور المودات المقترحة والتعليقات
"""

import re
from typing import List, Dict, Any
from bs4 import BeautifulSoup

class MCPEDLImageFilter:
    """فلتر ذكي للصور من MCPEDL"""

    def __init__(self):
        # قائمة الأقسام غير المرغوبة
        self.unwanted_sections = [
            'you may also like',
            'related posts',
            'similar mods',
            'recommended',
            'suggestions',
            'comments',
            'responses',
            'installation guides',
            'android',
            'ios',
            'windows 10',
            'feedback',
            'support',
            'donation',
            'credits',
            'social media',
            'follow us',
            'subscribe'
        ]

        # أنماط الصور الثابتة
        self.static_image_patterns = [
            r'shield\.png',
            r'shield\.\w+\.png',
            r'_nuxt',
            r'logo',
            r'favicon',
            r'header',
            r'footer',
            r'sidebar',
            r'banner',
            r'advertisement',
            r'social-icon',
            r'share-button',
            r'widget',
            r'placeholder',
            r'loading',
            r'spinner',
            r'data:image/svg\+xml',
            r'data:image/png;base64',
            r'gravatar\.com',
            r'secure\.gravatar',
            r'/avatar',
            r'profile_pic',
            r'user_avatar',
            r'r2\.mcpedl\.com/users',
            r'/img/empty\.png',          # صور فارغة
            r'mcpedl\.com/img/',         # جميع صور الموقع الثابتة
        ]

        # مصادر الصور الموثوقة
        self.trusted_sources = [
            'media.forgecdn.net/attachments',
            'edge.forgecdn.net/files',
            'mcpedl.com/wp-content/uploads',
            'api.mcpedl.com/storage',
            'r2.mcpedl.com/content',
        ]

    def filter_images_from_html(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج وفلترة الصور من HTML"""
        valid_images = []

        print("🔍 بدء فلترة الصور المحسنة...")

        # 1. استخراج الصور من المحتوى الرئيسي فقط
        main_content_images = self._extract_main_content_images(soup)
        valid_images.extend(main_content_images)

        # 2. استخراج الصور من الأنماط المباشرة
        pattern_images = self._extract_pattern_images(soup)
        valid_images.extend(pattern_images)

        # 3. فلترة نهائية
        filtered_images = self._final_filter(valid_images)

        print(f"📊 نتائج الفلترة: {len(filtered_images)} صورة صالحة من أصل {len(valid_images)}")

        return filtered_images

    def _extract_main_content_images(self, soup: BeautifulSoup) -> List[str]:
        """استخراج الصور من المحتوى الرئيسي فقط"""
        images = []

        # تحديد المناطق الرئيسية للمحتوى
        main_selectors = [
            'article',
            'div.post-page__content',
            'div.entry',
            'main',
            'div.content',
            'div.post-content',
            'div.article-content'
        ]

        for selector in main_selectors:
            content_area = soup.select_one(selector)
            if content_area:
                print(f"✅ تم العثور على منطقة المحتوى: {selector}")

                # استخراج الصور من هذه المنطقة
                area_images = content_area.find_all('img')

                for img in area_images:
                    src = self._get_image_src(img)
                    if src and self._is_valid_content_image(img, src):
                        if src not in images:
                            images.append(src)
                            print(f"✅ صورة محتوى صالحة: {src[:60]}...")

                break  # استخدم أول منطقة محتوى موجودة

        return images

    def _extract_pattern_images(self, soup: BeautifulSoup) -> List[str]:
        """استخراج الصور باستخدام الأنماط المباشرة"""
        images = []
        all_html = str(soup)

        # أنماط الصور الموثوقة - محسنة لتشمل جميع مجلدات forgecdn
        trusted_patterns = [
            # forgecdn patterns - شامل لجميع المجلدات
            r'https://media\.forgecdn\.net/attachments/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
            r'media\.forgecdn\.net/attachments/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',

            # forgecdn patterns محددة
            r'https://media\.forgecdn\.net/attachments/\d+/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https://media\.forgecdn\.net/attachments/description/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',

            # MCPEDL patterns
            r'https://mcpedl\.com/wp-content/uploads/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https://api\.mcpedl\.com/storage/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https://r2\.mcpedl\.com/content/[^"\s<>/users]+\.(?:jpg|jpeg|png|gif|webp)',
        ]

        for pattern in trusted_patterns:
            found_urls = re.findall(pattern, all_html, re.IGNORECASE)
            for url in found_urls:
                clean_url = url.strip('"\'<>)')
                if clean_url not in images:
                    images.append(clean_url)
                    print(f"✅ صورة من نمط موثوق: {clean_url[:60]}...")

        return images

    def _is_valid_content_image(self, img_element, src: str) -> bool:
        """فحص ما إذا كانت الصورة من المحتوى الصالح"""
        # فحص المصدر
        if not self._is_trusted_source(src):
            return False

        # فحص النص المحيط
        if self._is_in_unwanted_section(img_element):
            print(f"❌ صورة في قسم غير مرغوب: {src[:50]}...")
            return False

        # فحص الصور الثابتة
        if self._is_static_image(src):
            print(f"❌ صورة ثابتة: {src[:50]}...")
            return False

        return True

    def _is_trusted_source(self, src: str) -> bool:
        """فحص ما إذا كان المصدر موثوق"""
        src_lower = src.lower()

        # فحص المصادر الموثوقة
        for trusted in self.trusted_sources:
            if trusted in src_lower:
                return True

        # قبول الصور من mcpedl.com بشكل عام (ما عدا المستخدمين)
        if 'mcpedl.com' in src_lower and '/users/' not in src_lower:
            return True

        return False

    def _is_in_unwanted_section(self, img_element) -> bool:
        """فحص ما إذا كانت الصورة في قسم غير مرغوب"""
        # فحص العنصر الأب والأجداد
        current = img_element
        for _ in range(5):  # فحص 5 مستويات للأعلى
            if current and current.parent:
                parent_text = current.parent.get_text().lower()

                # فحص النصوص غير المرغوبة
                for unwanted in self.unwanted_sections:
                    if unwanted in parent_text:
                        return True

                # فحص الفئات والمعرفات
                parent_class = ' '.join(current.parent.get('class', [])).lower()
                parent_id = current.parent.get('id', '').lower()

                unwanted_classes = [
                    'related', 'suggestion', 'recommendation', 'comment',
                    'sidebar', 'footer', 'header', 'navigation', 'menu'
                ]

                if any(unwanted in parent_class or unwanted in parent_id
                       for unwanted in unwanted_classes):
                    return True

                current = current.parent
            else:
                break

        return False

    def _is_static_image(self, src: str) -> bool:
        """فحص ما إذا كانت الصورة ثابتة"""
        src_lower = src.lower()

        for pattern in self.static_image_patterns:
            if re.search(pattern, src_lower):
                return True

        return False

    def _get_image_src(self, img) -> str:
        """استخراج رابط الصورة من عنصر img"""
        src = (img.get('src') or
               img.get('data-src') or
               img.get('data-lazy-src') or
               img.get('data-original') or
               img.get('data-srcset', '').split(',')[0].strip().split(' ')[0])

        if src:
            src = src.strip()

            # إضافة البروتوكول إذا كان مفقوداً
            if src.startswith('//'):
                src = 'https:' + src
            elif src.startswith('/'):
                src = 'https://mcpedl.com' + src
            elif not src.startswith('http'):
                src = 'https://mcpedl.com/' + src.lstrip('/')

        return src or ''

    def _final_filter(self, images: List[str]) -> List[str]:
        """فلترة نهائية للصور"""
        filtered = []

        for img_url in images:
            # تجنب التكرار
            if img_url in filtered:
                continue

            # فحص نهائي للجودة
            if self._is_high_quality_image(img_url):
                filtered.append(img_url)
            else:
                print(f"❌ تم رفض صورة في الفلترة النهائية: {img_url[:50]}...")

        return filtered[:20]  # حد أقصى 20 صورة

    def _is_high_quality_image(self, url: str) -> bool:
        """فحص جودة الصورة"""
        url_lower = url.lower()

        # رفض الصور الصغيرة
        small_sizes = ['16x16', '32x32', '64x64', '80x80', '100x100', '120x120']
        if any(size in url_lower for size in small_sizes):
            return False

        # رفض الأيقونات
        icon_indicators = ['icon', 'favicon', 'thumb', 'avatar']
        if any(indicator in url_lower for indicator in icon_indicators):
            return False

        # قبول الصور من المصادر الموثوقة
        if any(trusted in url_lower for trusted in self.trusted_sources):
            return True

        # قبول الصور التي تحتوي على مؤشرات المحتوى
        content_indicators = [
            'screenshot', 'image', 'photo', 'mod', 'addon',
            'texture', 'pack', 'resource', 'behavior'
        ]

        if any(indicator in url_lower for indicator in content_indicators):
            return True

        return False

def test_filter():
    """اختبار الفلتر"""
    print("🧪 اختبار فلتر الصور المحسن")
    print("=" * 40)

    # HTML تجريبي
    test_html = """
    <html>
        <body>
            <article>
                <img src="https://media.forgecdn.net/attachments/1180/464/dragon-mod.png" alt="Dragon Mod">
                <img src="https://mcpedl.com/wp-content/uploads/2024/texture-pack.jpg" alt="Texture Pack">
            </article>
            <div class="related-posts">
                <h3>You may also like</h3>
                <img src="https://mcpedl.com/wp-content/uploads/2024/other-mod.jpg" alt="Other Mod">
            </div>
            <div class="comments">
                <img src="https://r2.mcpedl.com/users/123/avatar.png" alt="User Avatar">
            </div>
            <img src="https://mcpedl.com/_nuxt/img/shield.6982c20.png" alt="Shield">
        </body>
    </html>
    """

    soup = BeautifulSoup(test_html, 'html.parser')
    filter_obj = MCPEDLImageFilter()

    filtered_images = filter_obj.filter_images_from_html(soup, "https://mcpedl.com/test/")

    print(f"\n📊 نتائج الاختبار:")
    print(f"   🖼️ صور مفلترة: {len(filtered_images)}")

    for i, img in enumerate(filtered_images, 1):
        print(f"   [{i}] {img}")

    # يجب أن نحصل على صورتين فقط (من article)
    expected = 2
    if len(filtered_images) == expected:
        print(f"\n✅ نجح الاختبار! تم استخراج {expected} صورة كما هو متوقع")
        return True
    else:
        print(f"\n❌ فشل الاختبار! متوقع {expected} صورة، حصلنا على {len(filtered_images)}")
        return False

if __name__ == "__main__":
    test_filter()
