[{"article_url": "https://www.9minecraft.net/dls-vein-miner-tree-capitator-addon-mcpe/", "timestamp": "2025-05-07 21:38:05", "extracted_data": {"mod_name": "DL's Vein Miner & Tree Capitator Addon", "version": "1.21", "primary_image_url": "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-Thumbnail.png", "other_image_urls": ["https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-1.png", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-2.png", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-3.jpg", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-4.jpg", null, null], "download_link": "https://download.cdn9mc.com/index.php?act=dl&id=1738899202", "bp_download_link": null, "rp_download_link": null}, "category": "Addons", "processed_image_urls": ["https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-Thumbnail.png", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-1.png", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-2.png", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-3.jpg", "https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-4.jpg"], "final_mod_url": "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/my_new_mods_bucket/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-1.21.mcaddon?", "final_mod_size": "174.22 KB", "failure_reason": "Critical error during publish step: Publish failed. Status: ATTRIBUTE_NOT_FOUND, Data: [{'id': '35f232db-1532-402a-80a8-0e37ddc44702', 'created_at': '2025-05-07T21:38:35.535545+00:00', 'name': \"DL's Vein Miner & Tree Capitator Addon\", 'description': \"Automatically processed: DL's Vein Miner & Tree Capitator Addon\", 'category': 'Addons', 'image_urls': ['https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-Thumbnail.png', 'https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-1.png', 'https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-2.png', 'https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-3.jpg', 'https://www.9minecraft.net/wp-content/uploads/2025/02/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-4.jpg'], 'version': '1.21', 'size': '174.22 KB', 'download_url': 'https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/my_new_mods_bucket/DLs-Vein-Miner-Tree-Capitator-Addon-MCPE-1.21.mcaddon?', 'downloads': 0, 'likes': 0, 'clicks': 0}]"}, {"article_url": "https://www.9minecraft.net/darklexx-vanilla-hammers-addon-mcpe/", "timestamp": "2025-05-07 21:53:18", "extracted_data": {"mod_name": "DL's <PERSON><PERSON>", "version": "1.21", "primary_image_url": "https://www.9minecraft.net/wp-content/uploads/2024/12/Van<PERSON>-<PERSON>s-Addon-MCPE-Thumbnail.png", "other_image_urls": ["https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-1.gif", "https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-2.png", "https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-3.png", "https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-4.png", "https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-5.png", "https://www.9minecraft.net/wp-content/uploads/2024/12/Vanilla-Hammers-Addon-MCPE-6.png"], "download_link": null, "bp_download_link": "https://download.cdn9mc.com/index.php?act=dl&id=1733204258", "rp_download_link": "https://download.cdn9mc.com/index.php?act=dl&id=1733204276"}, "category": "Addons", "processed_image_urls": ["https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-Thumbnail_1746654809_lfltptai.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-1_1746654815_a6lh8e5y.jpeg?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-2_1746654817_neonfme7.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-3_1746654819_bikvds6k.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-4_1746654820_7xbjqkyw.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-5_1746654821_aeykg04g.png?", "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/image/img_Van<PERSON>-<PERSON>s-Addon-MCPE-6_1746654822_oic3yop4.png?"], "final_mod_url": null, "final_mod_size": null, "failure_reason": "Mod file processing failed: HTTPSConnectionPool(host='download.cdn9mc.com', port=443): Read timed out. (read timeout=30)"}, {"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "timestamp": "2025-05-13 11:16:07", "failure_reason": "name 'gemini_extraction_cache' is not defined", "extracted_data": null}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "timestamp": "2025-05-13 11:16:09", "failure_reason": "name 'gemini_extraction_cache' is not defined", "extracted_data": null}, {"article_url": "https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html", "timestamp": "2025-05-13 11:16:11", "failure_reason": "name 'gemini_extraction_cache' is not defined", "extracted_data": null}, {"article_url": "https://mcpeland.io/en/mods/1515-addon-ben-10-dna-hunt.html", "timestamp": "2025-05-13 11:16:13", "failure_reason": "name 'gemini_extraction_cache' is not defined", "extracted_data": null}, {"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "timestamp": "2025-05-13 15:58:29", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Pick up & Carry", "version": null, "full_mod_description": "Ever wanted to pick up a mob or player and move it to another location? With this add-on you can do it without any commands, right in survival! This addon allows you to pick up any mobs or players and carry them anywhere without any commands.\n\nCreated By sirob\n\nYou can pick up a mob or player only if you are standing on the ground. Also, you should not have items in both hands. And you shouldn't be in the water. Only in this case you can pick up a mob / player.\n\nWhen you carry a mob / player, you have to move neatly. If the mob is submerged in water, it will fall out of your hands. If you start falling from a height, then after flying 3 blocks, it will fall out of your hands. If you take any item in your hand, the mob will also fall out of your hands.", "mod_features": ["Now it supports 1.21.60!", "Now it supports characters from Character Creator! You can use your character created in the Character Creator! Even animated items and capes are supported!", "Fixed broken vanilla animation of holding heavy core!"], "primary_image_url": "/uploads/posts/2023-01/logo14_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-02/medium/pick-up-carry_2.png", "/uploads/posts/2022-02/medium/pick-up-carry_3.png", "/uploads/posts/2022-02/medium/pick-up-carry_4.png", "/uploads/posts/2022-02/medium/pick-up-carry_8.png", "/uploads/posts/2022-02/medium/pick-up-carry_9.png", "/uploads/posts/2022-02/medium/pick-up-carry_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "timestamp": "2025-05-13 15:58:52", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "DeadZone", "version": "1.53", "full_mod_description": "DeadZone is a survival addon inspired by the game DayZ that puts you in the role of a survivor in a post-apocalyptic world. The goal is simple: survive as long as possible, search every building for loot, and craft your own story along the way.Created by NekoZack It features unique \"2.5D\" styled weapons, along with a variety of clothing and accessories to customize your look in the post-apocalyptic setting. Plus, it offers a visually appealing texture style that adds to the immersion DeadZone offers a wide variety of items, including melee weapons, firearms, food, drinks, miscellaneous items, and medical supplies. It also includes clothing and accessories, such as shirts, pants, helmets, masks, and more, allowing you to explore buildings in style with your own unique look.", "mod_features": ["Stamina", "Dehydration/Thirst", "Infection (Hunger effect)", "Radiation (Wither effect)", "Bleeding", "Broken bone (Slowness effect)"], "primary_image_url": "/uploads/posts/2025-03/tak-berjudul16820250227165818_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.png", "/uploads/posts/2024-12/deadzone-addon-v141_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_7.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_8.jpg", "/uploads/posts/2024-12/new-deadzone-addon_8.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html", "timestamp": "2025-05-13 15:59:11", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Survivor's Airdrop", "version": null, "full_mod_description": "This amazing addon lets you summon airdrops directly into your Minecraft world, providing valuable items to enhance your survival!There are three types of airdrops, each offering specific resources for different stages of progression.Created By AL3XANDRE ", "mod_features": ["Convenience: Get essential items wherever you are.", "Versatility: Choose the type of airdrop based on your current needs.", "Enhanced Survival: Access items that boost your chances in combat and exploration."], "primary_image_url": "/uploads/posts/2025-03/srsy_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/survivors-airdrop-addon_2.png", "/uploads/posts/2025-03/medium/survivors-airdrop-addon_3.png", "/uploads/posts/2025-03/survivors-airdrop-addon_4.png", "/uploads/posts/2025-03/survivors-airdrop-addon_5.png", "/uploads/posts/2025-03/survivors-airdrop-addon_6.png", "/uploads/posts/2025-03/survivors-airdrop-addon_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2291&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1515-addon-ben-10-dna-hunt.html", "timestamp": "2025-05-13 15:59:26", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Ben 10: <PERSON>", "version": null, "full_mod_description": "Embark on an intergalactic adventure by crafting the Omnitrix with Minecraft resources! Unlock powerful aliens like Heatblast, battle enemies, collect their DNA, and transform into them using the Omnitrix. Each alien has unique abilities, such as Heatblast's fiery powers and flight abilities. Explore, fight, and conquer the universe with the Omnitrix at your side!", "mod_features": [], "primary_image_url": "/uploads/posts/2025-03/bens_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/ben-10-dna-hunt-addon_13.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_2.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_3.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_9.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_11.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2289&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1520-addon-sofa-amp-couch-furnitures.html", "timestamp": "2025-05-13 15:59:42", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Sofa & Couch Furnitures", "version": null, "full_mod_description": "Spruce Up Your Builds with Sofas & Couches!This addon brings two delightful furniture sets to your Minecraft world, perfect for creating cozy living spaces! where players can now enhance their virtual homes with two stunning sets of furniture: the luxurious Comfy Chair Couch and the cozy Small Sofa.<br><br><b>Created By <PERSON> <br><br></b>", "mod_features": ["The Comfy Chair Couch: Relax in style with this plush couch and matching chair.", "Small Sofa: Add a touch of comfort to smaller rooms with this compact sofa.", "Surprise Colors! Each time you spawn a sofa or couch, it will appear in a random, vibrant color!"], "primary_image_url": "/uploads/posts/2025-03/gfgfgf_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/medium/sofa-couch-furnitures_2.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_5.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_4.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_390.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_290.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2295&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1521-addon-the-slenderman.html", "timestamp": "2025-05-13 15:59:57", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "The Slenderman", "version": null, "full_mod_description": "The <PERSON><PERSON>derman is a Fictional  Supernatural Character and <PERSON><PERSON><PERSON><PERSON><PERSON> who is invincible and  has  strong attack powers and special abilities when attacking. This fantastic add-on adds this C<PERSON><PERSON> <PERSON><PERSON> who has extremely strong strength and Amazing Attack Abilities! Created By <PERSON> PE Addons He Will Attack mostly the Monsters Family Mobs in and others in your Minecraft world", "mod_features": ["Improved Character AI – <PERSON><PERSON><PERSON> is now smarter, faster, and more unpredictable. He will teleport more strategically and become more aggressive over time.", "New Sound Effects – Added eerie ambient sounds and Slenderman-specific noises to intensify the atmosphere.", "New Animations – <PERSON><PERSON><PERSON> now has more fluid movements, making his presence even more terrifying."], "primary_image_url": "/uploads/posts/2025-03/the-slenderman_1.png", "other_image_urls": ["/uploads/posts/2025-03/medium/the-slenderman_3.png", "/uploads/posts/2025-03/medium/the-slenderman_4.png", "/uploads/posts/2025-03/medium/the-slenderman_10.png", "/uploads/posts/2025-03/medium/the-slenderman_9.png", "/uploads/posts/2025-03/medium/the-slenderman_8.png", "/uploads/posts/2025-03/medium/the-slenderman_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2296&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "timestamp": "2025-05-13 16:02:32", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Pick up & Carry", "version": null, "full_mod_description": "Ever wanted to pick up a mob or player and move it to another location? With this add-on you can do it without any commands, right in survival! This addon allows you to pick up any mobs or players and carry them anywhere without any commands.\n\nCreated By sirob\n\nYou can pick up a mob or player only if you are standing on the ground. Also, you should not have items in both hands. And you shouldn't be in the water. Only in this case you can pick up a mob / player.\n\nWhen you carry a mob / player, you have to move neatly. If the mob is submerged in water, it will fall out of your hands. If you start falling from a height, then after flying 3 blocks, it will fall out of your hands. If you take any item in your hand, the mob will also fall out of your hands.", "mod_features": ["Now it supports 1.21.60!", "Now it supports characters from Character Creator! You can use your character created in the Character Creator! Even animated items and capes are supported!", "Fixed broken vanilla animation of holding heavy core!"], "primary_image_url": "/uploads/posts/2023-01/logo14_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-02/medium/pick-up-carry_2.png", "/uploads/posts/2022-02/medium/pick-up-carry_3.png", "/uploads/posts/2022-02/medium/pick-up-carry_4.png", "/uploads/posts/2022-02/medium/pick-up-carry_8.png", "/uploads/posts/2022-02/medium/pick-up-carry_9.png", "/uploads/posts/2022-02/medium/pick-up-carry_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "timestamp": "2025-05-13 16:02:49", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "DeadZone", "version": "1.53", "full_mod_description": "DeadZone is a survival addon inspired by the game DayZ that puts you in the role of a survivor in a post-apocalyptic world. The goal is simple: survive as long as possible, search every building for loot, and craft your own story along the way.Created by NekoZack It features unique \"2.5D\" styled weapons, along with a variety of clothing and accessories to customize your look in the post-apocalyptic setting. Plus, it offers a visually appealing texture style that adds to the immersion DeadZone offers a wide variety of items, including melee weapons, firearms, food, drinks, miscellaneous items, and medical supplies. It also includes clothing and accessories, such as shirts, pants, helmets, masks, and more, allowing you to explore buildings in style with your own unique look.", "mod_features": ["Stamina", "Dehydration/Thirst", "Infection (Hunger effect)", "Radiation (Wither effect)", "Bleeding", "Broken bone (Slowness effect)"], "primary_image_url": "/uploads/posts/2025-03/tak-berjudul16820250227165818_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.png", "/uploads/posts/2024-12/deadzone-addon-v141_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_7.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_8.jpg", "/uploads/posts/2024-12/new-deadzone-addon_8.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html", "timestamp": "2025-05-13 16:02:59", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Survivor's Airdrop", "version": null, "full_mod_description": "This amazing addon lets you summon airdrops directly into your Minecraft world, providing valuable items to enhance your survival!There are three types of airdrops, each offering specific resources for different stages of progression.Created By AL3XANDRE ", "mod_features": ["Convenience: Get essential items wherever you are.", "Versatility: Choose the type of airdrop based on your current needs.", "Enhanced Survival: Access items that boost your chances in combat and exploration."], "primary_image_url": "/uploads/posts/2025-03/srsy_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/survivors-airdrop-addon_2.png", "/uploads/posts/2025-03/medium/survivors-airdrop-addon_3.png", "/uploads/posts/2025-03/survivors-airdrop-addon_4.png", "/uploads/posts/2025-03/survivors-airdrop-addon_5.png", "/uploads/posts/2025-03/survivors-airdrop-addon_6.png", "/uploads/posts/2025-03/survivors-airdrop-addon_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2291&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1515-addon-ben-10-dna-hunt.html", "timestamp": "2025-05-13 16:03:10", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Ben 10: <PERSON>", "version": null, "full_mod_description": "Embark on an intergalactic adventure by crafting the Omnitrix with Minecraft resources! Unlock powerful aliens like Heatblast, battle enemies, collect their DNA, and transform into them using the Omnitrix. Each alien has unique abilities, such as Heatblast's fiery powers and flight abilities. Explore, fight, and conquer the universe with the Omnitrix at your side!", "mod_features": [], "primary_image_url": "/uploads/posts/2025-03/bens_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/ben-10-dna-hunt-addon_13.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_2.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_3.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_9.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_11.png", "/uploads/posts/2025-03/medium/ben-10-dna-hunt-addon_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2289&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1520-addon-sofa-amp-couch-furnitures.html", "timestamp": "2025-05-13 16:03:22", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Sofa & Couch Furnitures", "version": null, "full_mod_description": "Spruce Up Your Builds with Sofas & Couches!This addon brings two delightful furniture sets to your Minecraft world, perfect for creating cozy living spaces! where players can now enhance their virtual homes with two stunning sets of furniture: the luxurious Comfy Chair Couch and the cozy Small Sofa.<br><br><b>Created By <PERSON> <br><br></b>", "mod_features": ["The Comfy Chair Couch: Relax in style with this plush couch and matching chair.", "Small Sofa: Add a touch of comfort to smaller rooms with this compact sofa.", "Surprise Colors! Each time you spawn a sofa or couch, it will appear in a random, vibrant color!"], "primary_image_url": "/uploads/posts/2025-03/gfgfgf_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/medium/sofa-couch-furnitures_2.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_5.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_4.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_390.png", "/uploads/posts/2025-03/medium/sofa-couch-furnitures_290.png", null], "download_link": "https://mcpeland.io/index.php?do=download&id=2295&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1521-addon-the-slenderman.html", "timestamp": "2025-05-13 16:03:31", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "The Slenderman", "version": null, "full_mod_description": "The <PERSON><PERSON>derman is a Fictional  Supernatural Character and <PERSON><PERSON><PERSON><PERSON><PERSON> who is invincible and  has  strong attack powers and special abilities when attacking. This fantastic add-on adds this C<PERSON><PERSON> <PERSON><PERSON> who has extremely strong strength and Amazing Attack Abilities! Created By <PERSON> PE Addons He Will Attack mostly the Monsters Family Mobs in and others in your Minecraft world", "mod_features": ["Improved Character AI – <PERSON><PERSON><PERSON> is now smarter, faster, and more unpredictable. He will teleport more strategically and become more aggressive over time.", "New Sound Effects – Added eerie ambient sounds and Slenderman-specific noises to intensify the atmosphere.", "New Animations – <PERSON><PERSON><PERSON> now has more fluid movements, making his presence even more terrifying."], "primary_image_url": "/uploads/posts/2025-03/the-slenderman_1.png", "other_image_urls": ["/uploads/posts/2025-03/medium/the-slenderman_3.png", "/uploads/posts/2025-03/medium/the-slenderman_4.png", "/uploads/posts/2025-03/medium/the-slenderman_10.png", "/uploads/posts/2025-03/medium/the-slenderman_9.png", "/uploads/posts/2025-03/medium/the-slenderman_8.png", "/uploads/posts/2025-03/medium/the-slenderman_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2296&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1523-addon-survival-frozen-update.html", "timestamp": "2025-05-13 16:03:44", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Survival+", "version": null, "full_mod_description": "Survival+ Add-On makes Minecraft tougher and more exciting! Face new wild animals, dangerous mobs, and the powerful Phantom Master. Craft spears, collect rare loot, and survive in a world full of threats. Are you ready for the challenge?\n\nCreated By WildForge Studios \n\nThe Frozen Update is here, bringing a chilling new survival experience with icy mobs, blocks, and items! Get ready to face the cold like never before:", "mod_features": ["<PERSON> C<PERSON>per – A new creeper variant that leaves icy explosions behind!", "Frozen Zombie – These undead freeze anything they touch.", "Ice Revenant – A mysterious and dangerous creature that lurks in the cold.", "Arctic Fox – Hunts small mobs in the snow and ice.", "Heart of the Frost – A rare item that grants Resistance against freezing mobs and the cold.", "Frozen Flesh – Dropped by the Frozen Zombie, can be used for crafting or potions.", "Frosted Stone – A new block found in icy biomes.", "Snow Crystal – A rare, decorative block dropped by Ice Revenants.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "White Endermen", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Polished Endstone", "Ender <PERSON>", "ducks and raccoons", "grizzly bears and crocodiles", "dread zombies", "phantom creatures", "powerful spears (wood, stone, iron, diamond, netherite, and more!)", "Phantom Fangs, Ghost Souls, and Crocodile Scutes", "Phantom Master", "Phantom Master Nose"], "primary_image_url": "/uploads/posts/2025-03/6aaa9b167e5e46a1a9e8be43cd4f4c6e_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/medium/survival-v030-end-update_2.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_5.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_4.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_3.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_2.jpg", "/uploads/posts/2025-03/medium/survival-v025-variants-update_9.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2299&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1527-addon-village-generator.html", "timestamp": "2025-05-13 16:04:02", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Village Generator", "version": null, "full_mod_description": "Build villages anywhere in your world with the run of a single command. Village Generator adds various functions to the game to help you design your own village from scratch quickly and easily. Now, go bring more life into your superflat worlds!", "mod_features": ["Individual structures", "Instant village", "Multiple village types", "Old village"], "primary_image_url": "/uploads/posts/2025-04/mcpedl-png.png", "other_image_urls": ["/uploads/posts/2025-04/medium/description_441532e7-4c6c-4cc5-9692-6f7bab692903.png", "/uploads/posts/2025-04/medium/description_2b2b8bf5-a256-4f51-a2ae-2944f469c6d4.png", "/uploads/posts/2025-04/medium/description_d9cb8eea-5d41-4f16-81be-5c45b6505698.png", "/uploads/posts/2025-04/medium/description_5c58f2cd-4912-485b-a413-0aeb532c6616.png", null, null], "download_link": "https://mcpeland.io/index.php?do=download&id=2308&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1542-addon-cursed-brainrot-italian-udin-din-din-dun-chimpanzini-bananini-update.html", "timestamp": "2025-05-13 16:04:17", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Cursed <PERSON>", "version": null, "full_mod_description": "Cursed Brainrot Italian is a chaos-driven, meme-heavy addon that introduces powerful, absurd, and terrifying entities based on a surreal mix of \"brainrot\" internet humor and exaggerated Italian cultural tropes. This mod is designed for players who want to break the limits of Minecraft’s seriousness and experience unpredictable, high-stakes encounters with over-the-top mobs.", "mod_features": ["<PERSON><PERSON> Tung <PERSON>hur", "<PERSON><PERSON><PERSON><PERSON>", "Brr Brr <PERSON><PERSON><PERSON>", "Chimpanzin<PERSON>", "<PERSON><PERSON>", "2 anomalies incoming"], "primary_image_url": "/uploads/posts/2025-04/1745959948_screenshot_2025-04-26-16-01-13-617_com-mojang.jpg", "other_image_urls": ["/uploads/posts/2025-04/medium/screenshot_2025-04-26-16-17-22-108_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-35-027_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-26-16-17-03-785_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-53-019_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-04-20-865_com-miui.jpg", "/uploads/posts/2025-04/medium/screenshot_2025-04-11-21-05-32-862_com-miui.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2329&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/184-pick-up-amp-carry.html", "timestamp": "2025-05-13 16:11:33", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Pick up & Carry", "version": null, "full_mod_description": "Ever wanted to pick up a mob or player and move it to another location? With this add-on you can do it without any commands, right in survival! This addon allows you to pick up any mobs or players and carry them anywhere without any commands.<br><br>Created <span style=\"vertical-align:inherit;\"><span style=\"vertical-align:inherit;\">By <i> </i><span style=\"color:#000000;\">sirob </span></span></span><br>You can pick up a mob or player only if you are standing on the ground. Also, you should not have items in both hands. And you shouldn't be in the water. Only in this case you can pick up a mob / player.When you carry a mob / player, you have to move neatly. If the mob is submerged in water, it will fall out of your hands. If you start falling from a height, then after flying 3 blocks, it will fall out of your hands. If you take any item in your hand, the mob will also fall out of your hands.", "mod_features": ["Now it supports 1.21.60!", "Now it supports characters from Character Creator! You can use your character created in the Character Creator! Even animated items and capes are supported!", "Fixed broken vanilla animation of holding heavy core!"], "primary_image_url": "/uploads/posts/2023-01/logo14_1-520x245.png", "other_image_urls": ["/uploads/posts/2022-02/medium/pick-up-carry_2.png", "/uploads/posts/2022-02/medium/pick-up-carry_3.png", "/uploads/posts/2022-02/medium/pick-up-carry_4.png", "/uploads/posts/2022-02/medium/pick-up-carry_8.png", "/uploads/posts/2022-02/medium/pick-up-carry_9.png", "/uploads/posts/2022-02/medium/pick-up-carry_10.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2271&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1452-addon-deadzone.html", "timestamp": "2025-05-13 16:11:56", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "DeadZone", "version": "1.162", "full_mod_description": "DeadZone is a survival addon inspired by the game DayZ that puts you in the role of a survivor in a post-apocalyptic world. The goal is simple: survive as long as possible, search every building for loot, and craft your own story along the way.Created by NekoZack It features unique \"2.5D\" styled weapons, along with a variety of clothing and accessories to customize your look in the post-apocalyptic setting. Plus, it offers a visually appealing texture style that adds to the immersion DeadZone offers a wide variety of items, including melee weapons, firearms, food, drinks, miscellaneous items, and medical supplies. It also includes clothing and accessories, such as shirts, pants, helmets, masks, and more, allowing you to explore buildings in style with your own unique look.", "mod_features": ["Stamina", "Dehydration/Thirst", "Infection (Hunger effect)", "Radiation (Wither effect)", "Bleeding", "Broken bone (Slowness effect)"], "primary_image_url": "/uploads/posts/2025-03/tak-berjudul16820250227165818_1-520x245.png", "other_image_urls": ["/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.png", "/uploads/posts/2024-12/deadzone-addon-v141_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v153-12080-support_2.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_7.jpg", "/uploads/posts/2024-12/deadzone-addon-v152_8.jpg", "/uploads/posts/2024-12/deadzone-addon-v141_10.jpg"], "download_link": "https://mcpeland.io/index.php?do=download&id=2274&lang=en", "bp_download_link": null, "rp_download_link": null}}, {"article_url": "https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html", "timestamp": "2025-05-13 16:12:14", "failure_reason": "Failed to process main mod link (returned None).", "extracted_data": {"mod_name": "Survivor's Airdrop", "version": null, "full_mod_description": "This amazing addon lets you summon airdrops directly into your Minecraft world, providing valuable items to enhance your survival!There are three types of airdrops, each offering specific resources for different stages of progression.", "mod_features": ["Convenience: Get essential items wherever you are.", "Versatility: Choose the type of airdrop based on your current needs.", "Enhanced Survival: Access items that boost your chances in combat and exploration."], "primary_image_url": "/uploads/posts/2025-03/srsy_1-520x245.png", "other_image_urls": ["/uploads/posts/2025-03/survivors-airdrop-addon_2.png", "/uploads/posts/2025-03/medium/survivors-airdrop-addon_3.png", "/uploads/posts/2025-03/survivors-airdrop-addon_4.png", "/uploads/posts/2025-03/survivors-airdrop-addon_5.png", "/uploads/posts/2025-03/survivors-airdrop-addon_6.png", "/uploads/posts/2025-03/survivors-airdrop-addon_7.png"], "download_link": "https://mcpeland.io/index.php?do=download&id=2291&lang=en", "bp_download_link": null, "rp_download_link": null}}]