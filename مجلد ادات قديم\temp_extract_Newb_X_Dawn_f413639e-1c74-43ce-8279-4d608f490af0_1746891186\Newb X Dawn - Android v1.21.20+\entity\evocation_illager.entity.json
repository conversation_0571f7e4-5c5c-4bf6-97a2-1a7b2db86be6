{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:evocation_illager", "min_engine_version": "1.8.0", "materials": {"default": "phantom"}, "textures": {"default": "textures/entity/illager/evoker"}, "geometry": {"default": "geometry.evoker.v1.8"}, "scripts": {"scale": "0.9375", "animate": ["controller_general", "controller_move"]}, "animations": {"general": "animation.evoker.general", "casting": "animation.evoker.casting", "look_at_target": "animation.common.look_at_target", "move": "animation.villager.move", "celebrating": "animation.humanoid.celebrating", "controller_general": "controller.animation.evoker.general", "controller_move": "controller.animation.villager.move"}, "particle_effects": {"spell": "minecraft:evoker_spell"}, "render_controllers": ["controller.render.evoker"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 40}}}}