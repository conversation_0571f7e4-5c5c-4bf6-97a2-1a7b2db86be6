#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from enhanced_mcpedl_extractor import extract_mcpedl_mod_enhanced

# Test without Gemini first
print("🔍 Testing basic extraction without Gemini...")
test_url = "https://mcpedl.com/eating-animation/"
print(f"🔗 URL: {test_url}")

try:
    result = extract_mcpedl_mod_enhanced(test_url, None)  # No Gemini key
    
    if result:
        print("✅ Basic extraction successful!")
        print(f"📝 Name: '{result.get('name', 'N/A')}'")
        print(f"📂 Category: '{result.get('category', 'N/A')}'")
        print(f"👤 Creator: '{result.get('creator_name', 'N/A')}'")
        print(f"📄 Description: '{result.get('description', 'N/A')[:100]}...'")
        print(f"🖼️ Images: {len(result.get('image_urls', []))}")
        print(f"🌐 Social: {len(result.get('creator_social_channels', []))}")
        
        if result.get('image_urls'):
            print("\n🖼️ First few images:")
            for i, img in enumerate(result['image_urls'][:3], 1):
                print(f"   {i}. {img}")
    else:
        print("❌ Extraction failed")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
