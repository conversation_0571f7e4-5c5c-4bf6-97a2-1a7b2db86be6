# -*- coding: utf-8 -*-
"""
Local File Storage Module
This module provides functionality for storing files locally as an alternative to cloud storage.
"""

import os
import shutil
import uuid
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

class LocalFileStorage:
    """
    A class that handles local file storage operations.
    """
    
    def __init__(self, base_dir: str = "local_storage"):
        """
        Initialize the LocalFileStorage with a base directory.
        
        Args:
            base_dir: The base directory for local storage
        """
        self.base_dir = base_dir
        self.images_dir = os.path.join(base_dir, "images")
        self.mods_dir = os.path.join(base_dir, "mods")
        self.metadata_file = os.path.join(base_dir, "metadata.json")
        self.is_initialized = True  # Add this attribute to match the interface expected by create_firebase_manager
        self.metadata = {}
        
        # Create directories if they don't exist
        os.makedirs(self.base_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.mods_dir, exist_ok=True)
        
        # Load metadata if it exists
        self._load_metadata()
    
    def _load_metadata(self):
        """Load metadata from the metadata file."""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            except Exception as e:
                print(f"⚠️ Error loading metadata: {e}")
                self.metadata = {"images": {}, "mods": {}}
        else:
            self.metadata = {"images": {}, "mods": {}}
    
    def _save_metadata(self):
        """Save metadata to the metadata file."""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Error saving metadata: {e}")
    
    def upload_image(self, file_path: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Upload an image to local storage.
        
        Args:
            file_path: Path to the image file
            metadata: Additional metadata for the image
            
        Returns:
            A dictionary with information about the uploaded image
        """
        if not os.path.exists(file_path):
            return {"success": False, "error": "File not found"}
        
        try:
            # Generate a unique ID for the image
            image_id = str(uuid.uuid4())
            
            # Get the file extension
            _, ext = os.path.splitext(file_path)
            
            # Create the destination path
            dest_filename = f"{image_id}{ext}"
            dest_path = os.path.join(self.images_dir, dest_filename)
            
            # Copy the file
            shutil.copy2(file_path, dest_path)
            
            # Create metadata entry
            image_metadata = {
                "id": image_id,
                "original_filename": os.path.basename(file_path),
                "filename": dest_filename,
                "path": dest_path,
                "size": os.path.getsize(dest_path),
                "uploaded_at": time.time(),
                "url": f"file://{os.path.abspath(dest_path)}",
                "metadata": metadata or {}
            }
            
            # Update metadata
            if "images" not in self.metadata:
                self.metadata["images"] = {}
            self.metadata["images"][image_id] = image_metadata
            self._save_metadata()
            
            return {
                "success": True,
                "image_id": image_id,
                "url": image_metadata["url"],
                "path": dest_path,
                "metadata": image_metadata
            }
            
        except Exception as e:
            print(f"⚠️ Error uploading image: {e}")
            return {"success": False, "error": str(e)}
    
    def upload_mod(self, file_path: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Upload a mod file to local storage.
        
        Args:
            file_path: Path to the mod file
            metadata: Additional metadata for the mod
            
        Returns:
            A dictionary with information about the uploaded mod
        """
        if not os.path.exists(file_path):
            return {"success": False, "error": "File not found"}
        
        try:
            # Generate a unique ID for the mod
            mod_id = str(uuid.uuid4())
            
            # Get the file extension
            _, ext = os.path.splitext(file_path)
            
            # Create the destination path
            dest_filename = f"{mod_id}{ext}"
            dest_path = os.path.join(self.mods_dir, dest_filename)
            
            # Copy the file
            shutil.copy2(file_path, dest_path)
            
            # Create metadata entry
            mod_metadata = {
                "id": mod_id,
                "original_filename": os.path.basename(file_path),
                "filename": dest_filename,
                "path": dest_path,
                "size": os.path.getsize(dest_path),
                "uploaded_at": time.time(),
                "url": f"file://{os.path.abspath(dest_path)}",
                "metadata": metadata or {}
            }
            
            # Update metadata
            if "mods" not in self.metadata:
                self.metadata["mods"] = {}
            self.metadata["mods"][mod_id] = mod_metadata
            self._save_metadata()
            
            return {
                "success": True,
                "mod_id": mod_id,
                "url": mod_metadata["url"],
                "path": dest_path,
                "metadata": mod_metadata
            }
            
        except Exception as e:
            print(f"⚠️ Error uploading mod: {e}")
            return {"success": False, "error": str(e)}
    
    def get_image(self, image_id: str) -> Dict[str, Any]:
        """
        Get information about an image.
        
        Args:
            image_id: The ID of the image
            
        Returns:
            A dictionary with information about the image
        """
        if "images" not in self.metadata or image_id not in self.metadata["images"]:
            return {"success": False, "error": "Image not found"}
        
        image_metadata = self.metadata["images"][image_id]
        
        if not os.path.exists(image_metadata["path"]):
            return {"success": False, "error": "Image file not found"}
        
        return {
            "success": True,
            "image_id": image_id,
            "url": image_metadata["url"],
            "path": image_metadata["path"],
            "metadata": image_metadata
        }
    
    def get_mod(self, mod_id: str) -> Dict[str, Any]:
        """
        Get information about a mod.
        
        Args:
            mod_id: The ID of the mod
            
        Returns:
            A dictionary with information about the mod
        """
        if "mods" not in self.metadata or mod_id not in self.metadata["mods"]:
            return {"success": False, "error": "Mod not found"}
        
        mod_metadata = self.metadata["mods"][mod_id]
        
        if not os.path.exists(mod_metadata["path"]):
            return {"success": False, "error": "Mod file not found"}
        
        return {
            "success": True,
            "mod_id": mod_id,
            "url": mod_metadata["url"],
            "path": mod_metadata["path"],
            "metadata": mod_metadata
        }
    
    def delete_image(self, image_id: str) -> Dict[str, Any]:
        """
        Delete an image from local storage.
        
        Args:
            image_id: The ID of the image
            
        Returns:
            A dictionary indicating success or failure
        """
        if "images" not in self.metadata or image_id not in self.metadata["images"]:
            return {"success": False, "error": "Image not found"}
        
        image_metadata = self.metadata["images"][image_id]
        
        try:
            if os.path.exists(image_metadata["path"]):
                os.remove(image_metadata["path"])
            
            # Remove from metadata
            del self.metadata["images"][image_id]
            self._save_metadata()
            
            return {"success": True}
            
        except Exception as e:
            print(f"⚠️ Error deleting image: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_mod(self, mod_id: str) -> Dict[str, Any]:
        """
        Delete a mod from local storage.
        
        Args:
            mod_id: The ID of the mod
            
        Returns:
            A dictionary indicating success or failure
        """
        if "mods" not in self.metadata or mod_id not in self.metadata["mods"]:
            return {"success": False, "error": "Mod not found"}
        
        mod_metadata = self.metadata["mods"][mod_id]
        
        try:
            if os.path.exists(mod_metadata["path"]):
                os.remove(mod_metadata["path"])
            
            # Remove from metadata
            del self.metadata["mods"][mod_id]
            self._save_metadata()
            
            return {"success": True}
            
        except Exception as e:
            print(f"⚠️ Error deleting mod: {e}")
            return {"success": False, "error": str(e)}
    
    def list_images(self) -> List[Dict[str, Any]]:
        """
        List all images in local storage.
        
        Returns:
            A list of dictionaries with information about each image
        """
        if "images" not in self.metadata:
            return []
        
        return [
            {
                "image_id": image_id,
                "url": metadata["url"],
                "path": metadata["path"],
                "original_filename": metadata["original_filename"],
                "size": metadata["size"],
                "uploaded_at": metadata["uploaded_at"]
            }
            for image_id, metadata in self.metadata["images"].items()
            if os.path.exists(metadata["path"])
        ]
    
    def list_mods(self) -> List[Dict[str, Any]]:
        """
        List all mods in local storage.
        
        Returns:
            A list of dictionaries with information about each mod
        """
        if "mods" not in self.metadata:
            return []
        
        return [
            {
                "mod_id": mod_id,
                "url": metadata["url"],
                "path": metadata["path"],
                "original_filename": metadata["original_filename"],
                "size": metadata["size"],
                "uploaded_at": metadata["uploaded_at"]
            }
            for mod_id, metadata in self.metadata["mods"].items()
            if os.path.exists(metadata["path"])
        ]

    def upload_mod_to_storage(self, file_content: bytes, filename: str) -> str:
        """
        Upload mod content to local storage and return a URL.

        Args:
            file_content: The mod file content as bytes
            filename: The filename for the mod

        Returns:
            A local file URL or None if upload failed
        """
        try:
            # Generate a unique ID for the mod
            mod_id = str(uuid.uuid4())

            # Get the file extension
            _, ext = os.path.splitext(filename)

            # Create the mod filename
            mod_filename = f"{mod_id}{ext}"
            mod_path = os.path.join(self.mods_dir, mod_filename)

            # Write the file content
            with open(mod_path, 'wb') as f:
                f.write(file_content)

            # Create metadata
            metadata = {
                "id": mod_id,
                "original_filename": filename,
                "filename": mod_filename,
                "size": len(file_content),
                "upload_date": datetime.now().isoformat(),
                "type": "mod"
            }

            # Save metadata
            self.metadata["mods"][mod_id] = metadata
            self._save_metadata()

            # Return a local file URL
            return f"file://{os.path.abspath(mod_path)}"

        except Exception as e:
            print(f"⚠️ Error uploading mod to storage: {e}")
            return None

    def upload_image_to_storage(self, file_content: bytes, filename: str) -> str:
        """
        Upload image content to local storage and return a URL.

        Args:
            file_content: The image file content as bytes
            filename: The filename for the image

        Returns:
            A local file URL or None if upload failed
        """
        try:
            # Generate a unique ID for the image
            image_id = str(uuid.uuid4())

            # Get the file extension
            _, ext = os.path.splitext(filename)

            # Create the image filename
            image_filename = f"{image_id}{ext}"
            image_path = os.path.join(self.images_dir, image_filename)

            # Write the file content
            with open(image_path, 'wb') as f:
                f.write(file_content)

            # Create metadata
            metadata = {
                "id": image_id,
                "original_filename": filename,
                "filename": image_filename,
                "size": len(file_content),
                "upload_date": datetime.now().isoformat(),
                "type": "image"
            }

            # Save metadata
            self.metadata["images"][image_id] = metadata
            self._save_metadata()

            # Return a local file URL
            return f"file://{os.path.abspath(image_path)}"

        except Exception as e:
            print(f"⚠️ Error uploading image to storage: {e}")
            return None


# Create a function to get a LocalFileStorage instance
def get_local_storage(base_dir: str = "local_storage") -> LocalFileStorage:
    """
    Get a LocalFileStorage instance.

    Args:
        base_dir: The base directory for local storage

    Returns:
        A LocalFileStorage instance
    """
    return LocalFileStorage(base_dir)