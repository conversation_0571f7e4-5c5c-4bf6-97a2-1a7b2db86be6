# -*- coding: utf-8 -*-
import os
import requests

# تغيير المجلد الحالي إلى مجلد الأداة
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)
from PIL import Image, ImageTk
from io import BytesIO
from supabase import create_client, Client
from bs4 import BeautifulSoup
import re
import time
import random
import string
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog, filedialog
import threading
import json
import unicodedata
import mimetypes
from urllib.parse import urljoin, urlparse
import zipfile
import traceback
from typing import Dict, List, Optional, Any

# --- Gemini Import ---
gemini_extraction_cache = {} # Global cache for Gemini extraction results

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("Warning: google-generativeai library not found. AI description generation will be disabled.")
    print("Install it using: pip install google-generativeai")

# --- MCPEDL Scraper Import ---
try:
    from mcpedl_scraper_fixed import scrape_mcpedl_mod, MCPEDLScraperFixed
    MCPEDL_SCRAPER_AVAILABLE = True
    print("MCPEDL Scraper module loaded successfully.")
except ImportError:
    MCPEDL_SCRAPER_AVAILABLE = False
    print("Warning: MCPEDL Scraper module not found. MCPEDL extraction features will be disabled.")

# --- Gemini Image Filter Import ---
try:
    from gemini_image_filter import GeminiImageFilter
    GEMINI_IMAGE_FILTER_AVAILABLE = True
    print("Gemini Image Filter module loaded successfully.")
except ImportError:
    GEMINI_IMAGE_FILTER_AVAILABLE = False
    print("Warning: Gemini Image Filter module not found. Smart image filtering will be disabled.")

# --- Gemini Social Extractor Import ---
try:
    from gemini_social_extractor import GeminiSocialExtractor
    GEMINI_SOCIAL_EXTRACTOR_AVAILABLE = True
    print("Gemini Social Extractor module loaded successfully.")
except ImportError:
    GEMINI_SOCIAL_EXTRACTOR_AVAILABLE = False
    print("Warning: Gemini Social Extractor module not found. Smart social media extraction will be disabled.")

# --- Firebase Integration Import ---
try:
    from firebase_config import FirebaseManager
    FIREBASE_AVAILABLE = True
    print("Firebase integration module loaded successfully.")
except ImportError:
    FIREBASE_AVAILABLE = False
    print("Warning: Firebase integration module not found. Firebase features will be disabled.")

try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    print("Warning: pyperclip library not found. Paste/Copy buttons will be disabled.")
    print("Install it using: pip install pyperclip")

# --- Configurations ---
STORAGE_URL = "https://mwxzwfeqsashcwvqthmd.supabase.co"
STORAGE_KEY = os.environ.get("SUPABASE_KEY", "")
IMAGE_BUCKET = "image"
MOD_BUCKET = "my_new_mods_bucket"
MAX_MOD_SIZE_MB = 100
MAX_IMAGE_SIZE_BYTES = 5 * 1024 * 1024 # 5MB limit for images

# Phosus API Keys
PHOSUS_AUTO_ENHANCE_API_KEY = os.environ.get("PHOSUS_AUTO_ENHANCE_API_KEY", "")
PHOSUS_SUPER_RES_API_KEY = os.environ.get("PHOSUS_SUPER_RES_API_KEY", "")
# Placeholder Phosus API Endpoints - these will need to be updated with actual URLs
PHOSUS_AUTO_ENHANCE_ENDPOINT = "https://api.phosus.com/v1/enhancement/auto" # Placeholder
PHOSUS_SUPER_RES_ENDPOINT = "https://api.phosus.com/v1/enhancement/super-resolution" # Placeholder

APP_DB_URL = 'https://ytqxxodyecdeosnqoure.supabase.co'
APP_DB_KEY = ""  # سيتم تحديدها من ملف api_keys.json
MODS_TABLE_NAME = 'mods'
CATEGORIES = ["Addons", "Shaders", "Texture Pack"]

def clean_simple_description(description: str) -> str:
    """تنظيف الوصف ليكون بسيط وبدون فقرات أو مسافات كبيرة"""
    if not description:
        return ""

    import re

    # إزالة أي نصوص بين أقواس مربعة مثل [ENGLISH_DESCRIPTION]
    description = re.sub(r'\[.*?\]', '', description)
    
    # إزالة المسافات الكبيرة بين الجمل
    description = re.sub(r'\n\s*\n', ' ', description)
    
    # إزالة المسافات الزائدة
    description = re.sub(r'\s+', ' ', description)
    
    # إزالة النقاط والعلامات الزائدة في بداية النص
    description = re.sub(r'^[\*\-\•\s]+', '', description)
    
    # إزالة أي عناوين أو فقرات منسقة
    description = re.sub(r'^#+\s*', '', description, flags=re.MULTILINE)
    
    # تنظيف النص من الكلمات التسويقية المفرطة
    marketing_words = [
        'fundamentally enhance', 'exceptional experience', 'remarkable addition',
        'innovative gameplay mechanics', 'completely transform', 'effortlessly navigating',
        'expansive landscapes', 'crucial discoveries', 'treasured locations',
        'newfound confidence', 'deepening your immersion', 'transformative mechanics',
        'visual excellence', 'high-quality textures', 'seamlessly into', 
        'elevating its aesthetic appeal', 'engineered for smooth performance',
        'fluid and responsive experience', 'delve into its added layers',
        'exceptionally easy installation', 'absolutely no technical expertise',
        'captivating content', 'unlock a more streamlined', 'true potential'
    ]
    
    for word in marketing_words:
        description = description.replace(word, '')
    
    # تنظيف المسافات الزائدة مرة أخرى
    description = re.sub(r'\s+', ' ', description)
    
    return description.strip()

def generate_simple_description_task(mod_name, mod_category, scraped_text, manual_features):
    """إنشاء وصف بسيط ومختصر للمود"""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء الوصف")
        return

    update_status(f"🔄 بدء إنشاء وصف بسيط للمود: {mod_name}")

    # بناء البرومبت للحصول على وصف بسيط
    prompt = f"""
اكتب وصف بسيط ومختصر لمود Minecraft:

اسم المود: {mod_name}
فئة المود: {mod_category}
معلومات إضافية: {scraped_text[:500] if scraped_text else "غير متوفر"}
الميزات: {manual_features if manual_features else "غير محدد"}

متطلبات الوصف:
- وصف بسيط في جملة أو جملتين فقط
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية للمود
- بدون ذكر إصدارات أو متطلبات تقنية
- أسلوب طبيعي وبسيط

مثال للأسلوب المطلوب:
"هذا المود يضيف كراسي قابلة للجلوس في عالم Minecraft مما يتيح للاعبين الراحة وتزيين منازلهم بأثاث وظيفي."

اكتب الوصف فقط بدون أي إضافات:
"""

    try:
        response = smart_gemini_request(prompt)
        if response:
            # تنظيف الوصف المولد
            simple_description = clean_simple_description(response)
            
            if simple_description:
                update_status("✅ تم إنشاء وصف بسيط بنجاح")
                # تحديث الحقل
                if 'publish_desc_text' in globals() and publish_desc_text is not None:
                    auto_populate_text_widget(publish_desc_text, simple_description)
                return simple_description
            else:
                update_status("❌ فشل في تنظيف الوصف المولد")
        else:
            update_status("❌ لم يتم الحصول على استجابة من Gemini")
    except Exception as e:
        update_status(f"❌ خطأ في إنشاء الوصف: {str(e)}")

def generate_telegram_descriptions_fixed(mod_data):
    """إنشاء أوصاف تيليجرام بسيطة بدون نصوص إضافية"""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء أوصاف التيليجرام")
        return None

    mod_name = mod_data.get('name', '')
    mod_category = mod_data.get('category', '')
    existing_description = mod_data.get('description', '')

    if not mod_name:
        update_status("❌ اسم المود مطلوب لإنشاء أوصاف التيليجرام")
        return None

    update_status(f"🔄 بدء إنشاء أوصاف التيليجرام للمود: {mod_name}")

    # برومبت محسن للحصول على أوصاف بسيطة
    prompt = f"""
اكتب وصفين بسيطين ومختصرين لمود Minecraft:

اسم المود: {mod_name}
النوع: {mod_category}
الوصف الموجود: {existing_description[:300] if existing_description else "غير متوفر"}

متطلبات:
- وصف عربي بسيط في جملة أو جملتين
- وصف إنجليزي بسيط في جملة أو جملتين  
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية
- بدون أي نصوص إضافية أو علامات

قدم الإجابة بالتنسيق التالي:

العربي: [الوصف العربي هنا]

الإنجليزي: [الوصف الإنجليزي هنا]
"""

    try:
        response = smart_gemini_request(prompt)
        if response:
            # استخراج الوصفين
            arabic_desc = ""
            english_desc = ""
            
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('العربي:'):
                    arabic_desc = line.replace('العربي:', '').strip()
                elif line.startswith('الإنجليزي:'):
                    english_desc = line.replace('الإنجليزي:', '').strip()
            
            # تنظيف الأوصاف
            if arabic_desc:
                arabic_desc = clean_simple_description(arabic_desc)
            if english_desc:
                english_desc = clean_simple_description(english_desc)
            
            if arabic_desc and english_desc:
                update_status("✅ تم إنشاء أوصاف التيليجرام بنجاح")
                return {
                    'ar': arabic_desc,
                    'en': english_desc
                }
            else:
                update_status("❌ فشل في استخراج الأوصاف من الاستجابة")
        else:
            update_status("❌ لم يتم الحصول على استجابة من Gemini")
    except Exception as e:
        update_status(f"❌ خطأ في إنشاء أوصاف التيليجرام: {str(e)}")
    
    return None

# استيراد باقي الكود من الملف الأصلي (سيتم نسخه)
# هذا مجرد مثال على الدوال المحدثة
# يجب دمج هذه الدوال مع الكود الأصلي
