# -*- coding: utf-8 -*-
"""
اختبار المستخرج المحسن لـ MCPEDL
Test Enhanced MCPEDL Extractor
"""

import os
import json
from enhanced_mcpedl_extractor import extract_mcpedl_mod_enhanced

def test_enhanced_extractor():
    """اختبار شامل للمستخرج المحسن"""
    
    # روابط اختبار مختلفة
    test_urls = [
        "https://mcpedl.com/dragon-mounts-v1-3-25/",
        "https://mcpedl.com/furniture-addon/",
        "https://mcpedl.com/cars-addon/",
        "https://mcpedl.com/realistic-shaders/"
    ]
    
    print("🧪 بدء اختبار المستخرج المحسن لـ MCPEDL")
    print("=" * 60)
    
    # الحصول على مفتاح Gemini من متغيرات البيئة
    gemini_key = os.environ.get("GEMINI_API_KEY")
    if not gemini_key:
        print("⚠️ مفتاح Gemini API غير متوفر. سيتم الاختبار بدون فلترة ذكية.")
    else:
        print("✅ مفتاح Gemini API متوفر. سيتم استخدام الفلترة الذكية.")
    
    results = []
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 اختبار {i}/{len(test_urls)}: {url}")
        print("-" * 50)
        
        try:
            # استخراج البيانات
            mod_data = extract_mcpedl_mod_enhanced(url, gemini_key)
            
            if mod_data:
                print(f"✅ نجح الاستخراج!")
                
                # عرض النتائج
                print(f"📝 اسم المود: {mod_data.get('name', 'غير محدد')}")
                print(f"📂 الفئة: {mod_data.get('category', 'غير محدد')}")
                print(f"📄 طول الوصف: {len(mod_data.get('description', ''))} حرف")
                
                # تحليل الصور
                images = mod_data.get('image_urls', [])
                print(f"🖼️ عدد الصور: {len(images)}")
                
                if images:
                    # فحص جودة الصور
                    forgecdn_images = [img for img in images if 'forgecdn.net' in img]
                    mcpedl_images = [img for img in images if 'r2.mcpedl.com' in img]
                    
                    print(f"   - صور ForgeCD: {len(forgecdn_images)}")
                    print(f"   - صور MCPEDL: {len(mcpedl_images)}")
                    
                    # فحص الصور المشبوهة
                    suspicious_images = []
                    suspicious_patterns = ['gravatar', 'avatar', 'profile', 'user', 'comment']
                    
                    for img in images:
                        if any(pattern in img.lower() for pattern in suspicious_patterns):
                            suspicious_images.append(img)
                    
                    if suspicious_images:
                        print(f"   ⚠️ صور مشبوهة: {len(suspicious_images)}")
                        for img in suspicious_images:
                            print(f"      - {img}")
                    else:
                        print("   ✅ لا توجد صور مشبوهة")
                
                # تحليل روابط التواصل الاجتماعي
                social_links = mod_data.get('creator_social_channels', [])
                print(f"🌐 روابط التواصل الاجتماعي: {len(social_links)}")
                
                if social_links:
                    for link in social_links:
                        print(f"   - {link}")
                    
                    # فحص روابط المشاركة المشبوهة
                    share_patterns = ['sharer.php', 'intent/tweet', 'share?url=']
                    suspicious_social = []
                    
                    for link in social_links:
                        if any(pattern in link.lower() for pattern in share_patterns):
                            suspicious_social.append(link)
                    
                    if suspicious_social:
                        print(f"   ⚠️ روابط مشاركة مشبوهة: {len(suspicious_social)}")
                        for link in suspicious_social:
                            print(f"      - {link}")
                    else:
                        print("   ✅ لا توجد روابط مشاركة مشبوهة")
                
                # حفظ النتائج
                results.append({
                    'url': url,
                    'success': True,
                    'mod_name': mod_data.get('name'),
                    'images_count': len(images),
                    'social_links_count': len(social_links),
                    'has_suspicious_images': len(suspicious_images) > 0,
                    'has_suspicious_social': len(suspicious_social) > 0 if 'suspicious_social' in locals() else False
                })
                
            else:
                print("❌ فشل الاستخراج")
                results.append({
                    'url': url,
                    'success': False,
                    'error': 'No data returned'
                })
                
        except Exception as e:
            print(f"❌ خطأ في الاستخراج: {e}")
            results.append({
                'url': url,
                'success': False,
                'error': str(e)
            })
    
    # عرض ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(results)}")
    print(f"❌ اختبارات فاشلة: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_images = sum(r['images_count'] for r in successful_tests) / len(successful_tests)
        avg_social = sum(r['social_links_count'] for r in successful_tests) / len(successful_tests)
        
        print(f"📈 متوسط الصور المستخرجة: {avg_images:.1f}")
        print(f"📈 متوسط روابط التواصل: {avg_social:.1f}")
        
        suspicious_image_count = sum(1 for r in successful_tests if r['has_suspicious_images'])
        suspicious_social_count = sum(1 for r in successful_tests if r['has_suspicious_social'])
        
        print(f"⚠️ مودات بصور مشبوهة: {suspicious_image_count}/{len(successful_tests)}")
        print(f"⚠️ مودات بروابط مشبوهة: {suspicious_social_count}/{len(successful_tests)}")
    
    if failed_tests:
        print("\n❌ الاختبارات الفاشلة:")
        for test in failed_tests:
            print(f"   - {test['url']}: {test.get('error', 'Unknown error')}")
    
    # حفظ النتائج في ملف
    with open('test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 تم حفظ النتائج في test_results.json")
    
    return results

def test_specific_features():
    """اختبار ميزات محددة"""
    print("\n🔬 اختبار الميزات المحددة")
    print("=" * 40)
    
    # اختبار فلترة الصور
    print("🖼️ اختبار فلترة الصور...")
    
    # اختبار استخراج التواصل الاجتماعي
    print("🌐 اختبار استخراج التواصل الاجتماعي...")
    
    print("✅ تم اختبار جميع الميزات")

if __name__ == "__main__":
    print("🚀 بدء اختبار المستخرج المحسن")
    
    # اختبار أساسي
    results = test_enhanced_extractor()
    
    # اختبار الميزات المحددة
    test_specific_features()
    
    print("\n🎉 انتهى الاختبار!")
