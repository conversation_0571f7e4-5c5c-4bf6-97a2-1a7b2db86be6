{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:skeleton_horse", "min_engine_version": "1.2.6", "materials": {"default": "spider"}, "textures": {"skeleton": "textures/entity/horse2/horse_skeleton", "markings_none": "textures/entity/horse2/horse_markings_none", "armor_none": "textures/entity/horse2/armor/horse_armor_none"}, "geometry": {"default": "geometry.horse.v2"}, "animations": {"setup": "animation.horse.v2.setup", "baby_transform": "animation.horse.v2.baby_transform", "walk": "animation.horse.v2.walk", "eat": "animation.horse.v2.eat", "stand": "animation.horse.v2.stand", "tail": "animation.horse.v2.tail"}, "scripts": {"initialize": ["variable.eat_anim = 0.0;", "variable.mouth_anim = 0.0;"], "pre_animation": ["variable.eat_anim = math.clamp(variable.eat_anim + (query.is_grazing ? ((1.0 - variable.eat_anim) * 0.4 + 0.05) : -variable.eat_anim * 0.4 - 0.05) * query.delta_time * 20.0, 0.0, 1.0);", "variable.mouth_anim = math.clamp(variable.mouth_anim + (variable.open_mouth ? ((1.0 - variable.mouth_anim) * 0.7 + 0.05) : -variable.mouth_anim * 0.7 - 0.05) * query.delta_time * 20.0, 0.0, 1.0);", "variable.head_x_rot = query.target_x_rotation + (query.modified_move_speed > 0.2 ? (math.cos(query.modified_distance_moved * 11.46) * 11.46 * query.modified_move_speed + query.modified_move_speed * 11.46) : 0.0);", "variable.inverse_max_stand_eat = (1.0 - math.max(variable.stand_anim, variable.eat_anim));", "variable.base_head_angle = variable.inverse_max_stand_eat * (30.0 + variable.head_x_rot + variable.mouth_anim  * math.sin((query.life_time * 20.0 + query.frame_alpha) * 57.3) * 2.86);", "variable.leg_stand_factor = math.cos((query.modified_distance_moved * 38.38) + 180.0);", "variable.leg_x_rot_anim = variable.leg_stand_factor * 45.8 * query.modified_move_speed;", "variable.leg1a_aabb = query.bone_aabb('leg1a');", "variable.y_adjust = query.is_baby ? 0.0 : (variable.leg1a_aabb.max.y - variable.leg1a_aabb.min.y) * (1.0 - (query.model_scale * 16.0));"], "animate": ["setup", {"baby_transform": "query.is_baby"}, "walk", "stand", {"eat": "!query.is_standing"}, {"tail": "variable.shake_tail"}]}, "render_controllers": ["controller.render.skeleton_horse.v1.and.v2"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 32}}}}