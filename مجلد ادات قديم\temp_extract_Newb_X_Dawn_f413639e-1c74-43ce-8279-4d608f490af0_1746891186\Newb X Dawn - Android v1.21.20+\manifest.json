{
    "format_version": 2,
    "header": {
        "description": "By Ale Graphics. To run the §lNewb §rX §lRTX §rshaders you need a patched apk v1.21.20+",
        "name": "Newb X Dawn v1.21.20+",
        "uuid": "990074cf-4afa-73e7-a21a-72106307b1d2",
        "version": [1, 1, 0],
        "min_engine_version": [1, 21, 20]
    },
    "modules": [
        {
            "description": "Newb X RTX compatible with RenderDragon",
            "type": "resources",
            "uuid": "100f3d8b-37b4-465f-8f56-941687e36c35",
            "version": [1, 1, 0]
        }
    ],
    "metadata": {
        "authors": ["alegraphics, devendrn"],
        "url": "https://github.com/devendrn/newb-x-mcbe/"
    }
}

}
ave_no_fog", "name": "No wave, No fog", "memory_tier": 1},
        {"folder_name": "no_fog", "name": "No fog", "memory_tier": 1},
        {"folder_name": "no_wave", "name": "No wave", "memory_tier": 1},
        {"folder_name": "vanilla_clouds_layer", "name": "Vanilla clouds layers", "memory_tier": 1},
        {"folder_name": "vanilla_clouds", "name": "Vanilla clouds", "memory_tier": 1},
        {"folder_name": "default", "name": "Default", "memory_tier": 1}
     ],
    "metadata":{"authors":["Ei chan"],"url":"https://github.com/devendrn/newb-x-mcbe/"}}}}}