#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة حماية موقع MCPEDL وتثبيت المكتبات المطلوبة
"""

import subprocess
import sys
import os

def install_cloudscraper():
    """تثبيت مكتبة cloudscraper"""
    print("🔧 تثبيت cloudscraper لتجاوز حماية Cloudflare...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "cloudscraper"])
        print("✅ تم تثبيت cloudscraper بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت cloudscraper: {e}")
        return False

def test_cloudscraper():
    """اختبار cloudscraper"""
    print("\n🧪 اختبار cloudscraper...")
    
    try:
        import cloudscraper
        
        # إنشاء scraper
        scraper = cloudscraper.create_scraper()
        print("✅ تم إنشاء cloudscraper بنجاح")
        
        # اختبار بسيط
        print("🌐 اختبار الاتصال...")
        response = scraper.get("https://httpbin.org/user-agent", timeout=10)
        
        if response.status_code == 200:
            print("✅ cloudscraper يعمل بشكل صحيح")
            return True
        else:
            print(f"⚠️ رمز استجابة غير متوقع: {response.status_code}")
            return False
            
    except ImportError:
        print("❌ cloudscraper غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار cloudscraper: {e}")
        return False

def test_mcpedl_connection():
    """اختبار الاتصال بموقع MCPEDL"""
    print("\n🌐 اختبار الاتصال بموقع MCPEDL...")
    
    try:
        from mcpedl_scraper_module import MCPEDLScraper
        
        scraper = MCPEDLScraper()
        
        # اختبار رابط بسيط
        test_url = "https://mcpedl.com/"
        
        print(f"📡 محاولة الوصول إلى: {test_url}")
        
        # محاولة جلب الصفحة الرئيسية
        soup = scraper.fetch_page(test_url)
        
        scraper.close()
        
        if soup:
            print("✅ تم الوصول إلى موقع MCPEDL بنجاح")
            
            # التحقق من وجود محتوى مفيد
            title = soup.find('title')
            if title and 'mcpedl' in title.get_text().lower():
                print("✅ محتوى الموقع صحيح")
                return True
            else:
                print("⚠️ محتوى الموقع غير متوقع")
                return False
        else:
            print("❌ فشل في الوصول إلى موقع MCPEDL")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_mod_extraction():
    """اختبار استخراج مود واحد"""
    print("\n🎯 اختبار استخراج مود...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # رابط مود للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"📥 محاولة استخراج: {test_url}")
        
        mod_data = scrape_mcpedl_mod(test_url)
        
        if mod_data:
            print("✅ تم استخراج البيانات بنجاح!")
            
            # عرض ملخص البيانات
            print("\n📋 ملخص البيانات المستخرجة:")
            print(f"  الاسم: {mod_data.get('name', 'غير متوفر')}")
            print(f"  الفئة: {mod_data.get('category', 'غير متوفر')}")
            print(f"  الصور: {len(mod_data.get('image_urls', []))} صورة")
            print(f"  المطور: {mod_data.get('creator_name', 'غير متوفر')}")
            
            return True
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إصلاح مشكلة حماية موقع MCPEDL")
    print("=" * 50)
    
    success_steps = 0
    total_steps = 4
    
    # الخطوة 1: تثبيت cloudscraper
    print("الخطوة 1/4: تثبيت cloudscraper")
    if install_cloudscraper():
        success_steps += 1
    
    # الخطوة 2: اختبار cloudscraper
    print("\nالخطوة 2/4: اختبار cloudscraper")
    if test_cloudscraper():
        success_steps += 1
    
    # الخطوة 3: اختبار الاتصال بـ MCPEDL
    print("\nالخطوة 3/4: اختبار الاتصال بـ MCPEDL")
    if test_mcpedl_connection():
        success_steps += 1
    
    # الخطوة 4: اختبار استخراج مود
    print("\nالخطوة 4/4: اختبار استخراج مود")
    if test_mod_extraction():
        success_steps += 1
    
    # النتائج
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {success_steps}/{total_steps} خطوات نجحت")
    
    if success_steps == total_steps:
        print("🎉 تم إصلاح المشكلة بنجاح!")
        print("✅ ميزة استخراج MCPEDL جاهزة للاستخدام")
        
        print("\n💡 الخطوات التالية:")
        print("1. شغل الأداة الرئيسية: python mod_processor.py")
        print("2. ابحث عن قسم 'استخراج من MCPEDL'")
        print("3. الصق رابط مود من mcpedl.com")
        print("4. اضغط 'استخراج البيانات من MCPEDL'")
        
    elif success_steps >= 2:
        print("⚠️ تم إصلاح جزء من المشكلة")
        print("💡 قد تعمل الميزة لكن بنجاح محدود")
        
        print("\n🔧 نصائح إضافية:")
        print("- جرب في وقت آخر")
        print("- استخدم VPN إذا أمكن")
        print("- تأكد من الاتصال بالإنترنت")
        
    else:
        print("❌ فشل في إصلاح المشكلة")
        
        print("\n🆘 حلول بديلة:")
        print("1. استخدم الاستخراج اليدوي")
        print("2. انسخ البيانات من الموقع مباشرة")
        print("3. راجع ملف MCPEDL_PROTECTION_BYPASS.md")
    
    return success_steps == total_steps

if __name__ == "__main__":
    try:
        success = main()
        
        print(f"\n{'🎯 الإصلاح مكتمل!' if success else '⚠️ الإصلاح جزئي'}")
        
        input("\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
