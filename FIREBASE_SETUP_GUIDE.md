# دليل إعداد Firebase Storage لأداة نشر المودات

## نظرة عامة
تم تطوير الأداة لتدعم Firebase Storage كخدمة تخزين أساسية للمودات، مع الاحتفاظ بـ Supabase كنسخة احتياطية وقاعدة بيانات رئيسية.

## معلومات المشروع
- **معرف المشروع**: `download-e33a2`
- **Storage Bucket**: `download-e33a2.firebasestorage.app`
- **API Key**: `AIzaSyB3t2Ae-24DWUQJxwZ5LCFZVZov0ncaC8c`

## خطوات الإعداد

### 1. إنشاء Service Account
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك: `download-e33a2`
3. اذهب إلى **Project Settings** > **Service Accounts**
4. انقر على **Generate new private key**
5. احفظ الملف باسم `firebase-service-account.json` في مجلد الأداة

### 2. تثبيت Firebase Admin SDK
```bash
pip install firebase-admin
```

### 3. إعداد الأداة
1. شغل الأداة
2. اذهب إلى **إعدادات** > **إعداد Firebase**
3. اختر ملف `firebase-service-account.json`
4. انقر على **اختبار الاتصال**
5. انقر على **حفظ الإعدادات**

## كيفية عمل النظام

### رفع المودات
1. **Firebase Storage**: يتم رفع المود إلى Firebase أولاً للحصول على رابط تحميل مباشر
2. **Supabase Storage**: يتم رفع نسخة احتياطية إلى Supabase
3. **قاعدة البيانات**: يتم حفظ معلومات المود في Supabase مع رابط Firebase كرابط التحميل

### مزايا Firebase Storage
- **روابط تحميل مباشرة**: لا تحتاج إلى معالجة إضافية
- **سرعة عالية**: شبكة توزيع محتوى عالمية
- **موثوقية**: خدمة Google Cloud
- **مجاني**: 5GB مساحة تخزين مجانية

### هيكل التخزين
```
Firebase Storage:
├── mods/           # ملفات المودات (.mcpack, .mcaddon)
└── images/         # صور المودات
```

## استكشاف الأخطاء

### خطأ: "Firebase SDK غير متوفر"
```bash
pip install firebase-admin
```

### خطأ: "ملف service account غير موجود"
- تأكد من وجود ملف `firebase-service-account.json` في مجلد الأداة
- تأكد من صحة مسار الملف

### خطأ: "فشل الاتصال بـ Firebase"
- تأكد من صحة ملف service account
- تأكد من تفعيل Firebase Storage في المشروع
- تحقق من اتصال الإنترنت

## الإعدادات المتقدمة

### تغيير قاعدة البيانات الافتراضية
في ملف `config.json`:
```json
{
  "database_provider": "firebase",  // أو "supabase"
  "use_firebase_for_mods": true,
  "use_firebase_as_primary": false
}
```

### إدارة الملفات
- **عرض الملفات**: استخدم Firebase Console
- **حذف الملفات**: يمكن حذفها من Console أو الأداة
- **النسخ الاحتياطي**: يتم الاحتفاظ بنسخة في Supabase

## الأمان
- ملف service account يحتوي على مفاتيح خاصة - لا تشاركه
- Firebase Storage مُعد للوصول العام للقراءة فقط
- الكتابة محدودة للتطبيق المصرح له فقط

## الدعم
إذا واجهت مشاكل في الإعداد، تأكد من:
1. صحة معلومات المشروع
2. تثبيت جميع المتطلبات
3. صحة ملف service account
4. تفعيل Firebase Storage في المشروع
