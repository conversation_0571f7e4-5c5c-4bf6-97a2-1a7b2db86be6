/**
 * All possible MinecraftEnchantmentTypes
 */
export declare enum MinecraftEnchantmentTypes {
    AquaAffinity = "minecraft:aqua_affinity",
    BaneOfArthropods = "minecraft:bane_of_arthropods",
    Binding = "minecraft:binding",
    BlastProtection = "minecraft:blast_protection",
    BowInfinity = "minecraft:infinity",
    Breach = "minecraft:breach",
    Channeling = "minecraft:channeling",
    Density = "minecraft:density",
    DepthStrider = "minecraft:depth_strider",
    Efficiency = "minecraft:efficiency",
    FeatherFalling = "minecraft:feather_falling",
    FireAspect = "minecraft:fire_aspect",
    FireProtection = "minecraft:fire_protection",
    Flame = "minecraft:flame",
    Fortune = "minecraft:fortune",
    <PERSON><PERSON>alker = "minecraft:frost_walker",
    Impaling = "minecraft:impaling",
    Knockback = "minecraft:knockback",
    Looting = "minecraft:looting",
    Loyalty = "minecraft:loyalty",
    LuckOfTheSea = "minecraft:luck_of_the_sea",
    Lure = "minecraft:lure",
    Mending = "minecraft:mending",
    Multishot = "minecraft:multishot",
    Piercing = "minecraft:piercing",
    Power = "minecraft:power",
    ProjectileProtection = "minecraft:projectile_protection",
    Protection = "minecraft:protection",
    Punch = "minecraft:punch",
    QuickCharge = "minecraft:quick_charge",
    Respiration = "minecraft:respiration",
    Riptide = "minecraft:riptide",
    Sharpness = "minecraft:sharpness",
    SilkTouch = "minecraft:silk_touch",
    Smite = "minecraft:smite",
    SoulSpeed = "minecraft:soul_speed",
    SwiftSneak = "minecraft:swift_sneak",
    Thorns = "minecraft:thorns",
    Unbreaking = "minecraft:unbreaking",
    Vanishing = "minecraft:vanishing",
    WindBurst = "minecraft:wind_burst"
}
/**
 * Union type equivalent of the MinecraftEnchantmentTypes enum.
 */
export type MinecraftEnchantmentTypesUnion = keyof typeof MinecraftEnchantmentTypes;
