#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار المستخرج المحسن مع رابط حقيقي من MCPEDL
"""

import sys
import os
import json

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_mcpedl_extraction():
    """اختبار الاستخراج مع رابط حقيقي من MCPEDL"""
    print("🌐 اختبار المستخرج المحسن مع رابط حقيقي من MCPEDL")
    print("=" * 70)

    try:
        # استيراد المكتبات المطلوبة
        import requests
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        print("✅ تم استيراد المكتبات بنجاح")
        
        # إنشاء مثيل من المستخرج
        extractor = MCPEDLExtractorFixed()
        print("✅ تم إنشاء مثيل من المستخرج")
        
        # رابط مود حقيقي للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"🔗 رابط الاختبار: {test_url}")
        
        # تحميل HTML من الرابط
        print("📥 تحميل HTML من الرابط...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        try:
            response = requests.get(test_url, headers=headers, timeout=30)
            response.raise_for_status()
            html_content = response.text
            print(f"✅ تم تحميل HTML بنجاح ({len(html_content)} حرف)")
        except requests.exceptions.RequestException as e:
            print(f"❌ فشل في تحميل الصفحة: {e}")
            print("💡 سيتم استخدام HTML تجريبي بدلاً من ذلك...")
            
            # HTML تجريبي كحل احتياطي
            html_content = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Dragon Mounts Addon | MCPEDL</title>
                <meta property="og:image" content="https://media.forgecdn.net/attachments/123/456/dragon_preview.jpg">
            </head>
            <body>
                <h1 class="post-page__title">Dragon Mounts Addon v1.3.25</h1>
                
                <div class="creator-name">
                    <a href="/user/dragonmaster123">DragonMaster123</a>
                </div>
                
                <div class="post-page__content">
                    <p>This amazing addon adds rideable dragons to your Minecraft world!</p>
                    
                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" width="560" height="315"></iframe>
                    
                    <img src="https://media.forgecdn.net/attachments/123/456/dragon_screenshot1.jpg" alt="Dragon Screenshot">
                    <img src="https://media.forgecdn.net/attachments/123/457/dragon_screenshot2.png" alt="Dragon Screenshot 2">
                    
                    <p>Features:</p>
                    <ul>
                        <li>Rideable dragons</li>
                        <li>Multiple dragon types</li>
                        <li>Dragon breeding system</li>
                    </ul>
                    
                    <p>Follow me on social media:</p>
                    <a href="https://discord.gg/dragonmods">Join our Discord</a>
                    <a href="https://youtube.com/channel/UC123456789">My YouTube Channel</a>
                    <a href="https://twitter.com/dragonmaster123">Twitter</a>
                </div>
            </body>
            </html>
            """
        
        # استخراج البيانات
        print("🔍 بدء استخراج البيانات...")
        mod_data = extractor.extract_mod_data(html_content, test_url, generate_ai_descriptions=False)
        
        if mod_data:
            print("\n✅ تم استخراج البيانات بنجاح!")
            
            # عرض النتائج
            print("\n📋 ملخص البيانات المستخرجة:")
            print(f"📝 اسم المود: {mod_data.get('name', 'غير محدد')}")
            print(f"👤 المطور: {mod_data.get('creator_name', 'غير محدد')}")
            print(f"🔗 ملف المطور: {mod_data.get('creator_profile_url', 'غير محدد')}")
            print(f"📂 الفئة: {mod_data.get('category', 'غير محدد')}")
            print(f"📄 طول الوصف: {len(mod_data.get('description', ''))} حرف")
            
            # تحليل المميزات الجديدة
            print("\n🆕 المميزات الجديدة:")
            
            # فيديوهات YouTube
            videos = mod_data.get('video_urls', [])
            print(f"🎥 فيديوهات YouTube: {len(videos)}")
            for i, video in enumerate(videos, 1):
                print(f"   [{i}] {video}")
            
            # الصور
            images = mod_data.get('image_urls', [])
            print(f"\n🖼️ الصور: {len(images)}")
            for i, img in enumerate(images[:5], 1):  # أول 5 صور فقط
                print(f"   [{i}] {img[:60]}...")
            if len(images) > 5:
                print(f"   ... و {len(images) - 5} صور أخرى")
            
            # روابط التواصل الاجتماعي
            social_links = mod_data.get('creator_social_channels', [])
            print(f"\n🌐 روابط التواصل الاجتماعي: {len(social_links)}")
            for i, link in enumerate(social_links, 1):
                print(f"   [{i}] {link}")
            
            # تقييم جودة النتائج
            print("\n📊 تقييم جودة النتائج:")
            
            quality_score = 0
            total_checks = 6
            
            # فحص الاسم
            if mod_data.get('name') and len(mod_data['name']) > 5:
                print("✅ اسم المود موجود ومناسب")
                quality_score += 1
            else:
                print("❌ اسم المود مفقود أو قصير")
            
            # فحص المطور
            if mod_data.get('creator_name'):
                print("✅ اسم المطور موجود")
                quality_score += 1
            else:
                print("❌ اسم المطور مفقود")
            
            # فحص ملف المطور (ميزة جديدة)
            if mod_data.get('creator_profile_url') and 'mcpedl.com' in mod_data['creator_profile_url']:
                print("✅ ملف المطور على MCPEDL موجود")
                quality_score += 1
            else:
                print("❌ ملف المطور على MCPEDL مفقود")
            
            # فحص الفيديوهات (ميزة جديدة)
            if videos and any('youtube.com' in v or 'youtu.be' in v for v in videos):
                print("✅ فيديوهات YouTube موجودة")
                quality_score += 1
            else:
                print("❌ فيديوهات YouTube مفقودة")
            
            # فحص الصور
            if images and len(images) > 0:
                forgecdn_images = [img for img in images if 'forgecdn.net' in img]
                if forgecdn_images:
                    print(f"✅ صور forgecdn موجودة ({len(forgecdn_images)}/{len(images)})")
                    quality_score += 1
                else:
                    print("⚠️ لا توجد صور forgecdn")
            else:
                print("❌ لا توجد صور")
            
            # فحص روابط التواصل (محسن)
            if social_links:
                platforms = set()
                for link in social_links:
                    if ':' in link:
                        platform = link.split(':')[0]
                        platforms.add(platform)
                print(f"✅ روابط التواصل موجودة ({len(platforms)} منصة)")
                quality_score += 1
            else:
                print("❌ روابط التواصل مفقودة")
            
            # النتيجة النهائية
            quality_percentage = (quality_score / total_checks) * 100
            print(f"\n🎯 نقاط الجودة: {quality_score}/{total_checks} ({quality_percentage:.1f}%)")
            
            if quality_percentage >= 80:
                print("🎉 جودة ممتازة! التحسينات تعمل بشكل مثالي")
            elif quality_percentage >= 60:
                print("👍 جودة جيدة - التحسينات تعمل بشكل صحيح")
            else:
                print("⚠️ جودة متوسطة - قد تحتاج لمراجعة بعض التحسينات")
            
            # حفظ النتائج للمراجعة
            output_file = "test_results_enhanced.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(mod_data, f, indent=2, ensure_ascii=False)
            print(f"\n💾 تم حفظ النتائج في: {output_file}")
            
            return True
            
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار المستخرج المحسن مع بيانات حقيقية")
    print("=" * 70)
    
    success = test_real_mcpedl_extraction()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 الاختبار نجح! التحسينات تعمل بشكل ممتاز")
        print("💡 يمكنك الآن استخدام المستخرج المحسن في الإنتاج")
    else:
        print("❌ الاختبار فشل - قد تحتاج لمراجعة التحسينات")
    
    return success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
