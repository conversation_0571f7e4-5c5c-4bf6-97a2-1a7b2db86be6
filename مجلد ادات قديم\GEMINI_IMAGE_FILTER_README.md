# فلتر الصور الذكي باستخدام Gemini AI

## نظرة عامة

تم تطوير ميزة جديدة تستخدم الذكاء الاصطناعي Gemini من Google لفلترة الصور المستخرجة من صفحات MCPEDL بذكاء. هذه الميزة تضمن اختيار الصور المتعلقة بالمود فقط وتجاهل الصور غير المرغوبة مثل صور المستخدمين والإعلانات.

## المميزات الجديدة

### 🤖 فلترة ذكية للصور
- **تحليل ذكي**: يستخدم Gemini AI لتحليل محتوى الصور
- **اختيار دقيق**: يختار فقط الصور المتعلقة بالمود
- **رفض الصور غير المرغوبة**: يتجاهل صور المستخدمين والإعلانات والمودات الأخرى

### 🎯 معايير الاختيار الذكية
- صور تُظهر عناصر اللعب الجديدة (كتل، عناصر، وحوش)
- لقطات شاشة من داخل اللعبة تُظهر المود
- صور توضيحية للمميزات
- واجهات المستخدم الجديدة أو القوائم

### ❌ معايير الرفض الذكية
- صور الملفات الشخصية أو الأفاتار
- صور المودات الأخرى المقترحة
- إعلانات أو صور ترويجية للموقع
- أيقونات أو شعارات
- صور فارغة أو تالفة

## الملفات المضافة/المحدثة

### 1. `gemini_image_filter.py` (جديد)
الملف الرئيسي للفلتر الذكي:
- **GeminiImageFilter**: كلاس رئيسي للفلترة
- **filter_mod_images()**: دالة فلترة الصور
- **تحميل وتحليل الصور**: تحميل الصور وتحويلها لـ base64
- **تحليل استجابة Gemini**: استخراج الصور المختارة

### 2. `mcpedl_extractor_fixed.py` (محدث)
- إضافة استيراد فلتر Gemini
- تحديث دالة `extract_images()` لاستخدام الفلتر
- إضافة دالة `_apply_gemini_filter()`
- إضافة دالة `_extract_mod_name_from_url()`

### 3. `mcpedl_scraper_module.py` (محدث)
- إضافة استيراد فلتر Gemini
- تحديث دالة `extract_images()` لاستخدام الفلتر
- إضافة دوال مساعدة للفلترة الذكية

### 4. `mod_processor.py` (محدث)
- إضافة استيراد فلتر Gemini
- تحضير للتكامل مع الواجهة الرئيسية

## كيفية الاستخدام

### 1. التحضير
```bash
# تثبيت المكتبات المطلوبة
pip install google-generativeai requests pillow beautifulsoup4
```

### 2. إعداد مفتاح Gemini API
أضف مفتاح Gemini API في ملف `config.json`:
```json
{
  "gemini_api_keys": ["your-gemini-api-key-here"]
}
```

أو كمتغير بيئة:
```bash
export GEMINI_API_KEY="your-gemini-api-key-here"
```

### 3. الاستخدام التلقائي
الفلتر يعمل تلقائياً عند استخراج الصور من MCPEDL:

```python
from mcpedl_scraper_module import scrape_mcpedl_mod

# استخراج بيانات المود مع فلترة ذكية للصور
mod_data = scrape_mcpedl_mod("https://mcpedl.com/your-mod-url/")

# الصور المستخرجة ستكون مفلترة بذكاء
images = mod_data['image_urls']
```

### 4. الاستخدام المباشر
```python
from gemini_image_filter import GeminiImageFilter

# إنشاء فلتر
filter = GeminiImageFilter("your-api-key")

# فلترة قائمة صور
image_urls = ["url1", "url2", "url3"]
filtered_images = filter.filter_mod_images(
    image_urls, 
    "Dragon Mounts", 
    "Adds rideable dragons to Minecraft"
)
```

## كيفية عمل الفلتر

### 1. تحليل الصور
- تحميل الصور من الروابط
- تحويلها إلى تنسيق base64
- التحقق من صحة الصور

### 2. إرسال إلى Gemini
- إنشاء prompt مفصل باللغة العربية
- إرسال الصور مع معلومات المود
- طلب تحليل ذكي للمحتوى

### 3. تحليل النتائج
- استخراج فهارس الصور المختارة
- إرجاع قائمة الصور المفلترة
- التعامل مع الأخطاء بذكاء

## مثال على Prompt المرسل لـ Gemini

```
أنت خبير في ألعاب Minecraft وتحليل الصور. مهمتك هي فحص الصور المرفقة واختيار الصور التي تتعلق بالمود/الإضافة فقط.

معلومات المود:
- الاسم: Dragon Mounts
- الوصف: Adds rideable dragons to Minecraft

لديك 5 صور للفحص. يرجى تحليل كل صورة وتحديد ما إذا كانت:

✅ صورة متعلقة بالمود: تُظهر محتوى المود، مميزاته، أو عناصر اللعب الجديدة
❌ صورة غير متعلقة: صور المستخدمين، الإعلانات، أيقونات الموقع، أو مودات أخرى

أجب بتنسيق JSON فقط:
{"selected_images": [0, 1, 3]}
```

## المعالجة الذكية للأخطاء

### 1. عدم توفر مفتاح API
- يعود للفلترة التقليدية
- يعرض رسالة تحذيرية

### 2. فشل تحميل الصور
- يتجاهل الصور التالفة
- يواصل مع الصور الصالحة

### 3. خطأ في Gemini
- يعود للصور الأصلية
- يسجل الخطأ للتشخيص

## الأداء والحدود

### حدود API
- معالجة 5 صور في كل مرة
- تأخير 2 ثانية بين المجموعات
- حد أقصى 2MB لكل صورة

### التحسينات
- ضغط الصور الكبيرة تلقائياً
- تخزين مؤقت للنتائج
- معالجة متوازية للمجموعات

## الرسائل والتشخيص

### رسائل النجاح
```
✅ تم تهيئة Gemini Image Filter بنجاح
🤖 بدء فلترة الصور باستخدام Gemini AI للمود: Dragon Mounts
🎯 Gemini AI فلتر الصور: 3 من أصل 8
```

### رسائل التحذير
```
⚠️ مفتاح Gemini API غير متوفر للفلترة الذكية
⚠️ فلتر Gemini لم يختر أي صور، سيتم إرجاع الصور الأصلية
```

### رسائل الخطأ
```
❌ خطأ في تطبيق فلتر Gemini: [تفاصيل الخطأ]
❌ فشل تحميل الصورة: [رابط الصورة]
```

## الاختبار

### اختبار الفلتر
```python
from gemini_image_filter import test_gemini_image_filter
test_gemini_image_filter()
```

### اختبار التكامل
```python
from mcpedl_extractor_fixed import test_extractor_with_saved_html
test_extractor_with_saved_html()
```

## المتطلبات

- Python 3.8+
- google-generativeai
- requests
- pillow
- beautifulsoup4
- مفتاح Gemini API صالح

## الدعم والمساعدة

في حالة مواجهة مشاكل:
1. تأكد من صحة مفتاح Gemini API
2. تحقق من الاتصال بالإنترنت
3. راجع رسائل الخطأ في وحدة التحكم
4. جرب الاختبارات المدمجة

---

**ملاحظة**: هذه الميزة تحسن بشكل كبير من دقة استخراج الصور وتوفر تجربة أفضل للمستخدم من خلال اختيار الصور المناسبة فقط.
