# -*- coding: utf-8 -*-
"""
اختبار نهائي للتأكد من أن جميع الإصلاحات تعمل
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mcpedl_scraper():
    """اختبار MCPEDL scraper المحسن"""
    print("🧪 اختبار MCPEDL scraper المحسن...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            print("✅ نجح الاختبار!")
            print(f"اسم المود: {result.get('name', 'غير متوفر')}")
            print(f"الفئة: {result.get('category', 'غير متوفر')}")
            print(f"المطور: {result.get('creator_name', 'غير متوفر')}")
            print(f"الإصدار: {result.get('version', 'غير متوفر')}")
            print(f"الحجم: {result.get('size', 'غير متوفر')}")
            print(f"عدد الصور: {len(result.get('image_urls', []))}")
            print(f"طول الوصف: {len(result.get('description', ''))} حرف")
            
            # التحقق من البيانات الأساسية
            if result.get('name') and result.get('category') and result.get('creator_name'):
                print("✅ جميع البيانات الأساسية متوفرة")
                return True
            else:
                print("⚠️ بعض البيانات الأساسية مفقودة")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_integration_with_gui():
    """اختبار التكامل مع واجهة المستخدم"""
    print("\n🖥️ اختبار التكامل مع واجهة المستخدم...")
    
    try:
        # محاولة استيراد الوحدات المطلوبة
        from mcpedl_scraper_module import scrape_mcpedl_mod, MCPEDL_SCRAPER_AVAILABLE
        
        if MCPEDL_SCRAPER_AVAILABLE:
            print("✅ وحدة MCPEDL scraper متوفرة")
        else:
            print("❌ وحدة MCPEDL scraper غير متوفرة")
            return False
        
        # اختبار الدالة الرئيسية
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            print("✅ يمكن استخدام الوحدة من mod_processor.py")
            return True
        else:
            print("❌ مشكلة في استخدام الوحدة")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_different_urls():
    """اختبار روابط مختلفة"""
    print("\n🔗 اختبار روابط مختلفة...")
    
    test_urls = [
        "https://mcpedl.com/dragon-mounts-v1-3-25/",
        "https://mcpedl.com/dragon-mounts-2/",
        "https://mcpedl.com/dragon-mounts/"
    ]
    
    success_count = 0
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        for i, url in enumerate(test_urls, 1):
            print(f"\nاختبار {i}: {url}")
            
            try:
                result = scrape_mcpedl_mod(url)
                
                if result and result.get('name'):
                    print(f"✅ نجح: {result['name']}")
                    success_count += 1
                else:
                    print("❌ فشل في الاستخراج")
                    
            except Exception as e:
                print(f"❌ خطأ: {e}")
        
        print(f"\nالنتيجة: {success_count}/{len(test_urls)} نجح")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def provide_usage_instructions():
    """تقديم تعليمات الاستخدام"""
    print("\n📋 تعليمات الاستخدام:")
    print("1. تشغيل mod_processor.py")
    print("2. إدخال رابط MCPEDL في حقل 'رابط صفحة المود'")
    print("3. الضغط على زر 'استخراج البيانات من MCPEDL'")
    print("4. انتظار استخراج البيانات وملء الحقول تلقائياً")
    print("5. مراجعة البيانات وتعديلها إذا لزم الأمر")
    print("6. نشر المود")
    
    print("\n🔧 الإصلاحات المطبقة:")
    print("✅ إصلاح مشاكل cloudscraper")
    print("✅ تحسين تحليل HTML")
    print("✅ إضافة مستخرج محسن")
    print("✅ معالجة أفضل للأخطاء")
    print("✅ آلية fallback محسنة")

def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🔧 اختبار نهائي لإصلاحات MCPEDL scraper")
    print("=" * 60)
    
    results = {}
    
    # اختبار المستخرج الأساسي
    results['basic_scraper'] = test_mcpedl_scraper()
    
    # اختبار التكامل
    results['integration'] = test_integration_with_gui()
    
    # اختبار روابط متعددة
    results['multiple_urls'] = test_different_urls()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    # تقييم عام
    success_rate = sum(results.values()) / len(results)
    
    if success_rate >= 0.8:
        print("\n🎉 الإصلاحات نجحت! يمكن استخدام الأداة الآن")
        provide_usage_instructions()
    elif success_rate >= 0.5:
        print("\n⚠️ الإصلاحات نجحت جزئياً - قد تحتاج مراجعة إضافية")
        provide_usage_instructions()
    else:
        print("\n❌ الإصلاحات لم تنجح بشكل كافي - تحتاج مراجعة شاملة")
    
    print(f"\nمعدل النجاح: {success_rate*100:.1f}%")

if __name__ == "__main__":
    main()
