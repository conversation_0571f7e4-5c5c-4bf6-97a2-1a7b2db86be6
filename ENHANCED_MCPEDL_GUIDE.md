# 🚀 دليل المستخرج المحسن لـ MCPEDL

## 📋 نظرة عامة

المستخرج المحسن لـ MCPEDL هو نظام ذكي لاستخراج بيانات المودات من موقع MCPEDL.com مع فلترة احترافية للصور ومواقع التواصل الاجتماعي باستخدام الذكاء الاصطناعي.

## ✨ الميزات الجديدة

### 🖼️ فلترة ذكية للصور
- **فلترة أولية**: إزالة الصور الواضحة غير المرغوبة (أفاتار، تعليقات، إلخ)
- **فلترة Gemini AI**: تحليل ذكي للصور لاختيار الصور المتعلقة بالمود فقط
- **تجنب المودات المقترحة**: فلترة صور المودات الأخرى المعروضة في الصفحة
- **أولوية المصادر الموثوقة**: تفضيل صور ForgeCD و MCPEDL الرسمية

### 🌐 استخراج ذكي لمواقع التواصل الاجتماعي
- **فحص شامل للصفحة**: البحث في جميع أجزاء الصفحة عن روابط التواصل
- **تجنب روابط المشاركة**: فلترة روابط المشاركة الخاصة بـ MCPEDL
- **تحليل Gemini**: تحديد الروابط الخاصة بمطور المود فقط
- **استخراج من النص**: البحث عن إشارات اجتماعية في النص

## 🛠️ التثبيت والإعداد

### 1. المتطلبات
```bash
pip install requests beautifulsoup4 google-generativeai cloudscraper
```

### 2. إعداد مفتاح Gemini API
```python
# في ملف api_keys.json
{
  "GEMINI_API_KEY": "your-gemini-api-key-here"
}
```

أو كمتغير بيئة:
```bash
export GEMINI_API_KEY="your-gemini-api-key-here"
```

### 3. الحصول على مفتاح API
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. احفظ المفتاح بأمان

## 📖 كيفية الاستخدام

### 🔄 الاستخدام البسيط
```python
from enhanced_mcpedl_extractor import extract_mcpedl_mod_enhanced

# استخراج بيانات المود مع جميع الميزات الذكية
mod_data = extract_mcpedl_mod_enhanced("https://mcpedl.com/your-mod-url/")

if mod_data:
    print(f"اسم المود: {mod_data['name']}")
    print(f"عدد الصور: {len(mod_data['image_urls'])}")
    print(f"روابط التواصل: {mod_data['creator_social_channels']}")
```

### 🎯 الاستخدام المتقدم
```python
from enhanced_mcpedl_extractor import EnhancedMCPEDLExtractor

# إنشاء مستخرج مخصص
extractor = EnhancedMCPEDLExtractor(gemini_api_key="your-key")

# استخراج البيانات
mod_data = extractor.extract_mod_data("https://mcpedl.com/mod-url/")

# فحص جودة النتائج
if mod_data:
    images = mod_data['image_urls']
    social_links = mod_data['creator_social_channels']
    
    print(f"تم استخراج {len(images)} صورة")
    print(f"تم استخراج {len(social_links)} رابط تواصل")
```

## 🔍 تحليل النتائج

### 📊 فحص جودة الصور
```python
images = mod_data['image_urls']

# فحص المصادر الموثوقة
forgecdn_images = [img for img in images if 'forgecdn.net' in img]
mcpedl_images = [img for img in images if 'r2.mcpedl.com' in img]

print(f"صور ForgeCD: {len(forgecdn_images)}")
print(f"صور MCPEDL: {len(mcpedl_images)}")

# فحص الصور المشبوهة
suspicious_patterns = ['gravatar', 'avatar', 'profile', 'user']
suspicious_images = [img for img in images 
                    if any(pattern in img.lower() for pattern in suspicious_patterns)]

if suspicious_images:
    print(f"⚠️ صور مشبوهة: {len(suspicious_images)}")
else:
    print("✅ جميع الصور نظيفة")
```

### 🌐 فحص روابط التواصل الاجتماعي
```python
social_links = mod_data['creator_social_channels']

# فحص روابط المشاركة المشبوهة
share_patterns = ['sharer.php', 'intent/tweet', 'share?url=']
suspicious_social = [link for link in social_links 
                    if any(pattern in link.lower() for pattern in share_patterns)]

if suspicious_social:
    print(f"⚠️ روابط مشاركة مشبوهة: {len(suspicious_social)}")
else:
    print("✅ جميع الروابط صحيحة")
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
python test_enhanced_extractor.py
```

### نتائج الاختبار المتوقعة
- ✅ استخراج ناجح للبيانات الأساسية
- 🖼️ فلترة صحيحة للصور (3-8 صور عالية الجودة)
- 🌐 استخراج دقيق لروابط التواصل الاجتماعي
- ❌ عدم وجود صور مودات مقترحة
- ❌ عدم وجود روابط مشاركة MCPEDL

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في مفتاح Gemini API
```
⚠️ خطأ في تهيئة Gemini: Invalid API key
```
**الحل**: تأكد من صحة مفتاح API وأنه مفعل

#### 2. فشل في جلب الصفحة
```
❌ خطأ في جلب الصفحة: 403 Forbidden
```
**الحل**: تأكد من تثبيت cloudscraper أو استخدم VPN

#### 3. عدم استخراج صور
```
📋 تم العثور على 0 صورة محتملة
```
**الحل**: تحقق من صحة رابط MCPEDL وأن الصفحة تحتوي على صور

## 📈 مقارنة الأداء

| الميزة | المستخرج العادي | المستخرج المحسن |
|--------|-----------------|------------------|
| استخراج الصور | ✅ | ✅✅ |
| فلترة الصور | ❌ | ✅✅ |
| جودة الصور | متوسطة | عالية |
| روابط التواصل | أساسي | ذكي |
| تجنب المشاركة | ❌ | ✅ |
| سرعة الاستخراج | سريع | متوسط |

## 🚀 التطوير المستقبلي

### ميزات مخططة
- [ ] دعم مواقع أخرى (CurseForge، ModDB)
- [ ] فلترة أكثر ذكاءً للمحتوى
- [ ] تحسين سرعة المعالجة
- [ ] واجهة مستخدم محسنة
- [ ] تصدير النتائج بصيغ متعددة

### المساهمة
نرحب بالمساهمات! الرجاء:
1. فتح Issue لمناقشة التحسينات
2. إنشاء Pull Request مع الكود المحسن
3. إضافة اختبارات للميزات الجديدة

## 📞 الدعم

للحصول على المساعدة:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 Discord: [رابط الخادم]
- 🐛 تقرير الأخطاء: [GitHub Issues]

---

**ملاحظة**: هذا المستخرج مصمم للاستخدام التعليمي والشخصي. الرجاء احترام شروط استخدام موقع MCPEDL.
