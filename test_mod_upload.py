# -*- coding: utf-8 -*-
"""
اختبار رفع مود تجريبي
Test Mod Upload
"""

import os
import json
from firebase_config import firebase_manager
from supabase import create_client, Client

def test_firebase_upload():
    """اختبار رفع ملف إلى Firebase Storage"""
    print("🧪 اختبار رفع ملف إلى Firebase Storage")
    print("=" * 50)
    
    # تهيئة Firebase
    if not firebase_manager.auto_initialize():
        print("❌ فشل في تهيئة Firebase")
        return False
    
    # إنشاء ملف تجريبي
    test_content = b"This is a real mod file content for MCPEDL"
    test_filename = "awesome_mod.mcpack"
    
    # رفع الملف
    print("🔄 رفع ملف تجريبي...")
    url = firebase_manager.upload_mod_to_storage(test_content, test_filename)
    
    if url:
        print(f"✅ تم رفع الملف بنجاح!")
        print(f"🔗 رابط Firebase: {url}")
        return url
    else:
        print("❌ فشل رفع الملف")
        return None

def test_supabase_insert():
    """اختبار إدراج بيانات في Supabase"""
    print("\n🧪 اختبار إدراج بيانات في Supabase")
    print("=" * 50)
    
    # تحميل إعدادات Supabase
    try:
        with open('api_keys.json', 'r', encoding='utf-8') as f:
            api_keys = json.load(f)
        
        supabase_url = api_keys.get('supabase_url')
        supabase_key = api_keys.get('SUPABASE_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ إعدادات Supabase غير مكتملة")
            return False
        
        # إنشاء عميل Supabase
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # بيانات تجريبية للمود (حقول متوافقة مع Supabase)
        test_mod_data = {
            "name": "Test Mod",
            "description": "This is a test mod for Firebase and Supabase integration",
            "category": "addon",
            "download_url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/test_mod.mcpack",
            "image_urls": ["https://storage.googleapis.com/download-e33a2.firebasestorage.app/images/test_image.jpg"],
            "version": "1.0.0",
            "size": "1.2 MB",
            "downloads": 0,
            "likes": 0,
            "clicks": 0,
            "description_ar": "هذا مود تجريبي لاختبار التكامل مع Firebase و Supabase",
            "is_featured": False,
            "is_popular": False,
            "is_free_addon": True
        }
        
        print("🔄 إدراج بيانات المود في Supabase...")
        response = supabase.table('mods').insert(test_mod_data).execute()
        
        if response.data:
            print("✅ تم إدراج المود في Supabase بنجاح!")
            print(f"📊 معرف المود: {response.data[0].get('id')}")
            return True
        else:
            print("❌ فشل إدراج المود في Supabase")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Supabase: {e}")
        return False

def test_complete_workflow():
    """اختبار سير العمل الكامل"""
    print("\n🚀 اختبار سير العمل الكامل")
    print("=" * 50)
    
    # 1. رفع ملف إلى Firebase
    firebase_url = test_firebase_upload()
    if not firebase_url:
        return False
    
    # 2. إدراج بيانات في Supabase
    if test_supabase_insert():
        print("\n✅ اكتمل اختبار سير العمل بنجاح!")
        print("🎉 Firebase Storage + Supabase Database يعملان بشكل صحيح")
        return True
    else:
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لرفع المودات")
    print("=" * 60)
    
    success = test_complete_workflow()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام جاهز لرفع المودات الحقيقية")
    else:
        print("\n❌ فشل في بعض الاختبارات")
        print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
