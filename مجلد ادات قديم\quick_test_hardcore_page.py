# -*- coding: utf-8 -*-
"""
اختبار سريع لصفحة Hardcore Starter Houses
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """اختبار سريع للصفحة"""
    print("🧪 اختبار سريع لصفحة Hardcore Starter Houses")
    print("=" * 50)
    
    # HTML محاكي للصفحة الحقيقية (بناءً على ما رأيناه)
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Hardcore Starter House - Bedrock Edition | Minecraft PE Maps</title>
    </head>
    <body>
        <h1>Hardcore Starter House - Bedrock Edition</h1>
        
        <!-- صور المستخدمين في التعليقات -->
        <img src="https://r2.mcpedl.com/users/386393/db5be2525b6a76f872241066d61f6ed5.png" alt="User Avatar">
        <img src="https://secure.gravatar.com/avatar/eb191aa7c0c32503faafeed581afb077?s=96&d=mm&r=g" alt="Gravatar">
        
        <!-- صور "You may also like" -->
        <img src="/img/empty.png" alt="Empty">
        <img src="/img/empty.png" alt="Empty">
        
        <!-- شعار الموقع -->
        <img src="/_nuxt/img/logo.e39b598.png" alt="MCPE DL">
        
        <!-- محتوى الصفحة -->
        <div class="downloads">
            <h3>Downloads</h3>
            <ul>
                <li>Map 17 - Sweet Home.mcworld (724.6 KB)</li>
                <li>Map 16 - Cozy Crib.mcworld (1.11 MB)</li>
                <li>Map 15 - Igloo.mcworld (408.88 KB)</li>
            </ul>
        </div>
        
        <!-- قسم "You may also like" -->
        <div class="related-posts">
            <h3>You may also like...</h3>
            <img src="/img/empty.png" alt="Related Mod">
        </div>
        
        <!-- التعليقات -->
        <div class="comments">
            <img src="https://r2.mcpedl.com/users/739634/19044e233c49d0bf9a81af1803acfbc6.png" alt="Comment User">
        </div>
    </body>
    </html>
    """
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        from bs4 import BeautifulSoup
        
        print("📋 تحليل HTML المحاكي...")
        
        # تحليل HTML
        soup = BeautifulSoup(test_html, 'html.parser')
        
        # فحص جميع الصور
        all_images = soup.find_all('img')
        print(f"📊 إجمالي وسوم <img>: {len(all_images)}")
        
        print("\n🔍 تحليل كل صورة:")
        for i, img in enumerate(all_images, 1):
            src = img.get('src', 'لا يوجد src')
            alt = img.get('alt', 'لا يوجد alt')
            print(f"   [{i}] {src}")
            print(f"       alt: {alt}")
            
            # تحليل النوع
            if '/img/empty.png' in src:
                print(f"       🔍 نوع: صورة فارغة (يجب رفضها)")
            elif 'gravatar.com' in src:
                print(f"       🔍 نوع: صورة مستخدم gravatar (يجب رفضها)")
            elif 'r2.mcpedl.com/users' in src:
                print(f"       🔍 نوع: صورة مستخدم mcpedl (يجب رفضها)")
            elif '_nuxt' in src:
                print(f"       🔍 نوع: صورة موقع (يجب رفضها)")
            elif 'media.forgecdn.net' in src:
                print(f"       🔍 نوع: صورة مود حقيقية (يجب قبولها)")
            else:
                print(f"       🔍 نوع: أخرى")
        
        # اختبار المستخرج
        print("\n🚀 اختبار المستخرج المحسن:")
        extractor = MCPEDLExtractorFixed()
        
        # اختبار فلترة كل صورة
        print("\n🔍 اختبار فلترة الصور:")
        valid_images = []
        
        for i, img in enumerate(all_images, 1):
            src = img.get('src', '')
            if src:
                # تحويل الروابط النسبية
                if not src.startswith('http'):
                    if src.startswith('/'):
                        src = 'https://mcpedl.com' + src
                    else:
                        src = 'https://mcpedl.com/' + src
                
                # اختبار الفلترة
                is_valid = extractor.is_definitely_mod_image(src)
                status = "✅ قُبلت" if is_valid else "❌ رُفضت"
                
                print(f"   [{i}] {status}: {src[:60]}...")
                
                if is_valid:
                    valid_images.append(src)
        
        print(f"\n📊 نتائج الفلترة:")
        print(f"   🖼️ صور صالحة: {len(valid_images)}")
        print(f"   ❌ صور مرفوضة: {len(all_images) - len(valid_images)}")
        
        if valid_images:
            print("   📋 الصور الصالحة:")
            for i, img in enumerate(valid_images, 1):
                print(f"      [{i}] {img}")
        else:
            print("   💡 لا توجد صور صالحة - هذا متوقع لهذا النوع من الصفحات")
        
        # اختبار استخراج كامل
        print("\n🔄 اختبار استخراج كامل:")
        mod_data = extractor.extract_mod_data(test_html, "https://mcpedl.com/hardcore-starter-houses/", generate_ai_descriptions=False)
        
        if mod_data:
            images = mod_data.get('image_urls', [])
            print(f"✅ تم الاستخراج: {len(images)} صورة")
            
            if images:
                for i, img in enumerate(images, 1):
                    print(f"   [{i}] {img}")
            else:
                print("   💡 لا توجد صور - هذا طبيعي لصفحات الخرائط")
        else:
            print("❌ فشل الاستخراج")
        
        # الخلاصة
        print(f"\n🎯 الخلاصة:")
        print(f"   - الصفحة تحتوي على {len(all_images)} صورة")
        print(f"   - جميعها صور موقع/مستخدمين (ليست صور مود)")
        print(f"   - المستخرج يرفضها بشكل صحيح")
        print(f"   - هذا طبيعي لصفحات الخرائط (.mcworld)")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_real_mod_page():
    """اختبار صفحة مود حقيقية مع صور"""
    print("\n" + "=" * 50)
    print("🧪 اختبار صفحة مود حقيقية مع صور")
    print("=" * 50)
    
    # HTML محاكي لصفحة مود تحتوي على صور حقيقية
    mod_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Dragon Mounts Mod | MCPEDL</title>
        <meta property="og:image" content="https://media.forgecdn.net/attachments/1180/464/dragon-main.png">
    </head>
    <body>
        <article>
            <h1>Dragon Mounts: Community Edition</h1>
            <div class="post-content">
                <p>This mod adds amazing dragons to your world!</p>
                <img src="https://media.forgecdn.net/attachments/1113/871/dragon-screenshot1.jpg" alt="Dragon Screenshot 1">
                <img src="https://media.forgecdn.net/attachments/1113/870/dragon-screenshot2.jpg" alt="Dragon Screenshot 2">
                <img src="https://mcpedl.com/wp-content/uploads/2024/dragon-feature.png" alt="Dragon Feature">
            </div>
        </article>
        
        <!-- صور يجب رفضها -->
        <div class="related-posts">
            <h3>You may also like</h3>
            <img src="/img/empty.png" alt="Related">
        </div>
        
        <div class="comments">
            <img src="https://r2.mcpedl.com/users/123/avatar.png" alt="User">
        </div>
    </body>
    </html>
    """
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        print("🔄 اختبار استخراج من صفحة مود حقيقية...")
        mod_data = extractor.extract_mod_data(mod_html, "https://mcpedl.com/dragon-mounts/", generate_ai_descriptions=False)
        
        if mod_data:
            images = mod_data.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            if images:
                print("   📋 الصور المستخرجة:")
                for i, img in enumerate(images, 1):
                    print(f"      [{i}] {img}")
                    
                    if 'media.forgecdn.net' in img:
                        print(f"           ✅ صورة مود حقيقية")
                    elif 'mcpedl.com/wp-content' in img:
                        print(f"           ✅ صورة محتوى")
                    else:
                        print(f"           ⚠️ صورة أخرى")
                
                # تحليل الجودة
                forgecdn_count = sum(1 for img in images if 'media.forgecdn.net' in img)
                print(f"\n📊 تحليل الجودة:")
                print(f"   🔥 صور forgecdn: {forgecdn_count}")
                print(f"   📊 إجمالي: {len(images)}")
                
                if forgecdn_count >= 2:
                    print("   🎉 ممتاز! تم استخراج صور مود حقيقية")
                    return True
                else:
                    print("   ⚠️ قليل من صور المود الحقيقية")
                    return False
            else:
                print("   ❌ لم يتم استخراج صور")
                return False
        else:
            print("❌ فشل الاستخراج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع لفهم مشكلة استخراج الصور")
    print("=" * 60)
    
    # اختبار صفحة بدون صور
    success1 = quick_test()
    
    # اختبار صفحة مع صور
    success2 = test_real_mod_page()
    
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print(f"   🎯 صفحة بدون صور: {'✅ نجح' if success1 else '❌ فشل'}")
    print(f"   🎯 صفحة مع صور: {'✅ نجح' if success2 else '❌ فشل'}")
    
    if success1 and success2:
        print("\n🎉 المستخرج يعمل بشكل صحيح!")
        print("💡 المشكلة ليست في الكود، بل في أن بعض صفحات MCPEDL لا تحتوي على صور")
    else:
        print("\n⚠️ هناك مشكلة في المستخرج تحتاج مراجعة")

if __name__ == "__main__":
    main()
