# دليل تثبيت ميزة استخراج MCPEDL

## نظرة عامة

هذا الدليل يوضح كيفية تثبيت وإعداد ميزة استخراج المودات من موقع mcpedl.com في أداة نشر المودات.

## المتطلبات المسبقة

### 1. Python والمكتبات الأساسية
تأكد من وجود Python 3.8+ والمكتبات التالية:

```bash
pip install requests beautifulsoup4 lxml
```

### 2. أداة نشر المودات الأساسية
تأكد من أن أداة نشر المودات تعمل بشكل صحيح قبل إضافة الميزة الجديدة.

## خطوات التثبيت

### الخطوة 1: إضافة ملف الوحدة
1. انسخ ملف `mcpedl_scraper_module.py` إلى مجلد أداة نشر المودات
2. تأكد من أن الملف في نفس مجلد `mod_processor.py`

### الخطوة 2: التحقق من التحديثات
تأكد من أن ملف `mod_processor.py` يحتوي على التحديثات التالية:

#### أ. الاستيرادات الجديدة
```python
from typing import Dict, List, Optional, Any

# --- NEW: MCPEDL Scraper Import ---
try:
    from mcpedl_scraper_module import scrape_mcpedl_mod, MCPEDLScraper
    MCPEDL_SCRAPER_AVAILABLE = True
    print("MCPEDL Scraper module loaded successfully.")
except ImportError:
    MCPEDL_SCRAPER_AVAILABLE = False
    print("Warning: MCPEDL Scraper module not found. MCPEDL extraction features will be disabled.")
```

#### ب. الدوال الجديدة
- `handle_mcpedl_extraction()`
- `mcpedl_extraction_task()`
- `populate_fields_from_mcpedl_data()`
- `map_mcpedl_category_to_app_category()`
- `populate_social_channels_from_mcpedl()`
- `clear_mcpedl_fields()`
- `auto_populate_field()`
- `auto_populate_text_widget()`

#### ج. واجهة المستخدم الجديدة
قسم "4.5. استخراج من MCPEDL" في الواجهة الرئيسية.

### الخطوة 3: اختبار التثبيت
1. شغل ملف الاختبار:
```bash
python test_mcpedl_scraper.py
```

2. إذا نجح الاختبار، شغل الأداة الرئيسية:
```bash
python mod_processor.py
```

3. ابحث عن قسم "4.5. استخراج من MCPEDL" في الواجهة

## التحقق من التثبيت

### علامات التثبيت الناجح:
- ✅ ظهور قسم "استخراج من MCPEDL" في الواجهة
- ✅ رسالة "MCPEDL Scraper module loaded successfully" في وحدة التحكم
- ✅ إمكانية إدخال رابط MCPEDL والضغط على زر الاستخراج

### علامات فشل التثبيت:
- ❌ عدم ظهور قسم MCPEDL في الواجهة
- ❌ رسالة "MCPEDL Scraper module not found" في وحدة التحكم
- ❌ أخطاء في الاستيراد عند تشغيل الأداة

## استكشاف الأخطاء

### خطأ: "ModuleNotFoundError: No module named 'mcpedl_scraper_module'"
**الحل:**
1. تأكد من وجود ملف `mcpedl_scraper_module.py` في المجلد الصحيح
2. تأكد من أن اسم الملف صحيح (بدون أخطاء إملائية)
3. تأكد من أن الملف ليس فارغاً أو تالفاً

### خطأ: "ModuleNotFoundError: No module named 'requests'"
**الحل:**
```bash
pip install requests beautifulsoup4 lxml
```

### خطأ: "AttributeError" في الواجهة
**الحل:**
1. تأكد من تحديث ملف `mod_processor.py` بالكامل
2. تأكد من إضافة جميع الدوال المطلوبة
3. أعد تشغيل الأداة

### قسم MCPEDL لا يظهر في الواجهة
**الحل:**
1. تحقق من رسائل وحدة التحكم
2. تأكد من أن `MCPEDL_SCRAPER_AVAILABLE = True`
3. تأكد من إضافة كود الواجهة في المكان الصحيح

## اختبار الميزة

### اختبار أساسي:
1. شغل الأداة
2. انتقل إلى قسم "4.5. استخراج من MCPEDL"
3. أدخل رابط مود من MCPEDL: `https://mcpedl.com/dragon-mounts-v1-3-25/`
4. اضغط "استخراج البيانات من MCPEDL"
5. تحقق من ملء الحقول تلقائياً

### اختبار متقدم:
1. جرب روابط مودات مختلفة
2. تحقق من استخراج الصور
3. تحقق من ملء قنوات التواصل الاجتماعي
4. تحقق من تحويل الفئات

## الصيانة والتحديث

### تحديث محددات CSS:
إذا تغير هيكل موقع MCPEDL، قد تحتاج لتحديث المحددات في `mcpedl_scraper_module.py`:

```python
self.selectors = {
    'title': 'h1.entry-title, h1.post-title, .post-header h1, h1',
    'description': '.entry-content p, .post-content p, .content p, article p',
    # ... باقي المحددات
}
```

### إضافة ميزات جديدة:
1. أضف دوال جديدة في `mcpedl_scraper_module.py`
2. حدث `populate_fields_from_mcpedl_data()` لدعم الحقول الجديدة
3. أضف عناصر واجهة جديدة حسب الحاجة

## الدعم

### في حالة وجود مشاكل:
1. شغل `test_mcpedl_scraper.py` للتشخيص
2. تحقق من سجل الأخطاء في الأداة
3. تأكد من تحديث جميع الملفات
4. تحقق من الاتصال بالإنترنت

### ملفات مهمة للمراجعة:
- `mcpedl_scraper_module.py` - الوحدة الرئيسية
- `mod_processor.py` - الأداة الرئيسية المحدثة
- `test_mcpedl_scraper.py` - ملف الاختبار
- `MCPEDL_FEATURE_README.md` - دليل الاستخدام

## الخلاصة

بعد اتباع هذه الخطوات، ستكون ميزة استخراج MCPEDL جاهزة للاستخدام. هذه الميزة ستوفر الكثير من الوقت والجهد في عملية نشر المودات من خلال الاستخراج التلقائي للبيانات.

تأكد من اختبار الميزة جيداً قبل الاستخدام الفعلي، وراجع دليل الاستخدام للحصول على أفضل النتائج! 🚀
