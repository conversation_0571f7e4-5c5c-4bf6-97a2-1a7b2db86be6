{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "drone:target", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"despawn": {"minecraft:instant_despawn": {}}}, "components": {"minecraft:type_family": {"family": ["target", "bot", "inanimate"]}, "minecraft:collision_box": {"width": 0.0, "height": 0.0}, "minecraft:damage_sensor": {"triggers": {"cause": "all", "deals_damage": false}}, "minecraft:knockback_resistance": {"value": 1, "max": 1}, "minecraft:timer": {"looping": false, "time": [2, 2], "time_down_event": {"event": "despawn"}}, "minecraft:environment_sensor": {"triggers": [{"filters": {}, "event": "follow_entity"}]}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}}, "events": {"follow_entity": {"queue_command": {"command": "execute at @e[c=1,r=0.6,type=!drone:target,type=!item,type=!drone:laser] run tp ~ ~0.6 ~"}}, "despawn": {"add": {"component_groups": "despawn"}}}}}