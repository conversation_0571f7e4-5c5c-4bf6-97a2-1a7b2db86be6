# -*- coding: utf-8 -*-
"""
تعديلات محددة لتطبيقها على mod_processor_broken_final.py
هذا الملف يحتوي على التعديلات الدقيقة التي يجب تطبيقها

المطور: MiniMax Agent
تاريخ التحديث: 2025-06-23
"""

# ===== التعديل 1: استبدال دالة generate_description_task =====

# ابحث عن هذا في الملف الأصلي:
"""
def generate_description_task(mod_name, mod_category, scraped_text, manual_features):
    \"\"\"
    Calls Gemini API to generate a SIMPLE, developer-style description
    after intelligently filtering the source text.
    \"\"\"
"""

# واستبدله بهذا:
def generate_simple_description_task(mod_name, mod_category, scraped_text, manual_features):
    """
    توليد وصف بسيط ومباشر للمود بدون تعقيدات
    """
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    # تنظيف السياق وتبسيطه
    clean_context = ""

    if manual_features:
        update_status("استخدام الميزات المدخلة يدوياً كسياق أساسي.")
        clean_context = manual_features
    elif scraped_text:
        update_status("تبسيط النص المستخرج لإنشاء سياق نظيف...")
        
        # استخراج الميزات الأساسية فقط
        lines = scraped_text.split('\n')
        feature_lines = []
        
        for line in lines:
            line_clean = line.strip()
            if line_clean and len(line_clean) > 10 and len(line_clean) < 200:
                # تنظيف النقاط والرموز
                clean_line = re.sub(r'^[\*\-•]\s*', '', line_clean)
                if clean_line and not any(word in clean_line.lower() for word in ['download', 'install', 'requirement', 'how to']):
                    feature_lines.append(clean_line)
                    
        if feature_lines:
            clean_context = "\n".join(feature_lines[:5])  # أول 5 ميزات فقط
        else:
            clean_context = scraped_text[:500] if scraped_text else ""
    else:
        update_status("⚠️ لا يوجد سياق للوصف.")
        return

    # برومت مبسط جداً لتوليد وصف قصير
    prompt = f"""
    أنت مطور مودات ماين كرافت. اكتب وصف بسيط جداً للمود.

    اسم المود: {mod_name}
    نوع المود: {mod_category}
    
    الميزات الأساسية:
    {clean_context}

    التعليمات:
    - اكتب وصف من جملة أو جملتين فقط
    - لا تكتب مقال أو فقرات متعددة
    - لا تضيف عناوين أو مسافات كبيرة
    - لا تذكر التحميل أو التثبيت
    - اكتب بطريقة مباشرة وبسيطة
    
    مثال على الأسلوب المطلوب:
    "يضيف هذا المود أدوات جديدة وطعام مميز لتحسين تجربة اللعب."

    اكتب الوصف فقط بدون أي إضافات:
    """

    retries = 0
    success = False
    generated_desc = ""
    
    while retries < 3 and not success:
        if not GEMINI_CLIENT_OK: 
            break
        try:
            update_status(f"📤 إرسال طلب لتوليد وصف بسيط...")
            full_response = smart_gemini_request(prompt)
            if not full_response: 
                raise Exception("لا يوجد رد من Gemini.")
            
            # تنظيف الرد من أي علامات أو نصوص إضافية
            generated_desc = full_response.strip()
            
            # إزالة أي علامات تنسيق
            generated_desc = re.sub(r'\[.*?\]', '', generated_desc)
            generated_desc = re.sub(r'\*\*.*?\*\*', '', generated_desc) 
            generated_desc = generated_desc.strip()
            
            # التأكد من أن الوصف بسيط
            if len(generated_desc) > 300:
                # قص الوصف إذا كان طويلاً
                sentences = generated_desc.split('.')
                if len(sentences) > 2:
                    generated_desc = '. '.join(sentences[:2]) + '.'
                    
            success = True
            
        except Exception as e:
            update_status(f"⚠️ خطأ في توليد الوصف البسيط: {e}")
            retries += 1
            break
    
    if success and generated_desc:
        if 'window' in globals() and window.winfo_exists():
            window.after(0, auto_populate_text_widget, publish_desc_text, generated_desc)
            update_status("✅ تم تحديث حقل الوصف بوصف بسيط ومباشر.")
    else:
        update_status("❌ فشل في توليد الوصف.")
    
    # إعادة تفعيل الزر
    if 'window' in globals() and window.winfo_exists():
        window.after(0, lambda: generate_desc_button.config(state=tk.NORMAL) if 'generate_desc_button' in globals() and generate_desc_button.winfo_exists() else None)


# ===== التعديل 2: استبدال دالة generate_arabic_description_task =====

# ابحث عن هذا في الملف الأصلي:
"""
def generate_arabic_description_task(mod_name, mod_category, scraped_text, manual_features):
    \"\"\"Calls Gemini API to generate a SIMPLE, developer-style Arabic description.\"\"\"
"""

# واستبدله بهذا:
def generate_simple_arabic_description_task(mod_name, mod_category, scraped_text, manual_features):
    """
    توليد وصف عربي بسيط ومباشر للمود
    """
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    # تحضير السياق
    context = ""
    if manual_features:
        context = manual_features
    elif scraped_text:
        context = scraped_text[:500]  # أول 500 حرف فقط
    else:
        update_status("⚠️ لا يوجد سياق للوصف العربي.")
        return

    # برومت مبسط للوصف العربي
    prompt = f"""
    أنت مطور مودات ماين كرافت عربي. اكتب وصف بسيط جداً للمود باللغة العربية.

    اسم المود: {mod_name}
    نوع المود: {mod_category}
    
    معلومات المود:
    {context}

    التعليمات:
    - اكتب جملة أو جملتين فقط باللغة العربية
    - لا تكتب مقال أو فقرات
    - لا تضيف عناوين أو علامات تنسيق
    - اكتب بأسلوب بسيط ومباشر
    - لا تذكر التحميل أو التثبيت
    
    مثال:
    "يضيف هذا المود أسلحة جديدة ووحوش مثيرة لجعل المغامرة أكثر تشويقاً."

    اكتب الوصف العربي فقط:
    """

    retries = 0
    success = False
    generated_desc = ""
    
    while retries < 3 and not success:
        if not GEMINI_CLIENT_OK:
            break
        try:
            update_status(f"📤 إرسال طلب لتوليد وصف عربي بسيط...")
            full_response = smart_gemini_request(prompt)
            if not full_response:
                raise Exception("لا يوجد رد من Gemini.")
            
            # تنظيف الرد
            generated_desc = full_response.strip()
            generated_desc = re.sub(r'\[.*?\]', '', generated_desc)
            generated_desc = re.sub(r'\*\*.*?\*\*', '', generated_desc)
            generated_desc = generated_desc.strip()
            
            # التأكد من أن الوصف بسيط
            if len(generated_desc) > 250:
                sentences = generated_desc.split('.')
                if len(sentences) > 2:
                    generated_desc = '. '.join(sentences[:2]) + '.'
                    
            success = True
            
        except Exception as e:
            update_status(f"⚠️ خطأ في توليد الوصف العربي: {e}")
            retries += 1
            break
    
    if success and generated_desc:
        if 'window' in globals() and window.winfo_exists():
            window.after(0, auto_populate_text_widget, publish_arabic_desc_text, generated_desc)
            update_status("✅ تم تحديث حقل الوصف العربي بوصف بسيط.")
    else:
        update_status("❌ فشل في توليد الوصف العربي.")
    
    # إعادة تفعيل الزر
    if 'window' in globals() and window.winfo_exists():
        window.after(0, lambda: generate_arabic_desc_button.config(state=tk.NORMAL) if 'generate_arabic_desc_button' in globals() and generate_arabic_desc_button.winfo_exists() else None)


# ===== التعديل 3: استبدال دالة generate_telegram_descriptions_task =====

# ابحث عن دالة generate_telegram_descriptions_task واستبدلها كاملة بهذا:
def generate_simple_telegram_descriptions_task(mod_data):
    """
    إنشاء أوصاف تيليجرام بسيطة بدون كلمات إنجليزية زائدة
    """
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء أوصاف التيليجرام")
        return None

    mod_name = mod_data.get('name', '')
    mod_category = mod_data.get('category', '')
    existing_description = mod_data.get('description', '')

    if not mod_name:
        update_status("❌ اسم المود مطلوب لإنشاء أوصاف التيليجرام")
        return None

    update_status(f"🔄 بدء إنشاء أوصاف التيليجرام للمود: {mod_name}")

    # برومت محسن ومبسط
    prompt = f"""
    أنت كاتب محتوى ماين كرافت. اكتب وصفين بسيطين لنفس المود: عربي وإنجليزي.

    اسم المود: {mod_name}
    نوع المود: {mod_category}
    
    معلومات المود:
    {existing_description[:300] if existing_description else "لا توجد معلومات إضافية"}

    التعليمات:
    - اكتب وصف بسيط من 2-3 جمل لكل لغة
    - لا تكتب مقالات طويلة أو فقرات متعددة
    - لا تضيف عناوين أو مسافات كبيرة
    - لا تذكر أرقام إصدارات أو متطلبات تقنية
    - اكتب بأسلوب طبيعي ومباشر

    اكتب الوصفين بهذا التنسيق الدقيق:

    ENGLISH:
    [الوصف الإنجليزي البسيط هنا]

    ARABIC:
    [الوصف العربي البسيط هنا]
    """

    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            update_status(f"📤 إرسال طلب إنشاء أوصاف التيليجرام (المحاولة {attempt + 1})...")
            response = smart_gemini_request(prompt)

            if response:
                response_text = response.strip()
                
                # استخراج الوصف الإنجليزي والعربي
                english_desc = ""
                arabic_desc = ""
                
                # البحث عن النمط ENGLISH: و ARABIC:
                english_match = re.search(r'ENGLISH:\s*\n?(.*?)(?=ARABIC:|$)', response_text, re.DOTALL | re.IGNORECASE)
                arabic_match = re.search(r'ARABIC:\s*\n?(.*?)$', response_text, re.DOTALL | re.IGNORECASE)
                
                if english_match:
                    english_desc = english_match.group(1).strip()
                    # تنظيف من أي علامات أو نصوص زائدة
                    english_desc = re.sub(r'\[.*?\]', '', english_desc)
                    english_desc = english_desc.strip()
                
                if arabic_match:
                    arabic_desc = arabic_match.group(1).strip()
                    # تنظيف من أي علامات أو نصوص زائدة
                    arabic_desc = re.sub(r'\[.*?\]', '', arabic_desc)
                    arabic_desc = arabic_desc.strip()
                
                # إذا لم تنجح الطريقة الأولى، جرب تقسيم النص
                if not english_desc or not arabic_desc:
                    lines = response_text.split('\n')
                    english_lines = []
                    arabic_lines = []
                    current_section = None
                    
                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue
                        if 'english' in line.lower():
                            current_section = 'english'
                            continue
                        elif 'arabic' in line.lower() or re.search(r'[\u0600-\u06FF]', line):
                            current_section = 'arabic'
                            if re.search(r'[\u0600-\u06FF]', line):
                                arabic_lines.append(line)
                            continue
                        
                        if current_section == 'english' and re.search(r'[a-zA-Z]', line):
                            english_lines.append(line)
                        elif current_section == 'arabic' or re.search(r'[\u0600-\u06FF]', line):
                            arabic_lines.append(line)
                    
                    if english_lines and not english_desc:
                        english_desc = ' '.join(english_lines)
                    if arabic_lines and not arabic_desc:
                        arabic_desc = ' '.join(arabic_lines)

                # التحقق من وجود المحتوى
                if english_desc and arabic_desc:
                    update_status("✅ تم إنشاء أوصاف التيليجرام بنجاح")
                    
                    # تحديث واجهة المستخدم
                    if 'window' in globals() and window.winfo_exists():
                        window.after(0, auto_populate_text_widget, telegram_desc_en_text, english_desc)
                        window.after(0, auto_populate_text_widget, telegram_desc_ar_text, arabic_desc)
                    
                    return {
                        'telegram_description_en': english_desc,
                        'telegram_description_ar': arabic_desc
                    }
                else:
                    update_status(f"⚠️ فشل في استخراج الأوصاف من المحاولة {attempt + 1}")
                    
        except Exception as e:
            update_status(f"❌ خطأ في المحاولة {attempt + 1}: {str(e)}")
            
    update_status("❌ فشل في إنشاء أوصاف التيليجرام بعد جميع المحاولات")
    return None


# ===== التعديل 4: إضافة دالة تنظيف الوصف =====

# أضف هذه الدالة في أي مكان في الملف:
def clean_basic_description(description):
    """
    تنظيف الوصف من أي كلمات إنجليزية زائدة أو علامات غير مرغوب فيها
    """
    if not description:
        return ""
    
    cleaned = description
    
    # إزالة العلامات الإنجليزية الزائدة
    patterns_to_remove = [
        r'\[ENGLISH_DESCRIPTION\]',
        r'\[/ENGLISH_DESCRIPTION\]',
        r'\[ARABIC_DESCRIPTION\]',
        r'\[/ARABIC_DESCRIPTION\]',
        r'\[DESCRIPTION\]',
        r'\[/DESCRIPTION\]',
        r'ENGLISH:',
        r'ARABIC:',
    ]
    
    for pattern in patterns_to_remove:
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
    
    # إزالة المسافات الزائدة والأسطر الفارغة المتعددة
    cleaned = re.sub(r'\n\s*\n', '\n', cleaned)
    cleaned = re.sub(r'^\s+|\s+$', '', cleaned, flags=re.MULTILINE)
    cleaned = cleaned.strip()
    
    return cleaned


# ===== التعديل 5: تحديث استدعاءات الدوال =====

# ابحث عن هذه الاستدعاءات واستبدلها:

# 5.1 في handle_generate_description:
# استبدل:
# run_in_thread(generate_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
# run_in_thread(generate_simple_description_task, mod_name, mod_category, scraped_context, manual_features_text)

# 5.2 في handle_generate_arabic_description:
# استبدل:
# run_in_thread(generate_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)
# بـ:
# run_in_thread(generate_simple_arabic_description_task, mod_name, mod_category, scraped_context, manual_features_text)

# 5.3 في handle_generate_telegram_descriptions:
# استبدل:
# telegram_descriptions = generate_telegram_descriptions_task(mod_data)
# بـ:
# telegram_descriptions = generate_simple_telegram_descriptions_task(mod_data)

# 5.4 في أي مكان آخر يتم استدعاء الدوال القديمة:
# استبدل جميع الاستدعاءات القديمة بالجديدة


# ===== التعديل 6: إضافة تنظيف الوصف عند العرض =====

# ابحث عن هذا السطر:
# auto_populate_text_widget(publish_desc_text, mod_data['description'])

# واستبدله بـ:
# cleaned_description = clean_basic_description(mod_data['description'])
# auto_populate_text_widget(publish_desc_text, cleaned_description)


# ===== التعديل 7: إضافة مستخرج الصور المحسن =====

# في بداية الملف، أضف هذا الاستيراد:
"""
# استيراد المستخرج المحسن
try:
    from enhanced_image_extractor import EnhancedImageExtractor, extract_main_mod_image_simple
    ENHANCED_IMAGE_EXTRACTOR_AVAILABLE = True
    print("Enhanced Image Extractor module loaded successfully.")
except ImportError:
    ENHANCED_IMAGE_EXTRACTOR_AVAILABLE = False
    print("Warning: Enhanced Image Extractor module not found.")
"""

# ===== ملخص التعديلات المطلوبة =====
"""
1. استبدال generate_description_task بـ generate_simple_description_task
2. استبدال generate_arabic_description_task بـ generate_simple_arabic_description_task  
3. استبدال generate_telegram_descriptions_task بـ generate_simple_telegram_descriptions_task
4. إضافة دالة clean_basic_description
5. تحديث جميع استدعاءات الدوال القديمة
6. إضافة تنظيف الوصف عند العرض
7. إضافة استيراد مستخرج الصور المحسن
8. تحديث استدعاءات استخراج الصور لاستخدام المستخرج الجديد
"""

print("تم تحديد جميع التعديلات المطلوبة. راجع التعليقات أعلاه لتطبيق التغييرات.")
