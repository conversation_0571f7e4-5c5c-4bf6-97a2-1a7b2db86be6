# -*- coding: utf-8 -*-
"""
اختبار فلتر الصور باستخدام Gemini AI
"""

import json
from gemini_image_filter import <PERSON><PERSON><PERSON><PERSON>ilter

def test_gemini_filter():
    """اختبار فلتر الصور"""
    print("🧪 اختبار فلتر الصور باستخدام Gemini...")
    
    # قراءة مفتاح API من ملف الإعدادات
    try:
        # محاولة قراءة من api_keys.json أولاً
        try:
            with open('api_keys.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            config_file = 'api_keys.json'
        except FileNotFoundError:
            # إذا لم يوجد، جرب config.json
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            config_file = 'config.json'

        # الحصول على أول مفتاح Gemini
        gemini_keys = config.get('gemini_api_keys', [])
        if not gemini_keys:
            print(f"❌ لا توجد مفاتيح Gemini في ملف {config_file}")
            return

        api_key = gemini_keys[0]
        print(f"✅ تم العثور على مفتاح API من {config_file}: {api_key[:10]}...")
        print(f"🔍 طول المفتاح: {len(api_key)} حرف")
        print(f"🔍 المفتاح فارغ؟ {api_key.strip() == ''}")
        print(f"🔍 نوع المفتاح: {type(api_key)}")

    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return
    
    # إنشاء فلتر الصور
    filter_instance = GeminiImageFilter(api_key)
    
    if not filter_instance.model:
        print("❌ فشل في تهيئة Gemini - الاختبار متوقف")
        return
    
    # صور تجريبية (صور حقيقية من MCPEDL)
    test_images = [
        "https://media.forgecdn.net/attachments/1244/313/20250703_131535-jpg.jpg",
        "https://media.forgecdn.net/attachments/1130/597/20220813111200_1-520x245-png.png",
        "https://mcpedl.com/img/empty.png"  # صورة فارغة (يجب رفضها)
    ]
    
    # معلومات المود التجريبية
    mod_name = "Squid Game: Sky Squid Game Recreation Map"
    mod_description = "A Minecraft map recreation of the popular Squid Game series"
    
    print(f"🔍 اختبار فلترة {len(test_images)} صور...")
    
    # تشغيل الفلتر
    try:
        filtered_images = filter_instance.filter_mod_images(test_images, mod_name, mod_description)
        
        print(f"✅ النتيجة: {len(filtered_images)} صور مفلترة من أصل {len(test_images)}")
        for i, img in enumerate(filtered_images, 1):
            print(f"   {i}. {img}")
            
        if len(filtered_images) > 0:
            print("✅ فلتر Gemini يعمل بشكل صحيح!")
        else:
            print("⚠️ لم يتم اختيار أي صور - قد تكون هناك مشكلة")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الفلتر: {e}")

if __name__ == "__main__":
    test_gemini_filter()
