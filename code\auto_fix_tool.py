#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة التطبيق التلقائي لإصلاحات مودات ماين كرافت
تقوم بتطبيق الإصلاحات تلقائياً على الملف الأصلي

المطور: MiniMax Agent
تاريخ الإنشاء: 2025-06-23
"""

import os
import re
import shutil
from datetime import datetime


class ModProcessorAutoFixer:
    """
    أداة تطبيق الإصلاحات التلقائية
    """
    
    def __init__(self, original_file_path):
        self.original_file = original_file_path
        self.backup_file = f"{original_file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.fixed_file = original_file_path.replace('.py', '_fixed.py')
        
    def create_backup(self):
        """إنشاء نسخة احتياطية من الملف الأصلي"""
        try:
            shutil.copy2(self.original_file, self.backup_file)
            print(f"✅ تم إنشاء نسخة احتياطية: {self.backup_file}")
            return True
        except Exception as e:
            print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def read_file(self, file_path):
        """قراءة محتوى الملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ فشل في قراءة الملف {file_path}: {e}")
            return None
    
    def write_file(self, file_path, content):
        """كتابة المحتوى إلى الملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم حفظ الملف: {file_path}")
            return True
        except Exception as e:
            print(f"❌ فشل في حفظ الملف {file_path}: {e}")
            return False
    
    def apply_description_fixes(self, content):
        """تطبيق إصلاحات دوال الوصف"""
        print("🔧 تطبيق إصلاحات دوال الوصف...")
        
        # إضافة دالة التنظيف
        clean_function = '''
def clean_basic_description(description):
    """
    تنظيف الوصف من أي كلمات إنجليزية زائدة أو علامات غير مرغوب فيها
    """
    if not description:
        return ""
    
    cleaned = description
    
    # إزالة العلامات الإنجليزية الزائدة
    patterns_to_remove = [
        r'\\[ENGLISH_DESCRIPTION\\]',
        r'\\[/ENGLISH_DESCRIPTION\\]',
        r'\\[ARABIC_DESCRIPTION\\]',
        r'\\[/ARABIC_DESCRIPTION\\]',
        r'\\[DESCRIPTION\\]',
        r'\\[/DESCRIPTION\\]',
        r'ENGLISH:',
        r'ARABIC:',
    ]
    
    for pattern in patterns_to_remove:
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
    
    # إزالة المسافات الزائدة والأسطر الفارغة المتعددة
    cleaned = re.sub(r'\\n\\s*\\n', '\\n', cleaned)
    cleaned = re.sub(r'^\\s+|\\s+$', '', cleaned, flags=re.MULTILINE)
    cleaned = cleaned.strip()
    
    return cleaned

'''
        
        # البحث عن مكان مناسب لإضافة الدالة
        if 'def clean_basic_description(' not in content:
            # إضافة الدالة بعد الاستيرادات
            import_end = content.find('# --- Configurations ---')
            if import_end == -1:
                import_end = content.find('class ')
            if import_end == -1:
                import_end = content.find('def ')
            
            if import_end != -1:
                content = content[:import_end] + clean_function + '\n' + content[import_end:]
                print("✅ تم إضافة دالة تنظيف الوصف")
            else:
                print("⚠️ لم يتم العثور على مكان مناسب لإضافة دالة التنظيف")
        
        return content
    
    def apply_function_replacements(self, content):
        """تطبيق استبدالات الدوال"""
        print("🔧 تطبيق استبدالات الدوال...")
        
        # استبدال استدعاءات الدوال
        replacements = [
            (r'generate_description_task', 'generate_simple_description_task'),
            (r'generate_arabic_description_task', 'generate_simple_arabic_description_task'),
            (r'generate_telegram_descriptions_task', 'generate_simple_telegram_descriptions_task'),
        ]
        
        for old, new in replacements:
            if old in content:
                content = content.replace(old, new)
                print(f"✅ تم استبدال {old} بـ {new}")
        
        return content
    
    def apply_image_extractor_import(self, content):
        """إضافة استيراد مستخرج الصور المحسن"""
        print("🔧 إضافة استيراد مستخرج الصور المحسن...")
        
        import_statement = '''
# استيراد المستخرج المحسن
try:
    from enhanced_image_extractor import EnhancedImageExtractor, extract_main_mod_image_simple
    ENHANCED_IMAGE_EXTRACTOR_AVAILABLE = True
    print("Enhanced Image Extractor module loaded successfully.")
except ImportError:
    ENHANCED_IMAGE_EXTRACTOR_AVAILABLE = False
    print("Warning: Enhanced Image Extractor module not found.")
'''
        
        # البحث عن مكان الاستيرادات الأخرى
        mcpedl_import = content.find('from mcpedl_image_extractor_v2 import')
        if mcpedl_import != -1:
            # إضافة بعد استيراد MCPEDL
            end_of_import = content.find('\n', mcpedl_import)
            content = content[:end_of_import] + import_statement + content[end_of_import:]
            print("✅ تم إضافة استيراد مستخرج الصور المحسن")
        
        return content
    
    def apply_cleaning_in_display(self, content):
        """تطبيق تنظيف الوصف عند العرض"""
        print("🔧 تطبيق تنظيف الوصف عند العرض...")
        
        # البحث عن أماكن عرض الوصف وإضافة التنظيف
        patterns = [
            (r"auto_populate_text_widget\(publish_desc_text, mod_data\['description'\]\)",
             "cleaned_description = clean_basic_description(mod_data['description'])\n            auto_populate_text_widget(publish_desc_text, cleaned_description)"),
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                print("✅ تم إضافة تنظيف الوصف عند العرض")
        
        return content
    
    def add_function_definitions(self, content):
        """إضافة تعريفات الدوال الجديدة"""
        print("🔧 إضافة تعريفات الدوال الجديدة...")
        
        # قراءة الدوال الجديدة من ملف الإصلاحات
        try:
            with open('mod_processor_fixed.py', 'r', encoding='utf-8') as f:
                fixed_content = f.read()
            
            # استخراج الدوال الجديدة
            functions_to_add = [
                'generate_simple_description_task',
                'generate_simple_arabic_description_task', 
                'generate_simple_telegram_descriptions_task'
            ]
            
            for func_name in functions_to_add:
                # البحث عن الدالة في ملف الإصلاحات
                func_start = fixed_content.find(f'def {func_name}(')
                if func_start != -1:
                    # العثور على نهاية الدالة
                    func_lines = []
                    lines = fixed_content[func_start:].split('\n')
                    indent_level = None
                    
                    for line in lines:
                        if line.strip() == '':
                            func_lines.append(line)
                            continue
                        
                        # تحديد مستوى المسافة البادئة للدالة
                        if indent_level is None and line.startswith('def '):
                            indent_level = len(line) - len(line.lstrip())
                        
                        # إذا وصلنا لخط بمسافة بادئة أقل، انتهت الدالة
                        if (indent_level is not None and 
                            line.strip() and 
                            not line.startswith(' ' * (indent_level + 4)) and
                            not line.startswith('def ') and
                            len(func_lines) > 1):
                            break
                        
                        func_lines.append(line)
                    
                    new_function = '\n'.join(func_lines)
                    
                    # البحث عن الدالة القديمة واستبدالها أو إضافة الجديدة
                    old_func_pattern = rf'def {func_name.replace("simple_", "")}.*?(?=\ndef |\Z)'
                    if re.search(old_func_pattern, content, re.DOTALL):
                        content = re.sub(old_func_pattern, new_function, content, flags=re.DOTALL)
                        print(f"✅ تم استبدال {func_name}")
                    else:
                        # إضافة الدالة في نهاية الملف
                        content = content.rstrip() + '\n\n' + new_function + '\n'
                        print(f"✅ تم إضافة {func_name}")
        
        except FileNotFoundError:
            print("⚠️ لم يتم العثور على ملف mod_processor_fixed.py")
        except Exception as e:
            print(f"⚠️ خطأ في إضافة الدوال: {e}")
        
        return content
    
    def apply_all_fixes(self):
        """تطبيق جميع الإصلاحات"""
        print("🚀 بدء تطبيق الإصلاحات...")
        
        # إنشاء نسخة احتياطية
        if not self.create_backup():
            return False
        
        # قراءة الملف الأصلي
        content = self.read_file(self.original_file)
        if content is None:
            return False
        
        # تطبيق الإصلاحات
        content = self.apply_description_fixes(content)
        content = self.apply_function_replacements(content)
        content = self.apply_image_extractor_import(content)
        content = self.apply_cleaning_in_display(content)
        content = self.add_function_definitions(content)
        
        # حفظ الملف المحدث
        if self.write_file(self.fixed_file, content):
            print(f"✅ تم إنشاء الملف المحدث: {self.fixed_file}")
            print(f"📄 النسخة الاحتياطية: {self.backup_file}")
            return True
        
        return False


def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🛠️  أداة الإصلاح التلقائي لمودات ماين كرافت")
    print("=" * 60)
    
    # البحث عن الملف الأصلي
    original_file = 'mod_processor_broken_final.py'
    
    if not os.path.exists(original_file):
        print(f"❌ لم يتم العثور على الملف: {original_file}")
        print("تأكد من أن الملف موجود في نفس المجلد")
        return
    
    # إنشاء مثيل من المصلح
    fixer = ModProcessorAutoFixer(original_file)
    
    # تطبيق الإصلاحات
    if fixer.apply_all_fixes():
        print("\n" + "=" * 60)
        print("🎉 تم تطبيق جميع الإصلاحات بنجاح!")
        print("=" * 60)
        print("\n📋 الخطوات التالية:")
        print("1. راجع الملف المحدث واختبر الوظائف")
        print("2. انسخ enhanced_image_extractor.py إلى نفس المجلد")
        print("3. اختبر الأوصاف الجديدة واستخراج الصور")
        print("4. في حالة وجود مشاكل، استخدم النسخة الاحتياطية")
    else:
        print("\n❌ فشل في تطبيق الإصلاحات")
        print("راجع الرسائل أعلاه لمعرفة المشكلة")


if __name__ == "__main__":
    main()
