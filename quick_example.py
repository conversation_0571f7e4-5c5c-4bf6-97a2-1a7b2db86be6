# -*- coding: utf-8 -*-
"""
مثال سريع لاستخدام مستخرج الصور المحسن
يوضح الطرق المختلفة للاستخدام

المطور: MiniMax Agent
التاريخ: 2025-06-22
"""

from mcpedl_image_extractor_v2 import extract_mcpedl_images, get_mod_images_only, MCPEDLImageExtractorV2

def example_simple_usage():
    """مثال 1: الاستخدام المبسط"""
    print("🔥 مثال 1: الاستخدام المبسط")
    print("-" * 40)
    
    url = "https://mcpedl.com/glow-em-all-shader/"
    
    # استخراج الصور فقط
    images = get_mod_images_only(url)
    
    print(f"✅ تم استخراج {len(images)} صورة:")
    for i, img in enumerate(images, 1):
        print(f"  {i}. {img}")


def example_detailed_usage():
    """مثال 2: الاستخدام مع التفاصيل"""
    print("\n🔧 مثال 2: الاستخدام مع التفاصيل")
    print("-" * 40)
    
    url = "https://mcpedl.com/simple-guns-fws/"
    
    # استخراج مع تفاصيل كاملة
    result = extract_mcpedl_images(url, use_selenium=True, headless=True)
    
    if result['success']:
        print(f"📝 اسم المود: {result['mod_name']}")
        print(f"🖼️ عدد الصور: {len(result['main_mod_images'])}")
        print(f"⚙️ طريقة الاستخراج: {result['extraction_method']}")
        print(f"📊 إحصائيات:")
        for key, value in result['statistics'].items():
            print(f"     {key}: {value}")
        
        print(f"\n🎯 الصور الرئيسية:")
        for i, img in enumerate(result['main_mod_images'][:5], 1):  # أول 5 فقط
            print(f"  {i}. {img}")
        
        if len(result['main_mod_images']) > 5:
            print(f"  ... و {len(result['main_mod_images']) - 5} صورة أخرى")
    else:
        print(f"❌ خطأ: {result['error']}")


def example_advanced_usage():
    """مثال 3: الاستخدام المتقدم"""
    print("\n⚙️ مثال 3: الاستخدام المتقدم")
    print("-" * 40)
    
    url = "https://mcpedl.com/azify-revive-shader/"
    
    # إنشاء مستخرج مخصص
    extractor = MCPEDLImageExtractorV2(
        prefer_selenium=True,  # استخدام Selenium للدقة
        headless=True,         # تشغيل المتصفح في الخلفية
        timeout=20             # مهلة انتظار 20 ثانية
    )
    
    try:
        result = extractor.extract_mod_images(url)
        
        if result['success']:
            print(f"🎉 نجح الاستخراج!")
            print(f"   المود: {result['mod_name']}")
            print(f"   الصور: {len(result['main_mod_images'])}")
            
            # تحليل أنواع الصور
            forgecdn_count = sum(1 for img in result['main_mod_images'] if 'forgecdn.net' in img)
            mcpedl_count = len(result['main_mod_images']) - forgecdn_count
            
            print(f"   صور forgecdn: {forgecdn_count}")
            print(f"   صور mcpedl: {mcpedl_count}")
        else:
            print(f"❌ فشل: {result['error']}")
    
    finally:
        extractor.close()  # مهم: إغلاق الموارد


def example_batch_processing():
    """مثال 4: معالجة دفعية لمودات متعددة"""
    print("\n📦 مثال 4: معالجة دفعية")
    print("-" * 40)
    
    urls = [
        "https://mcpedl.com/glow-em-all-shader/",
        "https://mcpedl.com/simple-guns-fws/", 
        "https://mcpedl.com/azify-revive-shader/"
    ]
    
    print(f"🔍 معالجة {len(urls)} مود:")
    
    total_images = 0
    successful_mods = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n{i}. معالجة: {url.split('/')[-2]}")
        
        try:
            images = get_mod_images_only(url)
            
            if images:
                print(f"   ✅ {len(images)} صورة")
                total_images += len(images)
                successful_mods += 1
            else:
                print(f"   ❌ لا توجد صور")
        
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)[:50]}...")
    
    print(f"\n📊 النتائج النهائية:")
    print(f"   مودات ناجحة: {successful_mods}/{len(urls)}")
    print(f"   إجمالي الصور: {total_images}")
    print(f"   متوسط الصور لكل مود: {total_images/successful_mods if successful_mods > 0 else 0:.1f}")


def example_error_handling():
    """مثال 5: معالجة الأخطاء"""
    print("\n🛡️ مثال 5: معالجة الأخطاء")
    print("-" * 40)
    
    # رابط غير صحيح
    bad_url = "https://mcpedl.com/non-existent-mod/"
    
    print(f"🔍 اختبار رابط غير صحيح: {bad_url}")
    
    result = extract_mcpedl_images(bad_url)
    
    if result['success']:
        print(f"✅ نجح (غير متوقع): {len(result['main_mod_images'])} صورة")
    else:
        print(f"❌ فشل كما متوقع: {result['error']}")
        print(f"💡 يمكنك معالجة هذا الخطأ في أداتك")


def main():
    """تشغيل جميع الأمثلة"""
    print("🚀 أمثلة استخدام مستخرج الصور المحسن")
    print("=" * 60)
    
    try:
        # تشغيل الأمثلة بالتسلسل
        example_simple_usage()
        example_detailed_usage() 
        example_advanced_usage()
        example_batch_processing()
        example_error_handling()
        
        print(f"\n🎉 انتهت جميع الأمثلة بنجاح!")
        print(f"\n💡 نصائح للاستخدام:")
        print(f"   • استخدم get_mod_images_only() للاستخدام السريع")
        print(f"   • استخدم extract_mcpedl_images() للحصول على تفاصيل أكثر") 
        print(f"   • استخدم MCPEDLImageExtractorV2() للتحكم الكامل")
        print(f"   • تأكد من إغلاق الموارد في الاستخدام المتقدم")
    
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الأمثلة: {str(e)}")
        print(f"💡 تأكد من تثبيت جميع المتطلبات بـ: pip install -r requirements.txt")


if __name__ == "__main__":
    main()
