# -*- coding: utf-8 -*-
"""
الدوال المحدثة التي يجب دمجها مع mod_processor_broken_final.py
"""

import re
import json

def clean_simple_description(description: str) -> str:
    """تنظيف الوصف ليكون بسيط وبدون فقرات أو مسافات كبيرة"""
    if not description:
        return ""

    # إزالة أي نصوص بين أقواس مربعة مثل [ENGLISH_DESCRIPTION]
    description = re.sub(r'\[.*?\]', '', description)
    
    # إزالة المسافات الكبيرة بين الجمل
    description = re.sub(r'\n\s*\n', ' ', description)
    
    # إزالة المسافات الزائدة
    description = re.sub(r'\s+', ' ', description)
    
    # إزالة النقاط والعلامات الزائدة في بداية النص
    description = re.sub(r'^[\*\-\•\s]+', '', description)
    
    # إزالة أي عناوين أو فقرات منسقة
    description = re.sub(r'^#+\s*', '', description, flags=re.MULTILINE)
    
    # تنظيف النص من الكلمات التسويقية المفرطة
    marketing_words = [
        'fundamentally enhance', 'exceptional experience', 'remarkable addition',
        'innovative gameplay mechanics', 'completely transform', 'effortlessly navigating',
        'expansive landscapes', 'crucial discoveries', 'treasured locations',
        'newfound confidence', 'deepening your immersion', 'transformative mechanics',
        'visual excellence', 'high-quality textures', 'seamlessly into', 
        'elevating its aesthetic appeal', 'engineered for smooth performance',
        'fluid and responsive experience', 'delve into its added layers',
        'exceptionally easy installation', 'absolutely no technical expertise',
        'captivating content', 'unlock a more streamlined', 'true potential'
    ]
    
    for word in marketing_words:
        description = description.replace(word, '')
    
    # تنظيف المسافات الزائدة مرة أخرى
    description = re.sub(r'\s+', ' ', description)
    
    return description.strip()

def generate_simple_description_task(mod_name, mod_category, scraped_text, manual_features):
    """إنشاء وصف بسيط ومختصر للمود"""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء الوصف")
        return

    update_status(f"🔄 بدء إنشاء وصف بسيط للمود: {mod_name}")

    # بناء البرومبت للحصول على وصف بسيط
    prompt = f"""
اكتب وصف بسيط ومختصر لمود Minecraft:

اسم المود: {mod_name}
فئة المود: {mod_category}
معلومات إضافية: {scraped_text[:500] if scraped_text else "غير متوفر"}
الميزات: {manual_features if manual_features else "غير محدد"}

متطلبات الوصف:
- وصف بسيط في جملة أو جملتين فقط
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية للمود
- بدون ذكر إصدارات أو متطلبات تقنية
- أسلوب طبيعي وبسيط

مثال للأسلوب المطلوب:
"هذا المود يضيف كراسي قابلة للجلوس في عالم Minecraft مما يتيح للاعبين الراحة وتزيين منازلهم بأثاث وظيفي."

اكتب الوصف فقط بدون أي إضافات:
"""

    try:
        response = smart_gemini_request(prompt)
        if response:
            # تنظيف الوصف المولد
            simple_description = clean_simple_description(response)
            
            if simple_description:
                update_status("✅ تم إنشاء وصف بسيط بنجاح")
                # تحديث الحقل
                if 'publish_desc_text' in globals() and publish_desc_text is not None:
                    auto_populate_text_widget(publish_desc_text, simple_description)
                return simple_description
            else:
                update_status("❌ فشل في تنظيف الوصف المولد")
        else:
            update_status("❌ لم يتم الحصول على استجابة من Gemini")
    except Exception as e:
        update_status(f"❌ خطأ في إنشاء الوصف: {str(e)}")

def generate_telegram_descriptions_fixed(mod_data):
    """إنشاء أوصاف تيليجرام بسيطة بدون نصوص إضافية"""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء أوصاف التيليجرام")
        return None

    mod_name = mod_data.get('name', '')
    mod_category = mod_data.get('category', '')
    existing_description = mod_data.get('description', '')

    if not mod_name:
        update_status("❌ اسم المود مطلوب لإنشاء أوصاف التيليجرام")
        return None

    update_status(f"🔄 بدء إنشاء أوصاف التيليجرام للمود: {mod_name}")

    # برومبت محسن للحصول على أوصاف بسيطة
    prompt = f"""
اكتب وصفين بسيطين ومختصرين لمود Minecraft:

اسم المود: {mod_name}
النوع: {mod_category}
الوصف الموجود: {existing_description[:300] if existing_description else "غير متوفر"}

متطلبات:
- وصف عربي بسيط في جملة أو جملتين
- وصف إنجليزي بسيط في جملة أو جملتين  
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية
- بدون أي نصوص إضافية أو علامات

قدم الإجابة بالتنسيق التالي:

العربي: [الوصف العربي هنا]

الإنجليزي: [الوصف الإنجليزي هنا]
"""

    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            update_status(f"📤 إرسال طلب إنشاء أوصاف التيليجرام إلى Gemini (المحاولة {attempt + 1})...")
            response = smart_gemini_request(prompt)
            
            if response:
                # استخراج الوصفين
                arabic_desc = ""
                english_desc = ""
                
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('العربي:'):
                        arabic_desc = line.replace('العربي:', '').strip()
                    elif line.startswith('الإنجليزي:'):
                        english_desc = line.replace('الإنجليزي:', '').strip()
                
                # تنظيف الأوصاف
                if arabic_desc:
                    arabic_desc = clean_simple_description(arabic_desc)
                if english_desc:
                    english_desc = clean_simple_description(english_desc)
                
                if arabic_desc and english_desc:
                    update_status("✅ تم إنشاء أوصاف التيليجرام بنجاح")
                    
                    # تحديث الحقول في الواجهة
                    if 'telegram_desc_en_text' in globals() and telegram_desc_en_text is not None:
                        auto_populate_text_widget(telegram_desc_en_text, english_desc)
                    
                    if 'telegram_desc_ar_text' in globals() and telegram_desc_ar_text is not None:
                        auto_populate_text_widget(telegram_desc_ar_text, arabic_desc)
                    
                    return {
                        'ar': arabic_desc,
                        'en': english_desc
                    }
                else:
                    update_status("❌ فشل في استخراج الأوصاف من الاستجابة")
            else:
                update_status("❌ لم يتم الحصول على استجابة من Gemini")
                
        except Exception as e:
            update_status(f"❌ خطأ في إنشاء أوصاف التيليجرام (المحاولة {attempt + 1}): {str(e)}")
            if attempt < max_attempts - 1:
                time.sleep(2)  # انتظار قبل المحاولة التالية
            continue
    
    update_status("❌ فشل في إنشاء أوصاف التيليجرام بعد عدة محاولات")
    return None

def generate_arabic_description_fixed(mod_data):
    """إنشاء وصف عربي بسيط للمود"""
    global gemini_model, current_gemini_key_index, GEMINI_CLIENT_OK

    if not GEMINI_CLIENT_OK:
        update_status("❌ Gemini client غير متوفر لإنشاء الوصف العربي")
        return None

    mod_name = mod_data.get('name', '')
    mod_category = mod_data.get('category', '')
    existing_description = mod_data.get('description', '')

    if not mod_name:
        update_status("❌ اسم المود مطلوب لإنشاء الوصف العربي")
        return None

    update_status(f"🔄 بدء إنشاء وصف عربي للمود: {mod_name}")

    # برومبت للحصول على وصف عربي بسيط
    prompt = f"""
اكتب وصف عربي بسيط ومختصر لمود Minecraft:

اسم المود: {mod_name}
النوع: {mod_category}
الوصف الإنجليزي: {existing_description[:400] if existing_description else "غير متوفر"}

متطلبات:
- وصف عربي بسيط في جملتين أو ثلاث جمل
- بدون فقرات أو مسافات كبيرة
- بدون كلمات تسويقية مفرطة
- ركز على الوظيفة الأساسية
- أسلوب طبيعي وواضح
- بدون أي نصوص إضافية أو علامات

اكتب الوصف العربي فقط:
"""

    try:
        response = smart_gemini_request(prompt)
        if response:
            # تنظيف الوصف المولد
            arabic_desc = clean_simple_description(response)
            
            if arabic_desc:
                update_status("✅ تم إنشاء الوصف العربي بنجاح")
                
                # تحديث الحقل في الواجهة
                if 'publish_arabic_desc_text' in globals() and publish_arabic_desc_text is not None:
                    auto_populate_text_widget(publish_arabic_desc_text, arabic_desc)
                
                return arabic_desc
            else:
                update_status("❌ فشل في تنظيف الوصف العربي المولد")
        else:
            update_status("❌ لم يتم الحصول على استجابة من Gemini للوصف العربي")
    except Exception as e:
        update_status(f"❌ خطأ في إنشاء الوصف العربي: {str(e)}")
    
    return None

# دالة لاستبدال clean_basic_description
def clean_basic_description(description: str) -> str:
    """استبدال الدالة الأصلية بالنسخة المحسنة"""
    return clean_simple_description(description)
