# -*- coding: utf-8 -*-
"""
اختبار Firebase Storage Manager
Test Firebase Storage Manager
"""

import os
import json
import time
from firebase_storage_manager import create_firebase_manager

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase Storage"""
    print("🧪 اختبار الاتصال بـ Firebase Storage")
    print("=" * 50)
    
    # إنشاء مدير Firebase
    manager = create_firebase_manager()
    
    if not manager:
        print("❌ فشل في إنشاء مدير Firebase")
        return False
    
    if not manager.is_initialized:
        print("❌ Firebase غير مهيأ")
        return False
    
    print("✅ تم إنشاء مدير Firebase بنجاح")
    
    # فحص الاتصال
    print("\n🔍 فحص الاتصال...")
    status = manager.check_connection()
    
    print(f"📊 نتائج الفحص:")
    print(f"   - متصل: {'✅' if status['connected'] else '❌'}")
    print(f"   - يمكن الوصول للـ bucket: {'✅' if status['bucket_accessible'] else '❌'}")
    print(f"   - يمكن الرفع: {'✅' if status['can_upload'] else '❌'}")
    print(f"   - يمكن التحميل: {'✅' if status['can_download'] else '❌'}")
    print(f"   - اسم الـ bucket: {status.get('bucket_name', 'غير محدد')}")
    
    if status['error']:
        print(f"   - خطأ: {status['error']}")
    
    if status['test_file_url']:
        print(f"   - رابط الملف التجريبي: {status['test_file_url']}")
    
    return status['connected'] and status['bucket_accessible']

def test_file_upload():
    """اختبار رفع ملف"""
    print("\n📤 اختبار رفع ملف")
    print("-" * 30)
    
    manager = create_firebase_manager()
    if not manager or not manager.is_initialized:
        print("❌ Firebase غير متوفر للاختبار")
        return False
    
    # ملفات اختبار (روابط عامة)
    test_files = [
        {
            'url': 'https://github.com/octocat/Hello-World/archive/refs/heads/master.zip',
            'name': 'Test Mod 1',
            'type': 'addon'
        },
        {
            'url': 'https://httpbin.org/bytes/1024',  # ملف 1KB للاختبار
            'name': 'Test File Small',
            'type': 'test'
        }
    ]
    
    uploaded_files = []
    
    for i, test_file in enumerate(test_files, 1):
        print(f"\n📁 اختبار {i}: {test_file['name']}")
        
        try:
            upload_info = manager.upload_mod_file(
                test_file['url'], 
                test_file['name'], 
                test_file['type']
            )
            
            if upload_info:
                print(f"✅ تم رفع الملف بنجاح!")
                print(f"   - الرابط: {upload_info['firebase_url']}")
                print(f"   - الحجم: {upload_info['file_size']} بايت")
                print(f"   - النوع: {upload_info['content_type']}")
                uploaded_files.append(upload_info)
            else:
                print(f"❌ فشل رفع الملف")
                
        except Exception as e:
            print(f"❌ خطأ في رفع الملف: {e}")
    
    return uploaded_files

def test_file_listing():
    """اختبار قائمة الملفات"""
    print("\n📋 اختبار قائمة الملفات")
    print("-" * 30)
    
    manager = create_firebase_manager()
    if not manager or not manager.is_initialized:
        print("❌ Firebase غير متوفر للاختبار")
        return []
    
    try:
        mods = manager.list_uploaded_mods(10)
        
        print(f"📊 تم العثور على {len(mods)} ملف:")
        
        for i, mod in enumerate(mods, 1):
            name = mod['name'].split('/')[-1]
            size_mb = (mod['size'] or 0) / (1024 * 1024)
            created = mod['created'].strftime('%Y-%m-%d %H:%M') if mod['created'] else 'غير محدد'
            
            print(f"   {i}. {name}")
            print(f"      - الحجم: {size_mb:.2f} MB")
            print(f"      - تاريخ الإنشاء: {created}")
            print(f"      - الرابط: {mod.get('public_url', 'غير متوفر')}")
        
        return mods
        
    except Exception as e:
        print(f"❌ خطأ في جلب قائمة الملفات: {e}")
        return []

def test_storage_stats():
    """اختبار إحصائيات التخزين"""
    print("\n📊 اختبار إحصائيات التخزين")
    print("-" * 30)
    
    manager = create_firebase_manager()
    if not manager or not manager.is_initialized:
        print("❌ Firebase غير متوفر للاختبار")
        return {}
    
    try:
        stats = manager.get_storage_stats()
        
        if stats:
            print(f"📈 إحصائيات التخزين:")
            print(f"   - إجمالي الملفات: {stats.get('total_files', 0)}")
            print(f"   - ملفات المودات: {stats.get('mod_files', 0)}")
            print(f"   - إجمالي الحجم: {stats.get('total_size', 0) / (1024*1024):.2f} MB")
            print(f"   - حجم المودات: {stats.get('mod_size', 0) / (1024*1024):.2f} MB")
            print(f"   - اسم الـ bucket: {stats.get('bucket_name', 'غير محدد')}")
        else:
            print("⚠️ لم يتم الحصول على إحصائيات")
        
        return stats
        
    except Exception as e:
        print(f"❌ خطأ في جلب الإحصائيات: {e}")
        return {}

def test_file_cleanup(uploaded_files):
    """تنظيف الملفات التجريبية"""
    print("\n🧹 تنظيف الملفات التجريبية")
    print("-" * 30)
    
    if not uploaded_files:
        print("⚠️ لا توجد ملفات للحذف")
        return
    
    manager = create_firebase_manager()
    if not manager or not manager.is_initialized:
        print("❌ Firebase غير متوفر للتنظيف")
        return
    
    for upload_info in uploaded_files:
        blob_name = upload_info['blob_name']
        
        try:
            success = manager.delete_mod_file(blob_name)
            if success:
                print(f"✅ تم حذف الملف: {blob_name}")
            else:
                print(f"❌ فشل حذف الملف: {blob_name}")
        except Exception as e:
            print(f"❌ خطأ في حذف الملف {blob_name}: {e}")

def run_comprehensive_test():
    """تشغيل اختبار شامل"""
    print("🚀 بدء الاختبار الشامل لـ Firebase Storage")
    print("=" * 60)
    
    # فحص الإعدادات
    print("🔧 فحص الإعدادات...")
    config_exists = os.path.exists('firebase_config.json')
    print(f"   - ملف الإعدادات: {'✅' if config_exists else '❌'}")
    
    if config_exists:
        try:
            with open('firebase_config.json', 'r') as f:
                config = json.load(f)
            
            required_fields = ['project_id', 'storage_bucket']
            missing_fields = [field for field in required_fields if not config.get(field)]
            
            if missing_fields:
                print(f"   - حقول مفقودة: {missing_fields}")
            else:
                print(f"   - المشروع: {config.get('project_id')}")
                print(f"   - الـ bucket: {config.get('storage_bucket')}")
        except Exception as e:
            print(f"   - خطأ في قراءة الإعدادات: {e}")
    
    # اختبار الاتصال
    connection_ok = test_firebase_connection()
    
    if not connection_ok:
        print("\n❌ فشل اختبار الاتصال. توقف الاختبار.")
        return
    
    # اختبار رفع الملفات
    uploaded_files = test_file_upload()
    
    # اختبار قائمة الملفات
    files_list = test_file_listing()
    
    # اختبار الإحصائيات
    stats = test_storage_stats()
    
    # تنظيف الملفات التجريبية
    test_file_cleanup(uploaded_files)
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    print(f"✅ اختبار الاتصال: {'نجح' if connection_ok else 'فشل'}")
    print(f"📤 اختبار الرفع: {len(uploaded_files)} ملف تم رفعه")
    print(f"📋 اختبار القائمة: {len(files_list)} ملف في القائمة")
    print(f"📊 اختبار الإحصائيات: {'نجح' if stats else 'فشل'}")
    
    if connection_ok and uploaded_files:
        print("\n🎉 جميع الاختبارات نجحت! Firebase Storage جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الإعدادات والاتصال.")

if __name__ == "__main__":
    run_comprehensive_test()
