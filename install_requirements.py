# -*- coding: utf-8 -*-
"""
ملف تثبيت متطلبات مستخرج الصور المحسن
يقوم بتثبيت المكتبات المطلوبة تلقائياً

المطور: MiniMax Agent
التاريخ: 2025-06-22
"""

import subprocess
import sys
import os

def install_package(package_name):
    """تثبيت مكتبة واحدة"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """تثبيت جميع المتطلبات"""
    print("🚀 بدء تثبيت متطلبات مستخرج الصور المحسن...")
    
    # المكتبات الأساسية (مطلوبة)
    essential_packages = [
        "beautifulsoup4>=4.12.0",
        "requests>=2.31.0",
        "urllib3>=2.0.0",
        "lxml>=4.9.0"
    ]
    
    # المكتبات المحسنة (موصى بها)
    enhanced_packages = [
        "selenium>=4.15.0",
        "cloudscraper>=1.2.71",
        "html5lib>=1.1"
    ]
    
    # تثبيت المكتبات الأساسية
    print("\n📦 تثبيت المكتبات الأساسية:")
    essential_success = 0
    for package in essential_packages:
        print(f"⏳ تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package}")
            essential_success += 1
        else:
            print(f"❌ فشل تثبيت {package}")
    
    # تثبيت المكتبات المحسنة
    print("\n🔧 تثبيت المكتبات المحسنة:")
    enhanced_success = 0
    for package in enhanced_packages:
        print(f"⏳ تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package}")
            enhanced_success += 1
        else:
            print(f"⚠️ فشل تثبيت {package} (اختياري)")
    
    # النتائج
    print(f"\n📊 نتائج التثبيت:")
    print(f"   المكتبات الأساسية: {essential_success}/{len(essential_packages)}")
    print(f"   المكتبات المحسنة: {enhanced_success}/{len(enhanced_packages)}")
    
    if essential_success == len(essential_packages):
        print("\n✅ تم تثبيت جميع المتطلبات الأساسية بنجاح!")
        
        if enhanced_success >= 2:  # Selenium + CloudScraper على الأقل
            print("🎉 تم تثبيت المكتبات المحسنة - ستحصل على أفضل أداء!")
        else:
            print("⚠️ بعض المكتبات المحسنة لم يتم تثبيتها - الأداء قد يكون محدود")
    else:
        print("\n❌ فشل في تثبيت بعض المتطلبات الأساسية!")
        print("يرجى تثبيتها يدوياً أو التحقق من الاتصال بالإنترنت")
        return False
    
    # اختبار سريع للاستيراد
    print("\n🧪 اختبار سريع للمكتبات:")
    test_imports = [
        ("beautifulsoup4", "from bs4 import BeautifulSoup"),
        ("requests", "import requests"),
        ("selenium", "from selenium import webdriver"),
        ("cloudscraper", "import cloudscraper")
    ]
    
    for name, import_code in test_imports:
        try:
            exec(import_code)
            print(f"✅ {name}: يعمل بشكل صحيح")
        except ImportError:
            print(f"❌ {name}: غير متاح")
    
    print("\n🎯 التثبيت مكتمل! يمكنك الآن استخدام مستخرج الصور المحسن.")
    return True

if __name__ == "__main__":
    main()
