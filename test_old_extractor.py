# -*- coding: utf-8 -*-
"""
Script de prueba para el extractor antiguo
"""

import os
import sys
import importlib.util

# Construir la ruta completa al archivo
old_tools_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "مجلد ادات قديم")
sys.path.append(old_tools_dir)

# Importar el módulo mcpedl_scraper_module usando importlib
try:
    scraper_path = os.path.join(old_tools_dir, "mcpedl_scraper_module.py")
    spec = importlib.util.spec_from_file_location("mcpedl_scraper_module", scraper_path)
    mcpedl_scraper_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mcpedl_scraper_module)
    scrape_mcpedl_mod = mcpedl_scraper_module.scrape_mcpedl_mod
    print("✅ Módulo importado correctamente usando importlib")
except Exception as e:
    print(f"❌ Error al importar el módulo: {e}")
    sys.exit(1)

# URL de prueba - la misma que estás usando
test_url = "https://mcpedl.com/find-the-button-plus/"

print(f"🧪 Probando extracción de imágenes de: {test_url}")
print("=" * 60)

try:
    # Extraer datos
    mod_data = scrape_mcpedl_mod(test_url)
    
    if mod_data:
        print("✅ Extracción exitosa!")
        print(f"📋 Nombre del mod: {mod_data.get('name', 'No definido')}")
        print(f"📂 Categoría: {mod_data.get('category', 'No definida')}")
        
        # Mostrar imágenes extraídas
        images = mod_data.get('image_urls', [])
        print(f"\n🖼️ Imágenes extraídas ({len(images)} imágenes):")
        print("-" * 40)
        
        if images:
            for i, image_url in enumerate(images, 1):
                print(f"{i:2d}. {image_url}")
                
                # Analizar fuente de la imagen
                if 'media.forgecdn.net' in image_url:
                    print(f"    📍 Fuente: Forgecdn (confiable)")
                elif 'r2.mcpedl.com' in image_url:
                    if '/users/' in image_url:
                        print(f"    📍 Fuente: MCPEDL Users (rechazada)")
                    else:
                        print(f"    📍 Fuente: MCPEDL CDN (confiable)")
                elif 'mcpedl.com' in image_url:
                    print(f"    📍 Fuente: MCPEDL (confiable)")
                else:
                    print(f"    📍 Fuente: Externa")
                print()
        else:
            print("❌ No se encontraron imágenes")
    else:
        print("❌ Falló la extracción de datos")
        
except Exception as e:
    print(f"❌ Error durante la prueba: {e}")
    import traceback
    traceback.print_exc()