# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشاكل MCPEDL scraper
"""

import os
import sys
import shutil
import traceback

def backup_original_files():
    """نسخ احتياطي للملفات الأصلية"""
    print("📁 إنشاء نسخ احتياطية...")
    
    files_to_backup = [
        'mcpedl_scraper_module.py',
        'mcpedl_selenium_scraper.py'
    ]
    
    backup_dir = 'backup_mcpedl_files'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    for file in files_to_backup:
        if os.path.exists(file):
            backup_path = os.path.join(backup_dir, f"{file}.backup")
            shutil.copy2(file, backup_path)
            print(f"✅ تم نسخ {file} إلى {backup_path}")

def fix_cloudscraper_issues():
    """إصلاح مشاكل cloudscraper"""
    print("\n🔧 إصلاح مشاكل cloudscraper...")
    
    try:
        # قراءة الملف الحالي
        with open('mcpedl_scraper_module.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة معالجة أفضل للتشفير
        encoding_fix = '''
                # معالجة التشفير المحسنة
                if response.encoding:
                    response.encoding = response.apparent_encoding or 'utf-8'
                
                # محاولة فك الضغط إذا لزم الأمر
                try:
                    content = response.text
                except UnicodeDecodeError:
                    content = response.content.decode('utf-8', errors='ignore')
                
                # التحقق من Content-Encoding
                content_encoding = response.headers.get('content-encoding', '').lower()
                if content_encoding in ['gzip', 'deflate', 'br']:
                    print(f"محتوى مضغوط: {content_encoding}")
        '''
        
        # البحث عن المكان المناسب للإدراج
        if 'response.text' in content and 'معالجة التشفير المحسنة' not in content:
            content = content.replace(
                'soup = BeautifulSoup(response.text, \'html.parser\')',
                encoding_fix + '\n                soup = BeautifulSoup(content, \'html.parser\')'
            )
            
            # حفظ الملف المحدث
            with open('mcpedl_scraper_module.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إصلاح مشاكل التشفير في cloudscraper")
        else:
            print("⚠️ الملف محدث بالفعل أو لا يحتاج إصلاح")
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح cloudscraper: {e}")

def fix_selenium_issues():
    """إصلاح مشاكل Selenium"""
    print("\n🤖 إصلاح مشاكل Selenium...")
    
    try:
        # قراءة الملف الحالي
        with open('mcpedl_selenium_scraper.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة معالجة أفضل لإغلاق المتصفح
        improved_close = '''
    def close(self):
        """إغلاق المتصفح بأمان"""
        if self.driver:
            try:
                # التحقق من حالة المتصفح أولاً
                try:
                    self.driver.current_url
                    # إغلاق جميع النوافذ
                    for handle in self.driver.window_handles:
                        self.driver.switch_to.window(handle)
                        self.driver.close()
                except:
                    pass
                
                # إنهاء المتصفح
                self.driver.quit()
                print("تم إغلاق متصفح Selenium بأمان")
            except Exception as e:
                print(f"خطأ أثناء إغلاق المتصفح: {e}")
                # محاولة إنهاء العملية
                try:
                    import subprocess
                    subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                                 capture_output=True, check=False)
                except:
                    pass
            finally:
                self.driver = None
        '''
        
        # استبدال دالة close القديمة
        if 'def close(self):' in content:
            # البحث عن بداية ونهاية الدالة
            start_pos = content.find('    def close(self):')
            if start_pos != -1:
                # البحث عن نهاية الدالة (الدالة التالية أو نهاية الكلاس)
                end_pos = content.find('\n    def ', start_pos + 1)
                if end_pos == -1:
                    end_pos = content.find('\nclass ', start_pos + 1)
                if end_pos == -1:
                    end_pos = len(content)
                
                # استبدال الدالة
                new_content = content[:start_pos] + improved_close + content[end_pos:]
                
                # حفظ الملف المحدث
                with open('mcpedl_selenium_scraper.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ تم إصلاح دالة إغلاق Selenium")
            else:
                print("⚠️ لم يتم العثور على دالة close")
        else:
            print("⚠️ الملف لا يحتوي على دالة close")
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح Selenium: {e}")

def add_fallback_mechanism():
    """إضافة آلية fallback محسنة"""
    print("\n🔄 إضافة آلية fallback محسنة...")
    
    fallback_code = '''
def scrape_mcpedl_with_fallback(url: str) -> Optional[Dict[str, Any]]:
    """استخراج بيانات MCPEDL مع آلية fallback محسنة"""
    
    methods = [
        ("Enhanced Scraper", lambda: enhanced_scraper_method(url)),
        ("Original Cloudscraper", lambda: original_cloudscraper_method(url)),
        ("Selenium", lambda: selenium_method(url)),
        ("Basic Requests", lambda: basic_requests_method(url))
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"🔄 محاولة {method_name}...")
            result = method_func()
            
            if result and result.get('name'):
                print(f"✅ نجح {method_name}")
                return result
            else:
                print(f"⚠️ {method_name} لم يعطِ نتائج مفيدة")
                
        except Exception as e:
            print(f"❌ فشل {method_name}: {e}")
            continue
    
    print("❌ فشل جميع الطرق")
    return None

def enhanced_scraper_method(url: str):
    """طريقة المستخرج المحسن"""
    from mcpedl_scraper_enhanced import EnhancedMCPEDLScraper
    
    scraper = EnhancedMCPEDLScraper()
    try:
        soup = scraper.fetch_page_enhanced(url)
        if soup:
            # استخراج البيانات الأساسية
            return extract_basic_data(soup, url)
    finally:
        scraper.close()
    return None

def original_cloudscraper_method(url: str):
    """الطريقة الأصلية مع cloudscraper"""
    from mcpedl_scraper_module import MCPEDLScraper
    
    scraper = MCPEDLScraper()
    try:
        return scraper.scrape_mod_data(url)
    finally:
        scraper.close()

def selenium_method(url: str):
    """طريقة Selenium"""
    try:
        from mcpedl_selenium_scraper import scrape_mcpedl_with_selenium
        return scrape_mcpedl_with_selenium(url)
    except ImportError:
        print("Selenium غير متوفر")
        return None

def basic_requests_method(url: str):
    """طريقة requests أساسية"""
    import requests
    from bs4 import BeautifulSoup
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            return extract_basic_data(soup, url)
    except:
        pass
    return None

def extract_basic_data(soup, url):
    """استخراج البيانات الأساسية"""
    title_element = soup.find('title')
    title = title_element.get_text().strip() if title_element else ""
    
    if title and len(title) > 3:
        return {
            'name': title.replace(' - MCPEDL', '').replace(' | MCPEDL', '').strip(),
            'description': 'A Minecraft addon extracted with fallback method.',
            'category': 'Addons',
            'image_urls': [],
            'version': '1.20+',
            'source_url': url
        }
    return None
'''
    
    try:
        # إضافة الكود إلى ملف منفصل
        with open('mcpedl_fallback.py', 'w', encoding='utf-8') as f:
            f.write('# -*- coding: utf-8 -*-\n')
            f.write('from typing import Dict, Optional, Any\n\n')
            f.write(fallback_code)
        
        print("✅ تم إنشاء آلية fallback محسنة في mcpedl_fallback.py")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء آلية fallback: {e}")

def test_fixes():
    """اختبار الإصلاحات"""
    print("\n🧪 اختبار الإصلاحات...")
    
    try:
        # اختبار المستخرج المحسن
        from mcpedl_scraper_enhanced import test_enhanced_scraper
        if test_enhanced_scraper():
            print("✅ المستخرج المحسن يعمل")
        else:
            print("⚠️ المستخرج المحسن يحتاج مراجعة")
        
        # اختبار آلية fallback
        try:
            from mcpedl_fallback import scrape_mcpedl_with_fallback
            test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
            result = scrape_mcpedl_with_fallback(test_url)
            
            if result:
                print("✅ آلية fallback تعمل")
            else:
                print("⚠️ آلية fallback تحتاج مراجعة")
                
        except Exception as e:
            print(f"⚠️ خطأ في اختبار fallback: {e}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 بدء إصلاح شامل لمشاكل MCPEDL scraper")
    print("=" * 60)
    
    try:
        # نسخ احتياطي
        backup_original_files()
        
        # إصلاح المشاكل
        fix_cloudscraper_issues()
        fix_selenium_issues()
        add_fallback_mechanism()
        
        # اختبار الإصلاحات
        test_fixes()
        
        print("\n🎉 تم الانتهاء من الإصلاحات!")
        print("💡 يمكنك الآن تشغيل mod_processor.py لاختبار التحسينات")
        
    except Exception as e:
        print(f"❌ خطأ في عملية الإصلاح: {e}")
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
