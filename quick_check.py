# -*- coding: utf-8 -*-
import os

def main():
    print("فحص سريع للملفات...")
    
    # فحص الملفات الجديدة
    new_files = [
        "enhanced_integration.py",
        "image_processor.py"
    ]
    
    for file in new_files:
        if os.path.exists(file):
            print(f"[OK] {file} موجود")
        else:
            print(f"[MISSING] {file} مفقود")
    
    # اختبار استيراد بسيط
    try:
        from enhanced_integration import get_enhanced_integration
        print("[OK] enhanced_integration يمكن استيراده")
    except Exception as e:
        print(f"[FAIL] enhanced_integration: {e}")
    
    try:
        from image_processor import ImageProcessor
        print("[OK] image_processor يمكن استيراده")
    except Exception as e:
        print(f"[FAIL] image_processor: {e}")
    
    print("\nالملخص:")
    print("- تم إنشاء ملفات جديدة لحل المشاكل")
    print("- تم إضافة دوال معالجة الصور")
    print("- تم إصلاح مشاكل الاستيراد")
    print("- تم تعطيل استخراج مواقع التواصل")
    print("- تم تحسين معالجة JSON")

if __name__ == "__main__":
    main()
